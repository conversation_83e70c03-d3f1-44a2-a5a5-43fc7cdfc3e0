@font-face {
    font-family: "Material Icons";
    font-style: normal;
    font-weight: 400;
    src: url("../fonts/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2") format("woff2");
}

@font-face {
    font-family: "Material Icons Outlined";
    font-style: normal;
    font-weight: 400;
    src: url("../fonts/gok-H7zzDkdnRel8-DQ6KAXJ69wP1tGnf4ZGhUce.woff2")
        format("woff2");
}

.ad_font-500 {
    font-weight: 500;
}

.ad_font-600 {
    font-weight: 600;
}

.ad_color-blue {
    color: #55c6ff;
}
.ad_padding-10 {
    padding: 10px;
}
.ad_font-9 {
    font-size: 9px;
}
.ad_font-10 {
    font-size: 10px;
}
.ad_font-11 {
    font-size: 11px;
}
.ad_font-12 {
    font-size: 12px !important;
}
.ad_font-13 {
    font-size: 13px;
}
.ad_font-14 {
    font-size: 14px;
}
.ad_font-15 {
    font-size: 15px;
}
.ad_font-16 {
    font-size: 16px;
}
.ad_font-18 {
    font-size: 18px;
}
.ad_font-20 {
    font-size: 20px;
}
.ad_font-22 {
    font-size: 22px;
}
.ad_font-24 {
    font-size: 24px;
}
.ad_font-32 {
    font-size: 32px;
}
.ad_txt-lowercase {
    text-transform: lowercase;
}
.ad_txt-uppercase {
    text-transform: uppercase;
}
.ad_txt-capitalize {
    text-transform: capitalize;
}
.ad_word-break {
    word-break: break-word;
}
.ad_whitespace-no {
    white-space: nowrap;
}
.ad_f-light {
    font-weight: 300;
}
.ad_f-normal {
    font-weight: 400;
}
.ad_f-regular {
    font-weight: 500;
}
.ad_f-bold {
    font-weight: 600;
}
.ad_font-italic {
    font-style: italic;
}
.adfeatured-text {
    padding: 0 15px;
}
.ad_font-bold {
    font-weight: 600;
}
.ad_line-height-n {
    line-height: normal;
}
.ad_inline-list li {
    list-style: none;
    display: inline;
}
.ad_text-decoration-none li a {
    text-decoration: none;
}
.ad_bg-transparent,
.su__btn-transparent {
    background-color: transparent;
}
.ad_bg-blue-grd {
    color: #fff;
    background: linear-gradient(90deg, #7886f7 0%, #55c7ff 100%);
}
.ad_bg-white,
.su__btn-white {
    background-color: #fff;
}
.ad_color-white,
.su__hover-white:hover {
    color: #fff;
}
.ad_color-black {
    color: #000;
}
.ad_color-lblack {
    color: #333a4d;
}
.ad_color-gray {
    color: #474660;
}
.ad_fill-blue {
    fill: #187ef2;
}
.ad_color-dgray,
.su__hover-dgray:hover {
    color: #555;
}
.ad_color-lgray,
.su__hover-lgray:hover {
    color: #5f5f61;
}
.ad_color-blue,
.hover-color-blue:hover {
    color: #0082ca;
}
.ad_color-lblue,
.hover-color-lblue:hover {
    color: #59befe;
}
.ad_color-dblue,
.hover-color-dblue:hover {
    color: #1190cc;
}
.ad_bg-black-50 {
    background-color: #444;
}
.ad_border-color {
    border-color: #1190cc;
}
.ad_bg-blue,
.su__btn-blue,
.su__hover-bg-blue:hover {
    background-color: #0082ca;
    color: #fff;
}
.ad_bg-light-gray,
.su__btn-light-gray,
.su__bg-gray-hover:hover {
    background: #f5f5f5;
}
.ad_bg-dark-gray,
.su__btn-dark-gray {
    background: #ededed;
}
.ad_bg-gray-50 {
    background: #fafafa;
}
.ad_bg-gray-40,
.su__bg-grayhover-40:hover {
    background: #f9fafc;
}
.ad_bg-lgray {
    background: gray;
}
.ad_fill-blue {
    fill: #197ff2;
}
.ad_fill-sblue {
    fill: #187ef2;
}
.ad_fill-orange {
    fill: #ffc10d;
}
.ad_fill-lblue {
    fill: #59befe;
}
.ad_fill-transparent {
    fill: transparent;
}
.ad_fill-white {
    fill: #fff;
}
.ad_fill-black {
    fill: #464646;
}
.ad_fill-dgray {
    fill: #aaaaaa;
}
.ad_fill-gray {
    fill: #bbb;
}
.ad_fill-hover-gray:hover {
    fill: #545454;
}
.ad_text-lowercase {
    text-transform: lowercase;
}
.ad_text-uppercase {
    text-transform: uppercase;
}
.ad_text-hover-underline:hover {
    text-decoration: underline;
}
.ad_text-capitalize {
    text-transform: capitalize;
}
.ad_font-italic {
    font-style: italic;
}
.ad_text-white {
    color: #fff;
}
.ad_text-dgray {
    color: #868e96;
}
.ad_text-dblack {
    color: #333a4d;
}
.ad_d-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.ad_flex-vcenter {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.ad_flex-hcenter {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.ad_flex-start {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
}
.ad_flex-1 {
    flex: 1;
}
.ad_visible {
    visibility: visible;
}
.ad_invisible {
    visibility: hidden;
}
.ad_radius-0 {
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}
.ad_radius {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}
.ad_radius-1 {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}
.ad_radius-2 {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
}
.ad_radius-3 {
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.ad_radius-50 {
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}
.ad_radius-bottom {
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
}
.ad_radius-top {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.ad_rotate-180 {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
}
.ad_rotate-90 {
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
}
.ad_rotate-45 {
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
}
.ad_shadow-lg {
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    -moz-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}
.ad_sm-shadow {
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.14);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.14);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.14);
}
.ad_shadow-hover:hover,
.su__lg-shadow {
    -webkit-box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
        0 5px 8px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
        0 5px 8px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
}
.ad_border {
    border: 1px solid #e6e6e6;
}
.ad_border-t {
    border-top: 1px solid #e6e6e6;
}
.ad_border-r {
    border-right: 1px solid #e6e6e6;
}
.ad_border-b {
    border-bottom: 1px solid #e6e6e6;
}
.ad_border-l {
    border-left: 1px solid #e6e6e6;
}
.ad_border-none {
    border: none;
}
.ad_border-t-none {
    border-top: none;
}
.ad_border-r-none {
    border-right: none;
}
.ad_border-b-none {
    border-bottom: none;
}
.ad_border-l-none {
    border-left: none;
}
.ad_dark-border {
    border: none;
}
.ad_cursor {
    cursor: pointer !important;
    outline: none;
}
.ad_z-index {
    z-index: 1;
}
.ad_zindex {
    z-index: 2;
}
.ad_zindex-1 {
    z-index: 9;
}
.ad_zindex-2 {
    z-index: 99;
}
.ad_zindex-3 {
    z-index: 999;
}
.ad_position-absolute {
    position: absolute;
}
.ad_position-relative {
    position: relative !important;
}
.ad_position-sticky {
    position: sticky;
}
.ad_overflow-hide {
    overflow: hidden;
}
.ad_h-100 {
    height: 100%;
}
.ad_font-montserrat {
    font-family: "Montserrat";
};

.ad_miniscroll::-webkit-scrollbar,
.mat-select-panel::-webkit-scrollbar {
    width: 4px;
    height: 0px;
    background-color: #ffffff;
}
.ad_miniscroll::-webkit-scrollbar-track,
.mat-select-panel::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 15%);
    box-shadow: inset 0 0 6px rgb(0 0 0 / 15%);
    background-color: #ffffff;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.example-chip-list .mat-chip-list-wrapper::-webkit-scrollbar-track {
    background-color: #ffffff;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.ad_miniscroll::-webkit-scrollbar-thumb,
.mat-select-panel::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #9e9e9e;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.example-chip-list .mat-chip-list-wrapper::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #848484;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.ad_miniscroll,
.mat-select-panel,
.example-chip-list .mat-chip-list-wrapper{
    scrollbar-width: thin;
    scrollbar-color: #797979 transparent;
}
.main-panel[data-background-color="black"] .ad_darkmode-bg1,
.app-sidebar[data-background-color="black"] .ad_darkmode-bg1 {
    background-color: #2c2b4c;
}
.main-panel[data-background-color="black"] .ad_darkmode-bg2,
.app-sidebar[data-background-color="black"] .ad_darkmode-bg2 {
    background-color: #1e1c44;
}
.main-panel[data-background-color="black"] .ad_darkmode-bg3,
.app-sidebar[data-background-color="black"] .ad_darkmode-bg3 {
    background-color: #121230;
}
.main-panel[data-background-color="black"] .ad_darkmode-bg4{
    background-color: #1a193f!important;
}
.main-panel[data-background-color="black"] .ad_darkmode-text-white {
    color: #fff;
}
.main-panel[data-background-color="black"] .ad_darkmode-text-black {
    color: #333;
}
.main-panel[data-background-color="black"] .ad_fill-darkmode-white {
    fill: #bfbec7;
}
.main-panel[data-background-color="black"] .ad_fill-darkmode-black {
    fill: #333;
}
.main-panel[data-background-color="black"] .ad_darkmode-text-white,
.main-panel[data-background-color="black"] places-boards,
.app-sidebar[data-background-color="black"] .ad_darkmode-text-white {
    color: #fff;
}
.main-panel[data-background-color="black"] .ad_darkmode-text-black,
.app-sidebar[data-background-color="black"] .ad_darkmode-text-black {
    color: #333;
}
.nav-collapsed .app-sidebar.noHover .ad_lastlogin-block,
.nav-collapsed .ad_lastlogin-block {
    visibility: hidden;
}
.nav-collapsed .app-sidebar.hover .ad_lastlogin-block {
    visibility: visible;
}
.main-content[data-background-color="black"] .card-block .ad_recent-dark {
    border-color: #4da1fc;
}
.main-content[data-background-color="black"] .ad_recent-dark:hover {
    background-color: #006dff26;
}
.main-content[data-background-color="black"] .ad_live-streaming-line {
    background-image: linear-gradient(to right, #1b193f 57%, #413a5e 0%);
}
.material-icons {
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: "liga";
    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;
    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;
    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;
    /* Support for IE. */
    font-feature-settings: "liga";
}

.material-icons-outlined {
    font-family: "Material Icons Outlined";
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
}

a {
    background: transparent;
}

.content-size {
    padding: 14px;
    width: 26px;
    height: 26px;
    background-repeat: no-repeat;
    background-size: cover;
    cursor: pointer;
}

.edit-content {
    background-image: url("assets/img/edit.svg");
}

/* .An-edit-label {
    background-image: url("assets/img/editLabel.svg");
} */

.edit-content:hover,
.edit-content:active {
    background-image: url("assets/img/edit-fill.svg");
}

.save-content{
    background-image: url("assets/img/save_content.svg");
}
.save-content:hover,
.save-content:active {
    background-image: url("assets/img/save_content_fill.svg");
}


.download-content {
    background-image: url("assets/img/Download_a.svg");
}

.sandboxToMigration{
    background: url("assets/img/sandboxToMigration.svg");
}

.sandboxToMigration:hover,
.sandboxToMigration:active{
    background: url("assets/img/sandboxToProductionHover.svg");
}

.migrationReqImg{
    background: url("assets/img/migrationReqIcon.svg");
    width: 38px;
    height: 38px;
    background-repeat: no-repeat;
    border-radius: 3px;
    cursor: pointer;
}

.migrationReqImg:hover,
.migrationReqImg:active{
    background: url("assets/img/migrationReqIconHover.svg");
    width: 38px;
    height: 38px;
    background-repeat: no-repeat;
    border-radius: 3px;
    cursor: pointer;
}

.download-content:hover,
.download-content:active {
    background-image: url("assets/img/Download-hover.svg");
}

.sync-content {
    background-image: url("assets/img/sync.svg");
}
.restore-content {
    background-image: url("assets/img/restore-enabled.svg");
}


.restore-content:hover,
.restore-content:active {
    background-image: url("assets/img/restore-hover.svg");
}

.restore-content-disabled {
    background-image: url("assets/img/restore-disabled.svg");
}

.crawling-sync-content {
    background-image: url("assets/img/stop.svg");
}

.stop-sync-content {
    background-image: url("assets/img/failcrawl.svg");
}
.ad_code-editor {
    background-image: url("assets/img/editor-icons.svg");
    margin-left: 8px;
}
.ad_code-editor:hover,
.ad_code-editor:active {
    background-image: url("assets/img/editor-icons-hover.svg");
}
a.adicon-customize.ad_btn-disabled::before {
    left: 7px;
    right: 0;
    top: -5px;
}
.syncing-content {
    background-image: url("assets/img/syncing.svg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    padding: 10px;
    animation: spin 4s linear infinite;
    transform: rotate(360deg);
}

.syncing-loader {
    background-image: url("assets/img/loader-sync.gif");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.sync-content:hover,
.sync-content:active,
.synced-content {
    background-image: url("assets/img/sync-fill.svg");
}

.to-be-synced-content {
    background-image: url("assets/img/needCrawling.svg");
}

.view-duplicacy {
    background-image: url("assets/img/view_duplicacy.svg");
}

.view-duplicacy:hover,
.view-duplicacy:active {
    background-image: url("assets/img/view_duplicacy-fill.svg");
}

.clone-content {
    border: 1px solid #eee;
    border-radius: 8px;
    background-image: url("assets/img/clone.svg");
    border: 1px solid #ebecee;
    border-radius: 6px;
}

.seelogs {
    background-image: url("assets/img/clone.svg");
}

.nologs {
    background-image: url("assets/img/No logs found.svg");
}

/* .close {
    background-image: url('assets/img/Cross_icon_dark_mode.svg');
} */
.main-panel[data-background-color="black"] .close{
    background-image: url("assets/img/close.svg");
}
.clone-content:hover,
.clone-content:active {
    background-image: url("assets/img/clone-fill.svg");
}

.delete-content {
    background-image: url("assets/img/delete1.svg");
}
.delete-disabled {
    background-image: url("assets/img/deleteDisable.svg");
}

.delete-content:hover,
.delete-content:active {
    background-image: url("assets/img/delete-fill.svg");
}

.delete-content-enabled {
    background-image: url("assets/img/delete-enabled.svg");
}

.delete-content-enabled:hover,
.delete-content-enabled:active {
    background-image: url("assets/img/delete-content-hover.svg");
}
.delete-content-disabled {
    background-image: url("assets/img/delete-disabled.svg");;
}

.arrow-up {
    background-image: url("assets/img/arrow-up.svg");
}

.arrow-down {
    background-image: url("assets/img/arrow-down.svg");
}

.find-replace-content {
    background-image: url("assets/img/find_and_replace.svg");
}

.find-replace-content:hover,
.find-replace-content:active {
    background-image: url("assets/img/find_and_replace_fill.svg");
}


.share-content {
    background-image: url("assets/img/share-icon.svg");
}

.share-content:hover,
.share-content:active {
    /* background-image: url("assets/img/delete-fill.svg"); */
    background-color: blue;
}

.add-condition {
    background-image: url("assets/img/add-condition.svg");
    height: 30px;
}

.main-content[data-background-color="black"] .add-condition {
    height: 29px;
}

.main-content[data-background-color="black"] button.sessionTrackButton {
    color: #fff;
    border: none !important;
    background-color: #1f1e40;
}

.add-condition:hover,
.add-condition:active {
    background-image: url("assets/img/add-condition-hover.svg");
}

.ancd-labelpopup tbody tr td input {
    color: #4b4b4b !important;
    font-size: 12px !important;
}

.ancd-labelpopup tbody tr td:first-child {
    color: #4b4b4b !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}


.table td {
    font-family: "Montserrat";
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.43;
    letter-spacing: normal;
    text-align: left;
    color: #4b4b4b;
}

.table thead th {
    vertical-align: middle;
    border: none;
    font-family: "Montserrat";
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.75;
    letter-spacing: normal;
    text-align: left;
    color: #707070;
}

oauthclients .table thead th {
    white-space: nowrap;
    position: sticky;
    top: 0;
    background: #fff;
}

.bubble-cloud {
    border: 10px solid #e0e8ec;
}

.main-content[data-background-color="black"] .bubble-cloud {
    border: 10px solid #121230;
}

oauthclients .table td {
    padding: 15px 10px !important;
    white-space: normal !important;
}

.main-panel[data-background-color="black"] oauthclients .form-control {
    background-color: transparent;
}

.main-panel[data-background-color="black"] .source-label-box .background-div {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"] .table thead th,
.main-panel[data-background-color="black"] .table thead.t-head {
    color: #ffffff;
    background-color: #1f1e40 !important;
}

.main-panel[data-background-color="black"] home-cmp .table thead th {
    color: #fff !important;
}

.main-content[data-background-color="black"] communityhelper .table thead th {
    color: #707070;
    background-color: #f4f8f9 !important;
}
.main-panel[data-background-color="black"] content-sources .table thead th {
    color: #fff;
    background-color: #201e40 !important;
}
.main-panel[data-background-color="black"]
    .mat-tab-label-active
    .mat-tab-label-content {
    color: #43425d;
}

.main-panel[data-background-color="black"] .form-control {
    background-color: #43425d;
    color: #fff;
    opacity: 0.6;
}

.main-panel[data-background-color="black"] .data-pick {
    background-color: transparent !important;
}

.table tbody + tbody {
    border-top: 2px solid #eceeef;
}

.table .table {
    background-color: #fff;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
}

.card,
.card-title {
    margin-bottom: 0.75rem;
}

.nav > li > a {
    position: relative;
    display: block;
    font-size: 17px;
    padding: 10px 8px !important;
}

.nav > li > a > img {
    max-width: none;
    margin-right: 3px;
}

.nav-tabs {
    border-bottom: 1px solid #ddd;
    background-color: #10a69a;
}

.nav-tabs > li {
    float: left;
    margin-bottom: -1px;
}

.nav-tabs > li > a {
    margin-right: 2px;
    line-height: 1.2;
    border: 1px solid transparent;
    border-radius: 0px 0px 0 0;
    color: #fff;
}

.nav-tabs.nav-justified {
    width: 100%;
    border-bottom: 0;
    display: block;
}

.nav-tabs.nav-justified > li {
    float: none;
}

.nav-tabs.nav-justified > li > a {
    text-align: center;
}

.nav-tabs.nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto;
}

@media (min-width: 768px) {
    .nav-tabs.nav-justified > li {
        display: table-cell;
        width: 1%;
    }

    .nav-tabs.nav-justified > li > a {
        margin-bottom: 0;
    }

  .mat-tab-custom>.mat-tab-header>.mat-tab-label-container {
    text-align: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: flex-start;;
  }
}

.nav-tabs.nav-justified > li > a {
    margin-right: 0;
    border-radius: 0px;
}

.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a:focus {
    border: 1px solid #2179b8;
    background-color: #2179b8;
}

@media (min-width: 768px) {
    .nav-tabs.nav-justified > li > a {
        border-bottom: 1px solid #ddd;
        border-radius: 0px 0px 0 0;
    }

    .nav-tabs.nav-justified > .active > a,
    .nav-tabs.nav-justified > .active > a:hover,
    .nav-tabs.nav-justified > .active > a:focus {
        border-bottom-color: #fff;
    }
}

.breadcrumb > li,
.navbar-brand,
.navbar-nav .nav-item,
.page-link {
    float: left;
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

.navbar {
    padding: 1rem;
    z-index: 1000 !important;
}

.main-content[data-background-color="black"] .navbar {
    position: relative;
}

.navbar:after {
    display: table;
}

.navbar-full {
    z-index: 1000;
}

.navbar-fixed-bottom,
.navbar-fixed-top {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030;
}

.navbar-fixed-top {
    top: 0;
}

.navbar-fixed-bottom {
    bottom: 0;
}

.navbar-sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1030;
    width: 100%;
}

@media (min-width: 544px) {
    .navbar {
        border-radius: 0;
    }

    .navbar-fixed-bottom,
    .navbar-fixed-top,
    .navbar-full,
    .navbar-sticky-top {
        border-radius: 0;
    }

    .main-panel[data-background-color="black"]
        analytics-v2
        .analytics-header-group,
    .main-panel[data-background-color="black"]
        analytics
        .analytics-header-group {
        border-right: solid 2px #625f7c;
    }
}

.main-panel[data-background-color="black"]
    analytics-v2
    .sectionMainDiv
    .CaseReportbckWhite {
    background-color: #1a193f;
    color: #fff;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .sectionMainDiv
    .CaseReportbck,
.main-panel[data-background-color="black"]
    analytics-v2
    .sectionMainDiv
    .CaseReportbckWhite
    > div {
    background-color: #12122f;
}

.navbar-brand {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    margin-right: 1rem;
    font-size: 1.25rem;
}

.navbar-brand:focus,
.navbar-brand:hover {
    text-decoration: none;
}

.navbar-brand > img {
    display: block;
}

@media (min-width: 544px) {
    .navbar-toggleable-xs {
        display: block !important;
    }
}

@media (min-width: 768px) {
    .navbar-toggleable-sm {
        display: block !important;
    }
}

@media (min-width: 992px) {
    .navbar-toggleable-md {
        display: block !important;
    }
}

.navbar-dark .navbar-divider {
    background-color: hsla(0, 0%, 100%, 0.075);
}

button:focus {
    outline: none;
}

.card {
    position: relative;
    display: block;
    background-color: #fff;
    border: solid 1px #eeeeee;
    border-bottom: none;
    box-shadow: 1.4px 4.8px 10px 0 rgba(149, 149, 149, 0.18);
    border-radius: 0;
}

.card-block {
    padding: 0;
}

.card-header {
    padding: 1rem 1.25rem;
    background-color: #fbfbfb;
}

.card-subtitle {
    margin-top: -0.375rem;
}

.card-subtitle,
.card-text:last-child {
    margin-bottom: 0;
}

.card-link:hover {
    text-decoration: none;
}

.card-link + .card-link {
    margin-left: 1.25rem;
}

.card > .list-group:first-child .list-group-item:first-child {
    border-radius: 0.25rem 0.25rem 0 0;
}

.card > .list-group:last-child .list-group-item:last-child {
    border-radius: 0 0 0.25rem 0.25rem;
}

.card-header {
    border-bottom: 1px solid #efefef;
    font-weight: bold;
}

.card-header:first-child {
    border-radius: 0.25rem 0.25rem 0 0;
}

.breadcrumb {
    padding: 0.5rem 1rem;
    margin: 0;
    background-color: #ececec;
    height: 34px;
    font-size: 13px;
}

.breadcrumb-icon {
    margin: 0 10px;
    font-size: 12px;
}

.breadcrumb:after {
    content: "";
    display: table;
    clear: both;
}

.breadcrumb > .active {
    color: #1b1e25;
}

.breadcrumb .fas {
    color: #6c6c6c;
    margin: 0 6px;
    font-size: 12px;
}

.breadcrumb li a,
.breadcrumb > li > .fa {
    color: #6c6c6c !important;
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin-top: 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

.page-item {
    display: inline;
}

.modal-footer:after,
.modal-header:after {
    display: table;
    content: "";
    clear: both;
}

.modal.fade .modal-dialog {
    transition: -webkit-transform 0.3s ease-out;
    transition: transform 0.3s ease-out;
    transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
    -webkit-transform: translateY(-25%);
    transform: translateY(-25%);
}

.modal.in .modal-dialog {
    -webkit-transform: translate(0);
    transform: translate(0);
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto !important;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-open {
    overflow: auto !important;
    padding-right: 0px !important;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
}

.modal-content {
    position: relative;
    background-color: #fafafa;
    border-radius: 8px;
    outline: 0;
    word-wrap: break-word;
    background-clip: padding-box;
}

.main-content[data-background-color="black"] .modal-content {
    background-color: #1f1e40;
}

.modal-backdrop {
    display: none;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.in {
    opacity: 0.5;
}

.modal-header {
    word-break: break-word;
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    display: block;
}

/* .main-content[data-background-color="black"] .modal-header {
    border-bottom: 1px solid #66567c;
} */

.modal-header .close {
    margin: -2px 0 0 0;
    padding: 0;
    color: #707070;
    opacity: 1;
}

.modal-title {
    margin: 0;
    line-height: 1.5;
    font-weight: 400;
}

.main-content[data-background-color="black"] .modal-title {
    font-weight: 500;
}

.modal-body {
    position: relative;
    padding: 6px;
    text-align: -webkit-right;
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}

.tooltip {
    position: absolute;
    display: block;
}

.modal-footer .btn + .btn {
    margin-bottom: 0;
    margin-left: 5px;
}

.modal-footer .btn-group .btn + .btn {
    margin-left: -1px;
}

.modal-footer .btn-block + .btn-block {
    margin-left: 0;
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
}

@media (min-width: 544px) {
    .modal-dialog {
        width: 390px;
        margin: 80px auto;
    }

    .modal-sm {
        width: 300px;
    }
}

.btn:focus {
    outline: none;
}

.main-container {
    position: absolute;
    top: 60px;
    overflow: hidden;
    left: 55px;
    right: 0;
    background-color: #f4f8f9;
}

.topnav {
    border-radius: 0;
    background-color: #fff;
    z-index: 2;
    height: 60px;
    display: block;
    border-bottom: 1px solid #eee;
}

.topnav .text-center {
    text-align: center;
    padding-left: 0;
    cursor: pointer;
    position: relative;
    width: 100%;
}

.topnav .top-right-nav .buy-now a {
    color: #d3d3d3;
}

.topnav .top-right-nav .dropdown-menu {
    top: 60px;
    right: 10px;
    position: fixed;
    left: auto;
    min-width: 7rem;
    padding: 0;
}

.topnav
    .top-right-nav
    .dropdown-menu
    .message-preview
    .media
    .media-body
    .media-heading {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 0;
}

.topnav .top-right-nav .dropdown-menu .message-preview .media .media-body p {
    margin: 0;
}

.topnav
    .top-right-nav
    .dropdown-menu
    .message-preview
    .media
    .media-body
    p.last {
    font-size: 13px;
    margin-bottom: 0;
}

.topnav .top-right-nav .dropdown-menu hr {
    margin-top: 1px;
    margin-bottom: 4px;
}

a:hover {
    text-decoration: none;
}

.sidebar {
    position: fixed;
    width: 55px;
    border: none;
    border-radius: 0;
    bottom: 0;
    padding-bottom: 40px;
    transition-duration: 0.2s;
    transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow-y: auto;
}

.sidebar .list-group a.list-group-item {
    background: #1a2733;
    border: 0;
    border-radius: 0;
    color: #d3d3d3;
    text-decoration: none;
    height: 40px;
    padding: 0.3rem 0;
}

.sidebar .list-group a.router-link-active,
.sidebar .list-group a:hover {
    transition: 0.05s linear;
    background-color: #061621;
    box-shadow: inset 3px 0px 0 0 #5fb72a;
}

.sidebar .sidebar-dropdown:focus {
    border-radius: none;
    border: none;
}

.sidebar .sidebar-dropdown .panel-title {
    font-size: 1rem;
    height: 50px;
    margin-bottom: 0;
}

.sidebar .sidebar-dropdown .panel-title a {
    color: #999;
    text-decoration: none;
    font-weight: 400;
    background: #006767;
}

.sidebar .sidebar-dropdown .panel-title a span {
    position: relative;
    display: block;
    padding: 1rem 1.5rem 0.75rem;
}

.sidebar .sidebar-dropdown .panel-title a:focus,
.sidebar .sidebar-dropdown .panel-title a:hover {
    color: #fff;
    outline: 0;
    outline-offset: -2px;
}

.sidebar .sidebar-dropdown .panel-title:hover {
    background: #004e4e;
}

.sidebar .sidebar-dropdown .panel-collapse {
    border-radius: 0;
    border: none;
}

.sidebar .sidebar-dropdown .panel-collapse .panel-body .list-group-item {
    border-radius: 0;
    background-color: #006767;
    border: 0 solid transparent;
}

.sidebar .sidebar-dropdown .panel-collapse .panel-body .list-group-item a {
    color: #999;
}

.sidebar .sidebar-dropdown .panel-collapse .panel-body .list-group-item:hover {
    background: #004e4e;
}

@media screen and (max-width: 768px) {
    .top-right-nav {
        display: none !important;
    }

    .sidebar {
        left: 0;
    }

    .loading {
        left: 39% !important;
    }

    #contentSource.instruct {
        display: none !important;
    }

    .mat-tab-custom > .mat-tab-header > .mat-tab-label-container {
        text-align: left;
        -webkit-box-pack: left;
        -ms-flex-pack: left;
        justify-content: left;
    }
}

.mat-tab-list {
    flex-grow: 0 !important;
}

.dropdown-menu {
    position: absolute !important;
    top: 110%;
    left: -22px;
    z-index: 1000;
    display: none;
    min-width: 164px !important;
    padding: 0;
    margin: 2px 0 0;
    font-size: 1rem;
    color: #373a3c;
    text-align: left;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.25rem;
}

.knowArrow {
    padding: 1px;
}

footer hr {
    margin-left: 0px;
    background: #e8e8ea;
    width: 94%;
}

.loadingScreen .spinner {
    display: inline-block;
}

.loadingScreen .spinner > div {
    width: 6px;
    height: 14px;
    display: inline-block;
}

.caseList .spinner > div,
.spinner > div {
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.loading {
    display: block;
    text-align: center;
    padding: 64px 0;
    margin: 0;
    font-size: 65px;
    opacity: 0.6;
    text-shadow: 1px 8px 20px;
    color: rgba(13, 43, 102, 0.67);
    font-family: Arial, Helvetica, sans-serif;
}

.caseList .spinner,
.cls-jiveCred,
.load .spinner,
.load .spinner > div,
.small-loader,
.spinner,
.spinner > div {
    display: inline-block;
}

.spinner > div {
    margin-top: 6px;
    width: 5px;
    height: 14px;
    background-color: #3b5998;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.caseList .spinner > div,
.load .spinner > div {
    width: 6px;
    height: 14px;
    background-color: #3b5998;
}

.spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

.caseList .spinner > div {
    display: inline-block;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.caseList .spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.caseList .spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

.load .spinner > div {
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

@-webkit-keyframes sk-bouncedelay {
    0%,
    80%,
    to {
        -webkit-transform: scale(0);
    }

    40% {
        -webkit-transform: scale(1);
    }
}

@keyframes sk-bouncedelay {
    0%,
    80%,
    to {
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    40% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

.load .spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.load .spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

.parent-div {
    padding: 1%;
    font-family: "Montserrat";
}

.small-loader {
    margin-left: 8px;
    margin-top: 1px;
}

.large-loader {
    margin-left: 50%;
    margin-top: 5%;
}

.freq-large-loader {
    margin-left: 50%;
}

.collapsible-title {
    margin-top: 20px;
    margin-bottom: 20px;
    background: teal;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 1px 2px 4px 0;
    color: #fff;
}

.btn-primary,
.btn-primary-yellow .btn-primary-imp-blue {
    padding: 0.375rem 1 rem;
    transition: 0.15s ease;
}

.btn-primary {
    color: teal;
    background-color: #fff;
    border-color: teal;
}

.btn-primary:hover {
    color: #fff;
    background-color: teal;
    border-color: teal;
}

.a-icon-edit {
    color: #a8a8a8;
}

.a-icon-edit:hover {
    color: #7e7e7e;
}

.a-icon-delete {
    color: #ff3232;
}

.a-icon-delete:hover {
    color: #b20000;
}

.btn-primary-yellow {
    color: #e59400;
    background-color: #fff;
    border-color: #e59400;
}

.btn-primary-yellow:hover {
    color: #fff;
    background-color: #e59400;
    border-color: #e59400;
}

.btn-primary-imp-blue {
    color: #fff;
    background-color: #2179b8;
    border-color: #2179b8;
}

.btn-primary-imp-blue:hover {
    color: #fff;
    background-color: #2179b8;
    border-color: #2179b8;
}

.t-head {
    background-color: #e0e8ec !important;
    border: none !important;
}

.main-content[data-background-color="black"] .content-rules-table thead.t-head {
    background-color: #1f1e40 !important;
}

.btn-primary:active {
    background-image: none;
}

.btn-primary:active,
.btn-primary:disabled:hover,
.btn-primary:focus {
    color: #fff;
    background-color: teal;
    border-color: teal;
}

.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link,
.navbar-dark .navbar-nav .nav-link:hover {
    color: #006767;
}

.sidebar {
    top: 90px;
    z-index: 999;
    background-color: #1a2733;
}

.checkboxActive {
    position: relative;
}

.checkboxActive label {
    width: 18px;
    height: 18px;
    cursor: pointer;
    position: absolute;
    background: #55c6ff;
    border-radius: 2px;
    text-align: center;
    left: 4%;
}

.checkboxActive label:after {
    content: "";
    width: 9px;
    height: 5px;
    position: absolute;
    top: 5px;
    left: 5px;
    border: 2px solid #fff;
    border-top: none;
    border-right: none;
    background: 0 0;
    opacity: 0;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.checkboxActive label:hover:after {
    opacity: 0;
}

.checkboxActive input[type="checkbox"] {
    visibility: hidden;
}

.checkboxActive input[type="checkbox"]:checked + label:after {
    opacity: 1;
}

.btn-primary.btnAddContent {
    font-size: 23px;
    padding: 0 10px;
}

.checkboxActive .colChk label.my-class,
.checkboxActive label.my-class {
    background: #fff !important;
    border: 1px solid #55c6ff !important;
}

.row-types .types-list.checkboxActive label {
    position: relative;
    left: -7%;
    top: 10px;
}

.main-panel[data-background-color="black"] .row-types td a {
    color: #fff !important;
}

.main-panel[data-background-color="black"] content-sources .row-types td a {
    color: inherit !important;
}

.cls-jiveCred {
    padding: 0;
    vertical-align: top;
    width: 100%;
}

input::-webkit-calendar-picker-indicator {
    color: transparent;
    opacity: 1;
}

input::-webkit-calendar-picker-indicator:after {
    content: "";
    display: block;
    background: url(../src/assets/img/activities_grey.png) no-repeat;
    background-size: 10%;
    width: 100px;
    height: 100px;
    position: absolute;
    -webkit-transform: translateX(-2%);
    transform: translateX(-2%);
}

input::-webkit-clear-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none;
}

.inline-block {
    display: inline-block;
}

.row-types label {
    word-break: break-all;
    font-weight: 400;
}

.breadcrumb.breadcrumb-contentsource {
    padding: 0.75rem 1.5rem;
}

.tile-title {
    font-size: 16px;
    font-weight: 600;
    padding: 0px 10px 0px 10px;
    vertical-align: middle;
}

.tile-number {
    padding: 10px;
    color: #217ab7;
    font-weight: 600;
    font-size: 30px;
    text-align: left;
    width: 90px;
    font-size: 16px;
    position: absolute;
    margin-top: 25px;
}

.row-types .types-list.checkboxActive .colChk label {
    left: -20px;
    top: 11px;
}

.row-types .types-list.checkboxActive .colChkBox label {
    left: -20px;
    top: 5px;
}

.checkboxActive.types-list.col-lg-12,
.checkboxActive.types-list.col-md-12,
.checkboxActive.types-list.col-sm-12,
.col-lg-2.colChk,
.col-md-2.colChk,
.col-sm-2.colChk,
.row-types .types-list.checkboxActive .col-lg-10,
.row-types .types-list.checkboxActive .col-md-10,
.row-types .types-list.checkboxActive .col-sm-10 {
    padding: 0;
}

.pointer {
    cursor: pointer;
}

.table-style {
    border-collapse: separate;
    border-spacing: 0;
    font-size: 15px;
    margin-top: 0.75rem;
    float: left;
}

.no-padding {
    padding: 0 1em !important;
}

.dataTableDiv .ahScrollbar::-webkit-scrollbar{
    width: 6px;
}

.example-chip-list .mat-chip-list-wrapper::-webkit-scrollbar{
    width: 6px;
}

.dataTableDiv .ahScrollbar::-webkit-scrollbar-thumb {
    background-color: #B7B7B7;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}

.dataTableDiv .ahScrollbar::-webkit-scrollbar-track {
    background-color: #ffffff;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.custom-scroll-bar::-webkit-scrollbar,
.e-rte-content::-webkit-scrollbar,
.perfect::-webkit-scrollbar,
.recommendations-section-height::-webkit-scrollbar,
.LineChartContainer::-webkit-scrollbar,
.BarChartContainer1::-webkit-scrollbar,
.Session-Tracking-Details-popup::-webkit-scrollbar,
.Top-Clicked-Searches::-webkit-scrollbar,
.topClicks::-webkit-scrollbar,
.mat-tab-body-content::-webkit-scrollbar,
.mat-dialog-content::-webkit-scrollbar,
.scroll-css::-webkit-scrollbar,
.Session-Analytics-Overview-graph::-webkit-scrollbar,
.asset-library-body-right::-webkit-scrollbar,
.scrollable-area::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f3f6fa;
}

.custom-scroll-bar::-webkit-scrollbar-thumb,
.e-rte-content::-webkit-scrollbar-thumb,
.perfect::-webkit-scrollbar-thumb,
.recommendations-section-height::-webkit-scrollbar-thumb,
.LineChartContainer::-webkit-scrollbar-thumb,
.BarChartContainer1::-webkit-scrollbar-thumb,
.Session-Tracking-Details-popup::-webkit-scrollbar-thumb,
.Top-Clicked-Searches::-webkit-scrollbar-thumb,
.topClicks::-webkit-scrollbar-thumb,
.mat-tab-body-content::-webkit-scrollbar-thumb,
.mat-dialog-content::-webkit-scrollbar-thumb,
.scroll-css::-webkit-scrollbar-thumb,
.Session-Analytics-Overview-graph::-webkit-scrollbar-thumb,
.asset-library-body-right::-webkit-scrollbar-thumb,
.scrollable-area::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.searchCount::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f3f6fa;
}

.searchCount::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.queryFilters-div::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f3f6fa;
}

.queryFilters-div::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.overflow-height::-webkit-scrollbar {
    width: 6px;
    background-color: #f3f6fa;
}

.overflow-height::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.actions-div::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f3f6fa;
}

.actions-div::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.ChartContainer1::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f3f6fa;
}

.ChartContainer1::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.suggest::-webkit-scrollbar {
    width: 6px;
    background-color: #f3f6fa;
}

.suggest::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.perfect::-moz-scrollbar {
    width: 5px;
    background-color: #b2b7be;
}

.activeType {
    border-top: 1px solid #e9e9e9;
    border-left: 1px solid #e9e9e9;
    border-right: 1px solid #e9e9e9;
    border-bottom: 1px solid #fff !important;
    margin-bottom: -1px !important;
    background-color: #fff !important;
}

.pagination-search-tuning {
    padding: 5px;
    float: right;
}

.pagination-search-tuning > nav > ul > li > a {
    cursor: pointer;
    color: #5fb72a;
    background-color: #fff;
}

.pagination-search-tuning > nav > ul > li > a:hover {
    background: #d3d3d3;
    color: #5fb72a;
}

.pagination > li.active > a {
    background: #55c6ff !important;
    color: #fff !important;
    border: 1px solid #dee2e6 !important;
}

.search-text-search-tuning {
    padding: 5px 0 10px;
}

.search-text-search-tuning > div {
    display: inline-block;
}

.search-tuning-loader-save {
    display: inline-block;
    margin-left: 10px;
}

.highlight {
    background: wheat;
}

.search-tuning-rocket {
    float: right;
    margin: 3px 0 0 5px;
    width: 20px;
    color: #8bc34a !important;
}

.table-places tbody tr td {
    border: none;
}

.grow {
    transition: all 0.2s ease-in-out;
}

.onoffswitch {
    position: relative;
    width: 90px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.onoffswitch-checkbox {
    display: none;
}

.onoffswitch-label {
    display: block;
    overflow: hidden;
    cursor: pointer;
    border-radius: 12px;
    width: 100px;
    height: 26px;
}

.onoffswitch-inner {
    display: block;
    width: 200%;
    margin-left: -100%;
    transition: margin 0.3s ease-in 0s;
}

.onoffswitch-inner:before,
.onoffswitch-inner:after {
    display: block;
    float: left;
    width: 50%;
    height: 30px;
    padding: 0;
    line-height: 26px;
    font-size: 14px;
    font-family: Trebuchet, Arial, sans-serif;
    font-weight: 700;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.onoffswitch-inner:before {
    content: "Active";
    padding-left: 10px;
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
    color: #ffffff;
}

.onoffswitch-inner:after {
    content: "Inactive";
    padding-right: 10px;
    background-color: #e9ebf0;
    color: #999999;
    text-align: right;
}

.onoffswitch-switch {
    display: block;
    margin: 4px 2px 8px 6px;
    width: 17px;
    height: 18px;
    background: #fff;
    position: absolute;
    top: 0;
    bottom: 0;
    /* right: 60px; */
    border-radius: 13px;
    -webkit-transition: all 0.3s ease-in 0s;
    transition: all 0.3s ease-in 0s;
}

.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {
    margin-left: 0;
}

.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {
    right: 0px;
}

.sidebar .list-group a.list-group-item.largeName {
    padding: 7px 0px;
    height: 91px;
}

.sidebar::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
    background-color: #f5f5f5;
    display: none;
}

.sidebar::-webkit-scrollbar {
    width: 2px;
    background-color: #f5f5f5;
    display: none;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: #1b1e25;
    display: none;
}

textarea::-webkit-scrollbar {
    width: 6px;
    background-color: #f3f6fa;
}

textarea::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

span.navbarItem {
    font-size: 16px;
    color: #1b1e25;
}

@media screen and (max-width: 360px) {
    @media screen and (min-width: 768px) {
        .top-right-nav {
            display: block;
        }
    }
}

td.lbl-form {
    float: left;
    display: inline-block;
    height: 23px;
}

td.lbl-form label {
    color: #7f8fa4;
    letter-spacing: normal;
    font-family: "Montserrat";
    font-size: 14px;
    text-transform: capitalize;
    margin-bottom: 0;
}

.main-panel[data-background-color="black"] td.lbl-form label {
    color: #fff;
    opacity: 0.6;
}

.parent-div {
    margin: 0;
}

.enlarge:hover {
    z-index: 434;
    border: 2px solid #55c5fe;
    box-shadow: 0 0 9.2px 2.8px #f3f8f9;
}

.enlarged {
    border-style: solid;
    border-width: 1px;
    border-color: rgb(206, 206, 206);
    background-color: rgb(250, 250, 250);
    box-shadow: 0px 0px 7.76px 0.24px rgba(33, 122, 183, 0.35);
    z-index: 434;
    transform: scale(1.007);
}

.enlarged-template {
    border-width: 1px;
    border-color: rgba(33, 122, 183, 0.35);
    background-color: rgb(250, 250, 250);
    box-shadow: 0px 0px 7.76px 0.24px rgba(33, 122, 183, 0.35);
    z-index: 0;
    transform: scale(1.007);
}

.cs-box {
    border: 1px solid #dfdfdf;
    cursor: pointer;
}

.cs-box-other {
    display: flex;
    border-radius: 8px;
    border: 2px solid #dfdfdf;
}

.main-panel[data-background-color="black"] .cs-box-other {
    background: #ffffff;
}

.cs-box-other-lower {
    margin-top: 20px;
}

.eng-character {
    font-weight: 500;
    margin-right: 6px;
    text-decoration: none;
    font-size: 14px;
    letter-spacing: 12px;
    color: #cacaca;
}

.active-character {
    color: #fff !important;
    border-radius: 4px;
    background: #55c6ff73;
    padding: 5px 0px 4px 8px;
    display: inline-block;
    width: 28px;
    height: 28px;
    text-align: center;
}

.eng-character:hover {
    color: #55c6ff;
    font-weight: bold;
}

a.themeColor.eng-character.ng-star-inserted.eng-character-active {
    color: #55c6ff;
}

a.themeColor.eng-character.eng-character-active {
    color: #277eab;
}

.eng-character-active {
    color: #55c6ff;
}

.filter-box:nth-child(even) {
    background-color: #f7f7f7;
}

.filter-is-selected {
    background-color: #e8f6ff !important;
}

/*generate search client styles*/

/*Form validations*/

.form-invalid-exclamation {
  width: 20px;
}

.main-panel[data-background-color="black"] .footerActions {
    background: #121230;
    border: none;
}

.main-panel[data-background-color="black"] objects-fields .footerActions {
    background: transparent;
    border: none;
}

.form-jv {
    width: 100%;
    display: inline-block;
}

.main-panel[data-background-color="black"] .mat-select-value-text {
    color: #fff !important;
    font-weight: 500;
}

/* amcharts maps */

.chartDiv {
    width: 100%;
    height: 450px;
    padding: 10px 15px;
}

/* Popover styling */

.activeTuning {
    background-color: #e6f5ff !important;
}

.tuning-head {
    width: 100%;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid hsl(0, 3%, 86%);
}

.frequency-form {
    padding: 7px 8px 9px 8px;
    display: inline-block;
    width: 230px;
}

select:focus {
    outline: none;
}

.center-align {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.forget-password {
    background: none;
    border: none;
    color: #2179b8;
    padding: 9px;
    cursor: pointer;
}

.forget-password-label:hover {
    box-shadow: 0 0 1px #a09797;
    border-radius: 5px;
}

.forget-password-label {
    padding-right: 5px;
}

/* DatePicker */

.daterangepicker .ranges li.active,
.ranges li:hover {
    border: none !important;
}

.daterangepicker .ranges li {
    background: none;
    border: none;
    line-height: 24px;
}

.daterangepicker {
    color: #333333;
}

.dropdown-menu {
    font-size: 13.5px;
}

.daterangepicker .calendar td,
.daterangepicker .calendar th {
    line-height: 23px;
}

/** Charts CSS*/

.tick text {
    fill: #707070;
    font-size: 12px;
    font-family: 'Montserrat';
}

.tick line {
    stroke: #eaf0f4;
}

.x-Axis path {
    stroke: #eaf0f4;
}

.y-Axis path {
    stroke: #eaf0f4;
}

.y-Axis .tick::first-child line {
    stroke: #c5c5c5;
}

.gantt-x-axis text {
    transform: rotate(-20deg);
}

.handle {
    fill: #016cfa !important;
    width: 3.5px !important;
}

.axis--x .selection {
    fill: #1b65c13b !important;
    fill-opacity: 0.6 !important;
}

.axis--x .overlay {
    fill: #ececec80 !important;
}

.axis--x .tick text {
    fill: #000000 !important;
    color: #000000 !important;
}

.sidebar-image {
    height: 20px;
}

a:active,
a:focus,
a:hover {
    outline: 0;
    text-decoration: none;
}

a {
    color: #0275d8;
    text-decoration: none;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    font-family: 'Montserrat'!important;
}

.fa,
.far,
.fas {
    color: #006efc;
    font-size: 14px;
}

.btn:focus {
    box-shadow: none;
}

body {
    font-size: 14px;
    color: #1b1e25;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
    background-color: #f3f8f9;
}

.topHeading {
    background-color: #f4f8f9;
    padding: 10px 20px;
    border-bottom: 1px solid #e9ebf0;
}

.sectionDiv {
    background-color: #fff;
    border-bottom: 0;
    margin: 20px;
    border-radius: 10px;
}

.sectionHead {
    font-family: "Montserrat";
    font-weight: 600;
    line-height: normal;
    letter-spacing: normal;
    color: #707070;
    padding: 15px;
}

.sectionSpan {
    opacity: 0.59;
    font-family: "Montserrat";
    font-size: 14px;
    padding: 15px;
    font-weight: 400;
}

.heading-source {
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    font-family: "Montserrat";
    font-weight: 600;
    line-height: 1;
    text-align: left;
    color: #707070;
}

.topHeading .heading-source {
    font-family: Montserrat;
    font-size: 22px;
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
}

.link-head {
    color: #006efc;
    border: none;
    background: transparent;
    width: 140px;
    cursor: pointer;
}

.inline-block .buttonPrimary i {
    font-size: 18px;
    color: #43425d;
}

.main-panel[data-background-color="black"] .inline-block .buttonPrimary {
    background: #1e1c44;
    border: 1px solid #afaebb;
    color: #fff;
}

.inline-block .buttonPrimary:hover {
    background: #55c6ff;
    border: none;
    color: #fff;
}

.inline-block .buttonPrimary:hover i {
    color: #fff !important;
}

.buttonPrimary {
    border: 1px solid #55c6ff;
    width: auto;
    min-width: 80px;
    border-radius: 2px;
    padding: 0px 10px;
    margin-right: 0px;
    font-weight: 500;
    background-color: transparent;
    color: #55c6ff;
    font-family: "Montserrat";
    font-size: 14px;
    line-height: 36px;
}

.float-left .buttonPrimary:nth-child(1) {
    margin-right: 20px;
}

.float-left .buttonPrimary{
    min-width: 70px;
}

.main-panel[data-background-color="black"] .footerActions .buttonPrimary {
    background: #121230;
    border: 2px solid #afaebb;
    color: #fff;
}

.main-panel[data-background-color="black"]
    objects-fields
    .footerActions
    .buttonPrimary {
    background-color: transparent;
    border: 2px solid #55c6ff;
    color: #55c6ff;
}

.main-panel[data-background-color="black"]
    objects-fields
    .footerActions
    .buttonPrimary:hover {
    background-color: #55c6ff;
    border: none;
    color: #fff;
}

.sectionMainDiv {
    padding: 0 15px 15px;
    font-family: "Montserrat";
}

.sectionLabel {
    opacity: 0.69;
    font-family: "Montserrat";
    font-size: 12px;
    font-weight: normal;
    color: #43425d;
    letter-spacing: 0px;
    line-height: 1.5;
}

.sectionButton {
    float: right;
    border: none;
}

.create-app {
    color: #fff;
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
    padding: 1px 11px;
}

.table-su {
    margin-bottom: 0;
    position: relative;
}

objects-fields .table-su {
    box-shadow: none;
}

home-cmp .table-su {
    box-shadow: 0 8px 10px -6px #9735351a;
    z-index: 5;
}

.table-su .selectedrow {
    background-color: white !important;
}

.table-su tbody tr {
    border-top: solid 1px rgba(0, 0, 0, 0.12);
    background-color: #ffffff;
}

session-report-detail .table-su tbody tr {
    border-left: solid 1px rgba(0, 0, 0, 0.12);
}

.sideTriangle {
    text-align: center;
}

.map_canvas {
    width: 580px;
    height: 310px;
    margin: 0;
    padding: 0;
}

.tool {
    pointer-events: none;
}

.ribbon {
    position: absolute;
    left: 10px;
    top: 10px;
    overflow: hidden;
    width: 75px;
    height: 75px;
    text-align: right;
}

.ribbon span {
    font-size: 11px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    line-height: 20px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    width: 100px;
    display: block;
    position: absolute;
    top: 19px;
    left: -20px;
    background-color: #ef701b;
    box-shadow: 0.6px 1.9px 3px 0 rgba(26, 39, 51, 0.29);
}

.buttonSecondary {
    float: right;
    background-color: transparent;
    border: 2px solid #afb2bc;
    width: auto;
    border-radius: 2px;
    font-family: "Montserrat";
    font-size: 14px;
    line-height: 36px;
    text-align: center;
    padding: 0px 10px;
    color: #4b4b4b;
    margin-right: 0px;
    font-weight: 500;
    min-width: 80px;
}

.main-panel[data-background-color="black"] .buttonSecondary,
.main-panel[data-background-color="black"] .detailed-Report-selected-heading {
    color: #ffffff;
}

.main-panel[data-background-color="black"] .buttonSecondary:hover {
    opacity: 1;
}

.main-panel[data-background-color="black"] .buttonPrimary:hover:not(.no-button-hover) {
    background: #55c6ff;
    color: #ffffff !important;
    border: none;
}

.main-panel[data-background-color="black"] .assetDiv .buttonPrimary:hover,
.main-panel[data-background-color="black"] .alertDiv .buttonPrimary:hover,
.main-panel[data-background-color="black"] .addonDiv .buttonPrimary:hover {
    background-image: linear-gradient(to left, #55c7ff, #7886f7) !important;
    color: #fff !important;
}

.buttonSecondary:hover {
    color: #fff;
    border: none;
    font-weight: 600;
    padding: 2px 12px;
    box-shadow: 4px 10px 16px 0 rgba(53, 88, 185, 0.16);
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
}

.btnBack {
    border: none;
}

.buttonPrimary:hover:not(.no-button-hover),
.btnBack:hover {
    border: none !important;
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
    color: #fff;
    box-shadow: none !important;
    padding: 1px 11px
}

.alertMainDiv .mat-tab-label-active {
    border-bottom: 2px solid #55c6ff;
    font-weight: bold;
    background: transparent;
}

.alertMainDiv .mat-tab-group.mat-primary .mat-ink-bar,
.alertMainDiv .mat-tab-nav-bar.mat-primary .mat-ink-bar {
    background-color: transparent;
}

.button-add-content:disabled,
.button-add-content:disabled:hover {
    box-shadow: none;
    color: #7f8fa4 !important;
    background-image: none;
    background: #F3F5F7 0% 0% no-repeat padding-box;
    border: 1px solid #F3F3F3;
    border-radius: 4px;
    width: 115px;
    height: 34px;
}

.reconnect-btn{
    width: 140px !important;
}

.main-content[data-background-color="black"] .button-add-content:disabled {
    border: none !important;
    box-shadow: none;
    background-color: transparent !important;
    color: #fff !important;
    background-image: none;
}

.button-add-content, .connected-btn {
    font-family: "Montserrat";
    height: 44px;
    border-radius: 8px;
    background-color: transparent !important;
    color: #7f8fa4;
    border: none;
    font-weight: 600;
    font-size: 14px;
}

.connecting-btn{
    color: #64DB66 !important;
    font: normal normal 600 14px/18px Montserrat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.button-authenticate-content{
    font: normal normal 500 12px/15px 'Montserrat', sans-serif;
    color: #55C6FF;
    background-color: transparent !important;
    text-decoration: underline;
    border: none;
    opacity: 1;
    letter-spacing: 0px;
    padding: 12px 0px 17px 0px ;
    /* padding-top: 14px;
    padding-bottom: 14px; */
}

.authenticated-user-content{
    padding: 5px;
    background: #DBEAFF 0% 0% no-repeat padding-box;
    border-radius: 4px;
    opacity: 1;
    text-align: left;
    font: normal normal 500 12px/15px Montserrat;
    letter-spacing: 0px;
    color: #43425D;
}

.main-panel[data-background-color="black"] .button-add-content-connected {
    color: #f59100;
}

.button-add-content-connected {
    font-family: "Montserrat";
    height: 45px;
    border-radius: 8px;
    background-color: transparent !important;
    color: #f59100;
    border: none;
    font-weight: 600;
    font-size: 14px;
}

.button-add-content-connected .connected {
    background-image: url("assets/img/connect_hover_color.svg");
}

.button-add-content:hover {
    background-image: none;
    color: #f59100 !important;
}

button {
    cursor: pointer;
}

.table-style {
    border-collapse: separate;
    border-spacing: 0;
    font-size: 15px;
    margin-top: 0.75rem;
}

.table-style .lbl-form {
    width: 100%;
}

.spanInfo {
    width: 546px;
    font-family: Montserrat;
    font-size: 13px;
    color: #787878;
    display: block;
}

.themeColor {
    color: #55c6ff;
    text-decoration: none;
    font-size: 14px;
    letter-spacing: 7px;
}

.infoColor {
    color: #ef701b;
    font-size: 15px;
}

.tab-content > .active {
    opacity: 1;
}

.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto {
    display: inline-block;
}

.footerActions {
    background-color: transparent;
    height: auto;
    text-align: center;
    bottom: 30px;
}

content-sources .float-right,
content-sources .float-left {
    margin-top: 20px;
}

button:disabled,
button:disabled:hover:not(.mat-tab-disabled) {
    cursor: not-allowed;
    border: 1px solid #eee !important;
    background-color: #f3f5f7 !important;
    color: #7f8fa4 !important;
    background-image: none;
    padding: 0px 10px !important;
}

.instruct {
    height: auto;
    width: 240px;
    float: right;
    font-size: 13px;
    font-weight: 400;
}

@media (min-width: 1200px) {
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12 {
        float: left;
    }
}

@media (min-width: 1400px) {
    .member-list-item-detail .options .option.button-link.user-scope-button {
        margin: 4px 0px;
    }

    .member-list-item-detail .options .option {
        margin-left: 45px !important;
    }
}

.mat-input-element:disabled {
    color: grey;
}

.smallImage {
    height: 44px;
    width: 44px;
}

.largeImage {
    margin: auto;
}

/* .mat-tab-labels {
  display: block !important;
} */

.footerOverlay {
    position: absolute;
    width: 100%;
}

.animate-div {
    z-index: 1000;
    position: fixed;
    width: 80vw;
    height: 80vh;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    background-color: #fff;
    transition: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    border-radius: 10px;
}
.animate-div-without-height{
    z-index: 1000;
    position: fixed;
    width: 80vw;
    height: fit-content;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    background-color: #fff;
    transition: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    border-radius: 10px;
}

.form-control {
    border-radius: 0;
    font-family: "Montserrat";
}

.mat-focused .mat-form-field-label,
.mat-focused .mat-form-field-required-marker {
    color: #55c6ff;
}

.mat-input-element {
    caret-color: #55c6ff;
    color: #43425d !important;
    font-weight: 500 !important;
    font-family: "Montserrat" !important;
}


.mat-form-field-label-wrapper {
    font-weight: 600;
    font-family: "Montserrat";
}

.main-panel[data-background-color="black"] .mat-input-element {
    color: #ffffff !important;
    background-color: transparent;
    font-weight: 500;
    font-family: "Montserrat";
}

.main-panel[data-background-color="black"] notifications .mat-input-element,
.main-panel[data-background-color="black"]
    notifications
    .mat-select-value-text {
    color: #b1b1b5 !important;
}

.main-panel[data-background-color="black"] objects-fields .content-list .mat-input-element,
.main-panel[data-background-color="black"] content-sources .user-detail-menu {
  color: #4b4b4b !important;
}

.assetDiv .buttonPrimary,
.addonDiv .buttonPrimary,
.backBtn,
.alertDiv .buttonPrimary,
.appsDiv .buttonPrimary {
    background-image: linear-gradient(to left, #55c7ff, #7886f7) !important;
    color: #fff !important;
}

.assetDiv .buttonPrimary label {
    padding: 12px 12px 0px 12px;
    margin: 0;
}

.hint-head {
    color: #f4961c;
    font-weight: normal;
    font-size: 14px !important;
    line-height: 1.29;
    letter-spacing: normal;
    font-weight: 500;
}

.main-content[data-background-color="black"] content-sources .table-su tbody tr,
.main-content[data-background-color="black"]
    community-helper
    .table-su
    tbody
    tr {
    background-color: #fff;
}

.main-content[data-background-color="black"]
    content-sources
    browse-component
    .table-su
    tbody
    tr {
    border-top: 2px solid #292852;
    background-color: transparent !important;
}

.main-content[data-background-color="black"]
    content-sources
    browse-component
    .table
    thead
    th {
    border-bottom: 2px solid #292852;
    background-color: transparent !important;
    color: #fff;
}

.main-content[data-background-color="black"]
    content-sources
    browse-component
    .search-img {
    background-color: #1a193f !important;
    border: 1px solid #1a193f !important;
    opacity: 1;
}

.main-content[data-background-color="black"]
    content-sources
    browse-component
    .browse-link
    > span {
    color: #b1b1b5;
    font-weight: 500;
}

.main-content[data-background-color="black"]
    content-sources
    browse-component
    .browse-cs-index {
    color: #292852;
    font-weight: 500;
}
.main-content[data-background-color="black"]
    content-sources
    browse-component
    .showing-browse-page,
.main-content[data-background-color="black"]
    content-sources
    browse-component
    .pagination-search-tuning
    > nav
    > ul
    > li
    > a,
    .main-content[data-background-color="black"] content-sources
    browse-component .underline,
.main-content[data-background-color="black"] duplicacy-checker .pagination a {
    color: #fff;
    background-color: transparent;
}

.main-content[data-background-color="black"]
    content-sources
    browse-component
    .page-link,
.main-content[data-background-color="black"]
    content-sources
    browse-component
    .pagination
    > li.active
    > a,
.main-content[data-background-color="black"] duplicacy-checker .pagination a {
    border: 2px solid #292852 !important;
}

.main-content[data-background-color="black"]
    content-sources
    browse-component
    .pagination-search-tuning
    > nav
    > ul
    > li
    > a:hover,
.main-content[data-background-color="black"] duplicacy-checker .pagination a {
    background: transparent;
    border: 1px solid #341754;
}

.main-content[data-background-color="black"] objects-fields .table-su tbody tr {
    border-top: solid 1px #1a193f;
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"] #queries-report,
.main-panel[data-background-color="black"] .animate-div-clone,
.main-content[data-background-color="black"]
    .oldAnalyticsSection
    .table-su
    tbody
    tr {
    background-color: #121230;
}

.main-content[data-background-color="black"] .oldAnalyticsSection .su-min,
.main-content[data-background-color="black"] .oldAnalyticsSection .su-max {
    fill: #5cbafd;
    background-color: #1e1c44;
}

.main-content[data-background-color="black"] .oldAnalyticsSection .su-min:hover,
.main-content[data-background-color="black"]
    .oldAnalyticsSection
    .su-max:hover {
    background-color: #45566f;
}

.main-content[data-background-color="black"]
    .su-analytics-head
    .mat-select-arrow,
.main-content[data-background-color="black"]
    .su-analytics-head
    .mat-form-field-label,
.main-content[data-background-color="black"] analytics-v2 .mat-select-arrow {
    color: #a0a0ac !important;
}

.main-panel[data-background-color="black"] .topHeading {
    background-color: #1e1c44;
    border-bottom: 2px solid #292852;
}

.main-panel[data-background-color="black"] .assetDiv label.sectionLabel {
    color: #fff;
    opacity: 0.6;
}

.main-panel[data-background-color="black"] .assetDiv .table-su {
    border-top: none;
    background-color: #121230;
    border-radius: 10px;
}

.main-panel[data-background-color="black"] .table-su tbody tr {
    border-top: none;
    background-color: #121230;
}

.mat-form-field-underline {
    bottom: 0 !important;
    /* display: none; */
}

.mat-form-field-ripple,
.main-content[data-background-color="black"]
    duplicacy-checker
    .pagination
    a.active {
    background-color: #55c6ff !important;
}

.mat-form-field-wrapper {
    padding-bottom: 0.1em !important;
}

.mat-form-field {
    font-family: "Montserrat";
    display: block !important;
    margin-bottom: 0.3em;
    margin-right: 22px;
}

.mat-tab-label {
    height: 60px;
    font-size: 14px;
    font-weight: normal;
    color: #43425d;
    font-family: "Montserrat";
    opacity: 1 !important;
}

.mat-tab-label-active .mat-tab-label-content {
    font-weight: bold;
}

.mat-tab-group.mat-primary .mat-ink-bar {
    background-color: #55c6ff;
}

.oldAnalyticsSection .mat-tab-label {
    text-align: left;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    ::-webkit-scrollbar-track {
    background-color: #121230;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    ::-webkit-scrollbar-thumb {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"] .session-box,
.main-panel[data-background-color="black"] .addData,
.main-panel[data-background-color="black"] .relatedTilesInfo,
.main-panel[data-background-color="black"] .oldAnalyticsSection .perfect {
    border: 1px solid #1f1e40;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .mat-form-field-underline {
    background-color: transparent;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection #mySearch,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .mat-tab-label:focus,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .mat-tab-label-active {
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .table
    td
    .barText,
.main-panel[data-background-color="black"] .oldAnalyticsSection .table th,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .mat-tab-label-active
    .su-header {
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .table td {
    border-top: 1px solid #1f1e40;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .su-header {
    color: #a0a0ac;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    app-bar-chart
    .BarChartContainer1,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .mat-tab-label-container {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"] .su-analytics-head,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .sectionMainDiv2,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .sectionMainDiv {
    background-color: #121230 !important;
}

.oldAnalyticsSection .mat-tab-label:focus,
.oldAnalyticsSection .mat-tab-label-active {
    background-color: #ffffff !important;
    opacity: 1 !important;
}

.oldAnalyticsSection .su-docs-link {
    color: #0275d8;
}

.oldAnalyticsSection .su-labels .mat-ink-bar {
    background-color: transparent !important;
    width: 0px !important;
}

.oldAnalyticsSection .su-labels .mat-tab-label-container {
    width: 100%;
}

.oldAnalyticsSection .su-labels .mat-tab-list {
    width: 100%;
}

.oldAnalyticsSection .su-labels .mat-tab-label {
    width: 50%;
    justify-content: left;
}

.mat-tab-label-active .mat-tab-label-content {
    font-weight: bold;
}

.mat-tab-group.mat-primary .mat-ink-bar {
    background-color: #55c6ff;
}

.mat-form-field-infix {
    width: 100% !important;
    cursor: pointer;
    border-top: none !important;
    margin-top: 15px !important;
    font-size: 14px;
    font-family: "Montserrat";
}

.boost-span {
    font-family: "Montserrat";
    width: 70px;
    height: 30px;
    padding: 5px;
    font-size: 14px;
    border: 1px solid #dfdfdf;
    font-weight: bold;
    text-align: center;
}

.loader-bounce {
    margin-top: 25%;
    width: 20%;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    -webkit-box-shadow: inset 0 0 0px 9999px white !important;
}

.main-content[data-background-color="black"] .table-su tbody tr {
    border-top: 1px solid #222049;
}

.main-content[data-background-color="black"]
    session-report-detail
    .table-su
    tbody
    tr {
    border-left: 1px solid #222049;
}

.main-content[data-background-color="black"] .sectionDiv,
.main-content[data-background-color="black"] .mat-tab-group {
    background-color: #121230;
}

.main-content[data-background-color="black"] conversions .sectionDiv{
    background-color: #1a193f;
}

.main-content[data-background-color="black"] content-gap-analysis .sectionDiv{
    background-color: #1a193f;
}

.main-panel[data-background-color="black"] .sectionHead .heading-source,
.main-panel[data-background-color="black"] .topHeading .heading-source,
.main-panel[data-background-color="black"]
    .topHeading
    .description-notification {
    color: rgb(255, 255, 255);
}


.main-panel[data-background-color='black'] .high-conversion-result{
  background: #1b193f !important;
}


.main-panel[data-background-color="black"] .sectionLabel {
    color: rgb(255, 255, 255);
}

.main-content[data-background-color="black"] .member-list-item-detail {
    border-bottom: 1px solid #222049;
}

.main-panel[data-background-color="black"] search-tuning .buttonPrimary-div,
.main-panel[data-background-color="black"]
    #searchAnalytics
    .mat-tab-body-wrapper,
.main-panel[data-background-color="black"] .footer-class {
    background-color: #1a193f;
}

.main-panel[data-background-color="black"] {
    background-color: #1a193f;
}

.main-panel[data-background-color="black"] .main-content {
    padding-bottom: 1px;
}

.main-panel[data-background-color="black"] .input-box {
    color: #fff;
}


.main-content {
    padding-bottom: 1px;
}

.wrapper.nav-collapsed footer {
    padding-left: 60px;
}

.mat-form-inline {
    display: inline-block !important;
    width: 90%;
}

.mat-expansion-panel-header:not(.mat-expanded) {
    cursor: pointer;
    height: 48px;
}

.search-image {
    background: url(assets/img/browse-search.svg);
    background-position: 100% 100%;
    background-repeat: no-repeat;
    background-size: cover;
    border: none;
    width: 18px;
}

.form-control:focus {
    border-color: #ced4da !important;
    box-shadow: 0 0 0 0rem rgba(0, 123, 255, 0.25) !important;
}

.infoInput {
    color: #787878;
    font-size: 11px;
    font-weight: 400;
}

.mat-form-field-disabled .mat-form-field-underline,
.main-content[data-background-color="black"]
    .mat-form-field-disabled
    .mat-form-field-underline {
    background-image: none !important;
    background-color: transparent;
}

.mat-tab-body-content {
    padding: 0 !important;
}

.search-text-area {
    height: 30px;
    border: solid 1px #e7ecf4;
    display: inline-block;
    margin-top: 10px;
    margin-bottom: 10px;
}

.search-text-area > input {
    display: inline-block;
    outline: none;
    border: none;
    height: 100%;
    width: 96%;
    padding: 5px;
}

.sortable-chosen {
    opacity: 1 !important;
    background-color: transparent;
}

.sortable-ghost {
    opacity: 0 !important;
}

.sortable-chosen.sortable-ghost {
    opacity: 0;
}

.d3-funnel-tooltip {
    z-index: 20;
}

.buttonActions {
    width: 110px !important;
    height: 33px;
}

.kg.header-hidden > .mat-tab-header {
    display: none;
}

.save {
    width: 19px;
    height: 19px;
    color: #006efc;
}

.hide-arrow-select > .mat-select-trigger > .mat-select-arrow-wrapper {
    display: none;
}

home-smp .mat-select-arrow {
    margin: 7px 5px !important;
}

.mat-form-field.mat-focused.mat-primary .mat-select-arrow {
    color: #56c5ff;
}

.hide-underline-select > .mat-input-wrapper > .mat-input-underline {
    display: none;
}

.row {
    margin-right: 15px;
    margin-left: 15px;
}

.mat-tab-group.mat-primary .mat-tab-label:not(.mat-tab-disabled):focus,
.mat-tab-group.mat-primary .mat-tab-link:not(.mat-tab-disabled):focus,
.mat-tab-nav-bar.mat-primary .mat-tab-label:not(.mat-tab-disabled):focus,
.mat-tab-nav-bar.mat-primary .mat-tab-link:not(.mat-tab-disabled):focus,
.mat-tab-group.mat-primary .mat-tab-label:not(.mat-tab-disabled):active {
    background-color: transparent;
    opacity: 1;
}

.freq-label {
    font-family: "Montserrat";
    font-size: 14px;
    font-weight: 500;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.57;
    letter-spacing: normal;
    text-align: left;
    color: #7f8fa4;
    margin: 0;
}

.main-panel[data-background-color="black"]
    .mat-tab-custom
    > .mat-tab-header
    > .mat-tab-label-container {
    background-color: #121230;
}

.main-panel[data-background-color="black"] #searchAnalytics .mat-tab-header {
    border-bottom: 2px solid #292852;
}

.field span {
    font-family: "Montserrat";
}

.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    font-family: "Montserrat";
    color: #4b4b4b;
}

.main-panel[data-background-color="black"] .mat-select-content,
.mat-select-panel-done-animating {
    background-color: #121230;
    color: #fff;
}

/* .mat-select-panel {
    max-width: 175px !important;
} */

.spinner-main {
    text-align: center;
    height: 100%;
    width: 100%;
    position: fixed;
    background-image: linear-gradient(-135deg, #55c7ff, #7886f7);
}

.loadingScreen {
    width: 100%;
    height: 100%;
}

.load-img {
    width: 25%;
}

#searchAnalytics mat-tab-group mat-tab-header mat-ink-bar {
    background-color: #55c6ff !important;
    height: 3px !important;
}

#searchAnalytics .mat-tab-label-container {
    border-radius: 10px 10px 0px 0px;
    justify-content: left;
}

#searchAnalytics .mat-tab-header {
    border-bottom: 2px solid #f4f8f9;
}

analytics-v2 .colubridae19Colors {
    color: #43425d;
    width: 140px;
    word-break: break-word;
}

#searchAnalytics .mat-tab-body-wrapper {
    padding-top: 0px;
}

.case-deflection .mat-tab-body-wrapper {
    height: 100%;
}

.mat-form-field-underline {
    background-color: #8d8d8e;
}

.mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
    color: #55c6ff;
}

.daterangepicker .ranges li.active,
.ranges li:hover {
    border: none !important;
    background-image: linear-gradient(to left, #55c7ff, #7886f7) !important;
    color: white;
    font-weight: bold;
    background: linear-gradient(to left, #55c7ff, #7886f7) !important;
}

.daterangepicker td.active {
    background-color: #55c6ff;
}

.mat-tab-group.mat-primary .mat-tab-label:focus {
    font-weight: bold;
    color: #43425d;
}

.mat-tab-group.mat-primary .mat-tab-label:not(.mat-tab-disabled):active {
    font-weight: bold;
    color: #43425d;
}

analytics-v2 app-new-pie-chart-small #graph-container {
    margin-top: 25px;
}

dashboard-cmp #dashboardChart app-new-pie-chart-small .Content-Source-header {
    display: none;
}

dashboard-cmp #dashboardChart app-new-pie-chart-small .result-value {
    display: none;
}

dashboard-cmp #dashboardChart app-new-pie-chart-small .result-size {
    display: none;
}

dashboard-cmp
    #dashboardChart
    app-new-pie-chart-small
    .dashboard-customise-pie-logo {
    width: 15px;
    height: 15px;
    stroke: black;
    border-radius: 50%;
    float: left;
    margin: 0px 5px;
}

.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
    background: #acc4f1 !important;
}

.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
    background-image: linear-gradient(-135deg, #55c7ff, #7886f7);
    background-color: transparent;
}

.ng2-pagination {
    margin: 0 !important;
}

home-cmp .mat-select {
    padding: 3px 0px 3px 0px;
}

home-cmp .mat-form-field-infix {
    padding: 2px 0px;
    margin-top: 0px !important;
}

.su-dashboardOptions .mat-form-field-underline {
    background-color: transparent;
}

.prev-btn {
    background-image: url("assets/img/previous.svg");
    padding: 6px;
    background-repeat: no-repeat;
    background-position: center;
}

.main-content[data-background-color="black"] .prev-btn {
    background-image: url("assets/img/previous_hover.svg");
}

.btn-action-cancel:hover .prev-btn {
    background-image: url("assets/img/previous_hover.svg");
}

.btn-action-cancel:disabled .prev-btn {
    background-image: url("assets/img/previous-disabled.svg");
}

.next-btn {
    background-image: url("assets/img/next.svg");
    padding: 6px;
    background-repeat: no-repeat;
    background-position: center;
}

.main-content[data-background-color="black"] .next-btn {
    background-image: url("assets/img/next_hover.svg");
}

.btn-action:hover .next-btn {
    background-image: url("assets/img/next_hover.svg");
}

.btn-action:disabled .next-btn {
    background-image: url("assets/img/next-disabled.svg");
}

.btn-action:hover,
.btn-action-cancel:hover,
.btn-action-save:hover {
    background-color: #55c6ff;
    background-image: none !important;
}

.connect,
.connected {
    margin-right: 4px;
    background-image: url("assets/img/connect.svg");
    padding: 10px;
    background-position: center;
    background-repeat: no-repeat;
}

.main-panel[data-background-color="black"] .connect {
    background-image: url("assets/img/connect_hover.svg");
}

content-sources .inline-block .button-add-content .connected {
    background-image: url("assets/img/connect_hover_color.svg");
}

content-sources .inline-block .button-add-content:hover .connect,
content-sources .inline-block .button-add-content:hover .connected {
    background-image: url("assets/img/connect_hover_color.svg");
}

content-sources .inline-block .button-add-content:disabled .connect {
    background-image: url("assets/img/connect.svg");
}

.main-content[data-background-color="black"]
    content-sources
    .button-add-content:disabled
    .connect {
    background-image: url("assets/img/connect_hover.svg");
}

home-cmp .table thead th {
    position: sticky;
    top: 0;
    background-color: #fff;
    color: #43425d;
    line-height: 1.4 !important;
}

home-cmp .table thead th:first-child,
home-cmp .table > tbody > tr > td:first-child {
    width: 80% !important;
    padding-left: 15px !important;
}

.ng2-pagination .current {
    padding: 1px 15px !important;
    background: #56c5fe !important;
    color: #ffffff;
    font-weight: 500 !important;
    cursor: default;
    border-radius: 5px;
}

.ng2-pagination a {
    color: #4b4b4b !important;
    padding: 1px 15px !important;
    border-radius: 5px;
}

.ng2-pagination .pagination-next a::after,
.ng2-pagination .pagination-next.disabled::after {
    display: none;
    content: "";
}

.oldAnalyticsSection .mat-tab-label-container {
    background-color: #ffffff;
    border-radius: 6px 6px 0px 0px !important;
}

.table-left {
    float: left;
    padding: 0 0 2% 2%;
    width: 55%;
}

.table-right, .table-right-custom {
    float: right;
    padding: 0px 0% 2% 0%;
    width: 45%;
}

.table-left-data,
.table-right-data, .table-right-data-custom {
    display: inline-block;
    width: 90%;
}

.mat-calendar-table-header th {
    padding: 0;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection #mySearch {
    background-color: #1f1e40 !important;
}

.main-panel[data-background-color="black"] .su-legendView,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    #pageTime-report,
.main-panel[data-background-color="black"]
    analytics-v2
    .search-summary-conversion {
    border-color: #1f1e40;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .table
    td
    .barText,
.main-panel[data-background-color="black"] .oldAnalyticsSection .table th,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .mat-tab-label-active
    .su-header {
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .su-header {
    color: #a0a0ac;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .sectionMainDiv {
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .chartLegends1
    .table-hover,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .chartLegends
    .table,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .legend-div-container-analytics {
    border-color: #1a193f !important;
}

.main-panel[data-background-color="black"] .no-ads-section,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .legend-small-analytics {
    border-bottom: 1px solid #1a193f !important;
    color: #ffffff;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .su-session,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .analytics-section-heading,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .Content-Source-header,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .analytics-section-header {
    background-color: #1f1e40 !important;
    border-color: #1f1e40;
    color: #ffffff !important;
    font-family: Montserrat;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .search-text-area1 {
    background-color: #1f1e40 !important;
    border: #121230 !important;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .panel-body,
.main-panel[data-background-color="black"] .oldAnalyticsSection .buttonPrimary,
.main-panel[data-background-color="black"] .oldAnalyticsSection .card {
    background-color: #121230 !important;
    border-color: #121230 !important;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection {
    background-color: #1a193f !important;
}

.main-panel[data-background-color="black"] .analytics-card {
    background-color: #292852;
    border-color: #292852;
    box-shadow: 0 4px 20px 0 #020913c7 !important;
}

.main-panel[data-background-color="black"] .analytics-footer-text,
.main-panel[data-background-color="black"] .overviewTitle,
.main-panel[data-background-color="black"] .analytics-card-count {
    color: #5cbafd;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .tick line,
.main-panel[data-background-color="black"] .y-Axis path {
    stroke: #1b1868 !important;
}
.main-panel[data-background-color="black"] .x-Axis path {
    stroke: #1b1868 !important;
}
.main-panel[data-background-color="black"] .tick line {
    stroke: #1b1868 !important;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .BarChartContainer1
    .x-Axis
    path,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .BarChartContainer1
    .y-Axis
    path,
.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .BarChartContainer1
    .tick
    line {
    stroke: #121230 !important;
}

.oldAnalyticsSection .su-labels .mat-tab-label-content {
    width: 100%;
}

.main-panel[data-background-color="black"] .downloadImg {
    fill: #5cbafd;
    background-color: #1a193f;
}

.main-panel[data-background-color="black"] .downloadImg:hover {
    fill: #ffffff;
    background-color: #5cbafd;
}

.main-panel[data-background-color="black"] analytics-v2 .form-group {
    background-color: #121230;
    color: #a0a0ac;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .mat-tab-custom-colubridae19 {
    background-color: #121230;
    color: #a0a0ac;
}

.main-panel[data-background-color="black"] analytics-v2 .sectionMainDiv {
    background-color: #19183d;
    color: #a0a0ac;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-section-heading {
    background-color: #1f1e40;
    color: #a0a0ac;
    border: none;
}

.main-panel[data-background-color="black"]
    .leadership-dashboard
    .dropdown-assisted-case-volume {
    background-color: #121230;
    border-color: #121230;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .su-session {
    background-color: #1f1e40;
    color: #a0a0ac;
    border: none;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-section-header {
    background-color: #1f1e40 !important;
    color: #ffffff;
    border: none;
}

.main-panel[data-background-color="black"] analytics-v2 .search-classifications,
.main-panel[data-background-color="black"]
    analytics-v2
    .searches-with-no-result {
    background-color: #121230;
}

.main-panel[data-background-color="black"] analytics-v2 #searchAnalytics {
    background-color: #1e1c44 !important;
}

.main-panel[data-background-color="black"] ah-analytics #searchAnalytics {
    background-color: #1e1c44 !important;
}


.main-panel[data-background-color="black"]
    analytics-v2
    .Search-Classifications-card {
    border: none;
}

.main-panel[data-background-color="black"] analytics-v2 .perfect {
    background-color: #1a193f !important;
    border: #1a193f solid !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .Search-Classifications-card {
    background-color: #1a193f;
}

.main-panel[data-background-color="black"] analytics-v2 .table-su .t-head {
    background-color: #1f1e40;
    color: #ffffff;
}

.main-content[data-background-color="black"] analytics-v2 .topHeading {
    background-color: #1e1c44 !important;
}

.main-content[data-background-color="black"]
    analytics-v2
    .mat-tab-body-wrapper {
    background-color: #121230;
}

.main-content[data-background-color="black"] analytics-v2 .mat-grid-tile {
    background-color: #1a193f;
}

.main-content[data-background-color="black"] analytics-v2 .table thead th {
    background-color: #1f1e40;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .side-select-list.active {
    background-color: #1a193f;
    background-image: none;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .side-select-list:hover {
    background-color: #1a193f;
    color: #ffffff;
}

.main-panel[data-background-color="black"] analytics-v2 .table td {
    background-color: #1a193f;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] analytics-v2 .table td {
    border-top: 1px solid #121230;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-new-pie-chart-small
    .Content-Source-header {
    background-color: #1f1e40;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-new-pie-chart-small
    .result-label {
    background-color: #1f1e40;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-new-pie-chart-small
    .result-value {
    background-color: #1f1e40;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-new-pie-chart-small
    .result-size {
    background-color: #1f1e40;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] analytics-v2 .card {
    background-color: #121230;
    border: #1a193f !important;
    box-shadow: 0 4px 20px 0 #020913c7 !important;
}

.main-panel[data-background-color="black"]
    .content-src-left-panel-parent
    > div
    > div,
.main-panel[data-background-color="black"] .content-src-left-panel-parent,
.main-panel[data-background-color="black"] .su-contentSource,
.main-panel[data-background-color="black"] analytics-v2 app-bar-chart table {
    border: solid 1px #1a193f;
}

.main-panel[data-background-color="black"]
    analytics-v2
    general-bar-chart
    table {
    border: solid #1a193f !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-bar-chart-filter
    table {
    border: solid #1a193f !important;
    background-color: #1a193f;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-bar-chart
    .BarChartContainer1 {
    background-color: #1f1e40;
    border: 10px solid #121230;
}

.main-panel[data-background-color="black"]
    analytics-v2
    general-bar-chart
    .BarChartContainer1 {
    background-color: #1f1e40;
    border: 10px solid #121230;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    app-bar-chart-filter
    .ChartContainer1 {
    background-color: #1a193f;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-line-conversion-chart
    .LineChartContainer
    svg {
    background-color: #1f1e40;
}

.main-content[data-background-color="black"] .sectionDiv > #searches,
.main-panel[data-background-color="black"]
    analytics-v2
    app-bar-chart-filter
    .drawBarChart
    svg {
    background-color: #1a193f;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    app-gantt-chart
    .ChartContainer
    svg {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .Session-Tracking-Details-popup
    .table-su {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    app-gantt-chart
    .session-box {
    background-color: #1f1e40;
    color: #ffffff;
    border: #121230;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    app-gantt-chart
    button.btn.btn-default {
    color: #ffffff !important;
    border: 1px solid #121230 !important;
    width: 100%;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-bar-chart
    .chartLegends {
    background-color: #1f1e40;
    border: 10px solid #121230;
}

.main-panel[data-background-color="black"]
    analytics-v2
    general-bar-chart
    .chartLegends {
    background-color: #1f1e40;
    border: 10px solid #121230;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    app-bar-chart-filter
    .chartLegends1 {
    background-color: #121230;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    app-bar-chart-filter
    .legendsDiv {
    background-color: #1a193f;
    height: 350px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-bar-chart
    .table
    td {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-new-pie-chart-small
    .legend-small-analytics {
    border-bottom: 1px solid #1a193f !important;
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"] analytics-v2 .darkmode {
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    general-bar-chart
    .table
    td {
    background-color: #1f1e40;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    app-bar-chart-filter
    .table
    td {
    background-color: #1a193f;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph
    .session-report-graph,
.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph-new
    .session-report-graph,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-graph
    #trackingDetail,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-graph
    .panel-body,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-graph-new
    .panel-body,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-graph
    .col-header,
.main-panel[data-background-color="black"]
    analytics
    session-report-graph
    .session-report-graph,
.main-panel[data-background-color="black"]
    analytics
    session-report-graph
    #trackingDetail,
.main-panel[data-background-color="black"]
    analytics
    session-report-graph
    .panel-body,
.main-panel[data-background-color="black"]
    analytics
    session-report-graph-new
    .panel-body,
.main-panel[data-background-color="black"]
    analytics
    session-report-graph
    .col-header {
    background-color: #121230 !important;
    border: #121230 !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .search-text-area
    input,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .search-text-area
    .search-text,
.main-panel[data-background-color="black"]
    analytics
    session-report-detail
    .search-text-area
    input,
.main-panel[data-background-color="black"]
    analytics
    session-report-detail
    .search-text-area
    .search-text {
    background-color: #1f1e40 !important;
    border: #121230 !important;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    app-bar-chart-filter
    .buttonPrimary,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .buttonPrimary {
    background: #121230;
    border: #121230 !important;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    .sectionMainDiv
    .modal-content-new {
    background: #1f1e40;
}

.main-panel[data-background-color="black"] #searchAnalytics .modal-content-new {
    background: #1f1e40;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    .sectionMainDiv
    .modal-title-new
    p
    button,
.main-panel[data-background-color="black"]
    #searchAnalytics
    .sectionMainDiv
    .modal-title-new {
    color: #fff;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    .sectionMainDiv
    .modal-header-new {
    border-bottom: none;
}

.main-panel[data-background-color="black"] #searchAnalytics .modal-header-new {
    border-bottom: none;
}

.black .mat-select-content,
.black .mat-select-panel-done-animating {
    background-color: #1e1e3f;
}

.black .mat-checkbox-label {
    color: white;
}
.black .mat-checkbox-frame {
    border-color: white;
}
.black .mat-pseudo-checkbox{
    border-color: white;
}
.main-panel[data-background-color="black"]
    analytics-v2
    mat-form-field.mat-input-container.mat-primary.mat-form-field-type-mat-select.mat-form-field-hide-placeholder
    label {
    background: transparent !important;
    color: #fff !important;
}

.black .mat-option,
.black .mat-option.mat-active {
    color: white;
}

/* used in email popup Do not alter this class */
.black .anautocompletate .mat-option {
    color: #fff;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .col-sm-12 {
    border: #121230 !important;
    background: #121230;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .panel-body {
    background-color: #121230 !important;
    border: #121230 !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .search-text-area,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    mat-form-field,
.main-panel[data-background-color="black"]
    analytics
    session-report-detail
    .search-text-area,
.main-panel[data-background-color="black"]
    analytics
    session-report-detail
    mat-form-field {
    background-color: #1f1e40 !important;
    border: #121230 !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail.mat-form-field-underline {
    background-color: #55c6ff;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    mat-form-field
    .mat-select-arrow {
    color: #55c6ff;
}

.main-panel[data-background-color="black"] analytics-v2 .analytics-card {
    background-color: #1a193f !important;
    border: #121230 !important;
}

.main-panel[data-background-color="black"] analytics-v2 .colubridae19Colors,
.main-panel[data-background-color="black"] analytics-v2 .analytics-card-count {
    color: #57c2fe;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-gantt-chart
    .modal-header {
    background-color: #1f1e40 !important;
    color: #a0a0ac;
    border: none;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-gantt-chart
    .modal_table
    .table
    td {
    padding-left: 20px !important;
    background-color: #1a193f !important;
}

.main-panel[data-background-color="black"] analytics-v2 app-gantt-chart .table {
    border: none !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-gantt-chart
    .modal-header
    .close,
.main-panel[data-background-color="black"] analytics-v2 app-bar-chart .barText,
.main-panel[data-background-color="black"]
    analytics-v2
    app-bar-chart-filter
    .barText,
.main-panel[data-background-color="black"]
    analytics-v2
    app-gantt-chart
    .barText,
.main-panel[data-background-color="black"]
    analytics-v2
    general-bar-chart
    .barText,
.main-panel[data-background-color="black"]
    #searchAnalytics
    .checkbox-wrapper
    .check-label {
    color: #ffffff !important;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    .check-in
    label.checkbox-checked {
    background-color: transparent !important;
}

.main-panel[data-background-color="black"] analytics-v2 .analytics-topbar {
    background-color: #121230;
    color: #a0a0ac;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-1 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/SiteVisits1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-1 {
    width: 225px;
    background: url(../src/assets/img/SiteVisitsFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-2 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/Clicks1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-2 {
    width: 225px;
    background: url(../src/assets/img/ClicksFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-3 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/SearchClientUsers1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-3 {
    width: 225px;
    background: url(../src/assets/img/SearchClientUsersFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-4 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/CasesLogged1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-4 {
    width: 225px;
    background: url(../src/assets/img/CasesLoggedFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-5 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/TotalSearches1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-5 {
    width: 225px;
    background: url(../src/assets/img/TotalSearchesFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-6 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/SearchesWithResults1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-6 {
    width: 225px;
    background: url(../src/assets/img/SearchesWithResultsFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-7 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/WithNoResult1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-7 {
    width: 225px;
    background: url(../src/assets/img/WithNoResultFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card-image-8 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/UnsuccessfulSearches1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-footer-img-8 {
    width: 225px;
    background: url(../src/assets/img/UnsuccessfulSearchesFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"] analytics-v2 .analytics-card:hover {
    background-image: linear-gradient(to bottom, #55c7ff, #7886f7);
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-1 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/SiteVisitsDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-1 {
    width: 225px;
    background: url(../src/assets/img/SiteVisitsFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-2 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/ClicksDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-2 {
    width: 225px;
    background: url(../src/assets/img/ClicksFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-3 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/SearchClientUsersDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-3 {
    width: 225px;
    background: url(../src/assets/img/SearchClientUsersFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-4 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/CasesLoggedDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-4 {
    width: 225px;
    background: url(../src/assets/img/CasesLoggedFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-5 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/TotalSearchesDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-5 {
    width: 225px;
    background: url(../src/assets/img/TotalSearchesFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-6 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/SearchesWithResultsDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-6 {
    width: 225px;
    background: url(../src/assets/img/SearchesWithResultsFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-7 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/WithNoResultDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-7 {
    width: 225px;
    background: url(../src/assets/img/WithNoResultFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-image-8 {
    width: 30px;
    height: 30px;
    background: url(../src/assets/img/UnsuccessfulSearchesDark.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-footer-img-8 {
    width: 225px;
    background: url(../src/assets/img/UnsuccessfulSearchesFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .download-session
    rect {
    fill: #5cbafd;
}
.main-panel[data-background-color="black"] {
    fill: #fff;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .download-session
    #Path_1128 {
    fill: #ffffff;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-gantt-chart
    .download-gantt-chart
    rect {
    fill: #5cbafd;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-gantt-chart
    .download-gantt-chart
    #Path_1128 {
    fill: #ffffff;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph
    .outer-box {
    background-color: #1f1e40 !important;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph
    .round-shape {
    border: 4px solid #121230 !important;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph
    .outer-box.active-flow-checked
    .inner-box {
    background-color: #55c6ff !important;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph
    .outer-box.active-flow-checked
    .inner-box
    .round-shape {
    background-color: transparent !important;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph
    .outer-box.outer-box.active-flow-unchecked
    .inner-box {
    background-color: #01a8f4 !important;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    session-report-graph
    .outer-box.active-flow-unchecked
    .inner-box
    .round-shape {
    background-color: transparent !important;
}

.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .sessionDetail,
.main-panel[data-background-color="black"]
    analytics
    session-report-detail
    .sessionDetail {
    border: #121230 !important;
}

content-sources .inline-block .SetBtn {
    color: #7f8fa4;
    background-color: transparent;
    border: none;
}

.main-panel[data-background-color="black"] .inline-block .SetBtn {
    background-color: transparent !important;
    border: none !important;
}

.main-panel[data-background-color="black"] .inline-block .SetBtn:hover {
    color: #f48b00;
}

content-sources .inline-block .SetBtn:disabled,
content-sources .inline-block .SetBtn:disabled:hover {
    color: #7f8fa4;
    background-color: transparent !important;
    border: none !important;
}

content-sources .inline-block .SetBtn:hover {
    color: #f48b00;
    background-color: transparent !important;
    border: none !important;
    padding: 0px;
}

.main-panel[data-background-color="black"] .buttonPrimary:disabled,
.main-panel[data-background-color="black"] .buttonPrimary:disabled:hover {
    cursor: not-allowed;
    border: 2px solid #625f7c !important;
    background-color: #121230 !important;
    color: #625f7c !important;
}

.mat-form-field-invalid .mat-form-field-label .mat-form-field-required-marker,
.mat-form-field-invalid .mat-form-field-label {
    color: #f59100;
}

.mat-form-field-invalid .mat-form-field-ripple {
  background-color: #f59100 !important;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .mySearch,
.main-panel[data-background-color="black"] .oldAnalyticsSection .graphLabel {
    color: #ffffff;
    font-family: "Montserrat";
}

.main-panel[data-background-color="black"] .analyticsSettings,
.main-panel[data-background-color="black"] .search-box-css,
.main-panel[data-background-color="black"] .oldAnalyticsSection .su-collapse {
    background-color: #121230;
}

@media (max-width: 475px) {
    .oldAnalyticsSection .su-labels .mat-tab-label {
        width: 100%;
        font-size: 16px;
    }

    .oldAnalyticsSection .mat-tab-labels {
        display: flex !important;
    }

    .oldAnalyticsSection .su-labels .mat-tab-label-container {
        width: auto !important;
    }

    .oldAnalyticsSection .su-labels .mat-tab-list {
        width: auto !important;
    }

    .main-panel[data-background-color="black"]
        analytics-v2
        .mat-form-field-underline,
    .main-panel[data-background-color="black"]
        analytics
        .mat-form-field-underline {
        background-color: transparent;
    }

    .main-panel[data-background-color="black"]
        analytics-v2
        .analytics-header-group
        .mat-form-field,
    .main-panel[data-background-color="black"]
        analytics
        .analytics-header-group
        .mat-form-field {
        border-color: #282750;
    }

    analytics-v2 .analytics-header-group .mat-form-field,
    analytics .analytics-header-group .mat-form-field {
        width: 100% !important;
        border-radius: 2px;
        padding: 7px 4px 7px 4px;
        border: solid 0.5px #bcbccb;
    }

    analytics-v2 .analytics-header-group,
    analytics .analytics-header-group {
        margin: 0px;
        padding: 2px;
        width: 100%;
    }

    .oldAnalyticsSection .card {
        margin: 18px 0;
    }

    .oldAnalyticsSection .row {
        margin-right: 0px;
    }

    .oldAnalyticsSection .mat-tab-header-pagination {
        display: none !important;
    }

    .oldAnalyticsSection .mat-tab-label-container {
        overflow: scroll;
    }
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-gantt-chart
    .modal-body.modal_table {
    margin: 0px !important;
}

.main-panel[data-background-color="black"] top-nav #menu-nav-button {
    background-color: #1e1c44;
    border: solid 2px #625f7c;
}

.app-sidebar[data-background-color="black"]
    .sidebar-header-mobile
    #menu-nav-button {
    background-color: #1e1c44 !important;
    border: solid 2px #625f7c !important;
}

.main-panel[data-background-color="black"] top-nav #mobile-view #text {
    background-color: #1e1c44 !important;
    border-bottom: solid 2px #625f7c;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    top-nav
    #mobile-view
    .su-logo-nav
    .su-logo-black {
    width: 115px;
    height: 25px;
    float: none;
    background: url(../src/assets/img/Logo-Mobile-view-Dark.svg);
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: 115px;
    top: 8px;
}

.app-sidebar[data-background-color="black"]
    .sidebar-header-mobile
    .sidebar-header-su-logo
    .sidebar-header-su-logo-img {
    width: 160px;
    height: 36px;
    background: url(../src/assets/img/Logo-Mobile-view-Dark.svg);
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: 160px;
    top: 15px;
}

@-webkit-keyframes fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@keyframes fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@-webkit-keyframes fadeInDown1 {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -4%, 0);
        transform: translate3d(0, -4%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@keyframes fadeInDown1 {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -4%, 0);
        transform: translate3d(0, -4%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
    .ad-heading-fixed .topHeading{background:#fff}
    .wrapper.nav-collapsed.menu-collapsed .topHeading.ad-heading-fixed-top {width: calc(100% - 60px);}
    .daterangepicker.dropdown-menu.ltr.opensleft{-webkit-animation:fadeInDown1 600ms alternate both;animation:fadeInDown1 600ms alternate both}
    .ad-heading-fixed-top{-webkit-animation:fadeInDown 800ms alternate both;animation:fadeInDown 800ms alternate both;position:fixed;top:0;right:0;z-index:9;width:calc(100% - 239px);display:inline-block;-webkit-box-shadow:0 -2px 12px -1px #000;box-shadow: 0 -4px 16px -1px rgb(33 33 33 / 31%);}
    .colubridae19-sectionDiv.ad-heading-space{margin-top:66px!important}
    .analytics-header-block{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%}
    .analytics-header-row{margin:0 5px;border:1px solid #ccc;padding:2px 5px;border-radius:4px;width:100%;background:#fff;height: 41px;}
    .analytics-header-row .mat-form-field-appearance-legacy .mat-form-field-infix{padding:.1375em 0;min-width:100px}
    .analytics-header-row .mat-select-value{width:100%;max-width:100%}
    #contentSource .select-client.analytics-header-row{max-width: 90%;width: 100%;}
    #contentSource .select-client.analytics-header-row .mat-select-value{width:100%;max-width: 142px !important;}
    .analytics-header-row .mat-select .mat-select-arrow{width:0;height:0;margin:0 4px;border:solid #656565;border-width:0 2px 2px 0;display:inline-block;padding:3px;-ms-transform:rotate(45deg);transform:rotate(45deg);-webkit-transform:rotate(45deg);position:relative;top:-2px}
    .analytics-header-row .analytics-header-field .mat-form-field-underline{display:none}
    .analytics-header-row .analytics-header-field .date-input{width:100%;height:auto;white-space:nowrap;display:inline-block;overflow:hidden;max-width:100%;min-width:194px;margin-top:-2px;position:relative;top:2px}
    .analytics-header-calendar{width:10px}
    .analytics-header-calendar svg{width:20px;position:absolute;right:0;top:-3px}
    .analytics-header-row .mat-form-field-appearance-legacy .mat-form-field-infix .mat-form-field-label{color:#656565;font-weight:400}
    #contentSource .search-object.analytics-header-row .mat-select-value-text>span{ max-width: 82px!important; }
    .analytics-header-row .mat-form-field-appearance-legacy .mat-select-value-text>span {margin-right: 6px;white-space: nowrap;text-overflow: ellipsis; overflow: hidden;max-width: 250px;display: inline-block;line-height: normal;}
    .ad_slide-toggle-row .content_panel-label{white-space: nowrap;text-overflow: ellipsis; overflow: hidden; display: inline-block;}
    .slide-toggle-bar-not .content_panel-label {max-width: 100%;}
    .ad_slide-toggle-show .content_panel-label {max-width: calc(100% - 32px)}
    .main-content[data-background-color='black'] #contentSource .search-object.analytics-header-row.emailSvg {background: #1a193f;  }
@media only screen and (min-width: 320px) and (max-width: 1024px) {
    analytics-v2 mat-grid-list {
        width: 1015px;
    }

    analytics-v2 .mat-tab-body-content {
        overflow: hidden;
    }
}

@media (min-width: 320px) and (max-width: 768px) {
    .load-img {
        width: 60%;
        margin-left: 10px;
    }

    .hint-head {
        padding-left: 0%;
    }

    #searchAnalytics .mat-tab-label-container .mat-tab-list {
        transform: translate3d(0px, 0px, 0px) !important;
    }
}

.main-content[data-background-color="black"] home-cmp .table > tbody > tr > td,
.main-content[data-background-color="black"]
    duplicacy-checker
    .table
    > tbody
    > tr
    > td {
    color: #b1b1b5 !important;
    border-top: 1px solid #222049;
}

.main-panel[data-background-color="black"] #CD,
.main-panel[data-background-color="black"] #CD1 {
    background-color: #1f1e40 !important;
    border: none !important;
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] .cd-color,
.main-panel[data-background-color="black"] #totalNumSessions,
.main-panel[data-background-color="black"] #totalCaseCreated {
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] .shadowDiv,
.main-panel[data-background-color="black"] .chooseTemplate,
.main-panel[data-background-color="black"] .var-box {
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"] #CD svg #Group_1655 text,
.main-panel[data-background-color="black"] #CD svg #Group_1655 rect {
    fill: #ffffff;
}

.main-panel[data-background-color="black"] #CD svg #Path_1286,
.main-panel[data-background-color="black"] #CD svg #Path_1285 {
    fill: #ffffff;
}

.main-content[data-background-color="black"] analytics-v2 .mat-select-value {
    background-color: transparent !important;
}

.main-content[data-background-color="black"] analytics-v2 text {
    fill: #ffffff !important;
}

.main-content[data-background-color="black"] ah-analytics text {
    fill: #ffffff !important;
}

.main-content[data-background-color="black"] analytics-v2 .legends_test {
    color: #ffffff !important;
}

.main-content[data-background-color="black"] analytics-v2 .tick line {
    stroke: #121230 !important;
}

.main-content[data-background-color="black"] #searchAnalytics .background-dark {
    background-color: transparent !important;
}

.main-panel[data-background-color="black"] .oldAnalyticsSection .no-doc {
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] .t-head {
    border: none !important;
}

home-cmp .no-doc {
    text-align: center !important;
    padding-top: 90px !important;
    width: 100%;
}

.doc-img {
    background-image: url("assets/img/no-document.svg");
    display: block;
    width: 100%;
    margin-top: 20px;
    padding: 50px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}
.no-graph-data{
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 48px;
    font-size: 20px;
    font-weight: 400;
}
.graph-loader{
    text-align: center;
    height: 400px;
    display: flex;
    justify-content: center;
    padding: 50px;
}
.search-consumption-outer{
    padding-top: 15px;
    padding-left: 15px;
    font-size: 16px;
    color: #707070;
    font-weight: 600;
}
.search-consumption-inner{
    font-size: 16px;
    color: #707070;
    font-weight: 600;
}
.p-100{padding: 100px;}

.main-content[data-background-color="black"] .doc-img {
    background-image: url("assets/img/no-document-black.svg");
}

home-cmp .perfect {
    border: none !important;
}

.main-content[data-background-color="black"] analytics-v2 .su-add,
.main-content[data-background-color="black"] analytics-v2 .su-sub {
    background-color: #1e1c44;
}

.main-content[data-background-color="black"] analytics-v2 .su-add:hover,
.main-content[data-background-color="black"] analytics-v2 .su-sub:hover {
    background-color: #45566f;
}

.main-content[data-background-color="black"] home-cmp .table-su {
    box-shadow: 0 4px 20px 0 #02091357;
}

.main-content[data-background-color="black"] home-cmp .table-su tbody tr {
    background-color: transparent;
}

objects-fields .table td {
    padding: 14px 14px;
    white-space: nowrap;
}

@media (min-width: 992px) and (max-width: 1440px) {
    analytics mat-tab-header .mat-tab-list .mat-tab-labels .mat-tab-label {
        height: 55px !important;
    }

    analytics mat-tab-header .mat-tab-list .mat-tab-labels .mat-tab-label {
        height: 55px !important;
    }
}

main-content[data-background-color="black"] analytics-v2 .su-add:hover,
.main-content[data-background-color="black"] analytics-v2 .su-sub:hover {
    background-color: #45566f;
}

@media (min-width: 992px) and (max-width: 1440px) {
    analytics-v2 .mat-tab-label {
        font-size: 14px;
    }
}

@media (min-width: 768px) and (max-width: 1440px) {
    .topHeading .heading-source {
        font-size: 16px !important;
    }
}

analytics mat-tab-header .mat-tab-list .mat-tab-labels .mat-tab-label {
    height: 55px !important;
}

#searchAnalytics .mat-tab-label {
    font-size: 14px;
}

.oldAnalyticsSection .mat-tab-group {
    font-family: "Montserrat";
}

session-report-detail .mat-input-wrapper.mat-form-field-wrapper {
    margin-bottom: 10px;
}

.main-content[data-background-color="black"] analytics-v2 .focus rect.tool,
.main-content[data-background-color="black"]
    analytics-v2
    .customTooltip
    rect.tool {
    fill: #121230 !important;
}

.main-content[data-background-color="black"]
    analytics-v2
    .content-part::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    analytics-v2
    .content-part::-webkit-scrollbar-track {
    width: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"]
    .ChartContainer1::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    .ChartContainer1::-webkit-scrollbar-track {
    width: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"] .e-rte-content::-webkit-scrollbar-track
.main-content[data-background-color="black"] .asset-library-body-right::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .perfect::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .recommendations-section-height::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .LineChartContainer::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .BarChartContainer1::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .Session-Tracking-Details-popup::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .Top-Clicked-Searches::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .topClicks::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .mat-tab-body-content::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .mat-dialog-content::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .scroll-css::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .Session-Analytics-Overview-graph::-webkit-scrollbar-track,
.main-content[data-background-color="black"] .scrollable-area::-webkit-scrollbar-track
{
    width: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"] .e-rte-content::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .asset-library-body-right::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .perfect::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .recommendations-section-height::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .LineChartContainer::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .BarChartContainer1::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .Session-Tracking-Details-popup::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .Top-Clicked-Searches::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .topClicks::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .mat-tab-body-content::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .mat-dialog-content::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .scroll-css::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .Session-Analytics-Overview-graph::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"] .scrollable-area::-webkit-scrollbar-thumb
{
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    .queryFilters-div::-webkit-scrollbar-track {
    width: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"]
    .queryFilters-div::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    generate-search-client
    .actions-div::-webkit-scrollbar-track {
    width: 6px;
    height: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"]
    generate-search-client
    .actions-div::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    .overflow-height::-webkit-scrollbar-track {
    width: 6px;
    height: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"]
    synonyms-cmp
    .suggestion-table::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    .overflow-height::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    synonyms-cmp
    .suggestion-table::-webkit-scrollbar-track {
    width: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"]
    synonyms-cmp
    .suggestion-table::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-content[data-background-color="black"]
    analytics-v2
    .tbody-content::-webkit-scrollbar-track {
    width: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"]
    analytics-v2
    .tbody-content::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

analytics-v2 app-bar-chart .tbody-content {
    height: 246px !important;
}

@media only screen and (min-width: 320px) and (max-width: 768px) {
    .mat-tab-header {
        overflow: auto !important;
    }

    .mat-tab-label-container {
        overflow: auto !important;
    }

    .mat-tab-labels {
        display: flex !important;
    }

    analytics-v2 .mat-tab-header-pagination {
        display: none !important;
    }
}

@media only screen and (min-width: 480px) and (max-width: 1024px) {
    .main-panel[data-background-color="black"]
        top-nav
        #mobile-view
        .su-logo-nav
        .su-logo-black {
        width: 200px;
        height: 40px;
        background-size: 200px;
    }
}

.main-content[data-background-color="black"]
    .mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label {
    color: #ffffff !important;
}

analytics-v2 app-bar-chart .chartLegends {
    padding-left: 0px;
}

.main-content[data-background-color="black"]
    #searchAnalytics
    .chartLegends1::-webkit-scrollbar-track {
    width: 6px;
    background-color: #121230 !important;
}

.main-content[data-background-color="black"]
    #searchAnalytics
    .chartLegends1::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

dashboardchart app-new-pie-chart-small .align {
    width: auto;
}

.main-panel[data-background-color="black"]
    #searchAnalytics
    app-bar-chart-filter
    .buttonPrimary:hover,
.main-panel[data-background-color="black"]
    analytics-v2
    session-report-detail
    .buttonPrimary:hover {
    background: #55c6ff !important;
}

.app-label {
    opacity: 0.69;
    font-family: "Montserrat";
    font-size: 12px;
    letter-spacing: normal;
    text-align: left;
    color: #43425d;
    text-transform: none;
    font-weight: normal;
}

.notification-tab {
    max-width: 100% !important;
    width: 100%;
}

.table-alerts > thead > td {
    white-space: nowrap;
}

notifications .sectionButton {
    padding: 1px 11px;
    margin: 20px 0px;
}

.main-panel[data-background-color="black"]
    .searchCount::-webkit-scrollbar-thumb,
.main-panel[data-background-color="black"]
    content-sources
    ::-webkit-scrollbar-thumb {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"]
    .searchCount::-webkit-scrollbar-track,
.main-panel[data-background-color="black"]
    content-sources
    ::-webkit-scrollbar-track {
    background-color: #121230;
}

.project-head {
    font-weight: 600;
    font-size: 15px;
}

.btn-action-reindex {
    border: 2px solid #55c6ff !important;
    color: #55c6ff;
}

.btn-action-reindex:hover {
    background-color: #55c6ff;
    background-image: none !important;
}

.mat-raised-button.mat-primary {
    background-color: #55c6ff;
}

.fa {
    color: #55c6ff !important;
}

.mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #55c6ff;
}

td.nomargin .mat-checkbox .mat-checkbox-layout{
  margin-bottom: 0px !important;
}

td.nomargin .mat-form-field-wrapper .mat-form-field-flex{
  padding: .35em .35em 0em .35em;
  background-color: transparent !important;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

td.nomargin .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix{
  margin-top: 0px !important;
  padding: 0px;
}

td.nomargin .mat-form-field-appearance-fill .mat-form-field-underline::before{
  height: 0px;
  background-color: transparent;
}

.mat-select-arrow{
  margin-top: 1em ;
}

.emails-chip .mat-chip-list-wrapper{
  overflow-y: auto !important;
  max-height: 60px !important;
  overflow-x: hidden;
  align-items: flex-start;
}

.emails-chip .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix{
  padding: 0.5em 0 0.5em 0;
}

.main-panel[data-background-color="black"] .alert-status{
  background-color: rgba(255, 255, 255, 0.1) !important;
}

td.nomargin .mat-checkbox .mat-checkbox-layout{
  margin-bottom: 0px !important;
}

td.nomargin .mat-form-field-wrapper .mat-form-field-flex{
  padding: .35em .35em 0em .35em;
  background-color: transparent !important;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

td.nomargin .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix{
  margin-top: 0px !important;
  padding: 0px;
}

td.nomargin .mat-form-field-appearance-fill .mat-form-field-underline::before{
  height: 0px;
  background-color: transparent;
}

.mat-select-arrow{
  margin-top: 1em ;
}
.crawlsubscibeform_container .mat-select-arrow{
  margin-top: 1em !important;
}

.emails-chip .mat-chip-list-wrapper{
  overflow-y: auto !important;
  max-height: 60px !important;
  overflow-x: hidden;
  align-items: flex-start;
}

.emails-chip .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix{
  padding: 0.5em 0 0.5em 0;
}

.main-panel[data-background-color="black"] .alert-status{
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.suDesigner .mat-tab-label {
    font-size: 14px;
    color: #8b8b8b;
}

.suDesigner .mat-tab-label:focus .mat-tab-label-content,
.suDesigner .mat-tab-label-active .mat-tab-label-content {
    color: #56c6ff;
}

.suDesigner .mat-ink-bar {
    background-color: #ffffff;
}

.suDesigner .mat-tab-header {
    border: 0px;
    position: relative;
    z-index: 2;
}

.reports-box .mat-checkbox-frame {
    border-color: #bcbccb;
}

.main-panel[data-background-color="black"] .analyticsSection,
.main-panel[data-background-color="black"] .su-analytics {
    background-color: #1a193f;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    #sessionAnalytic
    .mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label,
.main-panel[data-background-color="black"] .analyticsLabel {
    color: #625f7c;
}

.main-panel[data-background-color="black"] .addOnsTable .mat-input-element {
    opacity: 1;
    color: #ffffff !important;
    font-weight: 600;
}

.main-panel[data-background-color="black"] .su-searchClient .sectionDiv {
    background-color: #121230;
}

.main-panel[data-background-color="black"] .designerSection {
    opacity: 1;
    background-color: #f4961c;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    .editorSection
    ::-webkit-scrollbar-track,
.main-panel[data-background-color="black"] .editorLabel .mat-tab-header {
    background-color: #121230;
}

.main-panel[data-background-color="black"] .topBar {
    background-color: #0c0c0c;
}

.main-panel[data-background-color="black"] .editorSection .ace-tm,
.main-panel[data-background-color="black"] .editorSection .ace-tm .ace_gutter {
    background: #070712;
    color: #ffffff;
}

.main-panel[data-background-color="black"] .imageFrame2 {
    background-image: url(assets/img/mobileDark.svg);
}

.main-panel[data-background-color="black"] .imageFrame1 {
    background-image: url(assets/img/desktopDark.svg);
}

.main-panel[data-background-color="black"] .imageFrame3 {
    background-image: url(assets/img/tabDark.svg);
    background-size: 44%;
}

.main-panel[data-background-color="black"] .imageFrame1 {
    background-size: 74%;
}

.main-panel[data-background-color="black"]
    .editorSection
    ::-webkit-scrollbar-track {
    background-color: #070712 !important;
}

.main-panel[data-background-color="black"] .editorSection ::-webkit-scrollbar {
    background-color: #070712 !important;
}

.main-panel[data-background-color="black"]
    .suggestion-table
    ::-webkit-scrollbar-thumb {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"]
    .suggestion-table
    ::-webkit-scrollbar-track {
    background-color: #070712 !important;
}

.main-panel[data-background-color="black"]
    .suggestion-table
    ::-webkit-scrollbar {
    background-color: #070712 !important;
}

.main-panel[data-background-color="black"]
    .editorSection
    ::-webkit-scrollbar-thumb {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"] .templateHeader {
    background-color: #0b0b1c;
}

.main-panel[data-background-color="black"] .content-src-left-panel-parent,
.main-panel[data-background-color="black"] .content-field-tab {
    background-color: #121230;
    border-color: #121230;
}

.rules-folder-head {
    background-color: #f5f5f5;
    margin-top: 25px;
    border-radius: 0px !important;
}

.rules-inner-folder {
    margin: 0px;
    background-color: #f5f5f5;
}

.tree-folder {
    max-height: 430px;
    min-height: 250px;
    background-color: #fff;
    overflow: auto;
    margin: 15px 0px;
    padding: 15px;
}

.arrow-btn {
    background-color: #ffffff;
    width: 40px;
    padding: 4px;
    height: 30px;
    border-radius: 8px;
    text-align: center;
    margin-left: 35%;
    cursor: pointer;
}
.tree-file-shown {
    max-height: 470px;
    min-height: 250px;
    overflow: auto;
    margin: 15px 0px;
    padding: 10px;
    background-color: #fff;
}

.select-file-inner {
    list-style-type: none;
    padding-left: 0px;
    max-height: 100px;
    overflow: auto;
}

.select-file-text {
    display: inline-block;
    margin-bottom: 0px;
    padding: 8px;
    position: relative;
    font-weight: 400;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 90%;
}

.rules-trash {
    position: relative;
    padding: 16px;
    background-repeat: no-repeat;
    background-position: center;
    /* margin-top: -2rem; */
}

.my-tooltip {
    white-space: pre-line;
}

.tooltip-content {
    word-break: break-word;
}  

.text-select-rule {
    color: #595959;
    margin-left: 8px;
}

.title-card {
    height: 40px;
    font-weight: 500;
    padding: 10px !important;
}

.arrow-button {
    height: 500px;
    padding-top: 200px;
    text-align: center;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: #55c6ff;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: #55c6ff;
}

.tree-head {
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 14px;
}

.jstree-anchor,
.mat-radio-label-content {
    font-family: "Montserrat";
}

.main-content[data-background-color="black"] .rules-folder-head,
.main-content[data-background-color="black"] .rules-inner-folder,
.main-content[data-background-color="black"] .select-file-text,
.main-content[data-background-color="black"] synonyms-cmp .form-group-heading,
.main-content[data-background-color="black"]
    synonyms-cmp
    .buttons-div-seprated {
    background-color: #1a193f;
}

.main-content[data-background-color="black"]
    synonyms-cmp
    .buttons-div-seprated {
    background-color: #111230 !important;
}

.main-content[data-background-color="black"] .tree-folder,
.main-content[data-background-color="black"] .tree-file-shown {
    background-color: #121230;
}

.main-content[data-background-color="black"] .mat-radio-label-content {
    color: #fff;
    font-weight: 500;
}

.main-content[data-background-color="black"] .arrow-btn {
    background-color: #121230;
    color: #fff;
}

.main-content[data-background-color="black"] .jstree-anchor {
    color: #fff !important;
}

.main-content[data-background-color="black"] .mat-radio-outer-circle {
    border-color: #fff;
}

.main-content[data-background-color="black"] content-sources .mat-card {
    background-color: #1a193f;
    border: 1px solid #1a193f;
    color: #fff;
}

.main-panel[data-background-color="black"] .row-top,
.main-panel[data-background-color="black"] .row-top-2,
.main-content[data-background-color="black"] .jstree-default .jstree-hovered {
    background-color: #1a193f;
    border-color: #1a193f;
}

.main-panel[data-background-color="black"] .su-fields {
    border-top: solid 1px #1a193f;
}

.main-panel[data-background-color="black"] .su-Footer,
.main-panel[data-background-color="black"] .agentHelperSection,
.main-panel[data-background-color="black"] .box-field,
.main-panel[data-background-color="black"] .kcsSection {
    background-color: #121230 !important;
}

.apps-table {
    width: 20%;
    word-break: break-all;
}

.upload-icon label {
    color: #fff;
    font-weight: 600;
}

.icon-upload {
    padding: 13px 32px;
    cursor: pointer;
    display: block;
    margin-top: 7px;
}

.icons-row {
    display: inline-block;
    width: 20%;
    margin: 20px;
}

/* dropdown font */

.mat-option-text {
    font-family: "Montserrat";
    font-weight: 500;
}

.mat-select-value-text {
    font-weight: 500;
}

generate-search-client .mat-select-value-text {
    font-weight: 400;
}

.mat-select-value-text > span {
    font-family: "Montserrat";
}

.mat-primary .mat-pseudo-checkbox-checked {
    background: #55c6ff;
}

.subSection {
    padding-bottom: 30px;
}

.addButtons {
    float: right;
    margin: 0px 0px 15px 0px;
    width: auto;
}

.su-tableHead {
    display: inline-block;
    font-family: Montserrat;
    font-weight: 600;
    color: #707070;
    line-height: 3;
    font-size: 14px;
}

.saveOrCancel {
    width: 100%;
    text-align: right;
    padding: 20px 20px 15px 10px;
}

.overlayDiv {
    box-shadow: 0 0px 10px 1px #777;
    padding: 18px;
    height: 44vh !important;
}

.tableHeader {
    font-family: "Montserrat";
    font-weight: 600;
    color: #707070;
    font-size: 14px;
    word-break: break-word;
}

.tableSubHeader {
    font-family: Montserrat;
    padding: 16px 10px;
    font-weight: 600;
    color: #8e8e8e;
    line-height: 1.22;
    font-size: 14px;
    margin-bottom: 0px;
}

.mainDiv {
    padding: 12px;
}

.alignHead {
    padding-left: 10px;
}

.metaInfoTable .mat-form-field-label {
    color: #7f8fa4 !important;
    font-family: "Montserrat";
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
}

.alignButtons {
    width: 100%;
    justify-content: center;
    padding: 0px 20px 18px 0px;
}

.metaInfoTable td {
    width: 50%;
    padding: 10px;
}

.metaInfoTable .mat-input-element,
.metaInfoTable .mat-select-value {
    font-weight: 600 !important;
}

.addData,
.relatedTilesInfo {
    table-layout: fixed;
    border: 1px solid #efefef;
    word-wrap: break-word;
}

.addData .t-head,
.relatedTilesInfo .t-head {
    background-color: #e0e8ec !important;
}

.relatedTilesInfo thead th,
.addData thead th {
    font-size: 14px;
    line-height: 2.4;
}

.graphLabels .mat-tab-label {
    font-size: 14px;
    color: #43425d;
    opacity: 1;
    font-family: Montserrat;
}

.main-panel[data-background-color="black"] .metaInfoTable .mat-form-field-label,
.main-panel[data-background-color="black"] .tableSubHeader,
.main-panel[data-background-color="black"] .tableHeader {
    color: rgba(255, 255, 255, 0.6);
}

.main-panel[data-background-color="black"] .su-tableHead,
.main-panel[data-background-color="black"] .relatedTilesInfo td,
.main-panel[data-background-color="black"] .addData td {
    color: #fff;
}

.main-panel[data-background-color="black"] .addLogic,
.main-panel[data-background-color="black"] .agentHelperPopup th {
    color: #fff !important;
}

.metaInfoTable .mat-form-field {
    width: 90%;
}

.metaInfoTable {
    width: 100%;
}

.main-panel[data-background-color="black"] .mat-form-field-underline {
    background-color: #3c3c40;
}

synonyms-cmp .Scope mat-checkbox label {
    margin-right: 10px;
    margin-bottom: 5px;
}

synonyms-cmp .Scope .mat-checkbox-frame {
    border-color: #bcbccb !important;
}

synonyms-cmp .Scope .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #ffffff;
    border: 2px solid #55c6ff;
}

synonyms-cmp .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #55c6ff;
    border: none;
}

.main-panel[data-background-color="black"]
    synonyms-cmp
    .Scope
    .mat-checkbox-checked.mat-accent
    .mat-checkbox-background,
.main-panel[data-background-color="black"] synonyms-cmp #edit,
.main-panel[data-background-color="black"] synonyms-cmp #delete {
    background-color: #121230;
}

.main-panel[data-background-color="black"] .no-preview,
.main-panel[data-background-color="black"] synonyms-cmp #edit,
.main-panel[data-background-color="black"] synonyms-cmp #delete {
    color: #ffffff;
}

.main-panel[data-background-color="black"] synonyms-cmp .add-button {
    background-color: #121230;
    border: 2px solid #55c6ff;
    color: #55c6ff;
}

.main-panel[data-background-color="black"] synonyms-cmp .add-button:hover {
    background-color: #55c6ff;
    border: 2px solid #55c6ff;
    color: #ffffff;
    background-image: none;
}

.main-panel[data-background-color="black"]
    synonyms-cmp
    .suggestion-removal-tab
    .details {
    color: #55c6ff;
}

.main-content[data-background-color="black"] .mat-select-arrow {
    color: #a0a0ac !important;
}

.main-content[data-background-color="black"] synonyms-cmp .reject-btn:hover {
    background-image: linear-gradient(to left, #ffb300, #ff8800) !important;
    color: #fff !important;
    border: none !important;
}

synonyms-cmp .Scope .mat-checkbox-checkmark-path {
    stroke: #55c6ff !important;
}

.main-panel[data-background-color="black"] synonyms-cmp .suggest {
  border: 0.5px solid #e9e9e9;
  /*border: 0.5px solid #1a193f;  code removed in merge conflict to be checked later*/
  background-color: #121230;
  color: #ffffff;
}

.main-panel[data-background-color="black"] .synonym-comma-sepreted > span {
  color: #fff !important;
}

.main-panel[data-background-color='black'] .exactMatchBox label {
  color: #fff;
}

.main-panel[data-background-color="black"] synonyms-cmp .active-more-enable {
    background-color: #111230;
}

.main-panel[data-background-color="black"] synonyms-cmp .synonym-delete-all {
    display: inline;
    line-height: 0;
    border-radius: 20px;
    color: #7f8fa4;
    cursor: pointer;
    position: relative;
    border: 1px solid #8d8996;
    font-size: 13px;
    padding: 2px;
    background: #111230;
}

.main-panel[data-background-color="black"] synonyms-cmp .active-more-enable {
    border: 1px solid #8d8996;
    border-left: 0px solid #8d8996;
}

.main-panel[data-background-color="black"] synonyms-cmp .synonym-list-delete {
    position: relative;
    top: -1px;
    cursor: pointer;
}

.main-panel[data-background-color="black"] .synonym-expanded-list {
    border-top: 1px solid #8d8996;
    padding: 10px;
    margin-bottom: 5px;
}

.main-panel[data-background-color="black"] synonyms-cmp textarea {
    border: 1px solid #1a193f;
    background-color: #121230 !important;
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    synonyms-cmp
    .moved-synonym-list-edit-all {
    border: 1px solid #111230;
}

.main-panel[data-background-color="black"] .moved-list-first-child {
    font-weight: 500;
    color: #fff;
}

.main-panel[data-background-color="black"] .synonym-list-item-term {
    font-weight: 500;
    color: #fff;
}

.main-panel[data-background-color="black"] .synonym-suggested-btn-abb {
    background: #e4e4e4;
}

.main-panel[data-background-color="black"] .synonym-suggested-btn {
    background: #e4e4e4;
}

.main-panel[data-background-color="black"]
    .synonym-list-check-box
    .synonym-list-item
    .mat-checkbox-frame {
    border-width: 1px;
    border-color: #bcbac5;
}

.main-panel[data-background-color="black"]
    .suggestion-table
    .suggested-items-list {
    border-width: 1px;
    border-color: #8d8a97;
}

.main-panel[data-background-color="black"]
    .suggestion-table
    .moved-synonym-list {
    border-width: 1px;
    border-color: #8d8a97;
}

.main-panel[data-background-color="black"]
    .suggestion-table
    .moved-synonym-list {
    border-color: #8d8a97;
}

.mat-tab-group {
    font-family: "Montserrat";
    border-radius: 10px;
}

.mat-select {
    font-family: "Montserrat";
}

.bot .mat-tab-label {
    opacity: 1;
    font-size: 14px;
}

.bot .mat-tab-labels {
    display: flex !important;
}

.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .active > .nav-link:focus,
.navbar-dark .navbar-nav .active > .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.active:focus,
.navbar-dark .navbar-nav .nav-link.active:hover,
.navbar-dark .navbar-nav .nav-link.open,
.navbar-dark .navbar-nav .nav-link.open:focus,
.navbar-dark .navbar-nav .nav-link.open:hover,
.navbar-dark .navbar-nav .open > .nav-link,
.navbar-dark .navbar-nav .open > .nav-link:focus,
.navbar-dark .navbar-nav .open > .nav-link:hover,
.sidebar .list-group a.router-link-active > .sidebarItem,
.sidebar .sidebar-dropdown .panel-collapse .panel-body .list-group-item a:hover,
.btn-primary:hover i,
.sidebar .list-group a:hover > .sidebarItem,
.main-panel[data-background-color="black"] .link-head,
.main-panel[data-background-color="black"] .button-add-content, .connected-btn
.main-panel[data-background-color="black"] .topHeading .heading-source,
.main-panel[data-background-color="black"]
    .mat-tab-label-active
    .mat-tab-label-content,
.main-panel[data-background-color="black"] .mat-tab-label-content,
.main-panel[data-background-color="black"] .alertMainDiv .mat-tab-label,
.main-panel[data-background-color="black"] .freq-label,
.main-panel[data-background-color="black"] .field label,
.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .nav-link.active,
.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .colubridae19Colors,
.main-panel[data-background-color="black"]
    analytics-v2
    .analytics-card:hover
    .analytics-card-count,
.main-panel[data-background-color="black"]
    top-nav
    #menu-nav-button
    .mat-button-wrapper,
.app-sidebar[data-background-color="black"]
    .sidebar-header-mobile
    #menu-nav-button
    .mat-button-wrapper,
.main-panel[data-background-color="black"] .savePreview,
.main-panel[data-background-color="black"] .template-name-label,
.main-content[data-background-color="black"]
    app-bar-chart-filter
    .selectedlegendContent,
.main-content[data-background-color="black"] .app-label,
.main-panel[data-background-color="black"] #client,
.main-panel[data-background-color="black"] synonyms-cmp .Synonym-Count,
.main-panel[data-background-color="black"] synonyms-cmp .Synonym-Pair,
.main-content[data-background-color="black"] .tree-head,
.main-content[data-background-color="black"] .text-select-rule,
.main-content[data-background-color="black"] #dropboxmaintree,
.main-content[data-background-color="black"] #treeWrapper,
.main-content[data-background-color="black"] pre,
.main-panel[data-background-color="black"] security .security-head,
.main-panel[data-background-color="black"] security .head-descp,
.main-panel[data-background-color="black"]
    security
    .mat-chip:not(.mat-basic-chip),
.main-panel[data-background-color="black"] security .table-su td,
.main-panel[data-background-color="black"]
    security
    .mat-form-field-empty.mat-form-field-label,
.main-panel[data-background-color="black"] .current-time-shown,
.main-panel[data-background-color="black"] adminanalytics .admin-head,
.main-panel[data-background-color="black"]
    adminanalytics
    .mat-paginator-page-size-label,
.main-panel[data-background-color="black"] adminanalytics .mat-header-cell,
.main-panel[data-background-color="black"]
    adminanalytics
    .mat-paginator-range-actions,
.main-panel[data-background-color="black"] .mat-cell,
.main-panel[data-background-color="black"] .time-icon,
.main-content[data-background-color="black"]
    mat-paginator.keyword-pagination
    .mat-paginator-range-label,
.main-content[data-background-color="black"]
    mat-paginator.keyword-pagination
    .mat-paginator-page-size-label,
.main-content[data-background-color="black"]
    mat-paginator.keyword-pagination
    .mat-paginator-container
    .mat-paginator-page-size,
.main-content[data-background-color="black"] .enter_certificate,
.main-content[data-background-color="black"] .instruction,
.main-content[data-background-color="black"] .mat-select-arrow,
.main-content[data-background-color="black"] notifications .table thead th,
.main-panel[data-background-color="black"] addons .table thead th,
.main-panel[data-background-color="black"]
    .mat-form-field-type-mat-select
    .mat-form-field-label,
.main-content[data-background-color="black"] account .inline-block,
.main-content[data-background-color="black"] account .card .card-header,
.main-content[data-background-color="black"] account .account-password,
.main-content[data-background-color="black"] content-sources .modal-title,
.main-content[data-background-color="black"] app-cron-components .modal-title,
.main-content[data-background-color="black"] content-sources .modal-header .close,
.main-content[data-background-color="black"] adminanalytics .admin-logs-table mat-header-cell,
.main-content[data-background-color="black"] oauthapilogs .sideTriangle h6,
.main-content[data-background-color="black"] .inactive-analytics,
.main-panel[data-background-color="black"] footer-inside footer p span,
.main-content[data-background-color="black"] addons .no-addons-desc,
.main-content[data-background-color="black"]
    addons
    .sectionDiv
    .heading-source.color-909090,
.main-content[data-background-color="black"] addons .add-on-description,
.main-content[data-background-color="black"] addons .add-on-know-more a,
.main-panel[data-background-color="black"] .ad-text-setting {
    color: #ffffff;
}

.main-content[data-background-color="black"] notifications .table td {
    color: #b1b1b5;
}

.main-content[data-background-color="black"] oauthclients .table td {
    color: #b1b1b5;
}

.main-panel[data-background-color="black"] security .add-ip-head {
    border: 2px solid #57c5fa !important;
}

.main-panel[data-background-color="black"] adminanalytics .mat-table {
    background-color: #121230;
}

.main-panel[data-background-color="black"] adminanalytics .mat-header-row {
    background-color: #1f1e40 !important;
}

.main-panel[data-background-color="black"] adminanalytics .mat-paginator,
.main-panel[data-background-color="black"] oauthapilogs .mat-paginator {
    background-color: #121230;
}

.main-panel[data-background-color="black"] oauthapilogs .hit-count {
    border: 2px solid #121230 !important;
    background-color: #1f1d40;
}

.main-content[data-background-color="black"] .filter-img {
    background-color: #1a193f !important;
    border: 1px solid #1a193f !important;
    opacity: 1;
    color: #fff !important;
}

.main-panel[data-background-color="black"] security textarea {
    background-color: #1a193f !important;
    border: 4px solid #1a193f !important;
    opacity: 1;
    color: #fff;
}

.drop_2::-webkit-scrollbar,
.shadowDiv::-webkit-scrollbar,
.sectionMainDivBot::-webkit-scrollbar {
    width: 6px;
    background-color: #f3f6fa;
}

.drop_2::-webkit-scrollbar-thumb,
.shadowDiv::-webkit-scrollbar-thumb,
.sectionMainDivBot::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #b2b7be;
}

.main-panel[data-background-color="black"] .drop_2::-webkit-scrollbar-track,
.main-content[data-background-color="black"]
    .shadowDiv::-webkit-scrollbar-track {
    width: 6px;
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"] .drop_2::-webkit-scrollbar-thumb,
.main-content[data-background-color="black"]
    .shadowDiv::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #1f1e40 !important;
}

.main-panel[data-background-color="black"] .base-href {
    padding: 12px;
}

.main-panel[data-background-color="black"] .listFormula {
    border: none;
}

.no-asset-image {
    font-size: 50px;
    display: block;
    opacity: 0.2;
    padding: 13px 32px;
}

.no-asset-label {
    font-size: 15px;
    font-weight: 500;
    opacity: 0.3;
}

.kcs-mat-tab mat-tab-header {
  display: none;
}

.lds-ellipsis {
    display: inline-block;
    position: relative;
    width: 64px;
    height: 64px;
    text-align: center;
}

.lds-ellipsis div {
    position: absolute;
    top: 27px;
    width: 11px;
    height: 11px;
    border-radius: 50%;
    background: #fff;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.lds-ellipsis div:nth-child(1) {
    left: 6px;
    animation: lds-ellipsis1 0.6s infinite;
}

.lds-ellipsis div:nth-child(2) {
    left: 6px;
    animation: lds-ellipsis2 0.6s infinite;
}

.lds-ellipsis div:nth-child(3) {
    left: 26px;
    animation: lds-ellipsis2 0.6s infinite;
}

.lds-ellipsis div:nth-child(4) {
    left: 45px;
    animation: lds-ellipsis3 0.6s infinite;
}

@keyframes lds-ellipsis1 {
    0% {
        transform: scale(0);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes lds-ellipsis3 {
    0% {
        transform: scale(1);
    }

    100% {
        transform: scale(0);
    }
}

@keyframes lds-ellipsis2 {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(19px, 0);
    }
}

intenttraining .mat-paginator-icon {
    vertical-align: top !important;
}

.mat-progress-bar-fill::after {
    background-color: #7886f7;
}

.mat-progress-bar-buffer {
    background-color: #cfecfb;
}

.main-panel[data-background-color="black"] .mat-progress-bar-buffer {
    background-color: #1b1e25 !important;
}

mat-paginator .mat-paginator-container .mat-icon-button {
    background-color: #56c5fe;
    border-radius: 5px;
    margin: 0px 5px;
    color: #ffffff;
    line-height: 1;
    height: 35px;
    width: 35px;
    padding: 0px !important;
}

mat-paginator .mat-paginator-container .mat-button-wrapper {
    display: block;
}

.overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10;
    cursor: default;
}

.manage-users .mat-tab-label,
.alertMainDiv .mat-tab-label {
    opacity: 1;
}

/* My account page */

.account-name {
    font-size: 11px;
    letter-spacing: normal;
    color: #717173;
    font-weight: 500;
}

.account-password {
    margin: 20px auto;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: normal;
    text-align: center;
    color: #a1a0ae;
}

.my-account-page .account-page {
    margin: 25px 0px 50px 0px;
    width: 712px;
    max-width: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 25px;
}

.register-form-head {
    border-bottom: 2px solid #f4f8f9;
}

.my-account-page label {
    font-size: 11px;
    font-weight: 600;
    float: left;
    color: #3c3c3f;
    margin-bottom: 9px;
}

.my-account-page .form-control,
.account-question.form-group .mat-input-flex {
    height: 40px;
    border: 1px solid #efefef;
    border-radius: 2px;
    font-size: 13px;
    font-weight: 500;
    padding-right: 15px;
    margin-bottom: 15px;
}

.password-input {
    width: 75%;
    display: inline-block;
    margin: 20px auto;
}

.account-buttons {
    width: 100%;
    text-align: center;
    position: relative;
    bottom: 30px;
}

.main-content[data-background-color="black"] .my-account-page .account-page {
    background: #121230;
}

.main-content[data-background-color="black"] .account-name {
    color: #b1b1b5;
}

.main-content[data-background-color="black"] .register-form-head {
    border-bottom: 2px solid #030319;
}

/* Scrollbar for Firefox Browser */

.perfect,
.recommendations-section-height,
.LineChartContainer,
.LineChartContainer,
.BarChartContainer1,
.Session-Tracking-Details-popup,
.Top-Clicked-Searches,
.topClicks,
.overflow-height,
.actions-div,
.ChartContainer1,
.suggest,
.sidebar,
textarea,
.oldAnalyticsSection,
.content-part,
.tbody-content,
.chartLegends1,
.shadowDiv,
.sectionMainDivBot,
.filter-container,
.filter-table,
.editorSection,
.searchCount,
.Session-Analytics-Overview-graph,
.queryFilters-div,
.mat-tab-body-content,
.mat-dialog-content,
.scroll-css,
.asset-library-body-right,
.scrollable-area {
    scrollbar-color: #b2b7be #f3f6fa;
    scrollbar-width: thin;
}

.main-content[data-background-color="black"] .perfect,
.main-content[data-background-color="black"] .recommendations-section-height,
.main-content[data-background-color="black"] .LineChartContainer,
.main-content[data-background-color="black"] .BarChartContainer1,
.main-content[data-background-color="black"] .Session-Tracking-Details-popup,
.main-content[data-background-color="black"] .Top-Clicked-Searches,
.main-content[data-background-color="black"] .topClicks,
.main-content[data-background-color="black"] .overflow-height,
.main-content[data-background-color="black"] .actions-div,
.main-content[data-background-color="black"] .ChartContainer1,
.main-content[data-background-color="black"] .suggest,
.main-content[data-background-color="black"] .sidebar,
.main-content[data-background-color="black"] textarea,
.main-content[data-background-color="black"] .oldAnalyticsSection,
.main-content[data-background-color="black"] .content-part,
.main-content[data-background-color="black"] .tbody-content,
.main-content[data-background-color="black"] .chartLegends1,
.main-content[data-background-color="black"] .shadowDiv,
.main-content[data-background-color="black"] .sectionMainDivBot,
.main-content[data-background-color="black"] .filter-container,
.main-content[data-background-color="black"] .filter-table,
.main-content[data-background-color="black"] .queryFilters-div,
.main-content[data-background-color="black"] .searchCount,
.main-content[data-background-color="black"] .Session-Analytics-Overview-graph,
.main-content[data-background-color="black"] .editorSection,
.main-content[data-background-color="black"] .mat-tab-body-content,
.main-content[data-background-color="black"] .mat-dialog-content,
.main-content[data-background-color="black"] .scroll-css,
.main-content[data-background-color="black"] .asset-library-body-right {
    scrollbar-color: #1f1e40 #121230;
    scrollbar-width: thin;
}

.alerts-button {
    border: none;
    background-color: transparent;
    padding: 0;
}

.alert-secret {
    padding: 0px;
    font-size: 14px;
}

@-moz-document url-prefix() {
    .alert-secret {
        font-size: 7px;
    }

    .home-text {
        font-size: 11px;
    }

    .home-text-count {
        font-size: 25px;
    }

    .inline-block {
        font-size: 16px;
    }

    .alertMainDiv .mat-tab-label-active {
        border-bottom: none;
    }

    mat-paginator .mat-paginator-container .mat-button-wrapper {
        padding: 0px !important;
    }
}

.editRelatedData {
    display: inline-block;
    width: 100%;
}

adminanalytics .input-Search {
    margin-right: 0px;
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
    border: none;
    color: #ffffff;
    cursor: pointer;
    top: 22px !important;
    position: absolute;
    right: 15px;
    padding: 1px 11px;
}

.main-panel[data-background-color="black"]
    analytics-v2
    .table
    tr
    .side-select-list,
.main-panel[data-background-color="black"] addons .table tr {
    border-bottom: 1px solid #292852;
}

.main-panel[data-background-color="black"] filters-report .su-topQueries {
    background: #121230;
}

.main-panel[data-background-color="black"]
    filters-report
    .Detailed-Report-selected-heading {
    color: #ffffff;
}

.edit-object {
    font-size: 14px;
    cursor: pointer;
    margin-right: 5px;
    color: #b1b1b5;
    line-height: 1.7;
}

.notify-icon {
    font-size: 22px;
    cursor: pointer;
    color: #b1b1b5;
}

.notify-icon:hover {
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-image: linear-gradient(to left, #55c7ff, #7886f7) !important;
}

/* calendar edit */

.mat-calendar-body-selected {
    background-color: #55c6ff;
    color: #fff;
}

.mat-calendar-body-today.mat-calendar-body-selected {
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.87);
}

.mat-slide-toggle-bar {
    background: #b2b5b9;
}

.mat-slide-toggle-thumb {
    background: #a9a9a9;
}

/* table thead headings fixed while scrolling */

analytics table thead.t-head th,
analytics-v2 table thead.t-head th {
    position: sticky;
    top: 0;
    background: white;
}

.definition {
    color: #43425d;
    font-weight: normal;
    opacity: 0.69;
    font-size: 12px;
    display: block;
    padding-top: 3px;
}

.main-panel[data-background-color="black"] .definition,
.main-panel[data-background-color="black"] .delete-msg-2 {
    color: #ffffff;
}

.inline-grid {
    display: inline-grid;
}

.line-height-normal {
    line-height: normal;
}

.line-height-1-5 {
    line-height: 1.5;
}

home-cmp dashboardchart app-new-pie-chart-small .legend-small {
    font-size: 13px;
    display: flex;
    min-width: 40%;
    width: 50%;
    float: left;
}

.active-analytics {
    font-size: 12px;
    color: #f4961c;
    font-weight: 600;
    opacity: 1;
}

.inactive-analytics {
    font-size: 12px;
    color: #43425d;
    opacity: 0.69;
    font-weight: 500;
}

.oldAnalyticsHeading {
    display: flex;
    width: 100%;
}

.topClicks {
    height: 376px;
    width: 100%;
    overflow: auto;
}

.Session-Tracking-Details-popup {
    overflow-x: auto;
    overflow-y: auto;
    height: 450px;
    max-height: calc(100% - 70px);
}

.menu-dropdown {
    background-image: url("assets/img/more.svg");
    padding: 10px;
    background-repeat: no-repeat;
    background-position: center;
    margin-bottom: 8px;
}

.moved-synonym-list .mat-input-underline.mat-form-field-underline {
    display: none;
}

.moved-synonym-list-edit-all .mat-input-underline.mat-form-field-underline {
    display: none;
}

.mat-input-element:disabled {
    color: #9f9f9f !important;
    font-family: "Montserrat" !important;
    font-size: 13px !important;
    font-weight: normal !important;
    font-stretch: normal;
    line-height: 1.19;
}

.c_model_dialog {
    width: 540px;
}

.c_model_content {
    width: 540px;
    height: 420px;
}

.modal-header.c_model_header {
  border: 0px;
  padding: 20px 15px 0px 20px;
}

.c_text_heading {
  color: #43425D;
  font-weight: bold;
  font-size: 15px;
  line-height: 15px;
  font-weight: 100;
  text-align: left;
}

.c_text_subheading {
    color: #707070;
    font-size: 12px;
    line-height: 15px;
    margin-top: 10px;
}

.crawlsubscribe-actions {
    margin-top: 18%;
    width: 100%;
}

.crawlsubscibeform_container {
  margin: 2% 8% 2% 2%;
  height: 100%
}

.crawlsubscribeform .mat-form-field {
    margin: 0;
}

.crawlsubscribeform .mat-input-wrapper {
    margin-top: 40px !important;
}


.emailerror {
  position: fixed;
  top: 28%;
  right: 12%;
}

.crawlsubscribeform .ng-touched.ng-invalid {
    color: #f59100;
}

.crawlsubscribeform .mat-form-field-label {
    color: #7f8fa4 !important;
}

.c-sub-save-button:disabled,
.c-sub-save-button:disabled:hover {
    background-color: #fff !important;
    border: none !important;
    cursor: not-allowed !important;
}

.crawlsubscribeform .mat-form-field-underline {
    background-color: #7f8fa4 !important;
}

.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background: #55c6ff;
}

synonyms-cmp .mat-accent .mat-checkbox-background,
synonyms-cmp .mat-checkbox-checked.mat-accent .mat-checkbox-background,
synonyms-cmp .mat-checkbox-inner-container,
synonyms-cmp .mat-radio-outer-circle,
synonyms-cmp .mat-radio-inner-circle,
synonyms-cmp .mat-checkbox-inner-container {
    height: 12px !important;
    width: 12px !important;
}

.main-content[data-background-color="black"]
    #cawl-subscription-model
    .modal-header {
    border-bottom: 0px solid #66567c;
}

#cawl-subscription-model .close {
    font-weight: 100;
    font-size: 38px;
    margin-right: 14px;
    width: 5px;
}

.main-content[data-background-color="black"] .c_text_heading {
    color: #fff;
}

home-cmp .top-section.col-xl-12 {
    display: flex;
}

.home-header {
    background-color: #fff;
    padding: 10px 15px;
    opacity: 0.75;
    border-radius: 10px;
}

.home-notification {
    position: relative;
    cursor: pointer;
}

.home-updates {
    padding: 5px;
    cursor: pointer;
    text-align: center;
    box-shadow: 0px -20px 30px 0 rgba(0, 0, 0, 0.05),
        0 4px 25px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    margin: 0px -5px;
}

.notification-popup {
    transform: scale(0.85);
    opacity: 0;
    position: absolute;
    width: 30%;
    overflow: visible;
    right: 0;
    background: #ffffff;
    margin-right: 20px;
    padding: 5px 5px 0 5px;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    transition: opacity 250ms cubic-bezier(0, 1, 0.4, 1),
        transform 250ms cubic-bezier(0.18, 1.25, 0.4, 1);
    box-shadow: 2px 10px 30px 0 rgba(0, 0, 0, 0.05),
        0 4px 25px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
}

.active-notification {
    transform: scale(1);
    opacity: 1;
    z-index: 11;
}

.messages {
    display: inline-flex;
    width: 100%;
    padding: 10px;
    overflow: hidden;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.announcements:first-child .messages {
    border-top: 1px solid rgba(0, 0, 0, 0.15);
}

.messages:hover {
    background: #55c6ff29 !important;
}

.readUnread {
    background: #ececec;
}

.notification-popup-up-arrow {
    min-height: 70vh;
    max-height: 70vh;
    overflow: hidden auto;
}

.backdrop-overlay {
    position: fixed;
    background: transparent;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    width: 100vw;
    height: 100vh;
}

.announcements {
    cursor: pointer;
}

.notification-popup .mat-tab-header {
    height: 35px;
    border-bottom: none;
}

.notification-popup .mat-tab-body-wrapper {
    height: 65vh;
}

.notification-popup .mat-tab-label {
    height: 35px;
}

.notification-popup .mat-tab-label .mat-tab-label-content {
    font-size: 14px !important;
}

.csm-message-btn.mat-button .mat-button-focus-overlay {
    background: transparent;
}

.csm-message-btn.mat-button:hover .mat-button-focus-overlay {
    background: transparent;
}

csm-dialog .table thead th,
recommendations .table thead th,
.main-panel[data-background-color="black"] csm-dialog .table thead th,
.main-panel[data-background-color="black"] recommendations .table thead th {
    font-size: 12px;
    
}

csm-dialog .table td,
recommendations .table td {
    font-size: 13px;
    font-weight: 500;
    word-break: break-word;
}

.main-content[data-background-color="black"] .c_text_heading {
    color: #fff;
}

csm-dialog-popup .buttonPrimary,
recommendations .buttonPrimary {
    height: 44px;
}

csm-dialog .table-su tbody tr,
csm-dialog .table-su tbody tr {
    border-top: solid 1px rgba(0, 0, 0, 0.12) !important;
    background-color: #ffffff !important;
}

.main-panel[data-background-color="black"] csm-dialog .table thead.t-head,
.main-panel[data-background-color="black"] recommendations .table thead.t-head {
    background-color: #e0e8ec !important;
    border: none !important;
}

.main-panel[data-background-color="black"] .csm-heading,
.main-panel[data-background-color="black"]
    home-cmp
    .css-scope
    .readUnread
    .subject,
.main-panel[data-background-color="black"]
    home-cmp
    .css-scope
    .readUnread
    .created-date {
    color: #fff;
}

.main-panel[data-background-color="black"] .csm-message-btn {
    color: #fff;
    border: 1px solid #afb2bc;
}

.main-panel[data-background-color="black"] .csm-message-btn:hover {
    border: 1px solid #7886f7;
    background-image: linear-gradient(270deg, #55c7ff, #7886f7);
}

.main-panel[data-background-color="black"] .message-icon-color {
    fill: #fff;
}

.main-panel[data-background-color="black"] .home-header {
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"] dashboardchart .outer-div {
    background: transparent;
}

.main-panel[data-background-color="black"] home-cmp .table thead.t-head {
    color: #ffffff;
    background-color: transparent !important;
}

.main-panel[data-background-color="black"] home-cmp .table thead th {
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"] top-nav .notification-popup {
    background: #1f1e40;
    color: #fff;
}

.main-panel[data-background-color="black"] top-nav .readUnread {
    background: #121230;
}

top-nav .mat-tab-label-active .mat-tab-label-content {
    font-weight: bold;
    color: #55c6ff !important;
}

top-nav .mat-ink-bar {
    display: none;
}

.main-panel[data-background-color="white"] top-nav .light-theme-icon,
.main-panel[data-background-color="black"] top-nav .dark-theme-icon {
    display: inline-block;
}

.main-panel[data-background-color="white"] top-nav .dark-theme-icon,
.main-panel[data-background-color="black"] top-nav .light-theme-icon {
    display: none;
}

.main-panel[data-background-color="black"]
    .css-scope
    .notification-popup-up-arrow {
    background-color: #1f1e40;
}

.main-panel[data-background-color="black"]
    .css-scope
    .notification-popup-up-arrow
    .mat-tab-label-container {
    background-color: #1f1e40;
}

home-cmp .mat-form-field .mat-form-field-label-wrapper > label {
    display: none !important;
}

.main-panel[data-background-color="black"] home-cmp .card {
    background-color: #1e1c44;
}

.main-panel[data-background-color="black"]
    home-cmp
    .dashboard-boxes
    .outer-div {
    background: #1e1c44;
}

.main-panel[data-background-color="black"] home-cmp .stage-2-heading,
.main-panel[data-background-color="black"] home-cmp .stage-1-heading,
.main-panel[data-background-color="black"] home-cmp .recommendationsList,
.main-panel[data-background-color="black"] home-cmp .su-insights,
.main-panel[data-background-color="black"] home-cmp .usageTrend,
.main-panel[data-background-color="black"]
    home-cmp
    .line-conversion-chart-names,
.main-panel[data-background-color="black"] .css-scope .subject,
.main-panel[data-background-color="black"] .css-scope .created-date {
    color: #ffffff;
}

.main-panel[data-background-color="black"] home-cmp .background,
.main-panel[data-background-color="black"] home-cmp .background2 {
    fill: #fff;
}

.main-panel[data-background-color="black"]
    .search-summary-conversion
    > .mat-form-field
    > .mat-input-wrapper
    > .mat-input-flex
    > .mat-input-infix
    > .mat-select
    span
    > span {
    color: #ffffff;
}

.main-panel[data-background-color="black"]
    analytics-v2
    app-line-conversion-chart
    .row.drawLineChart {
    background: #1f1e40;
}

.main-content[data-background-color="black"] analytics-v2 .legends_test {
    background: #121230;
}

analytics-v2 .DrawLineChart {
    margin-top: 15px;
}

.main-panel[data-background-color="black"] .viewNotifications {
    background-color: white !important;
}

.main-panel[data-background-color="black"] .su-trends {
    background: #1f1e40;
    border: 1px solid #1f1e40;
}

.main-panel[data-background-color="black"] .su-trends:before {
    background: #1f1e40;
    color: #1f1e40;
    margin: 0 15px;
    padding: 0px;
}

.main-panel[data-background-color="black"] .su-trends:hover:before {
    background: #121230;
    color: #121230;
}

.main-panel[data-background-color="black"] .recommendationsSection,
.main-panel[data-background-color="black"] .actionableInsights {
    border-top: 1px solid #1e1e40;
}

.main-panel[data-background-color="black"] .su-dashboardOptions {
    border: 1px solid #ffffff3d;
}

home-cmp .queryFilters-div table tbody tr:hover {
    background: transparent;
}

home-cmp .focus {
    transform: translate(27%, 20%) !important;
}

.su-stats:before {
    content: "";
    position: relative;
    top: 45px;
    right: -25px;
    width: 0px;
    border-left: 3px dotted #dcdbdb;
    height: 55px;
}

.main-panel[data-background-color="black"] .su-stats:before {
    border-left: 3px dotted #68a3fa6b;
}

.main-panel[data-background-color="black"]
    home-cmp
    .queryFilters-div
    table
    tbody
    tr:hover {
    background: transparent;
}

app-line-conversion-chart .x-Axis path {
    display: none;
}

.cdk-overlay-pane .mat-tooltip {
    box-shadow: 0 7px 14px 0 rgba(11, 11, 180, 0.2);
    background-color: #56c6ff;
    padding-right: 8px;
    font-family: "Montserrat";
    font-size: 12px;
    font-weight: 500;
    /* max-width: 178px; */
}

.cdk-overlay-dark-backdrop {
    background: rgba(0, 0, 0, 0.57) !important;
}

.cdk-overlay-container.black .csm-popup-head,
.cdk-overlay-container.black .notificationTitle {
    background: #1f1e40;
}

.mat-progress-bar {
    height: 3px !important;
}

.Case-Deflection-loader {
    position: absolute !important;
    height: 2px !important;
}

search-tuning .mat-form-field-infix {
    height: 30px;
}

.addClass {
    z-index: 1;
}

.switchTemplate .mat-tab-group,
.switchTemplate .mat-tab-group .mat-tab-body-wrapper {
    height: 100%;
}

content-sources field-conditions .infoInput {
    opacity: 1;
}

.display-flex {
    display: flex !important;
}

.display-none{
    display:none;
}

.margin-TB-auto-LR-0 {
    margin: auto 0px;
}
.margin-TB-0-LR-auto {
    margin: 0px auto;
}
.margin-left-auto {
    margin-left: auto;
}

.margin-left-10px {
    margin-left: 10px;
}

.margin-left-15px {
    margin-left: 15px;
}

.margin-right-15px {
    margin-right: 15px;
}

.margin-right-20px {
    margin-right: 20px;
}

.margin-right-25px {
    margin-right: 25px;
}

.margin-right-8px {
    margin-right: 8px;
}
.margin-left-20px{
    margin-left: 20px;
}

.padding-15 {
    padding: 15px;
}

.padding-20px {
    padding: 20px;
}

.padding-0px {
    padding: 0px !important;
}

.padding-5px{
    padding:5px;
}

.margin-0 {
    margin: 0px !important;
}

.padding-top-2px {
    padding-top: 2px;
}

.padding-bottom-5px {
    padding-bottom: 5px;
}

.padding-bottom-15px {
    padding-bottom: 15px !important;
}

.line-height-1 {
    line-height: 1;
}

.line-height-30 {
    line-height: 30px;
}

.text-align {
    text-align: center;
}

.padding-15px {
    padding: 15px !important;
}

.navbar-nav .top-right-nav {
    margin: auto;
    line-height: normal;
    padding: 0px 10px;
}

.padding-right-5px {
    padding-right: 5px !important;
}

.margin-right-5px {
    margin-right: 5px;
}

.padding-left-15px {
  padding-left: 15px !important;
}

.padding-right-15px {
  padding-right: 15px !important;
}

.padding-top-10px {
    padding-top: 10px;
}

.padding-top-15px {
    padding-top: 15px;
}

.padding-top-20px {
    padding-top: 20px;
}
 
.padding-bottom-10px {
    padding-bottom: 10px;
}

.padding-bottom-15px {
    padding-bottom: 15px;
}

.padding-bottom-20px {
    padding-bottom: 20px;
}

.margin-bottom-20px {
    margin-bottom: 20px;
}

.padding-LR-25px {
    padding: 0px 25px;
}

.Filter-Preference {
    width: 803px;
    height: 450px;
}

.margin-right-10px {
  margin-right: 10px !important;
}

search-tuning .mat-input-element {
    font-size: 12px !important;
    line-height: 16px !important;
}

.Save-Cancel {
  padding: 20px 25px 20px;
  text-align: center;
  bottom: 0;
  width: 100%;
}

.justify-content {
    justify-content: center;
}

.border-none {
    border: none !important;
}

.account-question {
    text-align: center;
}

.account-question.form-group {
    text-align: center;
    display: inline-block;
    width: 100%;
}

.account-question.form-group .question {
    margin-right: 0px;
    margin-bottom: 10px;
}

.account-question.form-group .mat-input-flex {
    padding: 0px 10px;
}

.account-question.form-group mat-form-field {
    margin-right: 0px;
}

.account-question .mat-input-underline.mat-form-field-underline {
    display: none !important;
}

.account-question .question .mat-form-field-infix {
    margin-top: 7px !important;
    font-size: 13px;
}

.account-warn {
    margin: 0px;
    font-weight: 500;
    font-size: 11px;
}

.account-warn p {
    font-size: 11px;
    margin-bottom: 0px;
}

.my-account {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 5px;
}

.account-warn {
    border: 1px solid #64acfc;
    padding: 10px;
    line-height: 1.6;
    position: relative;
}

.account-warn-highlight {
    font-weight: 600;
    background-color: aliceblue;
}

.account-warn-img {
    fill: #fff;
    background-color: #64acfc;
    padding: 3px;
    border-radius: 15px;
    height: 27px;
    width: 27px;
}

.account-warn .account-warn-anim {
    position: absolute;
    left: -14px;
    top: -14px;
}

.account-pass-buttons {
    margin-top: 10px;
    height: 20px;
    padding: 10px;
    margin: 0px auto;
}

label.password-tip {
    float: right;
    font-size: 10px;
    margin-bottom: 0px;
    font-weight: 500;
    margin-top: 6px;
    color: #3c3c3f;
    padding-right: 3px;
}

.account-pass-buttons:hover {
    cursor: pointer;
}

.account-warn p {
    font-size: 13px;
    margin-bottom: 0px;
}

.main-panel[data-background-color="black"]
    mat-form-field.mat-input-container.mat-primary.mat-form-field-type-mat-select.mat-form-field-hide-placeholder
    label {
    background-color: #43425d !important;
}

.main-panel[data-background-color="black"] .account-page,
.main-panel[data-background-color="black"] .my-account-page label {
    background-color: #212039 !important;
    color: #fff !important;
}

.main-panel[data-background-color="black"] .form-control,
.main-panel[data-background-color="black"]
    .account-question.form-group
    .mat-input-flex {
    background-color: #43425d;
    border-color: #625f7c;
}

.main-panel[data-background-color="black"] .account-warn-highlight {
    color: #fff;
    background-color: #64acfb;
}

.account-warn-anim > .account-warn-img > path {
    animation: tada 1000ms infinite both;
}

@keyframes tada {
    from {
        transform: scale3d(1, 1, 1);
    }

    10%,
    20% {
        transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    }

    30%,
    50%,
    70%,
    90% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }

    40%,
    60%,
    80% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }

    to {
        transform: scale3d(1, 1, 1);
    }
}

.my-account-page > .account-page {
    animation: fadeIn 1000ms both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.main-content[data-background-color="black"] .lightThemeTitleBackground {
    background-color: unset !important;
}

.main-content[data-background-color="black"] .kcsReportBorder {
    border: none !important;
}

.main-content[data-background-color="black"] .kcsReportBackground-theme {
    background-color: unset !important;
}

.white-space {
    white-space: nowrap;
}

.main-panel[data-background-color="black"] .Su-sessionActivity {
    position: absolute;
    top: 0px;
    background: #121230;
    width: 57%;
    right: 0px;
    height: 100%;
    padding: 10px;
    overflow: hidden;
    padding-top: 0px;
    padding-right: 0;
    height: 442px;
    overflow-y: auto;
}

.su-artical-failed-deflaction .mat-form-field-infix {
    display: inherit;
}

.caseCreatedTable {
    padding: 15px;
    /* overflow-x: auto;  */
    /* min-width: 256px;
    white-space: nowrap; */
}

.noCaseCreated {
    display: flex;
    justify-content: center;
}

.loader {
    text-align: center;
}

#basic-addon2 {
    position: absolute;
    top: 13px;
    right: 23px;
    float: right;
    background: none;
    font-size: 20px;
}

.srcBarIcon {
    font-size: 15px;
    line-height: 2.2rem !important;
    background: none;
    border: none;
    color: grey !important;
    margin-left: 10px;
}

.customInputList input {
    outline: none;
}

.customInputList input[type="search"] {
    -webkit-appearance: textfield;
    -webkit-box-sizing: content-box;
    font-family: inherit;
    font-size: 100%;
}

.customInputList input[type="search"] {
    border: solid 1px #ccc;
    width: 55px;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    transition: all 0.5s;
}

.customInputList input[type="search"]:focus {
    width: 130px;
    background-color: #fff;
    border-color: #66cc75;
    -webkit-box-shadow: 0 0 5px rgba(109, 207, 246, 0.5);
    -moz-box-shadow: 0 0 5px rgba(109, 207, 246, 0.5);
    box-shadow: 0 0 5px rgba(109, 207, 246, 0.5);
}

.customInputList input:-moz-placeholder {
    color: #999;
}

.customInputList input::-webkit-input-placeholder {
    color: #999;
    padding-left: 10px;
}

.customInputList input[type="search"] {
    float: right;
    width: 34px;
    padding-left: 10px;
    color: transparent;
    cursor: pointer;
}

.customInputList input[type="search"]:focus {
    padding-left: 32px;
    color: #000;
    background-color: #fff;
    cursor: auto;
    position: absolute;
    width: 22%;
    top: 0px;
    right: 261px;
    border: none;
}

.customInputList input:-moz-placeholder {
    color: transparent;
}

.customInputList input::-webkit-input-placeholder {
    color: transparent;
}

.main-content[data-background-color="black"] .searchboxFilter .searchIcon {
    background-color: #1f1e40 !important;
    border: #121230 !important;
}

.main-content[data-background-color="black"] .searchboxFilter .searchFont {
    background-color: #1f1e40 !important;
    border: #121230 !important;
}

.main-content[data-background-color="black"] .sessiontrackFilter {
    background-color: #1f1e40 !important;
    border: #121230 !important;
}

.main-content[data-background-color="black"]
    .sessiontrackFilter
    .menuDropDownfilter {
    color: #fff;
    font-weight: 500;
}

.main-content[data-background-color="black"] .searchboxFilter .searchFont {
    color: #ffffff !important;
}

.black .matOptionFilter {
    color: #ffffff;
    background: #1e1e40 !important;
    border-color: #111230 !important;
}

.black .matOptionFilter:hover {
    color: #ffffff;
    background: #111230 !important;
    border-color: #111230 !important;
}

.black .menuButton {
    color: #ffffff;
    background: #1e1e40;
    border-color: #111230 !important;
}

.black .menuButton:hover {
    color: #ffffff;
    background: #111230 !important;
    border-color: #111230 !important;
}

.black .optionsFilter {
    color: #ffffff;
    background: #1e1e40 !important;
}

.black .optionsFilter:hover {
    color: #ffffff;
    background: #111230 !important;
    border-color: #111230 !important;
}

.main-content[data-background-color="black"] .matChipItems {
    color: #ffffff !important;
    background: #1e1e40;
    border-color: #111230 !important;
}

.main-content[data-background-color="black"] .matChipFont {
    color: #ffffff !important;
}

.main-content[data-background-color="black"] .chipClear {
    color: #ffffff;
    background: #1e1e40;
    border-color: #111230 !important;
}

.black .filterPanel .mat-option {
    color: #ffffff;
    background: #1e1e40;
}

.black .filterPanel .mat-option:hover {
    color: #ffffff;
    background: #111230 !important;
}

.black .innerFilterPanel .mat-option {
    color: #ffffff;
    background: #1e1e40;
}

.black .innerFilterPanel .mat-option:hover {
    color: #ffffff;
    background: #111230 !important;
}

.cdk-overlay-container.black .mat-autocomplete-panel,
.cdk-overlay-container.black .mat-select-panel {
    background-color: #121230;
}

.main-content[data-background-color="black"] analytics-results .contentFilter {
    background-color: #1a193f;
}

.main-content[data-background-color="black"] .content-data-table .table td,
.main-content[data-background-color="black"]
    .content-data-table
    .table
    td
    .date {
    color: #fff !important;
}
.main-content[data-background-color="black"]
    generate-search-client
    td.search-client-detail {
    color: #fff !important;
    background-color: #292852;
    border-bottom: 1px solid #121230 !important;
}

.align-self {
    align-self: center;
}

.width-100 {
    width: 100%;
}

.main-panel[data-background-color="black"] .mat-tab-header-pagination-chevron {
    border-color: #fff !important;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-disable {
  cursor: not-allowed !important;
}

.font-weight-400 {
    font-weight: 400 !important;
}

.font-weight-500 {
    font-weight: 500 !important;
}

.margin-auto {
    margin: auto;
}

.wrapper.nav-collapsed.menu-collapsed .app-sidebar:not(.hover) li.addons-heading .addons-dropdown,
.wrapper.nav-collapsed.menu-collapsed .app-sidebar:not(.hover) li.addons-heading .administration-dropdown,
.wrapper.nav-collapsed.menu-collapsed .app-sidebar:not(.hover) div.nav-item ul#addon-toggle,
.wrapper.nav-collapsed.menu-collapsed .app-sidebar:not(.hover) div.nav-item ul#cs-toggle,
.wrapper.nav-collapsed.menu-collapsed .app-sidebar:not(.hover) div.nav-item ul#administration-toggle,
.wrapper.nav-collapsed.menu-collapsed .app-sidebar:not(.hover) div.nav-item ul#llmIntegration-toggle {
    display: none;
}

div.nav-item li.display-flex.addons-heading > a.router-link-active:before {
    content: "";
    display: block;
    z-index: -1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-left: solid 5px #1fb5eb;
    background: transparent;
    -webkit-transition: 0.35s ease left;
    -o-transition: 0.35s ease left;
    -moz-transition: 0.35s ease left;
    transition: 0.35s ease left;
}
.ad_fweight-300 {
    font-weight: 300;
}
.ad_fweight-400 {
    font-weight: 400;
}
.ad_fweight-500 {
    font-weight: 500;
}
.ad_fweight-bold {
    font-weight: 500;
}
a.ad_btn-disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.45;
    position: relative;
}
a.ad_btn-disabled:before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: -4px;
    bottom: 0;
    background: rgb(234, 234, 234);
    z-index: -1;
    width: 29px;
    border-radius: 8px;
    height: 29px;
    background-repeat: no-repeat;
    background-size: cover;
}
a.ad_btn-disabled:hover {
    text-decoration: none;
}
a.ad_btn-disabled:focus {
    text-decoration: none;
}
a.ad_btn-disabled:active {
    text-decoration: none;
}
.sectionDiv.adding-new-content a.pointer.ad_btn-disabled:first-child:before {
    content: "";
    top: 0px;
}
.main-content[data-background-color="black"] a.ad_btn-disabled:before {
    background: #1a1925;
}
/* .adding-new-content td.table-set.mobile-content-source.actions a.pointer.a-icon-edit {
    display: inline;
} */
.adding-new-content td.table-set.mobile-content-source.actions a#delete {
    margin-left: 0px;
}

td.search-client-detail
    a.pointer.ad_btn-disabled.ng-trigger.ng-trigger-opened:before {
    left: 1px;
}
.adding-new-content
    td.table-set.mobile-content-source.actions.actions
    a:first-child {
    left: -12px;
}
.main-content[data-background-color="black"]
    td.search-client-detail
    a.ad_btn-disabled:before,
.main-content[data-background-color="black"]
    .adding-new-content
    td.table-set.mobile-content-source.actions
    a.pointer.ad_btn-disabled:before {
    background: #e6e6e6;
}

.main-content[data-background-color="black"]
    addons
    .no-addons-img
    svg
    #Shadow
    ellipse {
    fill: #ffffff21;
}

.main-content[data-background-color="black"] addons .add-on-know-more {
    background-color: #121230;
    border: 1px solid #55c6ff;
}

.main-content[data-background-color="black"] addons .add-on-name.color-909090 {
    color: #ffffff !important;
}

.app-sidebar[data-background-color="black"] svg .down-arrow {
    fill: #ffffff;
}

.main-panel[data-background-color="black"] .table th {
    border-top: 1px solid #121230 !important;
}

.main-panel[data-background-color="black"] .table th,
.main-panel[data-background-color="black"] .table td {
    border-bottom: 1px solid #121230 !important;
    color: #ffffff;
}
.searchClickPositionReport {
    padding-top: 12px;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .search-click-header {
    background-color: #1f1e40 !important;
    border-color: #1f1e40;
    color: #ffffff !important;
    font-family: Montserrat;
}

.main-panel[data-background-color="black"] analytics-v2 .search-click-header {
    background-color: #1f1e40 !important;
    color: #ffffff;
    border: none;
}

.cumulativeDeflectionTrends .mat-form-field-underline {
    display: none;
}

.cumulativeDeflectionTrends .mat-select-value-text {
    padding-left: 5px;
}
.nav-collapsed #desktopview .sidebar-header.logo-back .instance_env-name {
    visibility: hidden;
}

.nav-collapsed #desktopview:hover .sidebar-header.logo-back .instance_env-name {
    visibility: visible !important;
}
.analytics-header-row mat-form-field.analytics-header-field {
    margin: 0;
}
.main-content[data-background-color="black"]
    analytics-v2
    .ad-heading-fixed
    .topHeading {
    background-color: #2c2b4c !important;
}
.main-panel[data-background-color="black"] .analytics-header-row {
    border: 1px solid #454465;
    background: #2c2b4c;
}
.main-panel[data-background-color="black"]
    analytics-v2
    .form-group.analytics-header-block {
    background-color: #2c2b4c;
    opacity: 1;
}
.main-panel[data-background-color="black"] .analytics-header-row {
    border: 1px solid #000;
    background: #121230;
}
.main-panel[data-background-color="black"] #searchAnalytics .mat-tab-link {
    color: #fff;
}
.main-panel[data-background-color="black"]
    #searchAnalytics
    .mat-tab-link.router-link-active {
    color: #55c6ff;
}
.main-panel[data-background-color="black"]
    .analytics-header-row
    .mat-form-field-appearance-legacy
    .mat-form-field-infix
    input {
    background: transparent;
    opacity: 1;
}
.main-panel[data-background-color="black"] .ad-dark-fill {
    fill: #fff !important;
}
.ad_date-remove-icon mat-datepicker-toggle.mat-datepicker-toggle {
    display: none;
}
.escalation-prediction-input-data .mat-tab-link-container,
.escalation-prediction-feature-classification .mat-tab-link-container,
.escalation-prediction-scoring-data .mat-tab-link-container,
.escalation-prediction-escalations-dashboard .mat-tab-link-container {
    display: inline-block;
    text-align: center;
    width: 100%;
}

.escalation-prediction-input-data .mat-tab-list,
.escalation-prediction-feature-classification .mat-tab-list,
.escalation-prediction-scoring-data .mat-tab-list,
.escalation-prediction-escalations-dashboard .mat-tab-list {
    display: inline-block;
}

.escalation-prediction-input-data .mat-form-field-infix {
    margin-top: 5px !important;
}

.escalation-prediction-input-data
    .mat-form-field-appearance-legacy
    .mat-form-field-underline {
    height: 0;
}

.escalation-prediction-input-data .mat-form-field {
    margin-right: 0px;
}

.escalation-prediction-input-data .mat-form-field-underline {
    width: 0px;
}
.flex-direction-column {
    flex-direction: column;
}

.main-panel[data-background-color="black"] search-tuning .mat-table {
    background-color: #121230;
}

.main-panel[data-background-color="black"] search-tuning .keywordTitle {
    background-color: #121230;
}

.main-panel[data-background-color="black"] search-tuning .addNewKeyword1 input {
    background-color: #121230;
    color: #fff !important;
}
.main-panel[data-background-color="black"] search-tuning .keywordError {
    background-color: #121230;
    color: #fff !important;
}

.escalation-prediction-input-data
    .mat-form-field-appearance-fill
    .mat-form-field-flex {
    padding-top: 0px;
}
.escalation-prediction-input-data .start-date-picker .mat-form-field-infix {
    margin-left: 40px;
    position: absolute;
}
.escalation-prediction-input-data
    .mat-form-field-appearance-fill
    .mat-form-field-flex {
    background-color: unset;
}
.ep-frequency-field .mat-form-field-infix {
    margin-top: 10px !important;
    margin-bottom: 10px;
}
.ep-date-field .mat-form-field-infix {
    margin-top: 10px !important;
    margin-bottom: 10px;
}
.main-panel[data-background-color="black"]
    accountdetails
    .myInstanceDiv
    .myInstanceDivChild,
.main-panel[data-background-color="black"]
    accountdetails
    .padding_bottom
    .usageDetailsDiv {
    background-color: #121230;
}

.main-panel[data-background-color="black"] accountdetails .accountDetailsDiv_1 {
    background-color: #121230;
}

.main-panel[data-background-color="black"]
    accountdetails
    .padding_bottom
    .usageDetailsDiv
    .apiUsageDiv
    .apiUsageDiv_1 {
    background-color: #121230;
}
.main-panel[data-background-color="black"] accountdetails .detailTitles {
    color: #fff;
}

.main-panel[data-background-color="black"] accountdetails .dropdownSelect {
    background-color: #121230;
}
.main-panel[data-background-color="black"] accountdetails .detailsValues,
.main-panel[data-background-color="black"] accountdetails .myinstanceValue a {
    color: #fff;
}

.main-panel[data-background-color="black"] accountdetails .apiUsageDiv_value {
    color: #fff;
}
.main-panel[data-background-color="black"] accountdetails .textDiv1 {
    color: #fff;
}
.main-panel[data-background-color="black"] accountdetails .textDiv2 {
    color: #fff;
}
.modal-user-content {
    overflow-y: auto;
    max-height: 360px;
}
.modal-user-content::-webkit-scrollbar {
    width: 4px;
    height: 0;
    background-color: #f5f5f5;
}
.ad_share-icon-row,
.ad_share-user-icon {
    width: 37px;
    height: 37px;
    background: #55c6ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.ad_share-user-icon {
    background: #e9ebf0;
}
.ad_share-header-main {
    align-items: flex-start;
    justify-content: space-between;
}
.ad_lable-title {
    font-size: 14px;
    font-weight: bold;
}
.ad_share-user-info {
    border-radius: 4px;
    margin-right: 17px;
    width: calc(100% / 4 - 18px);
}
.ad_share-inner-block {
    border: 1px solid #ccc;
    box-sizing: border-box;
    border-radius: 4px;
}
.ad_share-user-info:active,
.ad_share-active {
    background-color: #55c6ff;
    border-color: #55c6ff;
    color: #fff;
}
.ad_share-inner-block:hover {
    border: 1px solid #55c6ff;
}

#shareSettingModal .modal-dialog {
    width: 100%;
}
.ad_share-modal-title {
    text-align: left;
    color: #000;
    font-size: 15px;
    font-weight: 500;
}
.ad_share-svg {
    border: 1px solid #ebebee;
    border-radius: 8px;
    width: 26px;
    height: 26px;
    box-sizing: border-box;
    padding: 4px;
}

.ad_share-svg:hover {
    border: 1px solid #4fc2ff;
    background-image: linear-gradient(to bottom, #55c7ff, #7886f7);
}

.ad_share-svg .ad_share-fill {
    fill: #b0b2bc;
}

.ad_share-svg:hover .ad_share-fill {
    fill: #fff;
}

.ad_share-user-inner {
    flex-wrap: wrap;
}
.ad_shared-owner {
    position: absolute;
    top: -10px;
    right: 14px;
    background-color: #ffad00;
    color: #fff;
    padding: 2px 6px;
    font-size: 12px;
    border-radius: 10px;
}

.modal-user-content::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #f5f5f5;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.modal-user-content::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #666;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
}
.modal-user-content {
    scrollbar-width: thin;
    scrollbar-color: #666 transparent;
}
.ad_share-user-info-row {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: calc(100% - 1px);
}
.ad_share-user-desc,
.ad_share-user-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.ad_btn-back {
    min-width: 170px;
}
.ad_heading-txt-top {
    max-width: calc(100% - 180px);
    flex: 1;
    word-break: break-word;
    padding-right: 5px;
}

@media (min-width: 576px) {
    #shareSettingModal .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
        top: 15%;
    }
}
@media (min-width: 768px) {
    #shareSettingModal .modal-dialog {
        max-width: 640px;
        margin: 1.75rem auto;
    }
}
@media (min-width: 992px) {
    #shareSettingModal .modal-dialog {
        max-width: 800px;
        top: 30%;
    }
}
@media (min-width: 1200px) {
    #shareSettingModal .modal-dialog {
        max-width: 1140px;
        top: 28%;
    }
}
.main-content[data-background-color="black"] .ad_darkmode-bg1 {
    background-color: #413a5e;
    color: #fff;
}
.main-content[data-background-color="black"] .ad_darkmode-bg2,
.main-panel[data-background-color="black"] .mat-expansion-panel-content {
    background-color: #1b193f;
    color: #fff;
}
.main-content[data-background-color="black"] .ad_darkmode-bg3 {
    background-color: #121230;
    color: #fff;
}
.main-content[data-background-color="black"] .ad_darkmode-color {
    color: #fff;
}
.ep-container .mat-progress-bar-fill::after {
    background-color: #57c5ff !important;
}
.main-panel[data-background-color="black"] .ad-heading-static .ad_darkmode-bg1 {
    background-color: #2c2b4c;
}

.ad_search-uid_API {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 162px;
    background: #e9ebf0;
    border-radius: 4px;
    color: #090909;
    border: 1px solid #e9ebf0;
    cursor: pointer;
}
.jstree-default .jstree-last {
    background-size: 0px !important;
}
/* Home page Redesign css */
.main-content[data-background-color="black"]
    .ad_community-table
    .content-data-table
    .table
    td.ad_darkmode-text-black,
.main-content[data-background-color="black"]
    .ad_community-table
    .content-data-table
    .table
    td.table-set.mobile-helper-bot.actions {
    color: #333 !important;
}
.main-content[data-background-color="black"] .disableCustomizeHeading {
    color: white;
}
.main-content[data-background-color="black"] .headingClass {
    color: white;
}

.main-content[data-background-color="black"]
    .filter-container::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: yellow;
}
adminanalytics .mat-form-field-infix {
    margin-top: 0px !important;
}

.main-content[data-background-color="black"] .note-text {
    color: white;
}
.ad_top-navbar.navbar-light .navbar-nav .nav-link:hover,
.ad_top-navbar.navbar-light .navbar-nav .nav-link:focus {
    color: #333;
}

::ng-deep .an-tooltip-sessionReport-sr {
    position: relative;
    width: 300px;
    left: -35px;
    word-break: break-all;
}

::ng-deep .tooltip-sessionReport-swnresult {
  left: 54px;
  position: relative;
  bottom: 18px;
  text-overflow: clip;
  word-break: break-all;
}
 /* .main-panel[data-background-color="black"] .pagination-search-tuning>.page-link  {
  background:#121230 !important  ;
  color: white !important;
}  */
 .main-panel[data-background-color="black"] .pagination-search-tuning>nav>ul>li>a {
  background:#121230 !important  ;
  color: white !important;
} 
.main-panel[data-background-color="black"] .paginationConatiner{
  color: white !important;
}
.main-panel[data-background-color="black"] .pagination-search-tuning>nav>ul>li.active>a {
  background:#009DA0 !important  ;
  color: white !important;
} 

.wrapper.nav-collapsed.menu-collapsed .highConversionClickCount{
  display: flex;
  justify-content: center;
  text-indent: -15px;
}
::ng-deep .an-tooltip-tiles{
  position: relative !important; 
  bottom: 50px !important;
  left: -40px !important;
  padding: 5px !important;
  word-break: break-all;
  width: auto;
  font-size: 14px !important;
}
.source-label-box .mat-expansion-indicator {
  border: 2px solid #bbb;
  height: 17px;
  width: 17px;
  box-sizing: border-box;
  transition: all 0;
  border-radius: 2px;
  left: 1rem;
  z-index: 1;
    position: absolute;
}
.source-label-box .mat-expansion-panel-body{
  padding: 0px !important;
}
.source-label-box .mat-expansion-indicator::after {
  content: "";
  padding: 2.5px;
  position: relative;
  top: -4px;
  left: 2px;
  border-width: 2px 2px 0 0;
}
.source-label-box .mat-expanded .mat-expansion-indicator::after {
  border-width: 2px 0 0 2px;
  top: -3px;
  left: 3.4px;
  content: "";
  padding: 2.5px;
  position: relative;
}
.source-label-box  .mat-expansion-panel-header{padding:0;}
.padding-10px-0px {
  padding: 10px 0px;
}
/* .cdk-overlay-container{
  left: 25px;
  top: -6px;
} */
.su_name_alignment{
  align-items: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.mat-form-field.mat-form-field-invalid .mat-form-field-ripple {
  background-color: #f44336 !important;
}

/* Aditya Changes */
.custom-row:after {
    content: "";
    display: table;
    clear: both;
}
.column-split1 {
    float: left;
    width: 35%;
    height: 450px;
    /* border-right: 1px solid #efefef; */
}
.column-split2 {
    float: left;
    width: 65%;
    overflow-y: auto;
}
.padding-left-10 {
    padding-left: 10px;
}
.padding-right-10 {
    padding-right: 10px;
}
.img-container {
    height: 100%;
    width: 100%;
    border-radius: 10px;
    white-space: nowrap;
    text-align: center;
}
.img-container img {
    max-height: 80%;
    max-width: 80%;
}
.img-helper {
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}
.up-down-padding-15 {
    padding: 15px 10px;
}
.selection-buttons {
    display: inline-block;
    margin: 0;
    width: 50%;
}
.save-cancel-container {
    display: inline-block;
    align-content: flex-end;
    width: 50%;
}
.save-cancel-container .buttonPrimary {
    font-size: 13px;
    line-height: 25px;
}
.row.up-down-padding-2 {
    width: 100%
}
.ContentSource-title.compensatory {
    margin: 6px 0px;
    font-size: 14px;
}
.align-button-right {
    float: right;
    line-height:30px;
}
.column-split2 > .mat-tab-group > .mat-tab-header {
    border: 1px solid #efefef;
    margin: 5px 15px;
    border-radius: 4px;
}
.column-split2 .mat-tab-label-content {
    font-size: 13px;
}
.column-split2 .mat-tab-label {
    height: 32px;
}
p.noteText {
    padding: 10px 15px 0;
    font-size: 13px;
    margin-bottom: 0px;
}
.sectionMainDiv.perfect.contentSource table td {
    font-size: 12px;
    font-weight: bold;
}
.sectionMainDiv.perfect.contentSource .material-icons {
    font-size: 22px;
}
.chose-content-tabs {
    padding: 0px 10px;
    border-radius: 6px;
    border: 2px solid #dedede;
    margin: 0px 15px;
}
.padding-10-20 {
    padding: 10px 20px !important;
}
.chose-content-tabs label {
    font-size: 12px;
    font-weight: bold;
}
.content-dropdown {
    display: flex;
    flex-direction: row;
    margin: 10px 0px 6px 0px;
}
.content-dropdown .mat-radio-container {
    width: 15px;
    height: 20px;
}
.content-dropdown .mat-radio-label-content {
    padding: 0px 5px 6px 5px;
}
.content-dropdown .mat-radio-button {
    margin-right: 30px;
}
.content-dropdown .mat-radio-inner-circle, 
.content-dropdown .mat-radio-outer-circle {
    height: 15px !important;
    width: 15px !important;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail li.filter-menu-hader {
    background-color: #1f1e40;
    color: #a0a0ac;
    border: none;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .filter-sidebar{
    background-color: #1a193f;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .filter-items{
    border-bottom: 1px solid #121230;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail span.menu-title{
    color: #FFFFFF;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .filter-avtivity_label{
    color: #FFFFFF;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .mat-radio-disabled  .mat-radio-label-content{
    color: rgba(0, 0, 0, 0.38) !important;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .mat-radio-disabled  .mat-radio-outer-circle{
    border-color: rgba(0, 0, 0, 0.38) !important;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .toggleActivity-label{
    color: #fff;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .content-facets-filter-data{
    color: #fff;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .content-facets-count{
    color: #fff;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .mat-checkbox-frame{
    border-color: white;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .searchboxFilter{
    background-color: #1f1e40 !important;
    border: #121230 !important;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail li.filter-menu-hader{
    line-height: 2.1;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .filter-sidebar-container{
    top: -54px;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .disable-filter{
    background: none !important;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .disable-filter li span.menu-title{
    color: #9A9A9A;
    opacity: 0.3;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .disable-filter li svg,.disable-filter li .filter-arrows svg{
    fill:  #9A9A9A !important;
    opacity: 0.3  !important;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .sessions-filter-icon{
    color: #FFFFFF;
}
.main-content[data-background-color="black"] analytics-v2 session-report-detail .sessionFilterButton{
    background: #121230;
    color: #FFFFFF;
    border: 1px solid #7290F8;
    border-radius: 1px;
    opacity: 1;
}
.main-panel[data-background-color="black"] .dropdown-select .form-control {
  background-color:#43425d!important;
}
.main-panel[data-background-color="black"] browse-component .dropdown-select .browse-font-size {
    background-color: transparent !important;
}
.main-panel[data-background-color="black"] browse-component .outer-view {
    background-color: #292852 !important;
}
.main-panel[data-background-color="black"] browse-component .card,
.main-panel[data-background-color="black"] browse-component.sectionDiv {
    background-color: #121230 !important;
}

.main-panel[data-background-color="black"] browse-component .cs-objects-selector, 
.main-panel[data-background-color="black"] browse-component .select-client-date, 
.main-panel[data-background-color="black"] browse-component .select-client {
    border: 1px solid #454465;
  }
.main-panel[data-background-color="black"] .intent-suggestion-text {
    color: white;
}
.main-panel[data-background-color="black"] mat-table {
    background-color: #121230;
    color: white;
}
.main-panel[data-background-color="black"] mat-paginator {
    background-color: #121230;
    color: white;
}
.main-panel[data-background-color="black"] .training-container .train-text {
    color: white !important;
}
.main-panel[data-background-color="black"] .training-container .train-date{
    color: white !important;
} 
.main-panel[data-background-color="black"] .intent-button .intentSuggestion {
    background-color: #121230 !important;
    padding: 9px;
    border: 1px solid #56c5fc;
}
.main-panel[data-background-color="black"] .intent-button .uploadIntent {
    background-color: #121230 !important;
    border: 1px solid #56c5fc;
    padding: 9px;
} 

::ng-deep .main-panel[data-background-color="black"] .intentSuggestionDiv mat-header-cell{
    background-color: #121230 !important;
}
.main-panel[data-background-color="black"] .intentSuggestionDiv .mat-checkbox-frame {
    border-color: #bcbccb !important;
}
.main-panel[data-background-color="black"] .su_intent-block .mat-header-row,
.main-panel[data-background-color="black"] .intentSuggestionDiv .mat-header-row {
    background-color: #121230 !important;
    border: 1px solid white;
}
.main-panel[data-background-color="black"] .su_intent-block .mat-header-row .mat-header-cell,
.main-panel[data-background-color="black"] .intentSuggestionDiv .mat-header-row .mat-header-cell{
    color: white;
} 
.main-panel[data-background-color="black"] .intentSuggestionTable .intent-table svg{
    fill: white;
} 
.main-panel[data-background-color="black"] .noIntentFound{
    color: white;
} 
.main-panel[data-background-color="black"] .training-container { 
    background: #111230 !important; 
}
.su__sectionDiv-search-client{background-color: #fff;border-bottom: 0;margin: 0px 20px 20px 20px;}
.ad_designer-open {
    overflow: hidden;
}
.resize-none{
    resize: none;
}
.mat-tab-custom.apiClient>.mat-tab-header>.mat-tab-label-container {
    -ms-flex-pack: center !important;
    justify-content: center !important;
}

.mat-title {
    overflow: visible !important;
}

.main-panel[data-background-color='black'] .searchFeedbkClickedPos {
    background: #1b1a3f 0% 0% no-repeat padding-box;
    border-style: dashed;
}

.main-panel[data-background-color="black"]
    .oldAnalyticsSection
    .search-feedbk-header {
    background-color: #1f1e40 !important;
    border-color: #1f1e40;
    color: #ffffff !important;
    font-family: Montserrat;
}

.matRole{
    margin-top: 0!important; 
}


intent mat-form-field.dropdown-list .mat-form-field-label {
    top: 2.4em;
}

/* intent mat-form-field.dropdown-list .mat-select-arrow {
    margin-top: 1em !important;
} */

intent .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0 1em 0;
}

intent mat-form-field.dropdown-list .mat-select-arrow {
   margin-top:  1.1em !important;
}

.add-synonym-tooltip.mat-tooltip {
    margin: 20px !important;
}

.tuning-info.mat-tooltip {
    max-width: 400px;
}

.tech-doc.mat-tooltip {
    max-width: 500px;
}

.mf-tooltip.mat-tooltip {
    max-width: 290px;
}

.tuning-tooltip.mat-tooltip {
    transform: translateX(-28%) !important;
    max-width: 218px;
}

.myPanelClass{
    margin-top: 30px !important; 
}

div.mat-select-panel.intent-add-synonyms {
    top: 45px;
    left: 30px;
    min-width: calc(100% + 35px) !important;
    position: absolute;
}

intent .mat-select-trigger .mat-select-value {
    display: table-cell;
    max-width: 0;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    /* padding-top: 5px !important; */
}

.main-panel[data-background-color="black"] .csm-body,
.main-panel[data-background-color="black"] csm-dialog .table td {
    background-color: #292852 !important;
}

.black .mat-pseudo-checkbox {
    color: white;
}
/* .main-panel[data-background-color="black"] .legends {
    color: #FFFFFF !important;
} */
.main-panel[data-background-color="black"] .legends_test{
    color: #FFFFFF !important;
}
.main-panel[data-background-color="black"] .quotaConsumptionTable {
    background-color: #292852 !important;
    border: 1px solid #121230 !important;
}
.main-panel[data-background-color="black"] .tableRowBorderHeader{
    background-color: #1f1e40 !important;
}
.main-panel[data-background-color="black"] .tableRowBorderApi,
.main-panel[data-background-color="black"] .tableRowBorder,
.main-panel[data-background-color="black"] .timeRangeColumnDiv,
.main-panel[data-background-color="black"] .label-head {
    color: #FFFFFF !important;
}
.main-panel[data-background-color="black"] .tick text {
    fill: #FFFFFF !important;
    color: #FFFFFF !important;
}
.main-panel[data-background-color="black"] .tool {
    fill: #121230 !important;
}
/* .main-content[data-background-color="black"] text {
    fill: #ffffff !important;
} */
.main-content[data-background-color="black"] .displayDate,
.main-content[data-background-color="black"] .displayTypeName,
.main-content[data-background-color="black"] .displayTypeData {
    fill: #ffffff !important;
}
.main-content[data-background-color="black"] .dateSelect input#select-range\ \ {
    fill: #ffffff !important;
    color: #ffffff !important;
    opacity: 1 !important;
}
.main-panel[data-background-color="black"] .mat-select-panel-wrap .sortFieldPanel {
    background: #292852 0% 0% no-repeat padding-box !important;
}

.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .consumptionRateSpan {
    color: #ffffff !important;
}
.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .timeSpan {
    color: #ffffff !important;
}
.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .consumptionSelectSpan {
    color: #ffffff !important;
}
.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .timeSelectSpan {
    color: #ffffff !important;
}
.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .consumptionRadioSpan {
    color: #ffffff !important;
}
.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .timeRadioSpan {
    color: #ffffff !important;
}

.main-panel[data-background-color="black"] .utcNoteApiLogs {
    background: #1f1e40 0% 0% no-repeat padding-box !important;
}
.main-panel[data-background-color="black"] .utcNoteLabelApiLogs,
.main-panel[data-background-color="black"] .utcNoteIconDivApiLogs {
    color: #ffffff !important;
}
.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .consumptionRadioSpan .mat-radio-outer-circle,
.main-panel[data-background-color="black"] .cdk-overlay-container.black .mat-autocomplete-panel, .cdk-overlay-container.black .mat-select-panel .timeRadioSpan .mat-radio-outer-circle {
    border-color: #ffffff !important;
}
.main-panel[data-background-color="black"] .axisValuesOnHoverApiLogs {
    fill: #ffffff !important;
}
.main-panel[data-background-color="black"] .focus .currentSession {
    fill: #ffffff !important;
    color: #ffffff !important;
}

.main-content[data-background-color="black"] .tableHeadDiv , 
.main-content[data-background-color="black"] .tableIndex,
.main-content[data-background-color="black"] .fieldLogicHead,
.main-content[data-background-color="black"] .fieldLogicDesc,
.main-content[data-background-color="black"] .second-head-1
{
    color: white;
}
.main-content[data-background-color="black"] .fieldLogicInput .mat-form-field-appearance-outline .mat-form-field-outline{
    background-color: #2C3652 !important;
}
.main-content[data-background-color="black"] .fieldLogicInput .mat-input-element::placeholder{
    color: white;
}
.main-content[data-background-color='black'] .list-head{
   background: #1f1e40 !important; 
   border: 1px solid #121230;
}
.main-content[data-background-color="black"] .serviceData{
    color: white ; 
}
.main-content[data-background-color="black"] .listTable tbody tr{
    border-top: none !important;
    border: 1px solid #121230;
    border-left: none;
    border-right: none;
}
.main-content[data-background-color="black"] .listTable tbody {
    border-top: none !important ;
}
.main-content[data-background-color="black"] .configList {
    background: #292852 !important; 
}
.cdk-overlay-container.black .deleteFieldPopup .mat-dialog-container,
.cdk-overlay-container.black .cancelDialog .mat-dialog-container,
.cdk-overlay-container.black .slackPopUp .mat-dialog-container,
.cdk-overlay-container.black .deletePopup .mat-dialog-container,
.cdk-overlay-container.black .guardAlert .mat-dialog-container
{
    background-color: #121230 !important; 
    background: #121230 !important; 
}
.cdk-overlay-container.black .deleteText,
.cdk-overlay-container.black .cancel-dialog p,
.cdk-overlay-container.black .popups-heads,
.cdk-overlay-container.black .input-head,
.cdk-overlay-container.black .slackPopUp .mat-input-element
{
    color: white !important;
}
.cdk-overlay-container.black .slackPopUp .mat-input-element:disabled{
    color: #9f9f9f !important;
}
.cdk-overlay-container.black .deleteButton,
.cdk-overlay-container.black .cancelButton
{
    background-color: #121230 ;
}
.main-content[data-background-color="black"] .agentHelperTable {
    background-color: #1f1e40 !important; 
    border: 1px solid #121230 !important;
}
.main-content[data-background-color="black"] .listBody {
    background-color: #292852 !important; 
}
.main-panel[data-background-color="black"] .listTable th {
   border-bottom : none !important;
   border-top: none !important;
}
.main-panel[data-background-color="black"] .listBody td{
    border-bottom: none !important;
}
.cdk-overlay-container.black .clearIconSlack{
    color: #fff ;
}




.main-content[data-background-color="black"] .add-new-config .mat-tab-label.mat-tab-label-active {
    background: transparent linear-gradient(90deg, #A6E3FE 0%, #57C2FE 100%) 0% 0% no-repeat padding-box;;
    opacity: 1!important;
    z-index: 1001;
}

.main-content[data-background-color="black"] .add-new-config .mat-tab-labels {
    background-color: #1E1C44;
    border-radius: 7px 25px 25px 7px;
}

.main-content[data-background-color="black"] .add-new-config .mat-tab-label {
    background: #1E1C44 0% 0% no-repeat padding-box;
}
.main-content[data-background-color="black"] .add-new-config .radio-box{
    color: white !important;
    background: #292852 0% 0% no-repeat padding-box !important;
    border: 1px solid #57C2FE !important;
}

/* .main-content[data-background-color="black"]  .add-new-config .radio-box:hover, .main-content[data-background-color="black"]  .add-new-config .radio-box-selected{
    box-shadow:0 0 2px 2px#768AF7;
    border-radius: 4px;  
  }

.main-content[data-background-color="black"] .add-new-config .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
border-color: #768AF7 !important;
}
.main-content[data-background-color="black"] .add-new-config .mat-radio-button.mat-accent .mat-radio-inner-circle {
background-color: #768AF7 !important;
}
  
.main-content[data-background-color="black"] .add-new-config .mat-radio-button.mat-radio-disabled .mat-radio-label-content {
    color: white!important;
  }
   */
  
.main-content[data-background-color="black"] .add-new-config .disabled-radio .mat-radio-outer-circle{
    border-style: hidden;
    border-color: #EFEFEF;
    background-color: #1E1C44;
}

.main-content[data-background-color="black"] .add-new-config .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline {
    color: #eaeaee0d;
}

.main-content[data-background-color="black"] .mat-form-field-appearance-outline .mat-form-field-outline-thick, .main-content[data-background-color="black"] .mat-form-field-appearance-outline .mat-form-field-outline {
    opacity: 1;
    color: #eaeaee85;
}


.main-content[data-background-color="black"] .add-new-config .select-field .mat-standard-chip {
    background-color: #57C2FE;
  }

/* .completedStep .mat-tab-label {
    background-color: #768AF7 !important;
} */

.main-content[data-background-color="black"] .add-new-config .mat-radio-button.mat-radio-disabled .mat-radio-label-content {
    color: white !important;
}

.main-content[data-background-color="black"] .configList .list-button {
    opacity: 1 !important;
}

.main-content[data-background-color="black"] .add-new-config .disableScField .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #26254a;
    border-radius: 5px;
  }

  .main-content[data-background-color="black"] .add-new-config .mat-select-placeholder ,
.main-content[data-background-color="black"] .add-new-config .mat-input-element::placeholder {
    color: white !important;
}

.main-content[data-background-color="black"] .add-new-config .mat-tab-group {
    background-color: #292852 !important;
}

.main-content[data-background-color="black"] .add-new-config .radio-buttons .disabled-radio {
    box-shadow: none !important;
    border-color: #eaeaee85 !important;
}

.main-content[data-background-color="black"] .tableHeadDiv {
    background-color: #1e1c44;
}

.main-content[data-background-color="black"]  .datConditionTable {
    border: 2px solid #1e1c44;
}

.main-content[data-background-color="black"]  .datConditionTable .border-top {
    border-top: 1px solid #1e1c44 !important;
}
.main-content[data-background-color="black"] .datConditionTable .tableField {
    background-color: #1e1c44;
    color: white;
}


.main-content[data-background-color="black"] .add-new-config .table-right .select-field .mat-input-element {
    color: white !important;
}

.black .outerHeader {
    background-color: #1F1E40 !important;
    color: white;
}

.black .mat-dialog-container{
    background: #121230 !important;
}


.black .queryHeading {
    color: #FFFFFF !important;
}


.black .description {
    color: #FFFFFF !important;
}

.black .tableHeader {
    background-color: #31314B !important;
}

.black .mat-table {
    background-color: #121230;
}

.black .mat-header-cell {
    color: #FFFFFF !important;
}

.black .mat-cell {
    color: #FFFFFF;
}
.black #Icon_ionic-md-close {
    fill: #FFFFFF;
}

.black #Path_6162 {
    fill: #FFFFFF;
}

.black .bb {
    fill: #FFFFFF;
}

.black .mat-input-element {
    color: #ffffff !important;
}

.black .mat-input-element:focus {
    color: #ffffff !important;
    background-color: #121230;
}

.black .analytics-header-row {
    border: 1px solid #000;
    background: #121230;
}

.black .ad-dark-fill {
    fill: #fff !important;
}

.black
    .mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label {
    color: #ffffff !important;
}

.black .doc-img {
    background-image: url("assets/img/no-document-black.svg");
}

.black .noResults {
    color: white;
}

.tableContainer .mat-dialog-container {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px -10px 15px #0000001A;
    border-radius: 15px;
    opacity: 1;
}
.user-attribute-info-dialog .mat-dialog-container {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 5px 80px #0000001F;
    border-radius: 8px;
    padding: 0;
}
.user-attribute-confirmation-dialog .mat-dialog-container {
    padding: 34px;
}
.main-content[data-background-color="black"] .ch-response{
    background-color: #1f1e40 !important;
}
.main-content[data-background-color="black"] .emailLabel , .main-content[data-background-color="black"] 
.designationLabel, .main-content[data-background-color="black"] .messageDivLabel , 
.main-content[data-background-color="black"] .descName, .main-content[data-background-color="black"] .head-ch-modal,
.main-content[data-background-color="black"] .templateSpan
{
    color: #ffffff !important;
}

.custom-scroll-bar::-webkit-scrollbar{
    background-color: transparent;;
}

.custom-scroll-bar::-webkit-scrollbar-thumb{
  background-color: white;
}

#delete-dialog-container{
    position: absolute;
    top:25.5%;
    left:36%;
    width: 480px;
}

.main-content[data-background-color="black"] .leadership-dashboard .highcharts-grid {
    stroke-opacity: 0.3;
 }

.e-popup.e-popup-close {
    display: none;
}

.main-panel[data-background-color="black"] .cost-formatter .custom-form-field label {
  color: #FFF;
}

.main-panel[data-background-color="black"] .cost-formatter .custom-form-field circle {
  fill: #1b193f;
}

.main-panel[data-background-color="black"] #costPerCase {
  background-color: inherit;
}

.main-content[data-background-color="black"] .ad_search-uid_API {
    background-color: #43425D;
    color: #FFFFFF;
    border: 1px solid #43425D;
}
.main-content[data-background-color="black"] .invite-user-color{
    /* border: 1px solid #43425D; */
    border: none;
}
.main-content[data-background-color='black'] .invite-user-color path{
    stroke: none;
}
.main-content[data-background-color='black'] .license svg path{
    stroke: none;
} 
.main-content[data-background-color='black'] .ContentSource-body{
    background-color:  #19183C;
    color: #A0A0AC;
}
.main-content[data-background-color='black'] .selectionDashboard{
    background-color:  #19183C;
    /* color: white; */
}

.main-content[data-background-color='black'] .selectionInfo{
    background-color:  #19183C !important;
    color: white;
}
/* .main-content[data-background-color='black'] .parameters{
    color:white;
} */
.main-content[data-background-color='black'] .selectionDashboard{
    background-color:  #121230 !important;
}
/* .main-content[data-background-color='black'] .dashboardView > div{
    background-color: #19183C !important;
    border-radius: 10px;
} */

.main-content[data-background-color='black'] .dashboardView{
    background-color: #19183C !important;
    border-radius: 10px;
}
.main-content[data-background-color='black'] .dashboardViewContent{
    background-color: #121230 !important;
}
.main-content[data-background-color='black'] #Rectangle_5852-2{
    fill: #121230;
}
.main-content[data-background-color='black'] #Rectangle_2614-2{
    fill:#19183C;
    color: white;
}
.main-content[data-background-color='black'] #Rectangle_5853-2{
    fill: #2E2E41;
}
.main-content[data-background-color='black'] #Was_this_article_helpful_{
    fill: white;
}
.main-content[data-background-color='black'] #Ellipse_763-2{
    fill:#19183C;
}
.main-content[data-background-color='black'] .ContentSource-title{
    color: white;
}
.main-content[data-background-color='black'] .parameters{
    color: white !important;
}
.main-content[data-background-color='black'] .page-rating-customization {
    background-color: #121230 !important ;
}
.main-content[data-background-color='black'] .collectFeedbackMain > span{
    color: white;
}
.main-content[data-background-color='black'] .search-Customization .InputLabelName{
    color: white !important;
}
.main-content[data-background-color='black'] .search-Customization .ng-star-inserted{
    color: white !important;
}
.main-content[data-background-color='black'] .search-Customization .mat-slide-toggle-content{
    color: white;
}
.main-content[data-background-color='black'] #user-feedback-edit_submit-button{
    color: white !important;
}
.main-content[data-background-color='black'] .previewText{
    color: white;
}
.main-content[data-background-color='black'] .parametersInfo{
    color: #A0A0AC !important;
}
.main-content[data-background-color='black'] #client {
    background-color: #19183C;
}
.main-content[data-background-color='black'] .relevancy-form{
    background-color: #19183C;
}
.main-content[data-background-color='black'] .relevance-sections{
    background-color: #19183C;
}
.main-content[data-background-color='black'] .Configurations-body{
    background-color: #19183C;
}
.main-content[data-background-color='black'] .feedbackText{
    background-color: #19183C;
}
.main-content[data-background-color='black'] .collectFeedbackTable{
    background-color: #19183C;
}
.main-content[data-background-color='black'] .collectFeedbackBg{
    background-color: #19183C !important;
}
.main-content[data-background-color='black'] .end-User-feedback{
    background-color: #19183C !important;
}
.main-content[data-background-color='black'] .table-su thead tr th{
    color: white !important;
}
.main-content[data-background-color='black'] .pageRating-text{
    color: white;
}
.main-content[data-background-color='black'] .shadowDivFeedback {
    background-color: #19183C !important;
    color : white;
}
.main-content[data-background-color='black'] .end-user-feedback_add-regex-inputbox::placeholder{
    color: #403F5A;
    font-weight: 600;
}
.main-content[data-background-color='black'] .addRegexForm #addButton{
    background-color: #19183C !important;
    color: #54C6FF !important;
    border-color: #54C6FF !important;
}
.main-content[data-background-color='black'] .addRegexForm #addButton:hover{
    background-color: #54C6FF !important;
    color: white !important;
}
.main-content[data-background-color='black'] .addRegexForm #addButton:disabled{
    background-color: #19183C !important;
    color: #54C6FF !important;
}
.main-content[data-background-color='black'] .example-margin{
    color: white !important;
}
.main-content[data-background-color='black'] #Rectangle_4348{
    fill : #292852;
}
.main-content[data-background-color='black'] .sectionHead .buttonPrimary:hover{
    border-color: #54C6FF !important;
}
.main-content[data-background-color='black'] .content-annotation-btn{
    background-color: #1A193F;
}
.main-content[data-background-color='black'] .content-annotation-btn:hover{
    background-color: #56C3FE;
    color: white;
}
.main-content[data-background-color='black'] .consumption-wrapper{
    background-color:#1E1C44 ;
}
.main-content[data-background-color='black'] .consumption-wrapper > div{
    background-color:#1E1C44 !important;
}
.main-content[data-background-color='black'] .footer{
    background-color:#1E1C44 ;  
}
.main-content[data-background-color='black'] #searchHitCount{
    background-color: #1E1C44;
}
.main-content[data-background-color='black'] #searchHitCount>div{
    background-color: #121230 !important;
}
.main-content[data-background-color='black'] .app-line-conversion-chart{
    background-color: #1E1C44;
}
.main-content[data-background-color='black'] .app-line-conversion-chart li{
    background-color: #1E1C44;
    color: white;
}
.main-content[data-background-color='black'] .app-line-conversion-chart li ul{
    background-color: #121230;
}
.main-content[data-background-color='black'] .title-bar{
    background-color: #1A193F;
}
.main-content[data-background-color='black'] .llm-name span{
    color: white !important;
}
/* .main-content[data-background-color='black'] #azure-coming-soon-id{
    background-color: #121230 !important;
} */
.main-content[data-background-color='black'] .connection-frame{
    background-color: #121230;
}
.main-content[data-background-color='black'] .connection-frame span{
    color: white !important;
}
.main-content[data-background-color='black'] input{
    background-color: #121230;
    color: white !important;
}
.main-content[data-background-color='black'] .dialog-body{
    background-color: #1E1C44 !important;
}
.main-content[data-background-color='black'] .dialog-body h2{
   color: white !important;
}
.main-content[data-background-color='black'] #azure-coming-soon-id{
    background: #121230 !important;
}
.main-content[data-background-color='black'] #bard-coming-soon-id{
    background: #121230 !important;
}
.main-content[data-background-color='black'] .provider-logo figure{
    background-color: #1A193F !important;
}
.main-content[data-background-color='black'] .aws-claude svg > g:nth-child(1) path{
    fill: white;
}
.main-content[data-background-color='black'] .coming-soon{
    background-color: #1A193F;
    color: white;
}
.main-content[data-background-color='black'] .help-block {
    background-color: #1E1C44 !important;
    border-radius: 5px;
    font-weight: 500;
}
.main-content[data-background-color='black'] .download-content-new{
    background-color: #121230 !important;
}
.main-content[data-background-color='black'] .download-content-new:nth-child(2){
   fill: white !important;
}
.main-content[data-background-color='black'] .input-session-timeout{
    padding: 5px;
}
.main-content[data-background-color='black'] .input-session-timeout + span label span{
    margin-right: 10px;
}
.main-content[data-background-color='black'] .input-session-timeout + span label {
    position: absolute;
    top: 40px;
}
.main-content[data-background-color='black'] #analyticSetting .addOnsTable input+span label span{
    margin-left : 5px;
    color: white;
}
.main-content[data-background-color='black'] #analyticSetting .addOnsTable input{
    padding: 5px;
}
.main-content[data-background-color='black'] .download-content-new:nth-child(2){
    fill: white;
    stroke: none;
}
.main-content[data-background-color='black'] #Component_14_1 path{
    stroke:none;
}
.main-content[data-background-color='black'] .FilterPreferences{
    background-color: #121230 !important;
}
.main-content[data-background-color='black'] .boxes{
    color: white;
}
.main-content[data-background-color='black'] .marginBottom span{
    color: white;
}
.main-content[data-background-color='black'] .matExpansionPanelHeader1{
    background-color: #121230 !important;
}
.main-content[data-background-color='black'] .content_panel-block.types-list.panels span{
    background-color: #121230 !important;
}

/* .main-content[data-background-color='black'] .content_panel-block span{
    background-color: #121230 !important;
} */
.main-content[data-background-color='black'] .matSlider div:nth-child(2){
    background-color: white;
}
.main-content[data-background-color='black'] .propertyDel {
    color: #121230;
}
.main-content[data-background-color='black'] .showText{
    color: #6495BD ;
}
.main-content[data-background-color='black'] .boostDisplay{
    color: white;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker
{
     background-color:#121230 !important;
     border: 1px solid white;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker .left{
    background-color: #2C2B4C ;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker .right{
    background-color: #2C2B4C ;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker .calendar-table{
    color: white;
    background-color: #2C2B4C;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker .in-range{
    color: white;
    background-color: #282932;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker .off{
    background-color: #2C2B4C;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker .start-date, .end-date{
    background-color: #357DBD !important;
}
body:has(.main-content[data-background-color="black"]) .daterangepicker .input-mini{
    background-color:  #2C2B4C;
    color:white;
}
body:has(.main-content[data-background-color="black"]) .analytics-dropdown{
    background-color: #2C2B4C !important;
}
body:has(.main-content[data-background-color="black"]) .analytics-dropdown .mat-option:hover{
    background-color: #121230 !important;
}
body:has(.main-content[data-background-color="black"]) .analytics-dropdown span{
    color: white;
}
body:has(.main-content[data-background-color="black"]) .analytics-dropdown .mat-option{
    background-color: #2C2B4C !important;
}
body:has(.main-content[data-background-color="black"]) .mat-select-panel{
    background-color: #2C2B4C !important;
}

.main-content[data-background-color='black'] input[name="apiKey"] {
    background-color:#121230 !important;
}
.main-content[data-background-color='black'] #searches .mat-tab-disabled {
    color: gray !important;
}
.main-content[data-background-color='black'] .llm-card { border-color:#1B193F !important}
.main-content[data-background-color='black'] .connection-frame{ border-color:#1B193F !important}
.main-content[data-background-color='black'] .connection-frame input{ border-color:#1B193F !important}

.main-content[data-background-color='black'] .connection-frame .key-input {
    border: 1px solid red; /* Change this to the desired color */
}
.main-content[data-background-color='black'] .title-bar p{
    color: white !important;
}
.main-content[data-background-color='black'] .analyticSetting input{
    background-color: #121230 !important;
}
.main-content[data-background-color='black'] .table-style{
    border-color: #494868 !important;
}
.main-content[data-background-color='black'] .analytics-header-row{
    border-color: #494868;
}
.main-content[data-background-color='black'] .pageRatingToggle{
    border-color: #494868 !important;
}
.main-content[data-background-color='black'] .searchForm{
    border-color: #494868 !important;
} 
.main-content[data-background-color='black'] .pageRating-preview-search{
    border-color: #494868 !important;
}
.main-content[data-background-color='black'] .twoTd .mat-form-field{
    border-color: #494868 !important;
}
.main-content[data-background-color='black'] .heading-source{
    color: #FFFFFF;
}
.main-content[data-background-color='black'] .definition{
    color: #FFFFFF;
}
.main-content[data-background-color='black'] .analytics-section-heading{
    color: #FFFFFF;
}
.main-content[data-background-color='black']  .su__insights_main_heading{
    color: #FFFFFF;
}
.main-content[data-background-color='black'] .su__icons_background {
    background: #121230 !important;
  }
.main-content[data-background-color='black'] .su__llm_insights_card ul li{
    background-color: #121230 !important;
  }
.main-content[data-background-color='black'] .su__total_request_count{
    color: #FFFFFF;
}
.main-content[data-background-color='black'] .su__stats-position{
    color: #FFFFFF;
}
.main-content[data-background-color='black'] .su__insights_data{
    color: #FFFFFF;
}
.main-content[data-background-color='black'] .privacy-policy-text{
    color:#FFFFFF
}
.main-content[data-background-color='black'] .consumption-head{
    background-color: #121230 !important;
    color: #ffffff !important;
}
.main-content[data-background-color='black'] .integration-type{
    color: #ffffff !important;
}
.main-content[data-background-color='black'] .quota-progress label{
    color: #ffffff !important;
}

.main-content[data-background-color='black'] .analytics-section-heading{
   border: solid 1px #494868;
}
.main-content[data-background-color='black'] .su__llm_insights_card{
    border: solid 1px #494868;
}
.main-content[data-background-color='black'] .consumption-grid{
    border: solid 1px #494868;
}
.main-content[data-background-color='black'] .consumption-grid .integration-list li{
    border-bottom: solid 1px #494868 !important;
}
.main-content[data-background-color='black'] .no-docs span{
    color: #FFFFFF;
}

.su__pt-50imp{
    padding-top: 50px !important;
}
.main-content[data-background-color='black'] .su__bounceLoader{
    background-color: #19183C;
}
.main-content[data-background-color="black"] .addRegexForm .mat-form-field-disabled{
    background-color: #23233a !important;
}
.main-content[data-background-color="black"] .searchForm a{
    background-color: #19183C;
}
.main-content[data-background-color="black"] .selectionDashboard svg > *:first-child path{
    fill: #19183C ;
}
.main-content[data-background-color="black"] .selectionDashboard svg g:last-of-type path{
    fill: #121230;
}
.main-content[data-background-color="black"] .selectionDashboard svg > path{
    fill: white ;
}
.main-content[data-background-color="black"] .dashboardView svg > *:nth-child(2){
    fill:#30304A !important;
}
.main-content[data-background-color="black"] .dashboardView app-svg svg g:last-of-type path{
    fill:#30304A ;
}
.main-content[data-background-color="black"] .dashboardView app-svg[name="search-feedback"] svg > :nth-last-child(4) {
    fill: #55C5FF; 
}

.main-content[data-background-color="black"]  .support-url-text{
    color: white;
}