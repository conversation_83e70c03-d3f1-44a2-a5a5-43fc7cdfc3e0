import { environment } from 'environments/environment';

/** 
 * use /backend for local backend connection.
 * use 'https://<feature7|6|5>.searchunify.com' for remote connection with any SU instance.
*/
// const baseHref = '/backend';
const baseHref = 'https://feature7.searchunify.com';

export class Helper {
  static allowSearchClientChildRoutes  = false;
  static allowAnalyticsConfigChildRoutes = false;
  static currentYear : number = new Date().getFullYear();
  static LOGIN_SVGS: {
    svgUrl: string,
    svgBgUrl: string,
    safeUrl: string,
  } = {
  svgUrl: '',
  svgBgUrl: '',
  safeUrl: '',
}
}

export const Variables = {
  desktopAmintion: true,
  animationSpeed: 50, //In ms
  animationType: 'left', //left, right, top, bottom
  baseHref: baseHref,
  _csrf: '',
  s3ClientId: '',
  baseHrefBot: '/chatbot/api/image',
  baseHrefBotCsv: '/chatbot/api/upload_bulk_intents',
  baseHrefBotUttrance: '/chatbot/api/upload_bulk_uttrances',
  authServerUrl: '/auth',
  analyticsURL: `${baseHref}/analytics`,
  dateRangeLimitDays: 183,
  apiLogsdateRangeLimitDays: 30,
  vectorSettingsDefault: {"minScoreThreshold": 0.4, "hybridSearchEnabled": false},
  gptRulesLimit: {minWords: 1, maxWords: 6},
  adminLoginImages : {
		s3:{
      loginBgImage : "https://d1ebk03cn42wjj.cloudfront.net/SU-publish-background.svg",
      loginReleaseImage : "https://d1ebk03cn42wjj.cloudfront.net/SU-publish-main.svg",
      previewLoginBgImage : "https://d1ebk03cn42wjj.cloudfront.net/SU-preview-background.svg",
      previewLoginReleaseImage : "https://d1ebk03cn42wjj.cloudfront.net/SU-preview-main.svg"
		}
	}
}

if (environment.production) {
  Variables.baseHref = '/colubridae19';
}

export const crawlerConfigVariables = {
  TotalCountSupportedCs: [
    { name: 'lithium', cs_type_id: 2 },
    { name: 'jira', cs_type_id: 6 },
    { name: 'salesforce', cs_type_id: 3 },
    { name:'stackoverflow', cs_type_id: 18 },
    { name:'servicenow', cs_type_id: 26 },
    { name:'confluence', cs_type_id: 4 },
    { name:'aha', cs_type_id: 42 },
    { name: 'contentful', cs_type_id: 49 },
    { name: 'zendesk', cs_type_id: 7 },
    { name : 'khorosAurora', cs_type_id: 71 }
  ]
}

export const CSID_WITH_FOLDER = [61, 24, 13, 12];

export const ADVANCED_QUERY = [
  {
    id:0,
    setName:"master",
    querySnippetName:"multi_match",
    fields:['copy_fields','exclude_copy_fields','include_title_fields']
  },
  {
    id:1,
    setName:"default",
    querySnippetName:"multi_match",
    type: "phrase",
    boost: 12,
    fields:['copy_fields']
  },
  {
    id: 2,
    setName: "crossfield",
    querySnippetName: "multi_match",
    type: "cross_fields",
    operator: 'AND',
    boost: 5,
    fields:["exclude_copy_fields", "copy_fields","include_title_fields"]
  },
  {
    id: 3,
    setName: "Combine AND with Boosted Title",
    querySnippetName: "multi_match",
    type: "most_fields",
    operator: 'AND',
    boost: 8,
    fields:["exclude_copy_fields", "copy_fields","include_title_fields"],
    boostedTitle: true,
  }
];

export const DEFAULT_ATTACHMENTS = '3';

export const DEFAULT_RELEVANCY_DATA = '';

export const DEFAULT_HIGHLIGHT_CONFIGURATIONS = {
  highlightType: 'unified',
  highlightOrder: 'score',
  highlightFragmentSize: 100,
  highlightNumberOfFragments: 5
};

export const DEFAULT_RAG_CONFIGURATIONS = {
  contextCreationType: 'highlight',
  resultsToConsider: 5,
  llmContextWordLimit: 1000,
  wordLimitPerDocument: 1000,
  wordLimitPerField: 100,
  llmWordCorrectionFactor: 0.9
};
const advanceQuery = JSON.stringify(ADVANCED_QUERY);
export const DEFAULT_RELEVANCY = `{"searchOperator": "OR","advanceQuery": ${advanceQuery}, "relevancyScores" : { \"enabled\": 1, \"dataRetention\": \"\", \"searchType\": 1}}`;

export const TEMP_MAPPING_EXCLUSIONS = [
  9, //website
  25 //sabaCloud
];

export const TEMP_MAPPING_CS = [
  41, //higherLogic,
  24, //dropbox,
  31, //zoomin,
  48, //vidyard,
  47 //jsWeb
];

export const CONTENT_SOURCE_TYPE = {
  jive               : 1,
  lithium            : 2,
  salesforce         : 3,
  confluence         : 4,
  sharepoint         : 5,
  jira               : 6,
  zendesk            : 7,
  slack              : 8,
  website            : 9,
  madcap             : 10,
  mindtouch          : 11,
  drive              : 12,
  box                : 13,
  helpScout          : 14,
  github             : 15,
  sap                : 16,
  youtube            : 17,
  stackOverflow      : 18,
  amazonS3           : 19,
  litmos             : 20,
  solr               : 21,
  customContentSource: 22,
  moodle             : 23,
  dropbox            : 24,
  sabaCloud          : 25,
  servicenow         : 26,
  jiraOnprem         : 27,
  marketo            : 28,
  dynamics           : 29,
  receptive          : 30,
  zoomin             : 31,
  docebo             : 32,
  vimeo              : 33,
  azureDevops        : 34,
  seismic            : 37,
  cornerstone        : 38,
  skillJar           : 39,
  higherLogic        : 41,
  aha                : 42,
  discourse          : 43,
  thoughtIndustries  : 44,
  rssFeed            : 45,
  microsoftTeams     : 46,
  jsWeb              : 47,
  vidyard            : 48,
  contentful         : 49,
  wistia             : 50,
  getguru            : 52,
  knowledgeOwl       : 53,
  document360	       : 51,
  insided            : 55,
  learnupon          : 58,
  vanilla            : 66,
  monday             : 60,
  aem                : 61,
  brightspace        : 67,
  khorosAurora       : 71,
  uservoice          : 56,
  file               : 69,
  heretto            : 59,
  bugzilla           : 68,
  freshdesk          : 72,
  freshservice       : 73,
  airtable           : 75,
  zulip              : 86
};

export const USER_ROLES = {
  ADMIN: 1,
  MODERATOR: 2,
  API_USER: 3,
  SUPER_ADMIN: 4,
};


export const TUNING_LABELS = {
  UNSAVED_VS_LIVE: 'Tuned vs Live Search',
  UNSAVED_VS_BASIC: 'Tuned vs Vanilla Search',
  LIVE_VS_BASIC: 'Live vs Vanilla Search',
  LIVE_SEARCH: 'Live Search',
  BASIC_SEARCH: 'Vanilla Search',
  UNSAVED_SEARCH: 'Tuned Search',
  UNSAVED_SEARCH_TOOLTIP: 'This search contains tuning changes',
  LIVE_SEARCH_TOOLTIP: 'Current live search results',
  TEMP_LIVE_SCORE_MSG: 'Rank in Live Search',
  TEMP_BASIC_SCORE_MSG: 'Rank in Vanilla Search',
  TEMP_BASIC_SEARCH_TOOLTIP: 'Search Result without any boosting'
}

export const OBJECT_CRAWL = [
  CONTENT_SOURCE_TYPE.salesforce,
  CONTENT_SOURCE_TYPE.contentful,
  CONTENT_SOURCE_TYPE.zendesk
];
