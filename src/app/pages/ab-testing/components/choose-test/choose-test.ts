import { Component, OnInit } from "@angular/core";
import { Dom<PERSON>anitizer, SafeHtml } from "@angular/platform-browser";
import { Router, ActivatedRoute } from '@angular/router';
import { abTestingService } from "../../../../services/abTesting.service";
export interface CustomValues {
    name: string;
}

@Component({
    selector: "choose-test",
    templateUrl: "choose-test.html",
    styleUrls: ["choose-test.scss"],
    providers: [abTestingService],
})
export class ChooseTestComponent implements OnInit {
    private indexServiceConfiguration : any
    private abTestloader: boolean = true;
    private featureCards: any = []
    constructor(private sanitizer: DomSanitizer, private router: Router,private activatedRoute: ActivatedRoute, private abTestingService: abTestingService) {}

    ngOnInit() {
        this.fetchIndexingConfigurations();
    }

    async fetchIndexingConfigurations(){
       await this.abTestingService.getIndexServiceConfiguration().then((result) => {
            this.indexServiceConfiguration = result.data
            this.setData(this.indexServiceConfiguration);
            this.abTestloader = false;
        })
      }

    setData(indexServiceConfiguration) {
        this.featureCards = [
            {
                isCardDisabled: !(indexServiceConfiguration.indexingConfig.vectorConfiguration.vectorIndexingEnabled && !indexServiceConfiguration.indexingConfig.vectorConfiguration.vectorIndexingRunningStatus && !indexServiceConfiguration.indexingConfig.vectorConfiguration.multilingualRunningStatus),
                title: "Search",
                description: "Experiment with UI variations to discover which design engages and converts users most effectively.",
                icon: "searchIcon",
                infoTooltip: {
                    infoTooltipDisplay: !(indexServiceConfiguration.indexingConfig.vectorConfiguration.vectorIndexingEnabled && !indexServiceConfiguration.indexingConfig.vectorConfiguration.vectorIndexingRunningStatus && !indexServiceConfiguration.indexingConfig.vectorConfiguration.multilingualRunningStatus),
                    message: "Neural/Hybrid search is not provisioned for your account. Please contact the support team for assistance.",
                },
                button: {
                    label: "Configure",
                    disabled: !(indexServiceConfiguration.indexingConfig.vectorConfiguration.vectorIndexingEnabled && !indexServiceConfiguration.indexingConfig.vectorConfiguration.vectorIndexingRunningStatus && !indexServiceConfiguration.indexingConfig.vectorConfiguration.multilingualRunningStatus),
                },
                badge: {
                    badgeDisplay: false,
                    label: "Coming Soon",
                    color: "orange",
                },
            },
            {
                isCardDisabled: true,
                title: "LLMs",
                description: "Pit leading AI models against each other to discover which provides the most trusted, accurate answers.",
                icon: "LLMs",
                infoTooltip: {
                    infoTooltipDisplay: false,
                    message: "Neural/Hybrid search is not provisioned for your account. Please contact the support team for assistance.",
                },
                button: {
                    label: "Configure",
                    disabled: true,
                },
                badge: {
                    badgeDisplay: true,
                    label: "Coming Soon",
                    color: "orange",
                },
            },
            {
                isCardDisabled: true,
                title: "Prompts",
                description: "A/B test different prompts to discover the precise instruction that unlocks the highest quality AI responses.",
                icon: "prompts",
                infoTooltip: {
                    infoTooltipDisplay: false,
                    message: "Neural/Hybrid search is not provisioned for your account. Please contact the support team for assistance.",
                },
                button: {
                    label: "Configure",
                    disabled: true,
                },
                badge: {
                    badgeDisplay: true,
                    label: "Coming Soon",
                    color: "orange",
                },
            },
            {
                isCardDisabled: true,
                title: "Embedding Model",
                description: "Discover which model best understands user intent to deliver truly semantic and relevant results.",
                icon: "embeddingModel",
                infoTooltip: {
                    infoTooltipDisplay: false,
                    message: "Neural/Hybrid search is not provisioned for your account. Please contact the support team for assistance.",
                },
                button: {
                    label: "Configure",
                    disabled: true,
                },
                badge: {
                    badgeDisplay: true,
                    label: "Coming Soon",
                    color: "orange",
                },
            },
            {
                isCardDisabled: true,
                title: "Search Configurations",
                description: "Test foundational algorithm settings to prove which configuration delivers the best search experience.",
                icon: "searchConfigurations",
                infoTooltip: {
                    infoTooltipDisplay: false,
                    message: "Neural/Hybrid search is not provisioned for your account. Please contact the support team for assistance.",
                },
                button: {
                    label: "Configure",
                    disabled: true,
                },
                badge: {
                    badgeDisplay: true,
                    label: "Coming Soon",
                    color: "orange",
                },
            },
            {
                isCardDisabled: true,
                title: "Search Tuning",
                description: "A/B test different ranking strategies to prove which approach best elevates your most valuable content.",
                icon: "searchTuning",
                infoTooltip: {
                    infoTooltipDisplay: false,
                    message: "Neural/Hybrid search is not provisioned for your account. Please contact the support team for assistance.",
                },
                button: {
                    label: "Configure",
                    disabled: true,
                },
                badge: {
                    badgeDisplay: true,
                    label: "Coming Soon",
                    color: "orange",
                },
            },
        ];
    }

    navigateToConfigurations(){
        this.router.navigate(['../test-configurations'], { relativeTo: this.activatedRoute });
    }
    navigateToTestReport(){
        this.router.navigate(['../test-report'], { relativeTo: this.activatedRoute });
    }

}
