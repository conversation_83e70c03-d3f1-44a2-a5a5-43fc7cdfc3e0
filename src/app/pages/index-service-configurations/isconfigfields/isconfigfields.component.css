.ind-object{
    display: flex;
    /* justify-content: space-between; */
    /* width: max-content; */
    margin-bottom: 3px;
}
.key-name{
    background-color: white;
    padding: 1.5px 10px;
    border-radius: 10px;
    border: 1px solid #5fb3fb;
    padding-bottom: 5px;
    height: auto;
    min-width: fit-content;
}
.equal-sign{
    margin: 0px 10px;
    border: none;
    background-color: transparent;
    font-size: 15px;
    font-weight: 500;
}
.key-wrapper{
    min-width: 24%;
}
.config-selector{
    /* justify-content: flex-start;
    display: flex; */
    min-width: 100%;
}
.line-view, .skip-cs-line{
    height: 2px;
    background-color: #c8e0fb;
    width: -webkit-fill-available;
}
.in-key-wrapper{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.switch {
    position: relative;
    display: inline-block;
    width: 38px;
    height: 25px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #11010175;
    -webkit-transition: .4s;
    transition: .4s;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 23px;
    width: 23px;
    left: 2px;
    bottom: 1px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
  }

  input:checked + .slider {
    background-color: #2196F3;
  }

  input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(12px);
    -ms-transform: translateX(12px);
    transform: translateX(12px);
  }

  /* Rounded sliders */
  .slider.round {
    border-radius: 17px;
  }

  .slider.round:before {
    border-radius: 50%;
  }

/* component.styles.css (or your component's CSS file) */
.slider.round.disabled-slide-toggle  {
  opacity: 0.5; /* Reduced opacity */
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

.skip-input-box-width{
  width: 400px !important;
}

.skip-cs-line { 
  width: 100px;
}

.save-button {
  border: 1px solid #5fb3fb;
  background-color: white;
  padding: 5px 25px;
  border-radius: 5px;
  font-size: 15px;
  color: #5fb3fb;
  font-family: inherit;
  font-weight: 500;
}
.save-button:hover {
  background-color: #5fb3fb;
  color: white;
}
.add-button {
  border: 1px solid #dc3545;
  background-color: white;
  padding: 5px 25px;
  border-radius: 5px;
  font-size: 15px;
  color: #dc3545;
  font-family: inherit;
  font-weight: 500;
}
.add-button:hover {
  background-color: #dc3545;
  color: white;
}
.save-button:disabled,
.save-button:disabled:hover,
.add-button:disabled,
.add-button:disabled:hover {
  border: 1px solid #ccc !important;
  background-color: #f8f9fa !important;
  padding: 5px 25px !important;
  border-radius: 5px !important;
  color: #adb5bd !important;
  cursor: not-allowed !important;
  opacity: 0.7 !important;
}

.pattern-filter-container {
  display: flex;
  gap: 20px;
  margin-left: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.in-key-wrapper {
  width: 250px;
}

.object-name.key-name {
  font-weight: 500;
  font-size: 13.5px;
}

.input-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-left: 10%;
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.pattern-input {
  width: 75%;
}

.button-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
}