import { Component, OnInit, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
  
@Component({
  selector: 'app-isconfigfields',
  templateUrl: './isconfigfields.component.html',
  styleUrls: ['./isconfigfields.component.css']
})
export class IsconfigfieldsComponent implements OnInit {

    @Input() configObject: any;
    @Input() configKey: any;
    @Input() reserveObject: any;
    @Input() parentKey: any;
    @Output() onPropertyChange = new EventEmitter<any>();
    @Input() keysEditableForVectorConfig: any = {};
    private keys: any = [];
    public vectorIndexingDisabled: boolean;
    private multilingualDisabled: boolean;
    private filteredKeys: any = {};
    tempPatternFilterArray: string[] = [];
    isSaveDisabled: boolean = true;

    constructor() { }

    ngOnInit(): void {
        this.vectorIndexingDisabled = false;
        this.multilingualDisabled = false;
        this.keys = Object.keys(this.configObject);
        if (this.configKey === 'OpenSearch Configuration' && this.configObject.settings.patternFilterArray === undefined) {
            this.configObject.settings.patternFilterArray = ["(\\p{L}+)"];
            this.reserveObject.settings.patternFilterArray = ["(\\p{L}+)"];
        }
        if (this.configObject['patternFilterArray']) {
            this.tempPatternFilterArray = [...this.configObject['patternFilterArray']]; 
        }
        this.keys = this.keys.filter(key => key !== 'patternFilterArray');
        if (this.configObject && this.configKey == 'Vector Configuration') {
            this.vectorIndexingDisabled = this.configObject.vectorIndexingRunningStatus;
            if ((!this.configObject.vectorIndexingEnabled && !this.configObject.hybridSearchEnabled) || (this.configObject.multilingualRunningStatus)) {
                this.multilingualDisabled = true;
            } else {
                this.multilingualDisabled = false;
            }
            Object.keys(this.keysEditableForVectorConfig).forEach(key => {
                this.filteredKeys[key] = this.configObject[key];
            });
            
            this.keys = Object.keys(this.filteredKeys);
            this.keys.splice(this.keys.indexOf('vectorizeDataFrequency'), 1);
        }
        if (this.configObject && this.configObject.tool_type) {
            const excludedKeys = ['tool_type', 'click_method'];
            this.keys = Object.keys(this.configObject)
                .filter((key) => !excludedKeys.includes(key));
        }
    }

    isObject(obj) {
        if(Array.isArray(obj) || (obj === Object(obj) && Object.keys(obj).length == 0)) return false;
        return obj === Object(obj);
    }

    parseValue(key){
        if(JSON.stringify(this.reserveObject[key]) === "{}") return 'Object {"key": value}';
        else if(Array.isArray(this.reserveObject[key])) return "Array [0,1,2,3]";
        else if(typeof this.reserveObject[key] == "boolean") return "Boolean true/false";
        else if(!isNaN(Number(this.reserveObject[key]))) return "Number 10"
        return "String";
      }

    onUpdate($event,key){
        let obj = {};
        if(this.parentKey) key = this.parentKey +'.'+ key;
        if(this.keysEditableForVectorConfig && Object.keys(this.keysEditableForVectorConfig).some(k => k == key)){
            if ( typeof $event === 'string') {
                this.filteredKeys[key] = $event;
            } else {
                this.filteredKeys[key] = $event.checked || ($event.target && $event.target.checked);
                if(key !== 'enableMultilingual') {
                    if ($event.target && $event.target.checked) {
                        this.multilingualDisabled = key !== 'vectorIndexingEnabled';
                        if (key === 'vectorIndexingEnabled') {
                            this.filteredKeys.vectorIndexingEnabled = true;
                            this.filteredKeys.hybridSearchEnabled = false;
                        } else {
                            this.filteredKeys.hybridSearchEnabled = true;
                            this.filteredKeys.vectorIndexingEnabled = false;
                        }
                        this.filteredKeys.enableMultilingual = false
                    } else if(key === 'enableVectorizationFromStart'){
                        this.filteredKeys.enableVectorizationFromStart = $event.checked;
                    } else {
                        this.multilingualDisabled = true
                        this.filteredKeys.enableMultilingual = false
                    }
                }
            }

            obj = { ...this.filteredKeys }
            Object.assign(this.configObject, obj);
        } else obj[key] = $event && $event.target ? $event.target.checked : $event;
        this.onPropertyChange.emit(obj);
    }

    checkIfVectorConfigKey(key) {
        return this.keysEditableForVectorConfig && this.keysEditableForVectorConfig.hasOwnProperty(key); // vectorizeDataFrequency is dependent on hybridSearchEnabled
    }

    showToggle(value){
        return typeof value === 'boolean';
    }

    onPatternFilterArrayChange(): void {
        this.isSaveDisabled = JSON.stringify(this.tempPatternFilterArray) === JSON.stringify(this.configObject['patternFilterArray']);
    }

    addPatternFilterField(): void {
        if (this.configObject['patternFilterArray'].length < 5) {
            this.configObject['patternFilterArray'].push(''); // Adds a new empty field
        }
        this.isSaveDisabled = false;
    }

    savePatternFilter(): void {
        this.configObject['patternFilterArray'] = this.tempPatternFilterArray.filter(item => item.trim() !== '');
        const obj = {
            settings : {
                patternFilterArray: this.configObject['patternFilterArray'] 
            }
        }
        this.onPropertyChange.emit(obj);
        this.isSaveDisabled = true;
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.configObject) {
          // Reset tempPatternFilterArray whenever configObject changes
          this.tempPatternFilterArray = [...(this.configObject['patternFilterArray'] || [])];
        }
      }
}
