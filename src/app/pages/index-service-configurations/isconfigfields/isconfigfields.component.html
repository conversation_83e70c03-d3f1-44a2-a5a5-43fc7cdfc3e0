<div class="iSConfigScreen" style="margin-bottom: 15px;">
    <div *ngFor="let key of keys" class="ind-object">
        <div class="key-wrapper">
            <div class="in-key-wrapper" style="width: 100%;">
                <strong class="ng-tns-c9-1 object-name key-name"
                        style="font-weight: 500;font-size: 13.5px;">
                    {{(keysEditableForVectorConfig && keysEditableForVectorConfig[key]) || key}}
                </strong>
                <div class="line-view"></div>
            </div>
        </div>
        <div *ngIf="!isObject(configObject[key])">
            <div *ngIf="key === 'vectorIndexingEnabled'">
                <label class="switch" [matTooltip]="vectorIndexingDisabled ? 'A process is running' : ''">
                    <input type="checkbox"
                    [(ngModel)]="configObject[key]" 
                    (change) = "onUpdate($event,key)" 
                    [checked]="configObject[key]" 
                    [ngClass]="{ 'disabled-slide-toggle': vectorIndexingDisabled}"
                    [disabled]="vectorIndexingDisabled">
                    <span [ngClass]="{ 'disabled-slide-toggle': vectorIndexingDisabled}" class="slider round"></span>
                </label>
            </div>
            <div *ngIf="key === 'enableVectorizationFromStart'" class="p-1">
                <mat-checkbox 
                [matTooltip]="enableVectorizationFromStartDisabled ? 'A process is running' : ''"
                [(ngModel)]="configObject[key]" 
                (change) = "onUpdate($event,key)" 
                [ngClass]="{ 'disabled-slide-toggle': enableVectorizationFromStartDisabled}"
                [disabled]="enableVectorizationFromStartDisabled">
                </mat-checkbox>
            </div>
            <div *ngIf="key === 'hybridSearchEnabled'">
                <label class="switch" [matTooltip]="vectorIndexingDisabled ? 'A process is running' : ''">
                    <input type="checkbox"
                    [(ngModel)]="configObject[key]" 
                    (change) = "onUpdate($event,key)" 
                    [checked]="configObject[key]"
                    [ngClass]="{ 'disabled-slide-toggle': vectorIndexingDisabled}"
                    [disabled]="vectorIndexingDisabled">
                    <span [ngClass]="{ 'disabled-slide-toggle': vectorIndexingDisabled}" class="slider round"></span>
                </label>
                <div *ngIf="configObject['hybridSearchEnabled']" style="display: flex;margin-left: 10px;">
                    <div class="key-wrapper">
                        <div class="in-key-wrapper" style="width: 250px;">
                            <strong class="ng-tns-c9-1 object-name key-name"
                                    style="font-weight: 500;font-size: 13.5px;">
                                {{keysEditableForVectorConfig && keysEditableForVectorConfig['vectorizeDataFrequency']}}
                            </strong>
                            <div class="line-view"></div>
                        </div>
                    </div>
                    <div style="display: flex;flex-direction: column;">
                        <input  type="text" [(ngModel)]="configObject['vectorizeDataFrequency']"
                                (ngModelChange)="onUpdate($event,'vectorizeDataFrequency')"
                                placeholder="{{parseValue('vectorizeDataFrequency')}}"
                                class="cs-form font_5 su-dblack su-p-0" [disabled]="vectorIndexingDisabled" />
                        <span>Enter valid cron expression</span>
                    </div>
                </div>
            </div>
            <div *ngIf="key === 'enableMultilingual'">
                <label class="switch" [matTooltip]="vectorIndexingDisabled ? 'A process is running' : ''">
                    <input type="checkbox"
                    [(ngModel)]="configObject[key]"
                    (change) = "onUpdate($event,key)"
                    [ngClass]="{ 'disabled-slide-toggle': vectorIndexingDisabled}"
                    [checked]="configObject[key]" 
                    [disabled]="vectorIndexingDisabled || !configObject.vectorIndexingEnabled">
                    <span [ngClass]="{ 'disabled-slide-toggle': vectorIndexingDisabled || !configObject.vectorIndexingEnabled }" class="slider round"></span>
                </label>
            </div>
            <div *ngIf="key === 'skipContentSourcesFromVectorization'" class="d-flex align-items-center">
                <div class="line-view skip-cs-line"></div>
                <input type="text" [(ngModel)]="configObject[key]"
                (ngModelChange)="onUpdate($event,'skipContentSourcesFromVectorization')"
                placeholder="Enter comma separated values of Content Source Ids"
                class="cs-form font_5 su-dblack su-p-0 skip-input-box-width">
            </div>
            <div *ngIf="!checkIfVectorConfigKey(key)">
                <span *ngIf="showToggle(configObject[key]); else elseBlock">
                    <label class="switch">
                        <input type="checkbox"
                            [(ngModel)]="configObject[key]"
                            (change) = "onUpdate($event,key)"
                            [checked]="configObject[key]">
                        <span class="slider round"></span>
                    </label>
                </span>
                <ng-template #elseBlock>
                    <input  type="text" [(ngModel)]="configObject[key]"
                        (ngModelChange)="onUpdate($event,key)"
                        placeholder="{{parseValue(key)}}"
                        *ngIf="!configObject.tool_type"
                        class="cs-form font_5 su-dblack su-p-0" />
                    <button *ngIf="configObject.tool_type"
                        (click)="configObject.click_method()"
                        class="save-button">
                        {{ configObject[key] }}
                    </button>
                </ng-template>
            </div>
            <div *ngIf="key === 'patternFilter' && configObject['patternFilter']" class="pattern-filter-container">
                <div class="key-wrapper">
                    <div class="in-key-wrapper">
                        <strong class="ng-tns-c9-1 object-name key-name">patternFilterArray</strong>
                        <div class="line-view"></div>
                    </div>
                </div>
                <div class="input-container">
                    <div *ngFor="let value of configObject['patternFilterArray']; let i = index" class="input-row">
                        <input type="text"
                               [(ngModel)]="tempPatternFilterArray[i]"
                               (input)="onPatternFilterArrayChange()"
                               placeholder="Enter pattern"
                               class="cs-form font_5 su-dblack su-p-0 pattern-input"/>
                    </div>
                    <div class="button-container">
                        <button type="button" 
                                (click)="savePatternFilter()" 
                                [disabled]="isSaveDisabled" 
                                class="save-button">
                            Save
                        </button>
                        <button type="button" 
                                (click)="addPatternFilterField()" 
                                [disabled]="configObject['patternFilterArray'].length >= 5" 
                                class="add-button">
                            Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <app-isconfigfields [configObject]="configObject[key]" [reserveObject]="reserveObject[key]"
        (onPropertyChange)="onPropertyChange.emit($event)"
        *ngIf="isObject(configObject[key])" [parentKey]="parentKey ? (parentKey+'.'+key) : key"
        class="config-selector"></app-isconfigfields>
    </div>
</div>
