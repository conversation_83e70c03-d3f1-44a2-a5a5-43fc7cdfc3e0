.outer-view{
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 25px;
    margin-bottom: 50px;
    overflow-x: hidden;
}
.search-view{
    justify-content: flex-end;
    display: flex;
    width: 85%;
    padding-top: 15px;
}
/* input{
    box-shadow: inset 0px 0px 2px rgba(176, 178, 188, 0.6);
    border: 1px solid rgba(176, 178, 188, 0.6);
    border-radius: 2px;
    height: 34px;
    width: 30%;
} */
.body-view{
    margin-top: 5px;
}
.key-wrapper{
    width: 100%;
    margin-bottom: 20px;
}
.key-header{
    width: 100%;
    background-color: #fff;
    padding: 10px 15px;
    padding-bottom: 8px;
    border-radius: 3px;
    border-bottom: 1px solid #d0d1d7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* cursor: pointer; */
    border: 1px solid rgb(95, 179, 251);
}
.active-key-header{
    background-color: rgba(95, 179, 251, 0.3);
}
.object-name{
    /* margin-bottom: 5px; */
    font-family: "Montserrat";
    font-size: 16px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.43;
    letter-spacing: normal;
    text-align: left;
    color: #4b4b4b;
}
.close-arrow{
    transform: rotate(180deg);
}
.open-arrow{
    transform: rotate(90deg);
}
.object-fields{
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-top: 15px;
}
.wrapper-object{
    width: 95%;
    padding-top: 15px;
}
.config-selector{
    width: 98%;
}
.save-button{
    border: 1px solid #5fb3fb;
    background-color: white;
    padding: 5px 25px;
    border-radius: 5px;
    font-size: 15px;
    color: #5fb3fb;
    font-family: inherit;
    font-weight: 500;
}
.save-button:hover{
    background-color: #5fb3fb;
    color: white;
}
.reset-button{
    border: 1px solid #dc3545 !important;
    color: #dc3545 !important;
    padding: 5px 18px !important;
    margin-left: 15px;
}
.reset-button:hover{
    background-color: #dc3545;
    color: white !important;
}
.default-button{
    padding: 5px 8px !important;
}
.button-view{
    display: flex;
    align-items: center;
}
.search-header-view{
    width: 95%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    margin-top: 15px;
    padding: 10px 10px 15px 10px;
    border-radius: 2px;
}
.cron-content{
    display: flex;
    background-color: #eee;
    flex-direction: column;
    padding: 15px 20px;
    margin-bottom: 5px;
    margin-top: 5px;
  }

  .cron-comment-name{
    font-family: "Montserrat";
    font-size: 16px;
    font-weight: 500;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.43;
    letter-spacing: normal;
    text-align: left;
    color: #4b4b4b;
  }
  .cron-command{
    margin-top: 3px;
  }

  .save-button{
    border: 1px solid #5fb3fb;
    background-color: white;
    padding: 5px 25px;
    border-radius: 5px;
    font-size: 15px;
    color: #5fb3fb;
    font-family: inherit;
    font-weight: 500;
  }
  .save-button:hover{
    background-color: #5fb3fb;
    color: white;
  }
  .reset-button{
    border: 1px solid #dc3545 !important;
    color: #dc3545 !important;
    padding: 5px 18px !important;
    margin-left: 15px;
  }
  .reset-button:hover{
    background-color: #dc3545;
    color: white !important;
  }
  .default-button{
      padding: 5px 8px !important;
  }
  .object-name{
      /* margin-bottom: 5px; */
      font-family: "Montserrat";
      font-size: 16px;
      font-weight: normal;
      font-style: normal;
      font-stretch: normal;
      line-height: 1.43;
      letter-spacing: normal;
      text-align: left;
      color: #4b4b4b;
  }
  .button-view{
      display: flex;
      align-items: center;
  }
  .cron-header{
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    overflow-y: hidden;
  }
  .cron-body{
    width: 100%;
    overflow-y: scroll;
    max-height: 400px;
  }
  .cron-loading{
    width: 100%;
    max-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .show-cron:hover{
    background-color: #758bf7;
    border-radius: 10px;
  }
  .cs-name-view{
    background-color: crimson;
    color: white;
    font-weight: bold;
    font-size: 13px;
    padding: 5px 15px;
    border-radius: 20px;
  }
  .health-loading{
    position: absolute;
    bottom: 20px;
    justify-content: center;
  }
  .left-cron-body{
    width: 40%;
    padding: 25px 0px 25px 5%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .left-cron-inner{
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  .right-cron-body{
    padding: 25px;
    width: 60%;
    height: 400px;
    background-color: #eee;
    overflow-y: scroll;
  }
  .left-cron-body span input{
    margin-right: 3px;
  }
  .center-content{
    overflow-y: hidden !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .health-button{
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
    border-radius: 7px;
    padding: 5.5px 4px;
    margin-right: 10px;
  }
  .button-new-content {
    color: #fff !important;
    box-shadow: none !important;
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
    font-family: "Montserrat";
    width: auto;
    border-radius: 2px;
    padding: 0px 10px;
  }

  .button-new-content:hover {
    box-shadow: 4px 10px 16px 0 rgba(53, 88, 185, 0.16) !important;
  }
  .font_5{font-weight: 500;}
  .centered-align{display: block !important;margin: 0 auto !important;}

.frequency-fields-header{
    width: 100%;
    display: flex;
    padding: 15px 15px 10px 15px;
    overflow-y: hidden;
  }
  .select-options-fields {
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
    outline: 0;
    box-shadow: none;
    border: 0 !important;
    background: white;
    background-image: none;
    flex: 1;
    padding: 0 .3em;
    color: black;
    cursor: pointer;
  }
  .select-options-fields::-ms-expand {
    display: none;
  }
  .select-div-fields {
    position: relative;
    display: flex;
    width: 12em;
    height: 2em;
    line-height: 2;
    background:white;
    overflow: hidden;
    border-radius: .25em;
    border: 0.5px solid #5fb3fb;
    margin-top: 1px;
    margin-bottom: 0;
    margin-left:auto;
    margin-right: 2px;
  }
  .select-div-fields::after {
    content: '\25BC';
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 0.2em;
    background: white;
    cursor: pointer;
    pointer-events: none;
    -webkit-transition: .25s all ease;
    -o-transition: .25s all ease;
    transition: .25s all ease;
  }
  .select-div-fields:hover::after {
    color: white;
    background: #5fb3fb!important;
  }
.update-button {
    background-color: white;
    color: black;
    padding: 8px 20px;
    margin: 8px 0;
    cursor: pointer;
    width: 65%;
    border: 2px solid rgba(176, 178, 188, 0.6);
}
.container {
    padding: 16px;
}
.freq-field-input{
  border: 1px solid #949494;
  padding: 12px;
  padding-top: 20px;
  vertical-align: top;
  border-radius: 2px;
  background-color: aliceblue;
  width: 90%;
  resize: none;
}
.freq-field-label{
  font-size: 13px;
  position: absolute;
  top: -12px;
  right: 70px;
  background-color: white;
  padding: 3px 15px;
  border-radius: 25px;
}

.right-cron-body-half{
  width: 100%;
  height: 50%;
}

.cron-input-textarea{
  width: 100%;
  height:100%;
  background-color: #eee;
}

.getCronServiceType{
  font-size: 10px;
  border: 1.5px solid rgb(189, 186, 186);
  margin: 3px 5px 0 5px;
  padding: 4px;
  border-radius: 50px;
}

.header-button{
  border: 1px solid #5fb3fb;
  color: #5fb3fb !important;
  width: max-content;
  padding: 12px 5px !important;
  border-radius: 0px;
  font-size: 14px;
  margin: 0px 4px;
  border-radius: 5px;
}
.header-button:hover{
  background-color: #5fb3fb;
  color: white !important;
}
