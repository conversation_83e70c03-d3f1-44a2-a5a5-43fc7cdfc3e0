<ng2-toasty></ng2-toasty>
<div class="overlay" *ngIf="isLoading" style="display: flex;align-items: center;">
    <div class="save-loader-search-client col-md-12"
         style="display: flex;justify-content: center;">
        <div *ngIf="isLoading" style="display: inline-block; margin-left: 0px">
        <div class="loadingScreen">
            <div class="spinner">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
            </div>
        </div>
        </div>
    </div>
</div>
<div class="outer-view">
    <div class="search-header-view">
        <div style="display: grid;">
            <strong class="ng-tns-c9-1 object-name" style="font-weight: 500;font-size: 22px; ">
                Indexing Configurations
            </strong>
        </div>
    </div>
    <div class="wrapper-object body-view">
        <div class="key-wrapper" *ngFor="let obj of configObject; let i = index;">
            <div class="{{selectedObjId == obj.id ? 'key-header active-key-header' : 'key-header'}}">
                <strong class="ng-tns-c9-1 object-name" style="font-weight: 500;word-break: break-word;  ">
                    {{obj.config_key}}
                </strong>
                <button class="save-button" *ngIf="selectedObjId != obj.id"
                    (click)="objectClicked(obj.id, obj.config_key, i);">
                    {{ obj.tool_type ? 'Open' : 'Edit' }}
                </button>
                <div *ngIf="selectedObjId == obj.id && !obj.tool_type" >
                    <div *ngIf="defaultCheckOff" class="button-view">
                        <button class="save-button" (click)="onSave()">Save Configuration</button>
                        <button class="save-button reset-button" (click)="onReset()">Reset Changes</button>
                        <button *ngIf="obj.config_key == 'OpenSearch Configuration'" class="save-button reset-button default-button" (click)="defaultCheckOff=!defaultCheckOff;">Set to Default</button>
                    </div>
                    <div *ngIf="!defaultCheckOff" class="button-view">
                        <strong class="ng-tns-c9-1 object-name" style="font-weight: 500;">
                            Are you sure, you want to set this config to default?
                        </strong>
                        <div class="button-view">
                            <button class="save-button" (click)="setAsDefault()"
                            style="padding: 5px 18px;margin-left: 10px;">Yes</button>
                            <button class="save-button reset-button" (click)="defaultCheckOff=true;">No</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="object-fields" *ngIf="selectedObjId == obj.id">
                <app-isconfigfields [configObject]="obj.config_value" [configKey]="obj.config_key"  [reserveObject]="reserveObject[i].config_value"
                (onPropertyChange)="onPropertyChange($event)" [keysEditableForVectorConfig]="keysEditableForVectorConfig" class="config-selector">
                </app-isconfigfields>
            </div>
        </div>
    </div>
</div>
