import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastyService, ToastOptions } from "ng2-toasty";
import { isService } from '../../../services/indexService.service';
import { IsconfigfieldsComponent } from '../isconfigfields/isconfigfields.component'
import { ContentSourceService } from '../../../services/contentSource.service';
import 'lodash';
import { config } from 'process';
declare var _:any;

@Component({
  selector: 'app-is-config',
  templateUrl: './is-config.component.html',
  styleUrls: ['./is-config.component.css'],
})
export class IsConfigComponent implements OnInit {

    public isLoading: Boolean = false;
    public userSuccess: Boolean = false;
    public configObject: any;
    public reserveObject: any;
    public selectedObjId: number;
    public selectedKey: String;
    public defaultCheckOff: Boolean = true;
    public updateObject: any;
    public currentIndex: number;
    public configMapping = {
        openSearchConfig: 'OpenSearch Configuration',
        vectorConfiguration: 'Vector Configuration',
        searchConfiguration: 'Search Configuration'
    };
    public keysEditableForVectorConfig: any = {
      'vectorIndexingEnabled': 'Real Time Vector',
      'enableVectorizationFromStart': 'Enable Vectorization From Start',
      'hybridSearchEnabled': 'Frequency Vector',
      'enableMultilingual': 'Enable Multilingual',
      'vectorizeDataFrequency': 'Set Vectorization Frequency',
      'skipContentSourcesFromVectorization': 'Skip Content Sources From Vectorization'
    };
    public indexingTools = [
      {
        Refresh: 'Refresh Index',
        tool_type: true,
        click_method: this.refreshIndexes.bind(this)
      },
    ];
    @ViewChild(IsconfigfieldsComponent) child:IsconfigfieldsComponent;
  
  constructor(
    private indexingService: isService,
    private toastyService: ToastyService
  ) {
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.fetchIndexingConfigurations();
    this.userSuccess = true;
  }

  isObject(obj) {
    if(Array.isArray(obj) || (obj === Object(obj) && JSON.stringify(obj) !== "{}")) return false;
    return obj === Object(obj);
  }

  valueParser(value){
    let response = {error: true};
    if(value === "" || value == undefined || value == null) return response;
    else if(typeof value !== "string") {}
    else if(value[0] == "[" || value[value.length-1] == "]")
      try{
        value = value.replace(/'/g, '"');
        value = JSON.parse(value);
      }catch(e){ return response; }
    else if(value[0] == "{" || value[value.length-1] == "}")
      try{
        value = value.replace(/'/g, '"');
        value = JSON.parse(value);
      }catch(e){ return response; }
    else if(["true","false"].includes(value.toLowerCase()))
      value = (value.toLowerCase() == "true");
    else if(!isNaN(Number(value)))
      value = Number(value);

    response.error = false;
    response["value"] = value;
    return response;
  }

  checkAndParseValues(arr){
    let response = {error: true, message: "", data: {}};
    let obj = {};

    for (let i = 0; i < arr.length; i+=2) {
      let keys = arr[i].split(".").reverse();
      let resp = this.valueParser(arr[i+1]);
      if(resp.error){
        response["message"] = `Invalid value provided for ${keys[keys.length-1]}`;
        return response;
      }
      arr[i+1] = resp["value"];
      let currentKey = {};

      while(keys.length){
        if(Object.keys(currentKey).length == 0){
          currentKey[keys.shift()] = arr[i+1];
        }else{
          let temp = currentKey;
          currentKey = {};
          currentKey[keys.shift()] = temp;
        }
      }
      obj = _.merge(obj, currentKey);
    }

    response.error = false;
    response["data"] = obj;
    return response;
  }

  fetchIndexingConfigurations(){
    this.indexingService.getIndexServiceConfiguration().then(result => {
        if(result.success && result.data.indexingConfig){
            let newConfigObject: any = [];
            Object.keys(result.data.indexingConfig).forEach((key, index) => {
                    newConfigObject.push({
                        id: index,
                        config_key: this.configMapping[key],
                        config_value: result.data.indexingConfig[key]
                    })
            })
            this.configObject = JSON.parse(JSON.stringify(newConfigObject));
            this.reserveObject = JSON.parse(JSON.stringify(newConfigObject));
            this.addIndexingTools();
            this.selectedObjId = -1;
            this.updateObject = [];
        }
        this.isLoading = false;
    }).catch((error) => {
        this.isLoading = false;
        this.showToast(JSON.stringify(error), "error");
    });
  }

  addIndexingTools() {
    const transformedObject = this.indexingTools.reduce((acc, tool, index) => {
      const key = Object.keys(tool).find(k => k !== 'tool_type' && k !== 'click_method');
      if (key) {
        acc[index] = {
          [tool[key]]: key,
          tool_type: tool.tool_type,
          click_method: tool.click_method
        };
      }
      return acc;
    }, {} as Record<number, any>);
    this.configObject.push({
      id: this.configObject.length,
      config_key: 'Indexing Tools',
      tool_type: true,
      config_value: transformedObject
    });
    this.reserveObject.push({
      id: this.configObject.length,
      config_key: 'Indexing Tools',
      tool_type: true,
      config_value: transformedObject
    });
  }

  formatValues(obj){
    let keys = Object.keys(obj);
    if(keys.length == 0){
      return "{}";
    }else{
      for(let key of keys){
        if(this.isObject(obj[key])){
          obj[key] = this.formatValues(obj[key]);
        }
      }
    }

    return obj;
  }

  objectClicked(id,key,i){
    this.defaultCheckOff = true;
    this.selectedObjId = id;
    this.selectedKey = key;
    let found = this.updateObject.find(o => o.id == id);

    if(!found){
      this.configObject[i].config_value = this.formatValues(this.configObject[i].config_value);
      this.currentIndex = this.updateObject.length;
      this.updateObject.push({ id, updatedValues: this.selectedKey === this.configMapping['vectorConfiguration'] ? {} : [] });
    }else{
      for (let i = 0; i < this.updateObject.length; i++) {
        if(this.updateObject[i].id == id){
          this.currentIndex = i;
          break;
        }
      }
    }
  }

  onPropertyChange($event){
    if (Object.keys(this.keysEditableForVectorConfig).some(key => key in $event)) {
      let keys = Object.keys($event);
      keys.forEach(key => {
          $event[key] = typeof $event[key] == 'string' ? $event[key].trim() : $event[key];
          if(!this.updateObject[this.currentIndex].updatedValues.hasOwnProperty(key)) {
            this.updateObject[this.currentIndex].updatedValues[key] = $event[key];
          } else this.updateObject[this.currentIndex].updatedValues[key] = $event[key];
      });
    }else{
      let key = Object.keys($event)[0];
      let index = this.updateObject[this.currentIndex].updatedValues.indexOf(key);
      if(index != -1)
        this.updateObject[this.currentIndex].updatedValues[index+1] = $event[key];
      else{
        this.updateObject[this.currentIndex].updatedValues.push(key);
        this.updateObject[this.currentIndex].updatedValues.push($event[key]);
      }
    }

  }

  showToast(title, type="success", msg?){
    var toastOptions: ToastOptions = {
      title, msg,
      showClose: true,
      timeout: 3000,
      theme: "default",
    };

    if(type == "success")
      this.toastyService.success(toastOptions);
    else if(type == "error")
      this.toastyService.error(toastOptions);
    else
      this.toastyService.warning(toastOptions);
  }

  onReset(){
    this.updateObject = this.updateObject.filter(o => o.id != this.selectedObjId);
    this.currentIndex = this.updateObject.length;
    this.updateObject.push({ id: this.selectedObjId, updatedValues: [] });

    for (let i = 0; i < this.configObject.length; i++) {
      if(this.configObject[i].id == this.selectedObjId){
        this.configObject[i].config_value = JSON.parse(JSON.stringify(this.reserveObject[i].config_value));
        this.configObject[i].config_value = this.formatValues(this.configObject[i].config_value);
        break;
      }
    }
  }

  compareRecursive(oldObj, newObj, path = '', changedProperties = {} ) {
    for (let key in newObj) {
        if (typeof newObj[key] === 'object' && newObj[key] !== null && !Array.isArray(newObj[key])) {
          changedProperties = {...changedProperties, ...this.compareRecursive(oldObj[key], newObj[key], `${path ? path + '.' : ''}${key}`)};
        } else {
            if (oldObj[key] !== newObj[key]) {
                changedProperties[`${path ? path + '.' : ''}${key}`] = newObj[key];
            }
        }
    }
    return changedProperties;
  }

  onSave(){
    let config_value;
    let oldConfig: {};
    let config_key = Object.keys(this.configMapping).find(key => this.configMapping[key] === this.selectedKey);

    let updatedArray = this.updateObject[this.currentIndex].updatedValues;
    if(this.selectedKey !== this.configMapping['vectorConfiguration']){
      if (updatedArray.length == 0) {
        this.showToast("No update found to save","warning");
        return;
      }
      const {error, data, message} = this.checkAndParseValues(updatedArray);
      if(error){
          this.showToast(message,"error");
          return;
      }
      else if(this.selectedKey === this.configMapping['searchConfiguration']) {
        const searchConfigurations = this.configObject.find(item => item.config_key === this.selectedKey);
        
        config_value = searchConfigurations.config_value;
        config_value.searchQueryLimit = parseInt(config_value.searchQueryLimit, 10);

        if(config_value.searchQueryLimit < 1 || config_value.searchQueryLimit > 1000) {
          this.showToast('Search query limit is out of range. Please set a limit between 1 and 1000.', 'error');
          return;
        }
      }
      else {
        config_value = data;
      };
    } else if (this.selectedKey === this.configMapping['vectorConfiguration']) {
      oldConfig = this.reserveObject.filter(k => k.config_key === this.selectedKey)[0].config_value;
      const changed: any = this.compareRecursive(oldConfig, updatedArray);
      console.log(changed);
      if (!changed || !Object.keys(changed).length) {
        this.showToast("No update found to save","warning");
        return;
      }
      else if (changed && (changed.hybridSearchEnabled || changed.vectorIndexingEnabled || changed.enableMultilingual)) {
        console.log('Vectorization will be triggered');
      }
      config_value = changed;
    }
    this.isLoading = true;
    this.indexingService.updateIndexingConfiguration({
      configKey: config_key, configValue: config_value, oldConfig
    }).then(res => {
      if(res.error){
        this.showToast(res.msg, "error");
      }else{
        if (res.response && res.response.vectorizationInitiated) {
          const index = this.configObject.findIndex(k => k.config_key === this.selectedKey);
          if (index>-1) {
            this.child.vectorIndexingDisabled = true;
          }
        }
        for (let i = 0; i < this.reserveObject.length; i++) {
          if(this.reserveObject[i].id == this.selectedObjId){
            this.reserveObject[i].config_value = JSON.parse(JSON.stringify(this.configObject[i].config_value));
            this.updateObject[this.currentIndex].updatedValues = [];
            break;
          }
        }
        this.showToast(`${config_key} config updated successfully.`,"success");
      }
      this.isLoading = false;
    }).catch(err => {
      this.isLoading = false;
      this.showToast(JSON.stringify(err), "error");
    });
  }

  setAsDefault(){
    this.isLoading = true;
    let config_key = Object.keys(this.configMapping).find(key => this.configMapping[key] === this.selectedKey);
    this.indexingService.resetIndexingConfiguration(
      {config_key}
    ).then(res => {
      if(res.error){
        this.showToast(res.error, "error");
      }else{
        const defaultConfig = res.response.data.config_value;
        for (let i = 0; i < this.reserveObject.length; i++) {
          if(this.reserveObject[i].id == this.selectedObjId){
            this.reserveObject[i].config_value = defaultConfig;
            this.configObject[i].config_value  = defaultConfig;
            this.configObject[i].config_value = this.formatValues(this.configObject[i].config_value);
            this.updateObject[this.currentIndex].updatedValues = [];
            break;
          }
        }
        this.defaultCheckOff = true;
        this.showToast(`${this.selectedKey} config was successfully reset.`,"success");
      }
      this.isLoading = false;
    }).catch(err => {
      this.isLoading = false;
      this.showToast(JSON.stringify(err), "error");
    });
  }

  toggleVectorIndexing(){
    this.indexingService.toggleVectorIndexing().then
  }

  refreshIndexes() {
    this.indexingService.refreshIndexes().then((result: any) => {
      if (result.success) {
        this.showToast('Indexes Refreshed')
      } else {
        this.showToast('Could Refresh Indexes', 'error');
      }
    }).catch((error: any) => {
      this.showToast(JSON.stringify(error), "error");
    });
  }
}

