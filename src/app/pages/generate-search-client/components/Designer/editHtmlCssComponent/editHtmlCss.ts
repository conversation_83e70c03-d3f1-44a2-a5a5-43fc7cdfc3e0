import { Component, OnInit, Input,AfterViewChecked,Renderer2,<PERSON>ementRef ,EventEmitter,Output, ViewChild, HostListener, } from '@angular/core';
import { SearchClientService } from '../../../../../services/searchClient.service';
import { GenerateSearchClientComponent } from "../../generate_search_client";
import { ToastyService } from 'ng2-toasty';
import { Variables } from '../../../../../variables/contants';
import { DomSanitizer } from "@angular/platform-browser";
import * as socketIo from 'socket.io-client';
import { CookieService } from '../../../../../services/cookie.service';
import { DiffEditorModel } from 'ngx-monaco-editor';
import { timingSafeEqual } from 'crypto';
import {FormGroup, FormBuilder, FormControl, FormGroupDirective, NgForm, Validators} from '@angular/forms';
import {ErrorStateMatcher} from '@angular/material/core';
import { ThemeService } from 'app/services/theme.service';
declare var $: any;
declare var jQuery: any;
@Component({
    selector: 'edit-html-css',
    templateUrl: 'editHtmlCss.html',
    styleUrls: ['./editHtmlCss.css','./treeStyles.css','./refactoredstyles.css'],
    providers: [SearchClientService, CookieService]
})

export class EditHtmlCssComponent implements OnInit,AfterViewChecked {
    userAddressValidations: FormGroup;
    i;
    counterValue = 12;
    get counter() {
        return this.counterValue + 0;
    }
    set counter(value) {
        this.counterValue = value;
    }
    decrementFont(event) {
        this.counter--;
        this.FontType = event.target.value
        this.FontType--;
        this.editorOptions = { ...this.editorOptions, fontSize: this.counter }
        this.diffeditorOptions = { ...this.diffeditorOptions, fontSize: this.counter }
    }
    incrementFont(event) {
        this.counter++;
        this.FontType = event.target.value
        this.FontType++;
        this.editorOptions = { ...this.editorOptions, fontSize: this.counter }
        this.diffeditorOptions = { ...this.diffeditorOptions, fontSize: this.counter }
    }

    SCFiles:Node[] = [];
    config = {
       showRootActionButtons: false,
       enableExpandButtons: true,
       enableDragging: false,
       rootTitle: 'Directory',
       validationText: '*',
       minCharacterLength: 1,
       setFontSize: 12,
       setIconSize: 12,
       setItemsAsLinks: true,
    };
    
    //Tree Action buttons events
    onStartDelete(event) {
       console.log('on start delete item');
    }
    onFinishDelete(event) {
       console.log('on finish delete item');
    }
    onCancelDelete(event) {
       console.log('on cancel delete item');
    }
    onadditem(event) {
       console.log("file adding option clicked");
    }
    onStartRenameItem(event) {
       console.log('File renaming option clicked');
    }
    onFinishRenameItem(event) {
       console.log("File renamed successfully");
    }




    @Output() testsaveButtonCheck = new EventEmitter<boolean>();
    @Input() uuid: any;
    @Input() search_client_type_id: any;
    mainUrl: String = '//' + window.location.host + Variables.baseHref;
    searchClientUrl;
    element: HTMLIFrameElement;
    expanded = false;
    private diffToggle: boolean = false;
    private createFileFolderPath : string = null;
    private listOfStandardDiffFiles: Array<String> = [];
    private inSyncWithS3: boolean = true;
    private fetchingStandardFiles = false;
    public isActive = false;
    public s3Compare = false;
    public isThemeSelect:any;
    public newComponentName:string = '';
    public newFileName:string = '';
    public newFolderName:string = '';
    public deleteComponentName:string;
    public userCustomComponentName:any;
    public isHideSearch=false;
    public showEditor=false;
    public isHideModalBuild=false
    public isHideBuild=false;
    public isShownApp=true;
    public isShownStyle=true
    public isShownBuild=true
    public uidRegex= /^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    public isShownAdditionalComponents=true
    public isShownCustomComponents=true
    // private selectedClient: any;
    private dynamicID:any;
    private buildChecker : any;
    private stickToBottom = true;
    private reactComponent: Array<String> = [];
    private componentHtml: String = '';
    private componentFullPath: String = '';
    private componentHtmlBackup: String = '';
    private saveButtonCheck: boolean = false;
    private deleteFileFolderPopup: boolean = false;
    private currentComponent: any;
    private buildType: any;
    private FontType:  number;
    private LanguageType: String = '';
    private socket: any;
    private logFileSource: any;
    private preTag: String = '';
    private postTag: String = '';
    private progressValue: any;
    private showPercentage :boolean=false;
    private finalProgressValue: any;
    private reactBuildBar:boolean=false;
    private modeValue: string;
    private isBuildFailed:boolean =false;
    private showProgressBar:boolean=true;
    private text : string;
    private isDarkTheme: boolean = true;
    private CompleteDiv :boolean = false;
    private disabledCreate:boolean=false;
    private disableCreateFileFolderButton:boolean = true;
    private disabledModal:boolean=false
    private cloneComponentName:string
    suUserandGzuser: boolean = false;
    previouslySelectedFile: HTMLElement | null = null;
    private reactRebuildWithFreshNpm: boolean = false;
    private activeTheme : String;
    private diffFilesArray : Array<String> = []
    editorLoader: boolean;
    editorOptions = { theme: "vs-dark", automaticLayout: true,language:"css", fontSize: 12,wordWrap: 'wordWrapColumn', wordWrapColumn: 500, readOnly:false};
    diffeditorOptions = { theme: "vs-dark", language:"js", automaticLayout: true, fontSize: 12, readOnly: true ,wordWrap: 'wordWrapColumn', wordWrapColumn: 50, originalEditable: true};
    code: string = 'function x() {\nconsole.log("Hello world!");\n}';
    originalModel: DiffEditorModel = {
        code: 'heLLo world!',
        language: 'javascript',
    };
    modifiedModel: DiffEditorModel = {
        code: 'hello orlando!',
        language: 'javascript',
    };
    public searchClientName:string ;
    public searchClientId: string;
    public searchClientUid: string;
    public listOfAllFilesData;
    dataInput: string= '';
    showFilesDiv: boolean = false;
    public selectedFileName: boolean = false;
    public selectedCurrentComponent: string = '';
    public openSearchBar: boolean = false;
    public searchClientType:any;
    private searchClientLanguage : String = '';
    public CloseModal :Boolean = false;
    private socketActive = false;

    constructor(private searchClientService: SearchClientService,
        private GenerateSearchClientComponent: GenerateSearchClientComponent,
        private cookieService: CookieService,
        private toastyService: ToastyService,
        private domSanitizer: DomSanitizer,
        private formBuilder: FormBuilder,
        private theme: ThemeService,
        private renderer: Renderer2, 
        private elementRef: ElementRef
    ) {
    }

       // CONTEXT MENU CODE
       contextmenu = false;
       contextmenuX = 0;
       contextmenuY = 0;
   
       onrightClick(e){
        let targetElement = e.target;
        console.log(targetElement);

        if((targetElement.classList.contains('tree-content-main'))){
            return;
        }

        let anchorTag = targetElement.querySelector('a.tree-link');

           if(e.target.closest(`.tree-child`)){
               const fileElement = e.target.closest(`.tree-child`) as HTMLElement;
               if(fileElement && fileElement.id){
                      const isFolder = JSON.parse(fileElement.id);
                      const pathDetails = JSON.parse(fileElement.id).path;
                      this.createFileFolderPath = pathDetails;
               }
           }
           this.contextmenuX=e.clientX
           this.contextmenuY=e.clientY
           this.contextmenu=true;
       }
   
       clickedfunction() {
           console.log("Clicked the function");
           this.contextmenu = false; // Hide the context menu
         }    
   
       //disables the menu
       disableContextMenu(){
           this.contextmenu= false;
       }

       deleteFileFolder(){
            this.deleteFileFolderPopup = false;
            this.searchClientService.deleteFileFolder(this.uuid, this.searchClientType, this.createFileFolderPath).then(result => {
                this.getSCFiles();
                jQuery.noConflict();
                this.toastyService.success({
                    title: `Deleted Successfully`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `file/folder deleted successfully`
                })
            }).catch((e)=>{

                this.toastyService.error({
                    title: `Something went wrong`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `Error with creating file`
                })
            })
       }

       createFile(){
        this.searchClientService.createNewFile(this.uuid, this.searchClientType, this.createFileFolderPath, this.newFileName).then(result => {
            if(JSON.parse(result).status == '82'){
                this.toastyService.error({
                    title: `File already exists`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `Error with File creation`
                })
            }else{
                this.toastyService.success({
                    title: `File created`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `Created the File`
                })
                this.newFileName = '';
            }

            this.getSCFiles();
            jQuery.noConflict();
            jQuery("#createFileModal").modal('hide');
        }).catch((e)=>{
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error with creating file`
            })
        })
       }

       createFolder(){
        this.searchClientService.createNewFolder(this.uuid,this.searchClientType,this.createFileFolderPath,this.newFolderName).then(result => {
            if(JSON.parse(result).status == '82'){
                this.toastyService.error({
                    title: `Directory already exists`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `Error with creating folder`
                })
            }else{
                this.toastyService.success({
                    title: `Directory created`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `Created the directory`
                })
                this.newFolderName = '';
            }
            this.getSCFiles();
            jQuery.noConflict();
            jQuery("#createFolderModal").modal('hide');
        }).catch((e)=>{
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error with creating folder`
            })
        })
       }

    getBuildFileInfo(){
        this.searchClientService.getBuildFile(this.uuid, this.searchClientType, this.searchClientLanguage).then(result => {
            
        }).catch((e)=>{
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error with search client buildinfo file`
            })
        })
    }
    ngOnInit() {
        /* Subscribe to theme */
        this.theme.activeTheme.subscribe(t=>{
            this.activeTheme = t;
            this.SelectTheme(t==='white');
        })
        this.suUserandGzuser =  this.GenerateSearchClientComponent.suUserandGzuser;
        this.userAddressValidations = this.formBuilder.group({
            newComponentName: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(20), Validators.pattern('^[a-zA-Z_]+$')]],
            userCustomComponentName:['', [Validators.required, Validators.minLength(1), Validators.maxLength(20), Validators.pattern('^[a-zA-Z_-]+$')]],
            newFileName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(20), Validators.pattern('^[a-zA-Z_]+\\.[a-zA-Z_]+$')]],
            newFolderName: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(20), Validators.pattern('^[a-zA-Z_-]+$')]],
        });
        this.buildType = {
            mode: 'production',
            type: 'search'
        };
        this.logFileSource = {
            logLines: ''
        }
        this.searchClientName = this.GenerateSearchClientComponent.desigerTabConfig.name;
        this.searchClientUid = this.GenerateSearchClientComponent.desigerTabConfig.uid;
        this.searchClientType = this.GenerateSearchClientComponent.desigerTabConfig.search_client_type_id;
        this.searchClientLanguage = this.GenerateSearchClientComponent.desigerTabConfig.language;
        this.searchClientId = this.GenerateSearchClientComponent.desigerTabConfig.id;
        /**
         *  Bring Search Client information to handle Search client cases
         */
        // this.selectedClient = {};
        // this.selectedClient = this.GenerateSearchClientComponent.desigerTabConfig.client;
        //this.getHighlightedQuery();

        if (this.searchClientLanguage === 'react') {
            this.socketConnection();
            this.getBuildFileInfo();
            this.getSCFiles();
        } else {
            this.getSCFiles()
        }
    
        if(this.searchClientLanguage != 'lwcSfConsole') {
            if(this.FileExists() == 200) {
                this.searchClientUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(this.mainUrl + '/resources/search_clients_custom/' + this.uuid + '/preview/index.html');
            }else {
                this.searchClientUrl = '';
            }
        }
    }

    //Angular hooks - reponsible for the various page states
    ngAfterViewChecked(){
        // const elements = this.elementRef.nativeElement.querySelectorAll('.tree-child[id*="\/feature-components"][id*="folder"] .tree-title .buttons-bar');
        const customCreatedFolders = this.elementRef.nativeElement.querySelectorAll('.tree-child[id*="\/custom-components"][id*="folder"] .tree-title');
        const customComponentAdd = this.elementRef.nativeElement.querySelector('.tree-child[id*="\/custom-components"][id*="folder"] .tree-title .buttons-bar');
        //Adding option to clone the component
        // elements.forEach(element => {
        //     if(element.lastChild.tagName !== 'SPAN'){
        //         const newspan = this.renderer.createElement('span');
        //         newspan.classList.add('clone-span','ad_clone-iconblock');
        //         newspan.setAttribute('data-toggle', 'modal');
        //         newspan.setAttribute('href', '#cloneComponent');
        //         newspan.setAttribute('title', 'Clone Compnent');
        //         newspan.setAttribute('mattooltipposition', 'below');
        //         this.renderer.appendChild(newspan, this.renderer.createText(''));
        //         this.renderer.appendChild(element, newspan.cloneNode(true));
        //     }
        // });

        //Adding option to create the custom component
        if(customComponentAdd && customComponentAdd.lastChild.tagName !== 'SPAN'){
            const createComp = this.renderer.createElement('span');
            createComp.classList.add('create-span','ad_btn-item-one','ad_btn-save');
            createComp.setAttribute('data-toggle', 'modal');
            createComp.setAttribute('href', '#createNewComponent');
            createComp.setAttribute('title', 'Create Component');
            this.renderer.appendChild(createComp, this.renderer.createText(''));
            this.renderer.appendChild(customComponentAdd, createComp.cloneNode(true));
        }

        // Adding the delete button for the custom components
        for (let i = 1; i < customCreatedFolders.length; i++) {
            const element = customCreatedFolders[i];
            if (element.lastChild.tagName !== 'SPAN') {
                const newspan = this.renderer.createElement('span');
                newspan.classList.add('delete-custom-span');
                newspan.setAttribute('data-toggle', 'modal');
                newspan.setAttribute('href', '#deleteComponent');
                newspan.setAttribute('title', 'Delete');
                this.renderer.appendChild(newspan, this.renderer.createText(''));
                this.renderer.appendChild(element, newspan.cloneNode(true));
            }
        }
    }

    extensionToLanguageMap: { [key: string]: string } = {
        'js': 'javascript',
        'css': 'css',
        'html': 'html',
        'svg': 'html',
        'json': 'json',
        'csv': 'csv',
        'jsx':'javascript',
        'babelrc':'json',
        'env':'javascript',
        'eslintrc':'json',
        'prettierrc':'json',
        'sh':'shell'
    };

    //Monaco editor diff code view code
    monacoOnEditorTextChange(event) {
            this.saveButtonCheck = true;
            this.testsaveButtonCheck.emit(this.saveButtonCheck)
            this.isHideModalBuild=true
    }

    @HostListener("mouseenter"["$event"])
    mouseHandler(event) {
      event.stopImmediatePropagation();
    }

    //Sidebar toggle code
    toggleSidebar()
    {   
      var sidebar = document.getElementsByClassName('sidebar-wrapper')[0];
      var SelectedFile = document.getElementsByClassName('file-name-container')[0];
      var topbar = document.getElementsByClassName('designerTab-topnav ')[0];
      var editor = document.getElementsByClassName('codeeditor-container')[0];
      topbar.classList.toggle('sidebar-toggled');     
      editor.classList.toggle('sidebar-toggled');     
      sidebar.classList.toggle('sidebar-toggled');
      SelectedFile.classList.toggle('sidebar-toggled');
      const resizeEvent = new Event('resize');
      window.dispatchEvent(resizeEvent);
    }

    //Fetching the SC files
    getSCFiles() {
        this.searchClientService.getAllSCFiles(this.uuid, this.searchClientType, this.searchClientLanguage, this.searchClientId).then(result => {
            this.listOfAllFilesData = JSON.parse(result).SCFiles;
            this.SCFiles = this.listOfAllFilesData;  
        }).catch((e)=>{
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error with fetching the search client files`
            })
        })
    }

    //fetching the difference between customUID vs Standard codebase file
    getDiff(){
        this.editorLoader = true;
        this.searchClientService.getFileDiff(this.uuid, this.componentFullPath, this.searchClientType, this.searchClientLanguage).then(result => {
            this.originalModel = {...this.originalModel, code:`${JSON.parse(result).customSCCode}` };
            this.modifiedModel = {...this.modifiedModel, code:`${JSON.parse(result).standardSCCode}` };
            this.editorLoader = false;
            this.saveButtonCheck = false;
            this.isHideModalBuild = false;
            this.testsaveButtonCheck.emit(this.saveButtonCheck)
        }).catch((e)=>{
            this.editorLoader = false;
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error with fetching the file diff`
            })
        })
    }

    uploadAtSC(){
        this.searchClientService.uploadToS3(this.uuid).then(result => {
            console.log({result});
            this.toastyService.success({
                title: `Uploading to S3 triggered`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Check SC service logs for more information`
            })
        }).catch((e)=>{
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error in uploading search client files at S3`
            })
        })
    }

    checkAtS3(){
        this.editorLoader = true;
        this.searchClientService.getS3Status(this.uuid, this.componentFullPath, this.searchClientType).then(result => {
            this.inSyncWithS3 = JSON.parse(result).files;
            
            document.querySelectorAll('.tree-child.insync').forEach(element => {
                element.classList.remove('insync');
            });
            document.querySelectorAll('.tree-child.notinsync').forEach(element => {
                element.classList.remove('notinsync');
            });
            
            JSON.parse(result).files.forEach((fileName) => {
                    if(fileName.inSync){
                        const mainFile = document.querySelector(`.tree-child[id*="${this.uuid}/${fileName.file}"]`);
                        mainFile.classList.add('insync');
                    }
                    else{
                        const mainFile = document.querySelector(`.tree-child[id*="${this.uuid}/${fileName.file}"]`);
                        mainFile.classList.add('notinsync');
                    }
                })

            this.editorLoader = false;
        }).catch((e)=>{
            this.editorLoader = false;
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error in checking the build files at S3`
            })
        })
    }

    getUpdatedStandardFiless(){
        this.fetchingStandardFiles = true;
        this.searchClientService.getUpdatedStandardFilees(this.uuid, this.searchClientType, this.searchClientLanguage).then(result => {
            const data = JSON.parse(result);
            const diffFiles = data.files;
            this.listOfStandardDiffFiles = diffFiles.map((f)=>{
               return f;
            })
            this.fetchingStandardFiles = false;
        }).catch((e)=>{
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error in fetching the updated standard files`
            })
        })
    }

    createFileAtCustom(){
        this.searchClientService.createStandardFiles(this.uuid, this.listOfStandardDiffFiles).then(result => {
            this.getSCFiles();
            jQuery.noConflict();
            jQuery("#fetchStandardFiles").modal('hide');

            this.toastyService.success({
                title: `File upload triggered`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Check SC service logs for more information`
            })
        })
    }

    //get the diff files list
    getStandardDiffFiles(showDiffFiles) {
        const divElements = document.querySelectorAll(`.tree-child`);
        const divElementsFolders = document.querySelectorAll(`.tree-title`);

        if(showDiffFiles){
            this.searchClientService.getAllDiffFiles(this.uuid, this.searchClientType, this.searchClientLanguage).then(result => {
                this.diffFilesArray = JSON.parse(result).files;
                
                divElements.forEach(div => {
                    this.diffFilesArray.forEach(function(p:string) {
                        if(div.id){
                            const divId = JSON.parse(div.id).path;
                            // Checking if the full file path matches
                            if (p === divId) {
                                div.classList.add('with-dot');
                            } else {
                                // Check if the folder is being matched
                                const folderPath = p.split('/').slice(0, -1).join('/');
                                const enterLoop = folderPath.split('/').filter(Boolean).length === 1;
                                if (folderPath.includes(divId) && !enterLoop) {
                                    div.querySelector('.tree-title').classList.add('with-dot');
                                }
                            }
                        }
                    });
                });
                this.editorLoader = false;
            }).catch((e)=>{
                this.editorLoader = false;
                this.toastyService.error({
                    title: `Something went wrong`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `Error in fetching the diff with standard folder`
                })
            })
        }else{
            divElements.forEach(div => {
                if(div.id){
                    div.classList.remove("with-dot");
                }
            })
            divElementsFolders.forEach(div => {
                    div.classList.remove("with-dot");
            })
            this.editorLoader = false;
        }
    }

    socketConnection(){
        setTimeout(()=>{
            this.socket = socketIo(`${window.location.origin}`, {
                path: `${Variables.baseHref}/sc/socket.io`
            });
        })
    }
    getBuildLogFiles() {
        if(!this.socketActive){
            this.logFileSource = {
                logLines: ''
            }
            this.socket.emit('stop-data', { data: 'extra' });
            this.socketActive = true;
            this.socket.emit('send-data', { uuid: this.uuid, tenantKey: localStorage.getItem("tenantKey") });
        }
        this.socket.off(`collect-data_${localStorage.getItem("tenantKey")}`);
        this.socket.on(`collect-data_${localStorage.getItem("tenantKey")}`,  (source) => {
            this.logFileSource.logLines = source.logLines;
            this.progressValue = source.progress;
            this.modeValue = source.loader;
            this.text = source.displayText;
            if(source.failed){
                clearInterval(this.buildChecker);
                this.isBuildFailed = true;
                this.CompleteDiv = false;
                this.showPercentage=false
                setTimeout(() => {
                    this.reactBuildBar = false;
                }, 5000);
            }
            this.stickToBottom && setTimeout(()=>{this.scrollToBottom()},200);
        });

    };
    stopWatching() {
        this.removeClassToBody;
        this.showProgressBar= true;
    }
    cloneComponents(cloneComponentName, userCustomComponentName, createMode?) {
            this.cloneComponentName = cloneComponentName;  
            if(cloneComponentName==""){
               this.disabledCreate=true}
            else{
               this.disabledCreate=false
                   let copyComponentName = this.cloneComponentName;
                   this.cloneComponentName = `${this.cloneComponentName}.${userCustomComponentName || ''}`;   //cloneComponentName + '.' + userCustomComponentName;
                   this.searchClientService.cloneReactComponent(this.uuid, this.cloneComponentName).then(result => {
                       if (JSON.parse(result).status == 200) {
                           this.userCustomComponentName = undefined;
                           //this.getReactComponents();
                           this.getSCFiles();
                           this.toastyService.success({
                               title: `Successfully ${createMode ? 'Created' : 'Cloned'}`,
                               showClose: true,
                               timeout: 3000,
                               theme: 'default',
                               msg: `${copyComponentName}`
                           })
                           jQuery.noConflict();
                           jQuery("#createNewComponent").modal('hide');
                       }
       
                   });
               }
    }
    createComponentbutton(cloneComponentName){
        if(this.searchClientLanguage === 'react'){
            if(cloneComponentName && (cloneComponentName.length=="" || cloneComponentName.trim().length < 4)){
                //  this.disabledCreate=false
            } else{
                this.disabledCreate=false
            }
        }
    }

    createFilebutton(filename){
            if(filename.trim().length == 0 || filename.trim().length < 4){
                 this.disableCreateFileFolderButton=true;
            } else{
                this.disableCreateFileFolderButton=false;
            }
    }
    createFolderbutton(filename){
        if(filename.trim().length == 0 || filename.trim().length < 4){
             this.disableCreateFileFolderButton=true;
        } else{
             this.disableCreateFileFolderButton=false;
        }
    }

    //Reading the SC file
    readSCFile(e){
        var filePath:string = '';
        var fileName:string = '';
        var editorLanguage:string = 'javascript';
        var pathDetails:string = '';
        var fileElement;
        this.diffToggle = false

        if(e.target && e.target.classList.contains('tree-btn') && this.isActive){
            this.getStandardDiffFiles(true);
        }

        //Highlighting the selected file
        if(e.target.closest(`.tree-child[id*='\"href\":\"file\"']`)){
            fileElement = e.target.closest(`.tree-child[id*='\"href\":\"file\"']`) as HTMLElement;

            if(fileElement && fileElement.id){
                    pathDetails = JSON.parse(fileElement.id).path;
            }

            if (!fileElement.classList.contains('selectedFile')) {
                if (this.previouslySelectedFile) {
                    this.previouslySelectedFile.classList.remove('selectedFile');
                }
                fileElement.classList.add('selectedFile');
                this.previouslySelectedFile = fileElement;
            }
        }
        
        //Cloning standard component click
        // if(e.target.closest('.tree-child') && e.target.closest('.tree-child').id && e.target.closest('.tree-child').id.includes('feature-components') && e.target.tagName === 'SPAN' && e.target.classList.contains('clone-span')){
        //     const obj = JSON.parse(e.target.closest('.tree-child').id);
        //     filePath = obj.path;
        //     const parts = filePath.split("/");
        //     const comp = parts[parts.length-1];
        //     this.cloneComponentName = comp;
        // }
                
        //making the call to the search client service to fetch the file data from the API
        if(e.target.closest('.tree-child') && e.target.closest('.tree-child').id)
        {
            const obj = JSON.parse(e.target.closest('.tree-child').id);
            filePath = obj.path;
            const pathArray = filePath.split("/");
            fileName = pathArray[pathArray.length - 1];
            this.deleteComponentName = fileName;
        }
    
        if(fileName && fileName.includes('.')){ 
                if(!this.isHideSearch){
                    this.showEditor=!this.showEditor;
                    this.isHideSearch=!this.isHideSearch;
                }
        
                const parts = fileName.split('.');
                const extension = parts[parts.length - 1];
                const editorLanguage = this.extensionToLanguageMap[extension] || 'plaintext';
                this.editorLoader = true;

                const strings = ['App-autocomplete', 'App-feedback', 'App-recommendation', 'App'];
                const found = strings.some(str => filePath.includes(str));

                if (found) {
                    this.s3Compare = true;
                }
                else{
                   this.s3Compare = false;
                }

                this.searchClientService.readFile(this.uuid,filePath).then(res => {
                    this.selectedCurrentComponent = fileName;
                    this.currentComponent = fileName;
                    this.componentFullPath = filePath;
                    this.componentHtml = res;
                    this.selectedFileName = true;
                    this.editorLoader = false;
                    this.showFilesDiv = false;
                    this.dataInput = ''
    
                    if(filePath.includes(`/${this.uuid}/logfile.txt`)){
                        this.editorOptions = { ...this.editorOptions, readOnly:true, language: editorLanguage }
                    }else{
                        this.editorOptions = { ...this.editorOptions, readOnly:false, language: editorLanguage }
                    }

                    this.getDiff();
                    // this.originalModel = { ...this.originalModel, code: `${this.componentHtml}` }
                    // this.modifiedModel = { ...this.modifiedModel, code: `${this.componentHtmlBackup}` }
                }).catch((e)=>{
                    this.editorLoader = false;
                    this.toastyService.error({
                        title: `Something went wrong`,
                        showClose: true,
                        timeout: 3000,
                        theme: 'default',
                        msg: `Error in reading the SC files`
                    })
                })
        }
    }


    deleteComponents(component) {
        this.searchClientService.deleteReactComponent(this.uuid, component).then(result => {
            //this.getReactComponents();
            this.getSCFiles();
            this.toastyService.success({
                title: 'Deleted Component',
                showClose: true,
                timeout: 3000,
                theme: 'default'
            })
        })
    };
    findComponent(component) {
        this.showFilesDiv = false;
        this.openSearchBar = false;
        if(!this.isHideSearch){
            this.showEditor=!this.showEditor;
            this.isHideSearch=!this.isHideSearch
        }
    }

    
    //Updating the SC file
    updateSCFile() {  
        this.searchClientService.writeFile(this.currentComponent, this.uuid, this.componentHtml, this.componentFullPath,this.searchClientType,this.searchClientLanguage).then(result => {
            this.toastyService.success({
                title: `${this.currentComponent} updated!`,
                showClose: true,
                timeout: 3000,
                theme: 'default'
            });
            this.saveButtonCheck = false;
            this.isHideModalBuild = false;
            this.testsaveButtonCheck.emit(this.saveButtonCheck);
        });
        this.originalModel = { ...this.originalModel, code: `${this.componentHtml}` }

        this.searchClientService.updateLastUpdatedByInfo({ uid: this.uuid });
    }
    FileExists() {
        var http = new XMLHttpRequest();
        http.open('HEAD', this.mainUrl + '/resources/search_clients_custom/' + this.uuid + '/preview/index.html', false);
        http.send();
        return http.status;
    }
    buildReact() {
        this.searchClientService.build(this.uuid, this.buildType.mode, this.buildType.type, this.reactRebuildWithFreshNpm).then(result => {
            this.searchClientService.updateLastUpdatedByInfo({ uid: this.uuid });
        });
        this.progressOFReactSC();
    }
    progressOFReactSC() {
        this.logFileSource = {
            logLines: ''
        }
        this.reactBuildBar = true;
        this.isBuildFailed = false;
        this.showPercentage= false;
        this.CompleteDiv = false;
        this.modeValue = 'indeterminate';
        this.text = 'Installing node modules, Please wait..';
        this.progressValue = 0;
        this.buildChecker && clearInterval(this.buildChecker)
        this.getBuildLogFiles();
        if (document.getElementById('reactBuildLogs')) {
            document.getElementById('reactBuildLogs').click()
        }
        this.buildChecker = setInterval(() => {
            if(this.modeValue == 'determinate'){
                this.reactBuildBar = true;
                this.showPercentage = true;
            }
            if(this.progressValue === 100 || this.logFileSource.logLines.includes('Build steps complete') ){
                clearInterval(this.buildChecker);
                this.progressValue = 100;
                this.socketActive = false;
                setTimeout(()=>{
                        this.CompleteDiv = true;
                        setTimeout(() => {
                            this.CompleteDiv = false;
                        }, 5000);
                        this.reactBuildBar = false;
                        this.showPercentage=false;
                        this.progressValue = 0;
                },1500)
            }
        }, 500);
    }

    /**
     * scrollToBottom - auto scroll to bottom of the logs
     */
    scrollToBottom() {
        let logContainer = document.getElementsByClassName("ClassForLogsOuterDiv")[0];
        if(logContainer) {
            logContainer.scrollTo({
                top: logContainer.scrollHeight,
                behavior : 'smooth'
            })
        }
    }

    downloadSearchClient() {
        if (this.searchClientLanguage === 'react') {
            this.searchClientService.downloadReactCode(this.uuid);
        };
    }


    scrollToTop() {
        if(document.getElementsByClassName('ClassForLogsOuterDiv')[0]) {
            let divElem = document.getElementsByClassName("ClassForLogsOuterDiv")[0];
            divElem.scrollTop = divElem.scrollHeight;
        }
    }

    addClassToBody() {
    this.disabledModal=false
        var addClass = document.getElementById("desktopview");
        if(addClass){  addClass.classList.add("addClass"); }
        this.showProgressBar = false;
    }
    removeClassToBody() {
        var removeClass = document.getElementById("desktopview");
        if(removeClass) { removeClass.classList.remove("addClass"); }
    }
    ngOnDestroy() {
        try{
            this.socket.emit("stop-data", {});
            this.socket.off(`collect-data_${localStorage.getItem("tenantKey")}`);
            this.socketActive = false;
            this.socket.close();
        }
        catch{}
    }
    // saveHighlightQuery(id) {
    //     let data = {
    //         searchClientId: id,
    //         pre_tag: this.preTag,
    //         post_tag: this.postTag
    //     }
    //     this.searchClientService.updateHighlightQuery(data).then((result) => {
    //         this.toastyService.success({
    //             title: "Highlight Query saved succesfully.",
    //             showClose: true,
    //             timeout: 3000,
    //             theme: 'default'
    //         });
    //     })
    // }
    // getHighlightedQuery() {
    //     this.searchClientService.getHighlightedQuery(this.selectedClient.id).then((result) => {
    //         this.preTag = result.data ? result.data.pre_tag : ''
    //         this.postTag = result.data ? result.data.post_tag : ''
    //     })
    // }

    SelectTheme(state?) {
        this.isThemeSelect = state;
        let theme = state ? "vs-light" : "vs-dark";
        this.editorOptions = { ...this.editorOptions, theme }
        this.diffeditorOptions = { ...this.diffeditorOptions, theme }
    }
    LanguageSelect(event) {
        this.LanguageType = event.value
        this.editorOptions = { ...this.editorOptions, language: event.value }
        this.originalModel = { ...this.originalModel, language: event.value }
        this.modifiedModel = { ...this.modifiedModel, language: event.value }
        this.diffeditorOptions = { ...this.diffeditorOptions, language: event.value }
    }

    fullScreenMode() {
        if (!document.fullscreenElement) { 
                const mainEditor = document.getElementById("main-editor");
                if(mainEditor){
                    mainEditor.requestFullscreen();
                }
                this.isDarkTheme = false;
            } else {
                this.isDarkTheme = false;
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
    }
    setFont(event){
        this.FontType=event.target.value
        this.counter=Number(this.FontType)
        this.editorOptions = { ...this.editorOptions, fontSize: this.counter }
        this.diffeditorOptions = { ...this.diffeditorOptions, fontSize: this.counter}
    }

    monacoOnEditorInit(event: any) {

        const diffEditor = event;
        const originalEditor = diffEditor.getOriginalEditor();
        const modifiedEditor = diffEditor.getModifiedEditor();

        // Add change listeners to both editors
        originalEditor.onDidChangeModelContent(() => {
           this.monacoOnEditorTextChanged('original', originalEditor.getValue());
        });

        modifiedEditor.onDidChangeModelContent(() => {
            this.monacoOnEditorTextChanged('modified', modifiedEditor.getValue());
        });
    }

    monacoOnEditorTextChanged(editorType: string, newValue: string) {
        this.componentHtml = newValue;
        this.saveButtonCheck = true;
        this.testsaveButtonCheck.emit(this.saveButtonCheck)
        this.isHideModalBuild=true
    }
    
    ToggleDiffButton(state?) {

        if(typeof state === "boolean"){
            this.isActive = state;
            if(this.isActive ){
                this.editorLoader = true;
                this.diffToggle = true;
                this.getStandardDiffFiles(this.isActive);
                this.getDiff();
            }else{
                this.editorLoader = true;
                this.diffFilesArray = [];
                this.getStandardDiffFiles(this.isActive);
            }
        }
        else{
            this.isActive = !this.isActive;
        }
    }

    closeEditor() {
        var element = document.body;
        element.classList.remove('ad_designer-open');
    }
    onClickRebuild(){
        this.isHideModalBuild=!this.isHideModalBuild
    }
    buildAndSave(){
        this.updateSCFile();
        this.addClassToBody();
    }
    hideBuildButton(){
        this.isHideBuild=!this.isHideBuild
    }
    toggleShow(data){
        if(data=='std-app'){
            this.isShownApp=!this.isShownApp
        }
        if(data=="style"){
            this.isShownStyle=!this.isShownStyle
        }
        if(data=='build'){
            this.isShownBuild=!this.isShownBuild
        }
        if(data=='isShownAdditionalComponents'){
            this.isShownAdditionalComponents=!this.isShownAdditionalComponents
        }
        if(data=="isShownCustomComponents"){
            this.isShownCustomComponents=!this.isShownCustomComponents
        }

    }
    dynamicModalData(id) {
        this.dynamicID = id;
        id = id - 1;
        let popupStrings = [{
            id: 1,
            name: 'There are unsaved changes',
            button1: 'Re-build without Save',
            button2: 'Save and Build',

            actionOne: "onClickRebuild()",
            actionTwo: "buildAndSave()"
        }, {
            id: 2,
            name: 'Are you sure you want to delete the Preview',
            button1: 'Cancel',
            button2: 'OK'
        }, {
            id: 3,
            name: 'Your changes will not be saved',
            button1: 'Cancel',
            button2: 'OK'
        }, {
            id: 4,
            name: 'If you Close this, your changes will not be',
            button1: 'Cancel',
            button2: 'OK'
        }];
        return popupStrings[id].name, popupStrings[id].actionOne, popupStrings[id].button1, popupStrings[id].button2
    }

}



