<ng2-toasty></ng2-toasty>
<mat-progress-bar mode="indeterminate" *ngIf="matLoader"></mat-progress-bar>

<div *ngIf="!isLoading && selectPlatform" [hidden]="designerActive" class="su-searchClient">
    <div class="topHeading display-flex">
        <div *ngIf="!addSearchClient" class="display-flex flex-direction-column margin-TB-auto-LR-0">
            <span class="heading-source">Add Search Client</span>
            <h6 class="margin-bottom-0px">
                <label class="platformSelect su-dblack">Select the type of the platform you want to add.</label>
            </h6>
        </div>
        <div *ngIf="addSearchClient" class="heading-source margin-TB-auto-LR-0">
            Manage {{addingSc}} Search Client
        </div>
        <div class="margin-left-auto">
            <button type="button" (click)="selectPlatformPage();searchText='';selectedSCtype='All';" class="buttonSecondary backButton"
                routerLinkActive="router-link-active" routerLink="/dashboard/generate-search-client">Back To Search Client</button>
        </div>
    </div>
    <div class="sectionDiv" *ngIf="!addSearchClient">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12 sectionMainDiv padding-LR-0px" style="float: none;"
            *ngIf="!addSearchClient">
            <div class="su-pt-1 su-pb-1 padding-right-15px padding-left-15px display-flex align-items-center">
                <span class="su-dblack font-12 font_6  su-pr-1">Category :</span>
                <mat-select panelClass="categoryClass" class="changeCategory" disableOptionCentering [(value)]="selectedSCtype"
                  (selectionChange)="selectedSCtype = $event.value">
                  <mat-option *ngFor="let categoryName of allCategories" [value]="categoryName.type">
                    {{categoryName.type}}</mat-option>
                </mat-select>
                <div class="categoryInput margin-left-auto">
                  <input type="search" [(ngModel)]="searchText" class="cs-form font_5 su-dblack su-p-0" placeholder="Search" />
                </div>
            </div>
            <!--POPULAR CATEGORY-->
            <div *ngIf="selectedSCtype == 'All' && !searchText">
                <div class="su-dblack ad_font-18 font_6 su-pt-1 su-pb-1 padding-right-15px padding-left-15px">Popular</div>
                <ng-container *ngFor="let platform of platformTypes; let index = index;">
                  <div class="addContentSc col-lg-3 col-md-6 col-sm-12 col-xs-12" style="display: inline-block;"
                    *ngIf="SC_POPULAR_CATEGORY.includes(platform.id)">
                    <label class="enlarge cs-box cs-box-other" [ngClass]="{'enlarged': newPlatform.platformId == platform.id, 'csInstalled' : platform.installed == true}"
                    style="margin-top: 18px;" for="select-type-{{platform.id}}">
                        <img *ngIf="platform.installed == true" class="scAdded" src="assets/img/blue-tick.svg">
                        <img [src]="platform.large_image" alt="{{platform.name}}" class="largeImage">
                        <span>
                            <input type="submit" [(ngModel)]="newPlatform.platformId" name="platformType"
                            [value]="platform.id" id="select-type-{{platform.id}}" (click)="toggleAddClient(platform.id, platform.name)"
                            style="background: none; border: none;" />
                        </span>
                    </label>
                  </div>
                </ng-container>
            </div>
            <div *ngFor="let contentType of allSupportedSearchClientCategories">
                <ng-container *ngIf="contentType.type == selectedSCtype || selectedSCtype == 'All'">
                  <div *ngIf="(contentType.showCategory && searchText) || !searchText"
                    class="su-dblack ad_font-18 font_6 su-ml-1 su-mt-1"> {{contentType.type}}</div>
                  <ng-container
                    *ngFor="let platform of platformTypes | category: searchText: allSupportedSearchClientCategories: selectedSCtype">
                    <!--SHOW SEARCH CLIENTS UNDER THEIR RESPECTIVE CATEGORY-->
                    <ng-container *ngIf="platform.category == contentType.id && platform.group == 'Drupal' && platform.showDrupal">
                      <div class="addContentSc col-lg-3 col-md-6 col-sm-12 col-xs-12" style="display: inline-block;">
                        <label class="enlarge cs-box cs-box-other" [ngClass]="{'enlarged': newPlatform.platformId == platform.id, 'csInstalled' : platform.installed == true}"
                        style="margin-top: 18px;" for="select-type-{{platform.id}}">
                            <img *ngIf="platform.installed == true" class="scAdded" src="assets/img/blue-tick.svg">
                            <img [src]="platform.large_image" alt="{{platform.name}}" class="largeImage">
                            <span>
                                <input type="submit" [(ngModel)]="newPlatform.platformId" name="platformType"
                                [value]="platform.id" id="select-type-{{platform.id}}" (click)="toggleAddClient(platform.id, 'Drupal')"
                                style="background: none; border: none;" />
                            </span>
                        </label>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="platform.category == contentType.id && !platform.group && platform.id !== 33 ">
                        <div class="addContentSc col-lg-3 col-md-6 col-sm-12 col-xs-12" style="display: inline-block;">
                          <label class="enlarge cs-box cs-box-other" [ngClass]="{'enlarged': newPlatform.platformId == platform.id, 'csInstalled' : platform.installed == true}"
                          style="margin-top: 18px;" for="select-type-{{platform.id}}">
                              <img *ngIf="platform.installed == true" class="scAdded" src="assets/img/blue-tick.svg">
                              <img [src]="platform.large_image" alt="{{platform.name}}" class="largeImage">
                              <span>
                                  <input type="submit" [(ngModel)]="newPlatform.platformId" name="platformType"
                                  [value]="platform.id" id="select-type-{{platform.id}}" (click)="toggleAddClient(platform.id, platform.name)"
                                  style="background: none; border: none;" />
                              </span>
                          </label>
                        </div>         
                      </ng-container>
                  </ng-container>
                </ng-container>
            </div>
            <!--NO SEARCH CLIENT MATCHES THE SEARCH STRING-->
            <div *ngIf="(platformTypes | category: searchText: allSupportedSearchClientCategories: selectedSCtype)?.length == 0">
                <img class="no-contentSource no-contentMatch centered-align su-pt-5" src="assets/img/noContentMatch.svg">
                <!-- <img class="no-contentSource no-contentMatchDark centered-align su-pt-5" src="assets/img/noContentMatchDark.svg"> -->
            </div>
            <div class="su-pt-1 su-pb-1 padding-right-15px padding-left-15px font_5 font-12"
                [ngClass]="{'text-align-center  centered-align su-p-1 su-pb-4 connectCS':(platformTypes | category: searchText: allSupportedSearchClientCategories: selectedSCtype)?.length == 0}">
                <div class="su-dblack">Couldn’t find your search client?</div>
                <div class="line-ht t-underline"><a class="su-dblack" href="https://community.searchunify.com/hc/en-us/community/posts/new?community_post%5Btopic_id%5D=9407781887645"
                    target="_blank">Create an Idea in SearchUnify Community.</a></div>
            </div>
            <!--NO SEARCH CLIENT MATCHES THE SEARCH SECTION ENDS-->
        </div>
    </div>
    <div class="sectionDiv addClient" *ngIf="addSearchClient">
        <div>
            <div class="sectionHeading su-platform">Add Search Client</div>
        </div>
        <div class="su-info">
            Once you have selected the platform from the available option, enter the name for the platform in the
            textbox below (refer to image) and click ‘Save’
        </div>
        <div class="cls-jiveCred">
            <table class="table-style sectionMainDiv su-addClient" id="adding">
                <tbody>
                    <tr>
                        <td class= "w-50" >
                            <mat-form-field>
                                <input matInput name="platformname" [(ngModel)]="newPlatform.name"
                                    placeholder="Enter Platform Name" class="clientDetails"
                                    (ngModelChange)="addUniqueSearchClient(newPlatform)">
                            </mat-form-field>
                        </td>
                        <td>
                            <mat-form-field>
                                <input matInput name="client_href" [(ngModel)]="newPlatform.client_href"
                                    placeholder="Enter base url" class="clientDetails">
                            </mat-form-field>
                        </td>
                        <td *ngIf="newPlatform.platformId == 22">
                            <mat-form-field>
                                <input matInput name="channel_id" [(ngModel)]="newPlatform.channel_id"
                                    placeholder="Enter Slack Channel id" class="clientDetails"
                                    (ngModelChange)="checkSlackChannels(newPlatform.channel_id)">
                            </mat-form-field>
                        </td>
                    </tr>
                    <tr *ngIf="newPlatform.platformId == 31" class="table-row-vertical-align">
                        <td class="w-50">
                            <mat-form-field>
                                <mat-select placeholder="Select SSO*" [(ngModel)]="newPlatform.ssoConfig.type" name="SSO">
                                    <mat-option *ngFor="let sso of ssoTypes" [value]="sso.name">{{sso.name | titlecase}}</mat-option>
                                  </mat-select>
                            </mat-form-field>
                        </td>
                    </tr>
                    <tr *ngIf="isDrupalSc">
                        <td class= "w-50">
                            <mat-form-field>
                                <mat-select placeholder="Select Version" [(ngModel)]="selectedDrupalVersion"  name="Drupal" (selectionChange)="drupalSelectionChange($event)">
                                    <mat-option value="{{drupal.name}}" *ngFor="let drupal of drupalVersions">{{drupal.name | titlecase}}</mat-option> 
                                </mat-select>
                            </mat-form-field>
                        </td>
                    </tr>
                    <tr *ngIf="isKhoros">
                        <td class= "w-50">
                            <mat-form-field>
                                <mat-select placeholder="Select Version" [(ngModel)]="selectedKhorosVersion"  name="Khoros" (selectionChange)="khorosSelectionChange($event)">
                                    <mat-option value="{{khoros.name}}" *ngFor="let khoros of khorosVersions ">{{khoros.displayName}}</mat-option> 
                                </mat-select>
                            </mat-form-field>
                        </td>
                    </tr>

                    <tr hidden>
                        <td>
                            <label for="Search Experience" class="language-title">Search Experience</label>
                            <mat-radio-group class="language-dropdown" aria-label="Search Experience" [(ngModel)]="newPlatform.language">
                                <mat-radio-button value="angular">Legacy</mat-radio-button>
                                <mat-radio-button value="react">
                                    Imperium
                                    <i class="material-icons generate-sc-info-icon" matTooltip="Check for seamless, faster search experience">info</i>
                                </mat-radio-button>
                            </mat-radio-group>
                        </td>
                    </tr>
                    <tr hidden>
                        <td>
                            <label for="Search Experience" class="language-title">Choose your component type</label>
                            <mat-radio-group class="language-dropdown" aria-label="Search Experience" [(ngModel)]="newPlatform.language">
                                <mat-radio-button value="angular">Visualforce component</mat-radio-button>
                                <mat-radio-button value="lwcSfConsole">
                                    Lightning web component
                                    <i class="material-icons generate-sc-info-icon" matTooltip="Supported only for Lightning Experience.">info</i>
                                </mat-radio-button>
                            </mat-radio-group>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="w-100 sectionMainDiv float-left pl-3 custom-uid" *ngIf="suUserandGzuser">
                <div class="checkbox-options pr-2 display-flex align-items-center padding-bottom-10px">
                    <mat-checkbox [checked]="customUIDfield" (change)="isCustomUID($event)" class="enteruidcheckbox">
                        <span class="checkbox-label">
                            Enter custom UID
                        </span>
                    </mat-checkbox>
                    <div class="w-50 pr-2" *ngIf="customUIDfield">
                        <mat-form-field>
                            <input matInput name="platformname" [(ngModel)]="newPlatform.customUID" (ngModelChange)="isUIDvalid(newPlatform.customUID)">
                        </mat-form-field>
                        <p class="error-msg" *ngIf="isValidUID">Invlid UID format(It should be valid v4 UID)</p>
                    </div>
                </div>
                <div *ngIf="customUIDfield" class="text-bold-500">Note: <i class="text-bold-400 ad_font-12"> By checking this property you are acknowledging that you understand the consequences such as (but not limited to) tracking of analytics data in continuation to what may have been previously already tracked for this UID etc.  It is still recommended to uncheck the option and let the system generate a new uid.</i></div>
                
            </div>
            <div class="saveSearchClient">
                <button class="buttonPrimary btn-action-save"
                    [disabled]="!(newPlatform.platformId && newPlatform.name && newPlatform.name.trim().length > 0 && newPlatform.client_href && newPlatform.client_href.trim().length > 0 && uniqueSearchClient && !newSCSaveButtonClicked)"
                    (click)="addSearchClientCheck()">Save</button>
            </div>
        </div>

    </div>
</div>

<div *ngIf="!selectPlatform && !contentTypeHide" [hidden]="designerActive">
    <div class="topHeading">
        <div class="heading-source tabLabel">Manage Search Clients
        </div>
        <div class="definition">
            Search client may be defined as the platform-community, website, CMS or a support portal where you wish to
            install the search. It is possible to generate multiple search clients in SearchUnify.
        </div>
    </div>
    <div class="sectionDiv ad_generate_dark_mode">
        <div class="sectionHead display-flex">
            <div class="margin-TB-auto-LR-0">
                <div class="heading-source">Search Client</div>
                <div>
                    <label class="sectionLabel">Create and download the search client for specific platforms. Add a new
                        platform, edit the platform and delete it.
                    </label>
                </div>
            </div>
            <div class="margin-left-auto">
                <!-- Not required here -->
                <!-- <div> -->
                    <div *ngIf="isProdInstance && suUserandGzuser && enableSboxProdFeature" matTooltip="Search Client Migration Requests" matTooltipPosition="below"
                        class="migrationRequestIcon" (click)="getSearchClientsFromSandbox();openMigrationOverlay();">
                        <div class="migrationToProductionDot">
                        </div>
                        <img class="migrationReqImg">
                    </div>
                    
                    <!-- <button mat-raised-button class="buttonPrimary buttonPrimary-custom button-add-new-client"
                        (click)="selectPlatformPage();setAddedSearchClient();indexChanged(6)" id="stepAddNewContentSource">Add
                        New Search Client</button> -->
                
                        <button mat-raised-button class="buttonPrimary buttonPrimary-custom button-add-new-client"
                        (click)="selectPlatformPage();setAddedSearchClient();indexChanged(6)" id="stepAddNewContentSource">Add
                        New Search Client</button>
                    <button class="buttonPrimary buttonPrimary-custom1 ecoSystem hover-border" (click)="ecoSystemScreen(); ecoSystem = true;" routerLinkActive="router-link-active"  routerLink="/dashboard/generate-search-client/ecosystems"
                           >Ecosystem
                        <span class="new-label" *ngIf="newFeatureEcoSystemProperty !== true">
                            New
                        </span>
                    </button>
                <!-- </div> -->
            </div>
       
        </div>
        <div class="sectionMainDiv">
            <div [@listAnimation]="contentSources.length" class="table-responsive scope" *ngIf="!isLoadingAdded">
                <table class="table table-su">
                    <thead class="t-head">
                        <tr>

                            <th class="p-0">
                                <table class="w-100">
                                    <colgroup>
                                        <col width="26%">
                                        <col width="16%">
                                        <col width="18%">
                                        <col width="18%">
                                        <col width="22%">
                                    </colgroup>
                                    <tr>
                                        <th>Platform Name</th>
                                        <th>Type</th>
                                        <th>Created Date
                                            <i>
                                                <!-- <br> -->
                                                <ng-container *ngIf="(userTimeZone | timeZone : userTimeZone:'sidebar':'timezoneName').length > 16; else elseBlock">
                                                    <small matTooltip="{{ userTimeZone | timeZone : userTimeZone:'sidebar':'timezoneName' }}" matTooltipPosition="below">({{ (userTimeZone | timeZone : userTimeZone:"sidebar":'timezoneName').slice(0,18)+'...' }})</small> 
                                                </ng-container>
                                                <ng-template #elseBlock><small>({{ userTimeZone | timeZone : userTimeZone:'sidebar':'timezoneName' }})</small>
                                                </ng-template>
                                            </i>
                                        </th>
                                        <th>UID</th>
                                        <th>Actions</th>
            
                                    </tr>
                                </table>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let source of contentSources; let index = index;" class="row-types">
                            <ng-container *ngIf="source.ab_test_parent == null">
                                <td class="p-0">
                                    <table class="w-100">
                                        <colgroup>
                                            <col width="26%">
                                            <col width="16%">
                                            <col width="18%">
                                            <col width="18%">
                                            <col width="22%">
                                        </colgroup>
                                        <tr>
                                            <td class="search-client-detail su_name_alignment ad_position-relative" *ngIf="source.display"  style="word-break: break-word;">
                                                <div [ngStyle]="{'margin-top' : abTestStatus[index] && abTestStatus[index].status == 1 ? '11px' : '' }" class="user-details-sc" matTooltipPosition="right" matTooltip="Created by - {{source.scOwnerName}} &#13;Last Updated by - {{source.last_updated_by?source.last_updated_by: 'N/A' }}" matTooltipClass="su-user-details-SC" >
                                                    <svg width="20" height="21" viewBox="0 0 20 21">
                                                        <defs>
                                                        <clipPath id="clip-Icon">
                                                            <rect width="20" height="21"/>
                                                        </clipPath>
                                                        </defs>
                                                        <g id="Icon" clip-path="url(#clip-Icon)">
                                                        <g id="Group_4268" data-name="Group 4268" transform="translate(-340 -330)">
                                                            <g id="user_1_" data-name="user (1)" transform="translate(345.278 336.279)">
                                                            <g id="Group_4257" data-name="Group 4257" transform="translate(2.021 0)">
                                                                <g id="Group_4256" data-name="Group 4256">
                                                                <path id="Path_3617" data-name="Path 3617" d="M123.618,0a2.618,2.618,0,1,0,2.618,2.618A2.621,2.621,0,0,0,123.618,0Z" transform="translate(-121)" fill="#ccc"/>
                                                                </g>
                                                            </g>
                                                            <g id="Group_4259" data-name="Group 4259" transform="translate(0 5.818)">
                                                                <g id="Group_4258" data-name="Group 4258">
                                                                <path id="Path_3618" data-name="Path 3618" d="M39.1,301.129A4.1,4.1,0,0,0,36.257,300H35.02a4.1,4.1,0,0,0-2.844,1.129A3.7,3.7,0,0,0,31,303.821a.3.3,0,0,0,.309.291h8.659a.3.3,0,0,0,.309-.291A3.7,3.7,0,0,0,39.1,301.129Z" transform="translate(-31 -300)" fill="#ccc"/>
                                                                </g>
                                                            </g>
                                                            </g>
                                                            <g id="Ellipse_87" data-name="Ellipse 87" transform="translate(340 331)" fill="none" stroke="#ccc" stroke-width="1">
                                                            <circle cx="10" cy="10" r="10" stroke="none"/>
                                                            <circle cx="10" cy="10" r="9.5" fill="none"/>
                                                            </g>
                                                            <rect id="Rectangle_4348" data-name="Rectangle 4348" width="5" height="6" transform="translate(357 336)" fill="#fff"/>
                                                            <path id="Path_3619" data-name="Path 3619" d="M4.857,4.471V.387A.382.382,0,0,0,4.2.117L2.827,1.491,1.731,2.58.117,4.193a.387.387,0,0,0,.27.664H4.471A.382.382,0,0,0,4.857,4.471Z" transform="translate(355.418 331.721) rotate(11)" fill="#ccc"/>
                                                        </g>
                                                        </g>
                                                    </svg>
                                                </div> 
                                                <span *ngIf="suUserandGzuser">
                                                    <span class="ad_font-10 s3-supported ad_position-absolute" matTooltip="Docker Supported" *ngIf="source.sc_label == 'Docker'">Docker</span>
                                                    <span class="ad_font-10 s3-supported ad_position-absolute" matTooltip="CloudFront Supported" *ngIf="source.sc_label == 'CloudFront'"> CloudFront</span>
                                                    <span class="ad_font-10 s3-supported ad_position-absolute" matTooltip="Docker Supported" *ngIf="source.sc_label == null && source.s3_supported == 0">Docker</span>
                                                    <span class="ad_font-10 s3-supported ad_position-absolute" matTooltip="CloudFront Supported" *ngIf="source.sc_label == null && source.s3_supported == 1"> CloudFront</span>
                                                </span>                                                           
                                                <span matTooltip="{{source.name}}">{{source.name.length> 20 ? source.name.substring(0,20) + '...' : source.name }} </span>
                                                <span class="imperium-sc" *ngIf="source.language == 'react' && source.search_client_type_id != 8" matTooltip="Imperium Search Experience">
                                                    <svg width="15" height="21" viewBox="0 0 15 21">
                                                        <defs>
                                                        <clipPath id="clip-Light_1">
                                                            <rect width="15" height="21"/>
                                                        </clipPath>
                                                        </defs>
                                                        <g id="Light_1" clip-path="url(#clip-Light_1)">
                                                        <g id="Component_11_5" data-name="Component 11 – 5" transform="translate(1 0)">
                                                            <g id="Group_4244" data-name="Group 4244" transform="translate(-354.667 -338.474)">
                                                            <path id="Path_3610" data-name="Path 3610" d="M101.876,20.921l8.112-13.968a.439.439,0,0,0-.439-.656l-5.4.733L105.442.262a.22.22,0,0,0-.391-.174L97.075,10.616a.439.439,0,0,0,.4.7l5.949-.626-1.95,10.076A.22.22,0,0,0,101.876,20.921Z" transform="translate(257.682 338.473)" fill="#ffdc64"/>
                                                            <path id="Path_3611" data-name="Path 3611" d="M99.569,9.887a.182.182,0,0,1-.169-.278L105.364.067a.217.217,0,0,0-.313.037L97.075,10.633a.439.439,0,0,0,.4.7l5.949-.626-1.95,10.076a.214.214,0,0,0,.173.254l3.342-10.575a.732.732,0,0,0-.757-.951Z" transform="translate(257.682 338.457)" fill="#ffc850"/>
                                                            </g>
                                                        </g>
                                                        </g>
                                                    </svg>
                                                </span>
                                                <span *ngIf="source.language == 'lwcSfConsole' || source.language == 'lwcSfLightning'" matTooltip="Lightning Web Component">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                                        <defs>
                                                        <clipPath id="clip-Update_icon">
                                                            <rect width="24" height="24"/>
                                                        </clipPath>
                                                        </defs>
                                                        <g id="Update_icon" data-name="Update icon" clip-path="url(#clip-Update_icon)">
                                                        <g id="Group_13558" data-name="Group 13558" transform="translate(6124.59 -5218.631)">
                                                            <path id="Path_1" data-name="Path 1" d="M480.772,483.259h6.621l-4.339,7.762h4.174l-11.611,14.9L480.9,492.1h-3.783Z" transform="translate(-6593.575 4735.963)" fill="#00a0df"/>
                                                            <path id="Line_1" data-name="Line 1" d="M-.048,4.923a.452.452,0,0,1-.168-.033A.452.452,0,0,1-.468,4.3L1.339-.216a.452.452,0,0,1,.588-.252.452.452,0,0,1,.252.588L.371,4.639a.452.452,0,0,1-.419.285Z" transform="translate(-6112.813 5221.33)" fill="#fff"/>
                                                            <g id="Path_12049" data-name="Path 12049" transform="matrix(-0.669, -0.743, 0.743, -0.669, -6113.913, 5245.907)" fill="none" stroke-linecap="round" stroke-dasharray="15 19">
                                                            <path d="M9.212,0A9.212,9.212,0,1,1,0,9.212,9.212,9.212,0,0,1,9.212,0Z" stroke="none"/>
                                                            <path d="M 9.212320327758789 0 C 14.30014038085938 0 18.42464065551758 4.124500274658203 18.42464065551758 9.212320327758789 C 18.42464065551758 9.214945793151855 18.42463874816895 9.217587471008301 18.42463684082031 9.220212936401367 C 18.42463684082031 9.221144676208496 18.42463493347168 9.221925735473633 18.42463302612305 9.222857475280762 C 18.42463111877441 9.224607467651367 18.42462921142578 9.226351737976074 18.42462730407715 9.22810173034668 C 18.42462539672852 9.229032516479492 18.42462158203125 9.230414390563965 18.42461967468262 9.231346130371094 C 18.42461776733398 9.232890129089355 18.42461395263672 9.234434127807617 18.42461013793945 9.235979080200195 C 18.42460632324219 9.237220764160156 18.42460441589355 9.238199234008789 18.42460060119629 9.23944091796875 C 18.42459678649902 9.240899085998535 18.42459106445312 9.24236011505127 18.42458724975586 9.243817329406738 C 18.42458343505859 9.245059013366699 18.4245777130127 9.246334075927734 18.42457389831543 9.247574806213379 C 18.42456817626953 9.248946189880371 18.42456245422363 9.250303268432617 18.42455673217773 9.251674652099609 C 18.42455291748047 9.252915382385254 18.42454719543457 9.254184722900391 18.42454147338867 9.255425453186035 C 18.42453384399414 9.256795883178711 18.42452812194824 9.25816535949707 18.42452049255371 9.259535789489746 C 18.42451477050781 9.260776519775391 18.42450714111328 9.262197494506836 18.42450141906738 9.26343822479248 C 18.42449378967285 9.264743804931641 18.42448616027832 9.266036987304688 18.42447853088379 9.267342567443848 C 18.42447090148926 9.268583297729492 18.42446327209473 9.270052909851074 18.4244556427002 9.271293640136719 C 18.42444801330566 9.27244758605957 18.42444038391113 9.273629188537598 18.4244327545166 9.274784088134766 C 18.42439270019531 9.280674934387207 18.42434692382812 9.286816596984863 18.42429542541504 9.292704582214355 C 18.42429161071777 9.293244361877441 18.42428779602051 9.293778419494629 18.42428207397461 9.294317245483398 C 18.42426681518555 9.296176910400391 18.42425346374512 9.297547340393066 18.42423629760742 9.299406051635742 C 18.42422866821289 9.300344467163086 18.42421913146973 9.30126953125 18.4242115020752 9.302207946777344 C 18.42419624328613 9.303756713867188 18.4241771697998 9.305685997009277 18.42416000366211 9.307234764099121 C 18.42415237426758 9.308097839355469 18.42414283752441 9.308957099914551 18.42413520812988 9.309819221496582 C 18.42411613464355 9.311677932739258 18.42409706115723 9.313325881958008 18.4240779876709 9.315184593200684 C 18.42406845092773 9.315949440002441 18.4240608215332 9.316704750061035 18.42405128479004 9.317469596862793 C 18.42403221130371 9.319328308105469 18.42401123046875 9.321078300476074 18.42399024963379 9.32293701171875 C 18.42398071289062 9.32365894317627 18.42397308349609 9.324389457702637 18.42396354675293 9.325111389160156 C 18.42394065856934 9.326969146728516 18.42391967773438 9.328793525695801 18.42389488220215 9.33065128326416 C 18.42388725280762 9.331308364868164 18.42387771606445 9.331991195678711 18.42387008666992 9.332648277282715 C 18.42384338378906 9.33481502532959 18.42382049560547 9.336501121520996 18.42379188537598 9.338667869567871 C 18.42378807067871 9.338862419128418 18.42378616333008 9.339056968688965 18.42378425598145 9.339251518249512 C 18.42228317260742 9.450687408447266 18.41882133483887 9.561012268066406 18.41339683532715 9.671460151672363 C 18.41339302062988 9.67155647277832 18.41338729858398 9.671658515930176 18.41338348388672 9.671754837036133 C 18.41327857971191 9.67390251159668 18.41317558288574 9.675952911376953 18.4130687713623 9.678099632263184 C 18.41304016113281 9.678665161132812 18.41301345825195 9.679225921630859 18.41298484802246 9.679791450500488 C 18.41289329528809 9.681632041931152 18.41281127929688 9.68326473236084 18.41271781921387 9.685104370117188 C 18.41267013549805 9.686043739318848 18.41262245178223 9.68699836730957 18.41257476806641 9.687936782836914 C 18.41249656677246 9.689470291137695 18.41241455078125 9.691052436828613 18.4123363494873 9.692584991455078 C 18.41227722167969 9.693748474121094 18.41221618652344 9.694911956787109 18.41215705871582 9.696074485778809 C 18.4120922088623 9.697300910949707 18.41201400756836 9.698833465576172 18.41194915771484 9.70005989074707 C 18.4118709564209 9.701574325561523 18.41179084777832 9.703073501586914 18.41171264648438 9.704588890075684 C 18.41166305541992 9.705508232116699 18.41159629821777 9.706777572631836 18.41154861450195 9.707696914672852 C 18.41098022460938 9.718403816223145 18.4103946685791 9.72911262512207 18.4097900390625 9.739809989929199 C 18.39422988891602 10.01550960540771 18.15810966491699 10.22638893127441 17.88241004943848 10.21083068847656 C 17.6067008972168 10.19527053833008 17.39582061767578 9.959150314331055 17.41138076782227 9.683450698852539 C 17.42018127441406 9.527590751647949 17.42464065551758 9.369080543518066 17.42464065551758 9.212320327758789 C 17.42464065551758 4.684030532836914 13.74061012268066 1 9.212320327758789 1 C 8.936180114746094 1 8.712320327758789 0.7761402130126953 8.712320327758789 0.5 C 8.712320327758789 0.2238597869873047 8.936180114746094 0 9.212320327758789 0 C 9.212320327758789 0 9.212320327758789 0 9.212320327758789 0 Z M 2.050588607788086 3.750211715698242 C 2.148950576782227 3.75022029876709 2.248350143432617 3.779181480407715 2.335269927978516 3.839520454406738 C 2.56210994720459 3.997000694274902 2.618350028991699 4.308540344238281 2.460870742797852 4.53538990020752 C 1.505159378051758 5.912100791931152 1 7.529360771179199 1 9.212320327758789 C 1 10.64369010925293 1.373470306396484 12.052490234375 2.080020904541016 13.28643035888672 C 2.766070365905762 14.48453998565674 3.751330375671387 15.4992504119873 4.929300308227539 16.22084999084473 C 5.164770126342773 16.3651008605957 5.238730430603027 16.67292022705078 5.094480514526367 16.90839958190918 C 4.950425148010254 17.14355278015137 4.64322566986084 17.21761322021484 4.407876968383789 17.07415008544922 C 4.191971778869629 16.94193077087402 3.98206901550293 16.8011531829834 3.778379440307617 16.65212821960449 C 3.778379440307617 16.65212821960449 3.778316497802734 16.65208053588867 3.778316497802734 16.65208053588867 C 3.770867347717285 16.64663124084473 3.763501167297363 16.6412239074707 3.756068229675293 16.63575172424316 C 3.756068229675293 16.63575172424316 3.75599193572998 16.63569641113281 3.75599193572998 16.63569641113281 C 3.739270210266113 16.62338447570801 3.72274112701416 16.61112976074219 3.70610523223877 16.59870719909668 C 3.706029891967773 16.59865188598633 3.705898284912109 16.59855270385742 3.705821990966797 16.59849548339844 C 3.698428153991699 16.59297561645508 3.691365242004395 16.58768463134766 3.683988571166992 16.5821418762207 C 3.683837890625 16.58202934265137 3.683815956115723 16.58201217651367 3.68366527557373 16.58189964294434 C 3.676288604736328 16.57635688781738 3.66936206817627 16.57113647460938 3.662002563476562 16.56557273864746 C 3.66185188293457 16.56545829772949 3.661675453186035 16.5653247833252 3.661525726318359 16.56521224975586 C 3.659685134887695 16.56381988525391 3.6580810546875 16.56260681152344 3.656241416931152 16.56121444702148 C 3.656167030334473 16.5611572265625 3.65619945526123 16.56118202209473 3.656124114990234 16.56112480163574 C 3.650608062744141 16.55694770812988 3.645262718200684 16.55289077758789 3.639756202697754 16.54870223999023 C 3.639680862426758 16.54864501953125 3.639707565307617 16.54866409301758 3.639633178710938 16.54860687255859 C 3.637797355651855 16.54721069335938 3.636090278625488 16.54591178894043 3.634255409240723 16.54451370239258 C 3.634180068969727 16.54445648193359 3.634088516235352 16.54438591003418 3.634014129638672 16.5443286895752 C 3.628510475158691 16.54013633728027 3.623263359069824 16.53612899780273 3.617770195007324 16.53192329406738 C 3.617694854736328 16.5318660736084 3.617713928222656 16.53188133239746 3.617639541625977 16.53182411193848 C 3.616037368774414 16.53059768676758 3.614158630371094 16.5291576385498 3.612557411193848 16.52792930603027 C 3.612257957458496 16.52770042419434 3.61196231842041 16.52747344970703 3.611662864685059 16.52724456787109 C 3.604800224304199 16.52198219299316 3.597560882568359 16.51641464233398 3.590713500976562 16.51113319396973 C 3.590415000915527 16.51090240478516 3.59006404876709 16.5106315612793 3.589765548706055 16.51040077209473 C 3.582919120788574 16.50511932373047 3.575682640075684 16.49951934814453 3.568851470947266 16.49421691894531 C 3.568628311157227 16.49404335021973 3.56820011138916 16.49371147155762 3.567976951599121 16.49353790283203 C 3.56638240814209 16.49230003356934 3.564576148986816 16.49089813232422 3.562983512878418 16.48965835571289 C 3.562909126281738 16.48960113525391 3.562752723693848 16.48948097229004 3.562678337097168 16.48942184448242 C 3.557445526123047 16.48535346984863 3.552330017089844 16.48136711120605 3.547104835510254 16.47728729248047 C 3.546808242797852 16.47705459594727 3.54652214050293 16.47683143615723 3.546224594116211 16.47660064697266 C 3.544407844543457 16.47518157958984 3.543134689331055 16.47418594360352 3.541317939758301 16.47276496887207 C 3.541095733642578 16.47259140014648 3.540827751159668 16.47238159179688 3.540604591369629 16.47220611572266 C 3.535611152648926 16.46829986572266 3.530647277832031 16.46440887451172 3.525662422180176 16.46049118041992 C 3.525217056274414 16.46014213562012 3.524828910827637 16.45983695983887 3.524384498596191 16.45948791503906 C 3.523024559020996 16.45841979980469 3.521188735961914 16.45697402954102 3.519829750061035 16.45590591430664 C 3.51945972442627 16.45561408996582 3.51901912689209 16.45526695251465 3.518649101257324 16.45497512817383 C 3.513667106628418 16.45105361938477 3.508396148681641 16.44689559936523 3.503423690795898 16.44296455383301 C 3.503275871276855 16.44284820556641 3.503078460693359 16.44269180297852 3.502930641174316 16.44257545471191 C 3.501347541809082 16.44132423400879 3.499752044677734 16.44005966186523 3.498169898986816 16.43880844116211 C 3.497800827026367 16.43851470947266 3.497392654418945 16.43819236755371 3.497023582458496 16.43790054321289 C 3.495442390441895 16.43664741516113 3.493823051452637 16.43536376953125 3.492242813110352 16.43411064147949 C 3.492169380187988 16.43405151367188 3.492022514343262 16.43393707275391 3.491949081420898 16.43387794494629 C 3.486756324768066 16.42975997924805 3.48177433013916 16.4257984161377 3.476591110229492 16.42166900634766 C 3.476222038269043 16.4213752746582 3.475850105285645 16.42107963562012 3.475481986999512 16.42078590393066 C 3.473904609680176 16.41952896118164 3.472289085388184 16.41824150085449 3.470712661743164 16.41698265075684 C 3.470491409301758 16.41680717468262 3.470270156860352 16.41662979125977 3.470048904418945 16.41645240783691 C 3.465095520019531 16.41249847412109 3.45984935760498 16.40830230712891 3.454903602600098 16.40433883666992 C 3.454609870910645 16.40410232543945 3.454313278198242 16.40386581420898 3.454019546508789 16.40362930297852 C 3.452445983886719 16.40236854553223 3.450888633728027 16.40111923217773 3.449315071105957 16.39985656738281 C 3.448947906494141 16.39956092834473 3.448577880859375 16.39926338195801 3.448210716247559 16.39896965026855 C 3.443268775939941 16.39500045776367 3.438611030578613 16.3912525177002 3.433677673339844 16.38727378845215 C 3.433237075805664 16.38691902160645 3.432908058166504 16.38665390014648 3.432468414306641 16.38629913330078 C 3.430898666381836 16.38503265380859 3.429628372192383 16.38400650024414 3.428059577941895 16.38274002075195 C 3.427546501159668 16.38232421875 3.426955223083496 16.3818473815918 3.426443099975586 16.38143348693848 C 3.424874305725098 16.38016510009766 3.423599243164062 16.37913513183594 3.422031402587891 16.37786674499512 C 3.421957969665527 16.3778076171875 3.422061920166016 16.37789154052734 3.421989440917969 16.37783241271973 C 3.418853759765625 16.37529563903809 3.415515899658203 16.37259101867676 3.412383079528809 16.37004852294922 C 3.411871910095215 16.3696346282959 3.411521911621094 16.36935043334961 3.411009788513184 16.36893463134766 C 3.409443855285645 16.3676643371582 3.408359527587891 16.36678314208984 3.406794548034668 16.36551094055176 C 3.406208992004395 16.36503601074219 3.405618667602539 16.36455535888672 3.405034065246582 16.36408042907715 C 3.403469085693359 16.36280822753906 3.402322769165039 16.36187553405762 3.400758743286133 16.36060333251953 C 3.400320053100586 16.3602466583252 3.399855613708496 16.35986709594727 3.399417877197266 16.35951042175293 C 3.396737098693848 16.35732841491699 3.394060134887695 16.35514640808105 3.391382217407227 16.35296058654785 C 3.390798568725586 16.35248374938965 3.390218734741211 16.35201072692871 3.38963508605957 16.35153388977051 C 3.388296127319336 16.35044097900391 3.387063026428223 16.34943199157715 3.385724067687988 16.34833908081055 C 3.384995460510254 16.34774208068848 3.384264945983887 16.34714508056641 3.383536338806152 16.34654998779297 C 3.382198333740234 16.34545516967773 3.380979537963867 16.34445762634277 3.379642486572266 16.34336280822754 C 3.378986358642578 16.34282493591309 3.378298759460449 16.34226226806641 3.377642631530762 16.34172439575195 C 3.374746322631836 16.33935165405273 3.372326850891113 16.33736610412598 3.36943244934082 16.33498954772949 C 3.369141578674316 16.33475112915039 3.368679046630859 16.33437156677246 3.368388175964355 16.33413124084473 C 3.367053031921387 16.33303451538086 3.365939140319824 16.33211898803711 3.364603996276855 16.33102035522461 C 3.363876342773438 16.33042335510254 3.363016128540039 16.32971572875977 3.362288475036621 16.32911682128906 C 3.360954284667969 16.32801818847656 3.359858512878418 16.32711601257324 3.358525276184082 16.32601737976074 C 3.357797622680664 16.32541847229004 3.356891632080078 16.32467079162598 3.356164932250977 16.32407188415527 C 3.354831695556641 16.32297325134277 3.353848457336426 16.32216262817383 3.35251522064209 16.3210620880127 C 3.351861953735352 16.32052230834961 3.351252555847168 16.32001876831055 3.350598335266113 16.31947898864746 C 3.348377227783203 16.31764602661133 3.345715522766113 16.31544494628906 3.34349536895752 16.31360816955566 C 3.34151554107666 16.31196975708008 3.339471817016602 16.31027603149414 3.337491035461426 16.30863571166992 C 3.336620330810547 16.30791473388672 3.335822105407715 16.30725288391113 3.334952354431152 16.30653190612793 C 3.333621978759766 16.30542755126953 3.332762718200684 16.30471611022949 3.331433296203613 16.30361175537109 C 3.33049201965332 16.30282974243164 3.329545974731445 16.30204391479492 3.328603744506836 16.30126190185547 C 3.326610565185547 16.29960441589355 3.324684143066406 16.29800415039062 3.322691917419434 16.29634475708008 C 3.320646286010742 16.29463958740234 3.318573951721191 16.29291534423828 3.3165283203125 16.29121017456055 C 3.31455135345459 16.2895622253418 3.312417984008789 16.28778076171875 3.310445785522461 16.28613471984863 C 3.309506416320801 16.28535079956055 3.308480262756348 16.28449249267578 3.307540893554688 16.28370666503906 C 3.305773735046387 16.28223037719727 3.303382873535156 16.28022956848145 3.301616668701172 16.27875137329102 C 3.29957389831543 16.27704048156738 3.297492980957031 16.27529716491699 3.295452117919922 16.27358436584473 C 3.293483734130859 16.27193260192871 3.29133415222168 16.27012634277344 3.289364814758301 16.26847457885742 C 3.28849983215332 16.26774787902832 3.287549018859863 16.26694869995117 3.286684989929199 16.26622009277344 C 3.284701347351074 16.26455307006836 3.282608985900879 16.26279067993164 3.28062629699707 16.26112174987793 C 3.279689788818359 16.26033401489258 3.278934478759766 16.25969696044922 3.277998924255371 16.25890731811523 C 3.276678085327148 16.25779342651367 3.275795936584473 16.25704956054688 3.274476051330566 16.25593566894531 C 3.273612022399902 16.25520706176758 3.272751808166504 16.25448226928711 3.271888732910156 16.25375175476074 C 3.27056884765625 16.25263786315918 3.269833564758301 16.25201606750488 3.268514633178711 16.25090026855469 C 3.266480445861816 16.24918174743652 3.264357566833496 16.24738502502441 3.262322425842285 16.24566268920898 C 3.261532783508301 16.24499320983887 3.260708808898926 16.24429512023926 3.259919166564941 16.24362564086914 C 3.257941246032715 16.24195098876953 3.255643844604492 16.2400016784668 3.253667831420898 16.23832511901855 C 3.251636505126953 16.23659896850586 3.249680519104004 16.23493957519531 3.247653961181641 16.23321533203125 C 3.245553016662598 16.2314281463623 3.243605613708496 16.22977066040039 3.241506576538086 16.22798347473145 C 3.240646362304688 16.22724914550781 3.239679336547852 16.22642517089844 3.238819122314453 16.22569274902344 C 3.236846923828125 16.2240104675293 3.234743118286133 16.22221565246582 3.232771873474121 16.22053146362305 C 3.230817794799805 16.21886444091797 3.228829383850098 16.2171630859375 3.226877212524414 16.21549224853516 C 3.224781036376953 16.21370124816895 3.222824096679688 16.2120246887207 3.220727920532227 16.21022796630859 C 3.219655990600586 16.20931053161621 3.218585014343262 16.2083911895752 3.217513084411621 16.20747184753418 C 3.215983390808105 16.2061595916748 3.214420318603516 16.20481872558594 3.212891578674316 16.20350456237793 C 3.211677551269531 16.20246124267578 3.210481643676758 16.20143508911133 3.209267616271973 16.20039176940918 C 3.207324981689453 16.1987190246582 3.205037117004395 16.19675064086914 3.203094482421875 16.19507789611816 C 3.200933456420898 16.19321632385254 3.198970794677734 16.19152450561523 3.196811676025391 16.18966102600098 C 3.195940017700195 16.18890762329102 3.194878578186035 16.18799209594727 3.19400691986084 16.18724060058594 C 3.192153930664062 16.18564033508301 3.190315246582031 16.18405151367188 3.188464164733887 16.18244934082031 C 3.186236381530762 16.18052291870117 3.184526443481445 16.17904090881348 3.182299613952637 16.1771125793457 C 3.180289268493652 16.17537117004395 3.17802906036377 16.17340850830078 3.176020622253418 16.17166519165039 C 3.175150871276855 16.17091178894043 3.174267768859863 16.17014503479004 3.173398971557617 16.16938972473145 C 3.171550750732422 16.16778373718262 3.169621467590332 16.16610717773438 3.16777515411377 16.16450119018555 C 3.166906356811523 16.16374397277832 3.166180610656738 16.16311073303223 3.165311813354492 16.16235542297363 C 3.164105415344238 16.16130447387695 3.162799835205078 16.1601676940918 3.161593437194824 16.15911674499512 C 3.159589767456055 16.15736961364746 3.157517433166504 16.15556144714355 3.155515670776367 16.15381240844727 C 3.154647827148438 16.15305519104004 3.153606414794922 16.15214538574219 3.152739524841309 16.15138816833496 C 3.15103816986084 16.14990043640137 3.149413108825684 16.14847946166992 3.147712707519531 16.14699172973633 C 3.146845817565918 16.14623260498047 3.146018028259277 16.1455078125 3.14515209197998 16.14474868774414 C 3.143806457519531 16.14356994628906 3.142339706420898 16.14228439331055 3.140995025634766 16.14110565185547 C 3.138998031616211 16.13935279846191 3.136882781982422 16.13749694824219 3.134885787963867 16.1357421875 C 3.134020805358887 16.13498306274414 3.133071899414062 16.1341495513916 3.132207870483398 16.13338851928711 C 3.130934715270996 16.13227081298828 3.129673957824707 16.13116073608398 3.128401756286621 16.13004112243652 C 3.127321243286133 16.12908935546875 3.125825881958008 16.12777328491211 3.124746322631836 16.12682151794434 C 3.123333930969238 16.12557792663574 3.121892929077148 16.12430763244629 3.120481491088867 16.12306213378906 C 3.119617462158203 16.12230110168457 3.118800163269043 16.12157821655273 3.117937088012695 16.12081718444824 C 3.116737365722656 16.1197566986084 3.115622520446777 16.11877250671387 3.114423751831055 16.11771392822266 C 3.113345146179199 16.11676025390625 3.112790107727051 16.11627006530762 3.111712455749512 16.11531639099121 C 3.110372543334961 16.11413192749023 3.109146118164062 16.11304664611816 3.107807159423828 16.11186027526855 C 3.107160568237305 16.11128807067871 3.106354713439941 16.11057472229004 3.105708122253418 16.1100025177002 C 3.103806495666504 16.10831642150879 3.101829528808594 16.10656356811523 3.099928855895996 16.10487747192383 C 3.099283218383789 16.10430526733398 3.098401069641113 16.1035213470459 3.097755432128906 16.10294723510742 C 3.096487998962402 16.10182189941406 3.095109939575195 16.1005973815918 3.093843460083008 16.09947204589844 C 3.092983245849609 16.09870719909668 3.09223747253418 16.09804344177246 3.091377258300781 16.0972785949707 C 3.090110778808594 16.09615135192871 3.088825225830078 16.09500694274902 3.087560653686523 16.09387969970703 C 3.086700439453125 16.09311485290527 3.086097717285156 16.09257888793945 3.085238456726074 16.09181213378906 C 3.083340644836426 16.09012222290039 3.081425666809082 16.08841323852539 3.079529762268066 16.08671951293945 C 3.078670501708984 16.08595275878906 3.078250885009766 16.08557891845703 3.077392578125 16.08481025695801 C 3.076128959655762 16.08368301391602 3.074847221374512 16.0825366973877 3.07358455657959 16.08140754699707 C 3.072726249694824 16.08064079284668 3.071792602539062 16.07980537414551 3.070934295654297 16.07903671264648 C 3.069742202758789 16.07796859741211 3.068446159362793 16.07680892944336 3.067254066467285 16.07574081420898 C 3.066396713256836 16.07497215270996 3.065740585327148 16.07438468933105 3.064883232116699 16.07361602783203 C 3.062991142272949 16.07192039489746 3.061017990112305 16.07015037536621 3.059126853942871 16.06845092773438 C 3.058698654174805 16.06806755065918 3.058045387268066 16.06748008728027 3.0576171875 16.06709480285645 C 3.056146621704102 16.06577301025391 3.054616928100586 16.06439971923828 3.05314826965332 16.06307601928711 C 3.052291870117188 16.06230735778809 3.051552772521973 16.06164169311523 3.050697326660156 16.06087112426758 C 3.049508094787598 16.05980110168457 3.048235893249512 16.05865478515625 3.047046661376953 16.05758285522461 C 3.046191215515137 16.05681228637695 3.045380592346191 16.05608177185059 3.044525146484375 16.0553092956543 C 3.042987823486328 16.05392265319824 3.041379928588867 16.05247116088867 3.039843559265137 16.05108451843262 C 3.03941535949707 16.05069732666016 3.038775444030762 16.05011940002441 3.038348197937012 16.04973411560059 C 3.036532402038574 16.04809379577637 3.034648895263672 16.04639053344727 3.032835006713867 16.04474830627441 C 3.031980514526367 16.04397583007812 3.031431198120117 16.04347801208496 3.030577659606934 16.04270553588867 C 3.029252052307129 16.04150390625 3.027975082397461 16.04034805297852 3.026650428771973 16.03914642333984 C 3.026010513305664 16.03856658935547 3.025027275085449 16.03767395019531 3.024387359619141 16.03709411621094 C 3.022923469543457 16.0357666015625 3.021421432495117 16.03440093994141 3.01995849609375 16.03307342529297 C 3.019318580627441 16.03249168395996 3.019022941589355 16.03222465515137 3.018383979797363 16.03164291381836 C 3.016433715820312 16.02987098693848 3.014487266540527 16.02809906005859 3.012538909912109 16.02632522583008 C 3.011899948120117 16.0257453918457 3.011238098144531 16.0251407623291 3.010599136352539 16.02455902099609 C 3.009207725524902 16.02329254150391 3.007809638977051 16.02201843261719 3.00641918182373 16.02075004577637 C 3.005568504333496 16.01997375488281 3.005111694335938 16.01955795288086 3.004261016845703 16.0187816619873 C 3.002801895141602 16.01744842529297 3.00133228302002 16.01610565185547 2.999873161315918 16.01477432250977 C 2.999447822570801 16.01438522338867 2.998666763305664 16.013671875 2.998242378234863 16.01328277587891 C 2.996296882629395 16.01150512695312 2.994275093078613 16.0096549987793 2.992331504821777 16.00787544250488 C 2.991695404052734 16.00729179382324 2.99139404296875 16.00701522827148 2.990756988525391 16.00643157958984 C 2.989230155944824 16.00503349304199 2.987741470336914 16.00366973876953 2.98621654510498 16.00226974487305 C 2.985579490661621 16.00168609619141 2.984866142272949 16.00102996826172 2.984230041503906 16.00044631958008 C 2.982773780822754 15.99910926818848 2.981307029724121 15.99776172637939 2.979851722717285 15.99642467498779 C 2.979216575622559 15.99584007263184 2.978784561157227 15.99544334411621 2.978148460388184 15.99485874176025 C 2.976140022277832 15.99301147460938 2.974148750305176 15.99117946624756 2.972141265869141 15.98933029174805 C 2.971929550170898 15.98913478851318 2.971554756164551 15.98878955841064 2.971343040466309 15.98859405517578 C 2.969613075256348 15.98699951171875 2.967889785766602 15.98541069030762 2.966160774230957 15.98381423950195 C 2.96552562713623 15.98322868347168 2.964814186096191 15.98257160186768 2.964179992675781 15.9819860458374 C 2.962727546691895 15.98064517974854 2.961291313171387 15.9793176651001 2.959839820861816 15.97797584533691 C 2.959417343139648 15.97758483886719 2.958541870117188 15.97677612304688 2.95811939239502 15.97638511657715 C 2.956254005432129 15.97465896606445 2.954463958740234 15.97300243377686 2.952600479125977 15.97127532958984 C 2.952388763427734 15.97107982635498 2.95223331451416 15.9709358215332 2.952022552490234 15.97074031829834 C 2.950020790100098 15.96888542175293 2.948036193847656 15.96704387664795 2.946036338806152 15.96518707275391 C 2.945403099060059 15.964599609375 2.945055961608887 15.96427726745605 2.944423675537109 15.96368980407715 C 2.942906379699707 15.9622802734375 2.941325187683105 15.96081066131592 2.939809799194336 15.95940113067627 C 2.939177513122559 15.95881271362305 2.938831329345703 15.95849132537842 2.938199043273926 15.9579029083252 C 2.936338424682617 15.95617198944092 2.93443775177002 15.95440196990967 2.932579040527344 15.95266914367676 C 2.932579040527344 15.95266914367676 2.932177543640137 15.95229530334473 2.932177543640137 15.95229530334473 C 2.930112838745117 15.9503698348999 2.928033828735352 15.94843006134033 2.925970077514648 15.94650363922119 C 2.925549507141113 15.94611072540283 2.925241470336914 15.94582176208496 2.924819946289062 15.9454288482666 C 2.92317008972168 15.94388675689697 2.921502113342285 15.94232749938965 2.919853210449219 15.9407844543457 C 2.919432640075684 15.94039154052734 2.918707847595215 15.93971347808838 2.918288230895996 15.93932056427002 C 2.916707992553711 15.93784141540527 2.915003776550293 15.93624496459961 2.913424491882324 15.93476486206055 C 2.913004875183105 15.93437099456787 2.912535667419434 15.93393135070801 2.912116050720215 15.93353843688965 C 2.910124778747559 15.93167114257812 2.908040046691895 15.92971420288086 2.906050682067871 15.92784595489502 C 2.905840873718262 15.92764854431152 2.905447959899902 15.92727851867676 2.905238151550293 15.92708206176758 C 2.903454780578613 15.92540645599365 2.901750564575195 15.92380332946777 2.899969100952148 15.92212677001953 C 2.899340629577637 15.92153549194336 2.89915943145752 15.92136478424072 2.898530960083008 15.92077350616455 C 2.896818161010742 15.91916084289551 2.895062446594238 15.91750621795654 2.893350601196289 15.91589164733887 C 2.893141746520996 15.91569519042969 2.892627716064453 15.91521072387695 2.89241886138916 15.91501331329346 C 2.89029598236084 15.91301155090332 2.888270378112793 15.91109848022461 2.886150360107422 15.90909576416016 C 2.886150360107422 15.90909576416016 2.886119842529297 15.90906715393066 2.886119842529297 15.90906715393066 C 2.884136199951172 15.90719223022461 2.882107734680176 15.90527248382568 2.880125999450684 15.90339660644531 C 2.879707336425781 15.903000831604 2.879207611083984 15.90252780914307 2.878789901733398 15.90213203430176 C 2.877150535583496 15.90057849884033 2.875463485717773 15.89898014068604 2.873825073242188 15.89742660522461 C 2.873407363891602 15.89702987670898 2.87297248840332 15.89661693572998 2.872554779052734 15.89622116088867 C 2.86845874786377 15.89233493804932 2.864419937133789 15.88849639892578 2.860331535339355 15.88460254669189 C 2.860122680664062 15.88440418243408 2.859396934509277 15.88371181488037 2.859188079833984 15.88351345062256 C 2.857484817504883 15.88189125061035 2.855719566345215 15.88020801544189 2.85401725769043 15.87858390808105 C 2.85360050201416 15.87818622589111 2.85333251953125 15.87793064117432 2.852916717529297 15.87753295898438 C 2.850942611694336 15.87564945220947 2.84896183013916 15.87375831604004 2.846989631652832 15.87187194824219 C 2.846989631652832 15.87187194824219 2.846827507019043 15.87171745300293 2.846827507019043 15.87171745300293 C 2.844718933105469 15.86970138549805 2.842685699462891 15.86775493621826 2.840579032897949 15.86573791503906 C 2.840164184570312 15.8653392791748 2.840130805969238 15.86530685424805 2.839715003967285 15.86490917205811 C 2.838016510009766 15.86328125 2.83622932434082 15.86156749725342 2.834531784057617 15.859938621521 C 2.83411693572998 15.85953998565674 2.833516120910645 15.85896396636963 2.833101272583008 15.85856533050537 C 2.83133602142334 15.85687065124512 2.829705238342285 15.85530376434326 2.82794189453125 15.85360813140869 C 2.827733993530273 15.85340881347656 2.827296257019043 15.85298728942871 2.827088356018066 15.85278797149658 C 2.825054168701172 15.85083103179932 2.822949409484863 15.84880542755127 2.820916175842285 15.84684658050537 C 2.820709228515625 15.84664726257324 2.820577621459961 15.84651947021484 2.820370674133301 15.84632015228271 C 2.818540573120117 15.84455680847168 2.816691398620605 15.84277248382568 2.814863204956055 15.84100723266602 C 2.814449310302734 15.84060764312744 2.813981056213379 15.84015655517578 2.813567161560059 15.83975601196289 C 2.811943054199219 15.83818817138672 2.810259819030762 15.83656120300293 2.808636665344238 15.83499145507812 C 2.808222770690918 15.83459091186523 2.807833671569824 15.83421516418457 2.80742073059082 15.833815574646 C 2.803362846374512 15.82988929748535 2.799295425415039 15.82594680786133 2.795245170593262 15.82201385498047 C 2.794832229614258 15.82161331176758 2.794594764709473 15.82138252258301 2.794181823730469 15.82098197937012 C 2.792427062988281 15.81927680969238 2.79061222076416 15.81751251220703 2.788858413696289 15.81580638885498 C 2.788652420043945 15.81560516357422 2.788259506225586 15.81522369384766 2.788053512573242 15.81502342224121 C 2.783872604370117 15.81095504760742 2.77973747253418 15.80692291259766 2.775564193725586 15.80284595489502 C 2.775358200073242 15.80264472961426 2.775298118591309 15.80258655548096 2.775092124938965 15.8023853302002 C 2.773207664489746 15.80054473876953 2.771270751953125 15.79864978790283 2.769387245178223 15.79680728912354 C 2.769181251525879 15.79660606384277 2.76883602142334 15.79626750946045 2.768630027770996 15.79606628417969 C 2.764528274536133 15.7920503616333 2.760275840759277 15.78788089752197 2.756181716918945 15.78385829925537 C 2.755976676940918 15.78365612030029 2.755912780761719 15.78359413146973 2.755707740783691 15.78339195251465 C 2.753828048706055 15.78154563903809 2.751900672912598 15.77964973449707 2.750022888183594 15.77780151367188 C 2.749612808227539 15.77739715576172 2.749639511108398 15.77742290496826 2.749229431152344 15.77701950073242 C 2.747217178344727 15.77503871917725 2.745279312133789 15.7731294631958 2.743269920349121 15.77114677429199 C 2.743064880371094 15.77094459533691 2.74334716796875 15.7712230682373 2.743142127990723 15.77102088928223 C 2.738988876342773 15.76692390441895 2.734822273254395 15.7628059387207 2.730676651000977 15.75870132446289 C 2.730472564697266 15.75849914550781 2.730120658874512 15.75815010070801 2.729916572570801 15.75794792175293 C 2.727977752685547 15.756028175354 2.726014137268066 15.75408172607422 2.724077224731445 15.75216007232666 C 2.724077224731445 15.75216007232666 2.723834037780762 15.75191879272461 2.723834037780762 15.75191879272461 C 2.719625473022461 15.74774360656738 2.715575218200684 15.7437162399292 2.71137523651123 15.73953247070312 C 2.710967063903809 15.73912620544434 2.711084365844727 15.73924350738525 2.710677146911621 15.73883724212646 C 2.708809852600098 15.73697757720947 2.707009315490723 15.73518180847168 2.705144882202148 15.73332118988037 C 2.704940795898438 15.73311805725098 2.70465087890625 15.73282909393311 2.704447746276855 15.73262596130371 C 2.700318336486816 15.7285041809082 2.696162223815918 15.7243480682373 2.69204044342041 15.72021865844727 C 2.691837310791016 15.72001457214355 2.691899299621582 15.72007751464844 2.691696166992188 15.71987342834473 C 2.689767837524414 15.717942237854 2.68787956237793 15.71604824066162 2.685954093933105 15.71411514282227 C 2.685750961303711 15.71391105651855 2.685442924499512 15.71360206604004 2.685239791870117 15.71339797973633 C 2.67906379699707 15.70719718933105 2.672861099243164 15.70095252990723 2.666703224182129 15.69473552703857 C 2.666501045227051 15.69453048706055 2.666412353515625 15.6944408416748 2.66620922088623 15.69423675537109 C 2.659985542297363 15.68795108795166 2.653894424438477 15.68178272247314 2.647688865661621 15.67547988891602 C 2.647486686706543 15.67527484893799 2.647280693054199 15.6750659942627 2.647078514099121 15.67486095428467 C 2.645098686218262 15.67284870147705 2.643095970153809 15.67081260681152 2.641117095947266 15.66879940032959 C 2.641117095947266 15.66879940032959 2.641056060791016 15.66873741149902 2.641056060791016 15.66873741149902 C 2.636900901794434 15.66450881958008 2.632773399353027 15.66030025482178 2.628626823425293 15.6560640335083 C 2.628626823425293 15.6560640335083 2.628115653991699 15.65554141998291 2.628115653991699 15.65554141998291 C 2.626206398010254 15.65359115600586 2.624138832092285 15.65147590637207 2.622231483459473 15.6495246887207 C 2.622031211853027 15.64931869506836 2.622241973876953 15.64953517913818 2.622041702270508 15.64932918548584 C 2.617833137512207 15.64502143859863 2.613767623901367 15.6408519744873 2.609567642211914 15.63653564453125 C 2.609366416931152 15.63632965087891 2.609552383422852 15.63652038574219 2.60935115814209 15.63631343841553 C 2.607448577880859 15.63435745239258 2.60550594329834 15.63236045837402 2.603605270385742 15.63040256500244 C 2.60340404510498 15.6301965713501 2.603102684020996 15.62988662719727 2.602902412414551 15.62967967987061 C 2.598837852478027 15.6254940032959 2.594720840454102 15.62124729156494 2.590664863586426 15.6170539855957 C 2.590664863586426 15.6170539855957 2.590521812438965 15.61690616607666 2.590521812438965 15.61690616607666 C 2.588624954223633 15.61494541168213 2.586538314819336 15.61278629302979 2.58464241027832 15.61082363128662 C 2.584443092346191 15.61061668395996 2.584283828735352 15.61045265197754 2.584084510803223 15.61024475097656 C 2.577940940856934 15.6038818359375 2.571946144104004 15.59765529632568 2.565820693969727 15.59127426147461 C 2.565621376037598 15.59106636047363 2.565447807312012 15.59088611602783 2.565248489379883 15.59067821502686 C 2.559123992919922 15.58429718017578 2.552950859069824 15.57784748077393 2.546844482421875 15.57144832611084 C 2.546844482421875 15.57144832611084 2.546767234802246 15.57136726379395 2.546767234802246 15.57136726379395 C 2.54468822479248 15.56918907165527 2.542679786682129 15.56708240509033 2.540602684020996 15.56490230560303 C 2.540602684020996 15.56490230560303 2.54056453704834 15.56486129760742 2.54056453704834 15.56486129760742 C 2.536410331726074 15.5605001449585 2.532292366027832 15.55616855621338 2.52814769744873 15.55179977416992 C 2.52814769744873 15.55179977416992 2.528099060058594 15.55174827575684 2.528099060058594 15.55174827575684 C 2.526026725769043 15.54956340789795 2.524044036865234 15.54747200012207 2.521973609924316 15.54528522491455 C 2.521973609924316 15.54528522491455 2.521873474121094 15.54517936706543 2.521873474121094 15.54517936706543 C 2.515727043151855 15.53868770599365 2.509651184082031 15.53225326538086 2.503523826599121 15.52574443817139 C 2.503326416015625 15.52553462982178 2.503302574157715 15.52550888061523 2.503105163574219 15.52530002593994 C 2.496977806091309 15.51879024505615 2.490907669067383 15.51232242584229 2.484798431396484 15.50579452514648 C 2.484798431396484 15.50579452514648 2.484720230102539 15.50571155548096 2.484720230102539 15.50571155548096 C 2.470316886901855 15.49032020568848 2.456027030944824 15.47494697570801 2.441727638244629 15.45945739746094 C 2.441727638244629 15.45945739746094 2.441693305969238 15.4594202041626 2.441693305969238 15.4594202041626 C 2.429501533508301 15.44621276855469 2.417291641235352 15.43290710449219 2.405174255371094 15.41962814331055 C 2.404979705810547 15.41941547393799 2.405111312866211 15.41955947875977 2.404916763305664 15.41934585571289 C 2.372505187988281 15.38381767272949 2.34033203125 15.34799766540527 2.3084716796875 15.31196403503418 C 2.3084716796875 15.31196403503418 2.30842399597168 15.31190967559814 2.30842399597168 15.31190967559814 C 2.268518447875977 15.26677799224854 2.229084014892578 15.22129058837891 2.190057754516602 15.17537689208984 C 2.190057754516602 15.17537689208984 2.190048217773438 15.17536544799805 2.190048217773438 15.17536544799805 C 2.133460998535156 15.10879135131836 2.077854156494141 15.04146671295166 2.02314567565918 14.97328186035156 C 2.02314567565918 14.97328186035156 2.023044586181641 14.97315692901611 2.023044586181641 14.97315692901611 C 2.017385482788086 14.96610355377197 2.011806488037109 14.95912933349609 2.006166458129883 14.95205879211426 C 2.006166458129883 14.95205879211426 2.006126403808594 14.95200824737549 2.006126403808594 14.95200824737549 C 1.998607635498047 14.94258117675781 1.991191864013672 14.93324375152588 1.983709335327148 14.92378616333008 C 1.983709335327148 14.92378616333008 1.983606338500977 14.92365741729736 1.983606338500977 14.92365741729736 C 1.972383499145508 14.90947151184082 1.961299896240234 14.89537906646729 1.950159072875977 14.88112545013428 C 1.950159072875977 14.88112545013428 1.950035095214844 14.88096809387207 1.950035095214844 14.88096809387207 C 1.944463729858398 14.87384033203125 1.938983917236328 14.86680793762207 1.933433532714844 14.85966491699219 C 1.933433532714844 14.85966491699219 1.93339729309082 14.85961818695068 1.93339729309082 14.85961818695068 C 1.925996780395508 14.85009288787842 1.918661117553711 14.84061527252197 1.911298751831055 14.8310604095459 C 1.911298751831055 14.8310604095459 1.911252975463867 14.83100128173828 1.911252975463867 14.83100128173828 C 1.905786514282227 14.82390975952148 1.900379180908203 14.81687164306641 1.894933700561523 14.80976390838623 C 1.894758224487305 14.80953502655029 1.894721984863281 14.80948829650879 1.894546508789062 14.80925941467285 C 1.88916015625 14.80222702026367 1.883790969848633 14.79519653320312 1.878423690795898 14.78814697265625 C 1.87824821472168 14.78791809082031 1.878236770629883 14.78790283203125 1.878063201904297 14.787672996521 C 1.876348495483398 14.78542327880859 1.874578475952148 14.78309631347656 1.872867584228516 14.78084468841553 C 1.872867584228516 14.78084468841553 1.872743606567383 14.78067970275879 1.872743606567383 14.78067970275879 C 1.869150161743164 14.77595138549805 1.865533828735352 14.77118301391602 1.861949920654297 14.76644802093506 C 1.861949920654297 14.76644802093506 1.861686706542969 14.76609802246094 1.861686706542969 14.76609802246094 C 1.859979629516602 14.76384353637695 1.858226776123047 14.76152420043945 1.856521606445312 14.75926685333252 C 1.856348037719727 14.75903701782227 1.856485366821289 14.75921821594238 1.856311798095703 14.75898838043213 C 1.85267448425293 14.75417423248291 1.849191665649414 14.74955272674561 1.845565795898438 14.74473094940186 C 1.845392227172852 14.74450016021729 1.84553337097168 14.74468803405762 1.845359802246094 14.74445724487305 C 1.843660354614258 14.74219703674316 1.841989517211914 14.7399730682373 1.840290069580078 14.73771190643311 C 1.840118408203125 14.73748111724854 1.840066909790039 14.7374153137207 1.839895248413086 14.73718452453613 C 1.834575653076172 14.7300968170166 1.829330444335938 14.72308921813965 1.824031829833984 14.71598625183105 C 1.823858261108398 14.71575546264648 1.823745727539062 14.71560478210449 1.823574066162109 14.71537399291992 C 1.818330764770508 14.70834541320801 1.813167572021484 14.70140266418457 1.807943344116211 14.69435882568359 C 1.807771682739258 14.69412803649902 1.80735969543457 14.69357109069824 1.807186126708984 14.69333839416504 C 1.805559158325195 14.69114208221436 1.803937911987305 14.68895435333252 1.802310943603516 14.6867561340332 C 1.802310943603516 14.6867561340332 1.802146911621094 14.68653583526611 1.802146911621094 14.68653583526611 C 1.798614501953125 14.68176078796387 1.795169830322266 14.67709636688232 1.791645050048828 14.67231464385986 C 1.791475296020508 14.67208194732666 1.791313171386719 14.6718635559082 1.791141510009766 14.671630859375 C 1.789575576782227 14.66950607299805 1.787981033325195 14.66733932495117 1.786417007446289 14.66521263122559 C 1.786245346069336 14.6649808883667 1.786052703857422 14.66471672058105 1.785881042480469 14.66448497772217 C 1.782417297363281 14.65977478027344 1.779037475585938 14.65516948699951 1.775583267211914 14.65045261383057 C 1.775411605834961 14.65021991729736 1.775140762329102 14.64984798431396 1.774969100952148 14.64961528778076 C 1.773408889770508 14.64748477935791 1.77189826965332 14.64541912078857 1.770339965820312 14.64328765869141 C 1.770170211791992 14.6430549621582 1.769956588745117 14.6427640914917 1.769786834716797 14.6425313949585 C 1.766336441040039 14.63781070709229 1.762884140014648 14.63307762145996 1.759443283081055 14.62834930419922 C 1.759443283081055 14.62834930419922 1.75910758972168 14.62789058685303 1.75910758972168 14.62789058685303 C 1.757499694824219 14.62567901611328 1.756025314331055 14.62365341186523 1.754417419433594 14.62143993377686 C 1.754247665405273 14.62120723724365 1.75383186340332 14.62063503265381 1.753662109375 14.62040138244629 C 1.750226974487305 14.6156702041626 1.746862411499023 14.61102867126465 1.743434906005859 14.60629081726074 C 1.743265151977539 14.60605716705322 1.743419647216797 14.60627174377441 1.743251800537109 14.60603809356689 C 1.741647720336914 14.60382175445557 1.739986419677734 14.60152149200439 1.738384246826172 14.59930419921875 C 1.738216400146484 14.59906959533691 1.737924575805664 14.59866619110107 1.737754821777344 14.59843158721924 C 1.736154556274414 14.59621429443359 1.734516143798828 14.59394359588623 1.732917785644531 14.59172439575195 C 1.732917785644531 14.59172439575195 1.732736587524414 14.59147262573242 1.732736587524414 14.59147262573242 C 1.729316711425781 14.58672714233398 1.725969314575195 14.58207035064697 1.722558975219727 14.57731914520264 C 1.722223281860352 14.57684993743896 1.722057342529297 14.57662010192871 1.721721649169922 14.57615089416504 C 1.720237731933594 14.57408142089844 1.718799591064453 14.57207584381104 1.717317581176758 14.57000541687012 C 1.71714973449707 14.56977081298828 1.716913223266602 14.5694408416748 1.716745376586914 14.56920623779297 C 1.713340759277344 14.56445121765137 1.710010528564453 14.55979061126709 1.706613540649414 14.55502891540527 C 1.706447601318359 14.55479335784912 1.706192016601562 14.55443572998047 1.706024169921875 14.55420112609863 C 1.704545974731445 14.55212688446045 1.703121185302734 14.55012702941895 1.701644897460938 14.54805183410645 C 1.701309204101562 14.54758167266846 1.701162338256836 14.54737377166748 1.700826644897461 14.54690361022949 C 1.697490692138672 14.54221439361572 1.694215774536133 14.53760242462158 1.690887451171875 14.53290748596191 C 1.690553665161133 14.53243637084961 1.690465927124023 14.53230953216553 1.690132141113281 14.53183841705322 C 1.688713073730469 14.52983665466309 1.687404632568359 14.52798843383789 1.685989379882812 14.52598571777344 C 1.68565559387207 14.52551460266113 1.685283660888672 14.52498912811279 1.68494987487793 14.52451801300049 C 1.683534622192383 14.52251434326172 1.682125091552734 14.5205192565918 1.680709838867188 14.51851558685303 C 1.680543899536133 14.51828002929688 1.680213928222656 14.51781177520752 1.680046081542969 14.51757621765137 C 1.678415298461914 14.51526355743408 1.676717758178711 14.51285457611084 1.675086975097656 14.51054000854492 C 1.674921035766602 14.51030445098877 1.674833297729492 14.51017951965332 1.674667358398438 14.50994300842285 C 1.673200607299805 14.50786018371582 1.671747207641602 14.5057954788208 1.670282363891602 14.50371170043945 C 1.669950485229492 14.50323867797852 1.669559478759766 14.50268268585205 1.669227600097656 14.50221061706543 C 1.667818069458008 14.50020217895508 1.666500091552734 14.49832630157471 1.665092468261719 14.49631786346436 C 1.664760589599609 14.49584579467773 1.664680480957031 14.49573230743408 1.664348602294922 14.49525928497314 C 1.662670135498047 14.49286460876465 1.661102294921875 14.49062728881836 1.659423828125 14.48822975158691 C 1.659423828125 14.48822975158691 1.659137725830078 14.48781967163086 1.659137725830078 14.48781967163086 C 1.657623291015625 14.48565483093262 1.656162261962891 14.48356437683105 1.654647827148438 14.48139762878418 C 1.654317855834961 14.48092460632324 1.653888702392578 14.48031044006348 1.653558731079102 14.47983741760254 C 1.652261734008789 14.47797966003418 1.650959014892578 14.47611141204834 1.649663925170898 14.47425365447998 C 1.649333953857422 14.47377967834473 1.648956298828125 14.47323989868164 1.648626327514648 14.4727668762207 C 1.645442962646484 14.4681978225708 1.642240524291992 14.46359252929688 1.639064788818359 14.45901870727539 C 1.638736724853516 14.45854377746582 1.638277053833008 14.45788288116455 1.637948989868164 14.4574089050293 C 1.63665771484375 14.45554733276367 1.635414123535156 14.4537525177002 1.634122848510742 14.45189094543457 C 1.633794784545898 14.451416015625 1.633369445800781 14.45080184936523 1.633041381835938 14.45032691955566 C 1.631589889526367 14.44823265075684 1.630060195922852 14.44602203369141 1.628612518310547 14.44392585754395 C 1.628446578979492 14.44368743896484 1.628440856933594 14.44367790222168 1.628276824951172 14.44344139099121 C 1.626667022705078 14.44111156463623 1.625099182128906 14.43884086608887 1.623491287231445 14.43651008605957 C 1.623163223266602 14.43603420257568 1.622825622558594 14.43554306030273 1.62249755859375 14.43506813049316 C 1.621212005615234 14.43320274353027 1.619997024536133 14.43144035339355 1.61871337890625 14.42957496643066 C 1.618221282958984 14.42886066436768 1.617942810058594 14.4284553527832 1.617452621459961 14.42774200439453 C 1.616115570068359 14.4257984161377 1.614776611328125 14.42385292053223 1.613441467285156 14.42190742492676 C 1.613277435302734 14.42166900634766 1.612922668457031 14.42115306854248 1.612758636474609 14.42091464996338 C 1.611156463623047 14.41858100891113 1.609560012817383 14.41625499725342 1.607959747314453 14.41391944885254 C 1.607795715332031 14.41368103027344 1.607410430908203 14.41311645507812 1.607246398925781 14.41287803649902 C 1.605913162231445 14.41093158721924 1.604593276977539 14.40900421142578 1.603261947631836 14.40705680847168 C 1.602935791015625 14.40657997131348 1.602291107177734 14.40563774108887 1.601964950561523 14.40516090393066 C 1.600793838500977 14.40344715118408 1.599674224853516 14.40180969238281 1.598505020141602 14.40009498596191 C 1.598014831542969 14.39937973022461 1.597646713256836 14.39883804321289 1.597158432006836 14.39812278747559 C 1.595615386962891 14.39586162567139 1.594141006469727 14.39369869232178 1.592599868774414 14.39143562316895 C 1.592273712158203 14.39095878601074 1.592147827148438 14.39077186584473 1.591823577880859 14.39029502868652 C 1.590547561645508 14.3884220123291 1.589189529418945 14.38642406463623 1.587915420532227 14.38455009460449 C 1.587427139282227 14.38383293151855 1.587057113647461 14.38328742980957 1.586570739746094 14.38257122039795 C 1.585403442382812 14.3808536529541 1.584211349487305 14.37909698486328 1.583045959472656 14.37737846374512 C 1.582721710205078 14.37689971923828 1.582168579101562 14.37608623504639 1.581844329833984 14.37560844421387 C 1.58030891418457 14.37334251403809 1.578725814819336 14.37100601196289 1.577190399169922 14.36873817443848 C 1.577190399169922 14.36873817443848 1.577098846435547 14.36860275268555 1.577098846435547 14.36860275268555 C 1.575511932373047 14.36625671386719 1.574054718017578 14.36409950256348 1.572467803955078 14.36175155639648 C 1.572145462036133 14.36127281188965 1.571722030639648 14.36064529418945 1.57139778137207 14.36016654968262 C 1.570182800292969 14.35836601257324 1.569047927856445 14.3566837310791 1.567834854125977 14.35488319396973 C 1.567350387573242 14.35416412353516 1.566965103149414 14.35359382629395 1.566482543945312 14.35287570953369 C 1.565162658691406 14.35091781616211 1.563901901245117 14.34904384613037 1.562583923339844 14.34708499908447 C 1.562261581420898 14.34660530090332 1.562154769897461 14.3464469909668 1.561832427978516 14.34596824645996 C 1.560358047485352 14.34377384185791 1.55882453918457 14.34149169921875 1.557350158691406 14.33929538726807 C 1.556867599487305 14.33857536315918 1.556339263916016 14.33778762817383 1.555856704711914 14.33706855773926 C 1.55485725402832 14.33557891845703 1.553750991821289 14.3339262008667 1.552751541137695 14.33243560791016 C 1.552268981933594 14.33171558380127 1.551597595214844 14.33071136474609 1.551115036010742 14.32999134063721 C 1.550012588500977 14.32834243774414 1.548934936523438 14.32673263549805 1.547832489013672 14.32508373260498 C 1.547512054443359 14.32460403442383 1.546846389770508 14.32360744476318 1.546524047851562 14.32312774658203 C 1.545055389404297 14.3209285736084 1.543561935424805 14.31868934631348 1.542095184326172 14.31648826599121 C 1.541614532470703 14.31576728820801 1.541175842285156 14.31510925292969 1.540695190429688 14.31438827514648 C 1.539699554443359 14.31289482116699 1.538713455200195 14.31141376495361 1.5377197265625 14.3099193572998 C 1.537078857421875 14.30895805358887 1.536369323730469 14.30789184570312 1.535728454589844 14.30692863464355 C 1.534893035888672 14.30567073822021 1.533935546875 14.30422973632812 1.533098220825195 14.30297088623047 C 1.532459259033203 14.30200862884521 1.531822204589844 14.30104827880859 1.531183242797852 14.30008602142334 C 1.529825210571289 14.29804039001465 1.528415679931641 14.29591464996338 1.527059555053711 14.29386711120605 C 1.526420593261719 14.29290390014648 1.52580451965332 14.29197311401367 1.525167465209961 14.29100894927979 C 1.524332046508789 14.28974914550781 1.523502349853516 14.28849411010742 1.522666931152344 14.28723335266113 C 1.521871566772461 14.2860279083252 1.521236419677734 14.28506851196289 1.520441055297852 14.28386306762695 C 1.519659042358398 14.28268051147461 1.51899528503418 14.28167533874512 1.518215179443359 14.28049278259277 C 1.517578125 14.27952766418457 1.516574859619141 14.2780065536499 1.515937805175781 14.2770414352417 C 1.51500129699707 14.27562141418457 1.514152526855469 14.27433395385742 1.513217926025391 14.27291297912598 C 1.512264251708984 14.27146530151367 1.511211395263672 14.26986694335938 1.510259628295898 14.26841831207275 C 1.509376525878906 14.26707649230957 1.508457183837891 14.26567840576172 1.507574081420898 14.26433563232422 C 1.506782531738281 14.26312828063965 1.506135940551758 14.26214408874512 1.505342483520508 14.26093673706055 C 1.504617691040039 14.25983047485352 1.503850936889648 14.2586612701416 1.503124237060547 14.25755596160889 C 1.50233268737793 14.25634765625 1.501640319824219 14.25529289245605 1.500848770141602 14.25408363342285 C 1.500123977661133 14.25297737121582 1.499349594116211 14.25179290771484 1.498624801635742 14.25068664550781 C 1.497516632080078 14.24899387359619 1.496400833129883 14.24728870391846 1.495294570922852 14.24559593200684 C 1.494363784790039 14.24417114257812 1.493474960327148 14.24281120300293 1.492546081542969 14.24138736724854 C 1.491756439208984 14.24017715454102 1.491096496582031 14.23916625976562 1.490306854248047 14.23795604705811 C 1.489583969116211 14.23684883117676 1.488845825195312 14.23571586608887 1.488124847412109 14.2346076965332 C 1.487335205078125 14.23339653015137 1.486625671386719 14.2323055267334 1.485836029052734 14.23109436035156 C 1.485115051269531 14.2299861907959 1.48436164855957 14.22882843017578 1.483640670776367 14.2277193069458 C 1.482852935791016 14.22650814056396 1.482269287109375 14.22560977935791 1.481481552124023 14.22439765930176 C 1.480194091796875 14.2224178314209 1.478977203369141 14.22054195404053 1.477691650390625 14.21856021881104 C 1.476905822753906 14.21734809875488 1.475934982299805 14.21584796905518 1.475149154663086 14.21463489532471 C 1.474479675292969 14.21360397338867 1.473909378051758 14.21272087097168 1.473241806030273 14.21168994903564 C 1.472455978393555 14.21047687530518 1.471624374389648 14.20919227600098 1.470840454101562 14.20797920227051 C 1.470172882080078 14.20694732666016 1.469532012939453 14.20595455169678 1.468864440917969 14.20492267608643 C 1.468080520629883 14.20370864868164 1.467279434204102 14.20246982574463 1.466495513916016 14.20125579833984 C 1.465726852416992 14.20006465911865 1.465023040771484 14.19897079467773 1.464254379272461 14.19777965545654 C 1.462844848632812 14.19559288024902 1.461647033691406 14.19373321533203 1.460239410400391 14.1915454864502 C 1.459625244140625 14.19059181213379 1.459011077880859 14.18963813781738 1.458396911621094 14.18868446350098 C 1.457614898681641 14.18746757507324 1.456630706787109 14.18593597412109 1.455848693847656 14.18472003936768 C 1.455287933349609 14.18384552001953 1.454616546630859 14.18280029296875 1.454055786132812 14.18192672729492 C 1.453273773193359 14.18070983886719 1.452423095703125 14.17938232421875 1.451642990112305 14.17816543579102 C 1.450979232788086 14.17713165283203 1.450403213500977 14.17623138427734 1.449739456176758 14.17519569396973 C 1.448024749755859 14.17251873016357 1.446701049804688 14.17045211791992 1.444988250732422 14.16777229309082 C 1.444530487060547 14.1670560836792 1.44407844543457 14.16634845733643 1.443620681762695 14.1656322479248 C 1.442686080932617 14.16417026519775 1.441778182983398 14.16274833679199 1.440845489501953 14.16128635406494 C 1.440387725830078 14.160569190979 1.439937591552734 14.15986442565918 1.439481735229492 14.15914726257324 C 1.438549041748047 14.15768432617188 1.437570571899414 14.15615272521973 1.436639785766602 14.15468978881836 C 1.436233520507812 14.15405178070068 1.435861587524414 14.15346622467041 1.435455322265625 14.15282821655273 C 1.433437347412109 14.149658203125 1.431356430053711 14.14638519287109 1.429340362548828 14.14321231842041 C 1.429189682006836 14.14297294616699 1.429042816162109 14.14274311065674 1.428890228271484 14.14250373840332 C 1.427961349487305 14.14103889465332 1.426946640014648 14.13943767547607 1.426017761230469 14.13797187805176 C 1.425561904907227 14.1372537612915 1.425144195556641 14.13659572601318 1.424688339233398 14.13587760925293 C 1.423761367797852 14.1344108581543 1.422832489013672 14.13294696807861 1.421905517578125 14.1314811706543 C 1.421451568603516 14.13076210021973 1.42108154296875 14.13017654418945 1.420625686645508 14.12945747375488 C 1.417535781860352 14.1245698928833 1.414169311523438 14.11923503875732 1.411087036132812 14.11434078216553 C 1.410734176635742 14.1137809753418 1.410430908203125 14.11330032348633 1.410078048706055 14.11273956298828 C 1.409154891967773 14.11127090454102 1.408056259155273 14.10952568054199 1.407133102416992 14.10805702209473 C 1.406730651855469 14.1074161529541 1.406373977661133 14.10684967041016 1.405971527099609 14.10620880126953 C 1.402740478515625 14.10106658935547 1.399280548095703 14.09554481506348 1.396059036254883 14.09039497375488 C 1.395908355712891 14.09015464782715 1.395648956298828 14.0897388458252 1.395498275756836 14.08949851989746 C 1.394424438476562 14.08778190612793 1.393533706665039 14.08635425567627 1.392461776733398 14.08463668823242 C 1.392061233520508 14.0839958190918 1.391761779785156 14.08351898193359 1.391361236572266 14.08287620544434 C 1.386768341064453 14.07551574707031 1.382213592529297 14.06819343566895 1.377639770507812 14.06081962585449 C 1.377389907836914 14.06041717529297 1.377227783203125 14.06015586853027 1.376977920532227 14.05975341796875 C 1.372406005859375 14.05237770080566 1.367504119873047 14.0444450378418 1.362951278686523 14.03705596923828 C 1.362802505493164 14.03681373596191 1.362676620483398 14.03660774230957 1.362527847290039 14.0363655090332 C 1.357671737670898 14.02848243713379 1.353055953979492 14.02096366882324 1.348224639892578 14.01306533813477 C 1.348175048828125 14.01298522949219 1.348178863525391 14.01298904418945 1.348129272460938 14.01290893554688 C 1.346920013427734 14.0109338760376 1.345785140991211 14.00907516479492 1.344577789306641 14.00710010528564 C 1.344577789306641 14.00710010528564 1.344474792480469 14.00693035125732 1.344474792480469 14.00693035125732 C 1.339649200439453 13.99902820587158 1.334924697875977 13.99126625061035 1.330121994018555 13.98334884643555 C 1.330121994018555 13.98334884643555 1.330062866210938 13.98325157165527 1.330062866210938 13.98325157165527 C 0.6528530120849609 12.86681747436523 0.2061843872070312 11.59509181976318 0.05601119995117188 10.23397541046143 C 0.05601119995117188 10.23397541046143 0.05600929260253906 10.23395824432373 0.05600929260253906 10.23395824432373 C 0.05547714233398438 10.22913074493408 0.05496025085449219 10.22441959381104 0.05443572998046875 10.21959114074707 C 0.05442428588867188 10.21949291229248 0.05442237854003906 10.21946716308594 0.05441093444824219 10.21936798095703 C 0.05388641357421875 10.21453952789307 0.0533905029296875 10.20994472503662 0.05287361145019531 10.20511341094971 C 0.05286216735839844 10.20501518249512 0.05286026000976562 10.20499324798584 0.05284881591796875 10.20489406585693 C 0.05233192443847656 10.20006275177002 0.0518341064453125 10.19538974761963 0.05132484436035156 10.19055557250977 C 0.05132484436035156 10.19055557250977 0.05131340026855469 10.19044876098633 0.05131340026855469 10.19044876098633 C 0.05080413818359375 10.18561458587646 0.05031204223632812 10.1809196472168 0.04980850219726562 10.1760835647583 C 0.04979896545410156 10.17598533630371 0.0498046875 10.1760368347168 0.04979324340820312 10.17593860626221 C 0.04929161071777344 10.17110252380371 0.04881095886230469 10.1664571762085 0.04831695556640625 10.16161918640137 C 0.04830551147460938 10.16152000427246 0.04830551147460938 10.16150569915771 0.0482940673828125 10.16140651702881 C 0.04804801940917969 10.1589879989624 0.04781913757324219 10.15675449371338 0.04757308959960938 10.15433502197266 C 0.04756355285644531 10.15423679351807 0.04755020141601562 10.15410423278809 0.04754066467285156 10.1540060043335 C 0.04655838012695312 10.14432811737061 0.04562950134277344 10.13505363464355 0.044677734375 10.1253662109375 C 0.04465866088867188 10.125168800354 0.04465293884277344 10.12510967254639 0.0446319580078125 10.12491130828857 C 0.04415702819824219 10.12006855010986 0.04372978210449219 10.11569976806641 0.04326057434082031 10.11085414886475 C 0.04324150085449219 10.11065673828125 0.04323768615722656 10.11060523986816 0.04321861267089844 10.11040687561035 C 0.04274940490722656 10.10556125640869 0.042327880859375 10.10117053985596 0.04186630249023438 10.09632301330566 C 0.04184722900390625 10.09612464904785 0.04184150695800781 10.09606456756592 0.04182243347167969 10.09586715698242 C 0.04136085510253906 10.09101867675781 0.04095458984375 10.08670234680176 0.04050064086914062 10.08185291290283 C 0.0404815673828125 10.08165454864502 0.04046821594238281 10.08151817321777 0.0404510498046875 10.08131980895996 C 0.03999710083007812 10.07647037506104 0.03960227966308594 10.07221412658691 0.03915596008300781 10.06736183166504 C 0.03912734985351562 10.06706428527832 0.03912544250488281 10.06703472137451 0.03909683227539062 10.06673717498779 C 0.0386505126953125 10.06188488006592 0.03825950622558594 10.05758762359619 0.03782081604003906 10.05273342132568 C 0.037811279296875 10.05263423919678 0.03778648376464844 10.0523624420166 0.03777694702148438 10.0522632598877 C 0.0373382568359375 10.04740810394287 0.03695106506347656 10.04307270050049 0.03651809692382812 10.03821659088135 C 0.03650093078613281 10.03801822662354 0.03648948669433594 10.03788471221924 0.03647232055664062 10.03768634796143 C 0.03625679016113281 10.03525829315186 0.03606224060058594 10.03305339813232 0.03584671020507812 10.03062438964844 C 0.03584671020507812 10.03062438964844 0.03583908081054688 10.03053760528564 0.03583908081054688 10.03053760528564 C 0.03541183471679688 10.02567958831787 0.03500175476074219 10.02097511291504 0.03458213806152344 10.01611614227295 C 0.03458213806152344 10.01611614227295 0.03455924987792969 10.01586246490479 0.03455924987792969 10.01586246490479 C 0.03434944152832031 10.01343250274658 0.03415489196777344 10.01117134094238 0.03394699096679688 10.00874042510986 C 0.03394699096679688 10.00874042510986 0.03394699096679688 10.00872421264648 0.03394699096679688 10.00872421264648 C 0.0337371826171875 10.00629329681396 0.03353309631347656 10.0038890838623 0.03332710266113281 10.00145816802979 C 0.03332710266113281 10.00145816802979 0.03332328796386719 10.00140571594238 0.03332328796386719 10.00140571594238 C 0.03290939331054688 9.996543884277344 0.03251075744628906 9.991794586181641 0.03210639953613281 9.986930847167969 C 0.03210639953613281 9.986930847167969 0.03209114074707031 9.986752510070801 0.03209114074707031 9.986752510070801 C 0.03168678283691406 9.981888771057129 0.03131484985351562 9.977382659912109 0.03091812133789062 9.972516059875488 C 0.03090095520019531 9.972316741943359 0.03088951110839844 9.972165107727051 0.03087234497070312 9.971966743469238 C 0.03047561645507812 9.967100143432617 0.03013801574707031 9.962934494018555 0.02974891662597656 9.958065986633301 C 0.02972412109375 9.957767486572266 0.02971076965332031 9.957599639892578 0.02968788146972656 9.957301139831543 C 0.029296875 9.952433586120605 0.02895927429199219 9.948178291320801 0.02857780456542969 9.943307876586914 C 0.02857017517089844 9.943207740783691 0.0285491943359375 9.942931175231934 0.02853965759277344 9.942831993103027 C 0.02815818786621094 9.937961578369141 0.02780723571777344 9.933431625366211 0.02743148803710938 9.928559303283691 C 0.02742385864257812 9.928459167480469 0.02742385864257812 9.928447723388672 0.02741622924804688 9.928348541259766 C 0.02722930908203125 9.925911903381348 0.02705955505371094 9.923698425292969 0.02687263488769531 9.921261787414551 C 0.02686691284179688 9.921161651611328 0.02684593200683594 9.920903205871582 0.02683830261230469 9.920804023742676 C 0.02665328979492188 9.918366432189941 0.02650833129882812 9.916451454162598 0.02632522583007812 9.914013862609863 C 0.02631759643554688 9.913914680480957 0.02630805969238281 9.913793563842773 0.02630043029785156 9.913694381713867 C 0.02575111389160156 9.90638256072998 0.02523231506347656 9.899404525756836 0.02470016479492188 9.892087936401367 C 0.02468490600585938 9.891888618469238 0.02467155456542969 9.891711235046387 0.024658203125 9.891511917114258 C 0.02430152893066406 9.88663387298584 0.02399063110351562 9.882309913635254 0.02364158630371094 9.877429962158203 C 0.02362823486328125 9.877229690551758 0.02362251281738281 9.877140998840332 0.02360725402832031 9.876941680908203 C 0.02326011657714844 9.872061729431152 0.02295875549316406 9.867788314819336 0.02261734008789062 9.862905502319336 C 0.02260398864746094 9.862706184387207 0.02258110046386719 9.862394332885742 0.0225677490234375 9.862194061279297 C 0.02222824096679688 9.857312202453613 0.02193260192871094 9.853024482727051 0.02159881591796875 9.848139762878418 C 0.02159309387207031 9.848039627075195 0.02157783508300781 9.847821235656738 0.02157020568847656 9.847721099853516 C 0.02123832702636719 9.842837333679199 0.02094078063964844 9.838440895080566 0.02061653137207031 9.833555221557617 C 0.02060890197753906 9.833455085754395 0.02058982849121094 9.833145141601562 0.02058219909667969 9.833045959472656 C 0.02025794982910156 9.828159332275391 0.01997184753417969 9.823822021484375 0.01965522766113281 9.818933486938477 C 0.01964759826660156 9.818833351135254 0.01962471008300781 9.818476676940918 0.01961898803710938 9.818376541137695 C 0.01931953430175781 9.813794136047363 0.01901626586914062 9.809062957763672 0.01872634887695312 9.804477691650391 C 0.018707275390625 9.804178237915039 0.01868820190429688 9.803894996643066 0.01866912841796875 9.803595542907715 C 0.01851463317871094 9.801150321960449 0.01838874816894531 9.799129486083984 0.0182342529296875 9.796683311462402 C 0.01822853088378906 9.79658317565918 0.01822662353515625 9.796561241149902 0.01822090148925781 9.796462059020996 C 0.01806831359863281 9.794015884399414 0.01794624328613281 9.792071342468262 0.01779556274414062 9.78962516784668 C 0.01778411865234375 9.789424896240234 0.01777458190917969 9.789271354675293 0.01776123046875 9.789071083068848 C 0.01761054992675781 9.786624908447266 0.01748085021972656 9.784531593322754 0.01733207702636719 9.782084465026855 C 0.01732063293457031 9.78188419342041 0.01731681823730469 9.781815528869629 0.017303466796875 9.781615257263184 C 0.01715469360351562 9.779169082641602 0.01705169677734375 9.777470588684082 0.01690483093261719 9.775023460388184 C 0.0168914794921875 9.774823188781738 0.01688003540039062 9.774604797363281 0.01686668395996094 9.774405479431152 C 0.01671981811523438 9.771957397460938 0.01659965515136719 9.769940376281738 0.01645278930664062 9.76749324798584 C 0.01644134521484375 9.767292976379395 0.01642799377441406 9.767063140869141 0.01641654968261719 9.766862869262695 C 0.01627159118652344 9.764415740966797 0.0161590576171875 9.762510299682617 0.01601409912109375 9.760062217712402 C 0.01600837707519531 9.75996208190918 0.01600837707519531 9.75994873046875 0.01600265502929688 9.759848594665527 C 0.01585960388183594 9.757400512695312 0.01572990417480469 9.755187034606934 0.01558876037597656 9.752737998962402 C 0.01557731628417969 9.752537727355957 0.01557540893554688 9.752497673034668 0.01556205749511719 9.752297401428223 C 0.01542091369628906 9.749848365783691 0.01530265808105469 9.747773170471191 0.01516151428222656 9.74532413482666 C 0.01516151428222656 9.74532413482666 0.01515388488769531 9.745181083679199 0.01515388488769531 9.745181083679199 C 0.0150146484375 9.742731094360352 0.01490402221679688 9.740764617919922 0.01476478576660156 9.738314628601074 C 0.01474761962890625 9.738015174865723 0.01472854614257812 9.737663269042969 0.01471138000488281 9.737362861633301 C 0.01445388793945312 9.732769012451172 0.01420021057128906 9.728206634521484 0.01394844055175781 9.723610877990723 C 0.01393318176269531 9.723310470581055 0.01391410827636719 9.722975730895996 0.01389884948730469 9.722675323486328 C 0.01366424560546875 9.718386650085449 0.01338768005371094 9.713266372680664 0.01316070556640625 9.708975791931152 C 0.01314353942871094 9.708674430847168 0.01312446594238281 9.708312034606934 0.01310920715332031 9.708011627197266 C 0.01297950744628906 9.705559730529785 0.01287269592285156 9.703555107116699 0.01274490356445312 9.701102256774902 C 0.01274490356445312 9.701102256774902 0.01274490356445312 9.70110034942627 0.01274490356445312 9.70110034942627 C 0.01263236999511719 9.698954582214355 0.01250457763671875 9.696467399597168 0.01239395141601562 9.694320678710938 C 0.01237869262695312 9.69402027130127 0.01235389709472656 9.693571090698242 0.01233863830566406 9.693270683288574 C 0.01222801208496094 9.691123962402344 0.01210212707519531 9.688654899597168 0.011993408203125 9.686508178710938 C 0.01198768615722656 9.686408042907715 0.01197433471679688 9.686117172241211 0.01196861267089844 9.686017036437988 C 0.01185989379882812 9.683870315551758 0.01175308227539062 9.681721687316895 0.01164436340332031 9.679574966430664 C 0.01162910461425781 9.67927360534668 0.01161575317382812 9.678988456726074 0.01160049438476562 9.678688049316406 C 0.01147842407226562 9.676234245300293 0.011383056640625 9.674301147460938 0.01126289367675781 9.671847343444824 C 0.01125144958496094 9.671646118164062 0.01124000549316406 9.671405792236328 0.01123046875 9.671204566955566 C 0.01112556457519531 9.66905689239502 0.0110321044921875 9.667141914367676 0.01092720031738281 9.664994239807129 C 0.01090812683105469 9.664592742919922 0.010894775390625 9.664292335510254 0.01087379455566406 9.663890838623047 C 0.01075553894042969 9.661436080932617 0.01067352294921875 9.659722328186035 0.01055717468261719 9.657266616821289 C 0.0105438232421875 9.656966209411621 0.01052284240722656 9.656553268432617 0.01050949096679688 9.656251907348633 C 0.0104217529296875 9.654410362243652 0.01030731201171875 9.651993751525879 0.01022148132324219 9.650152206420898 C 0.0102081298828125 9.649850845336914 0.01019477844238281 9.649589538574219 0.01018142700195312 9.649288177490234 C 0.01006698608398438 9.646832466125488 0.009984970092773438 9.645083427429199 0.0098724365234375 9.642626762390137 C 0.009853363037109375 9.64222526550293 0.009840011596679688 9.641905784606934 0.009820938110351562 9.641504287719727 C 0.009721755981445312 9.639354705810547 0.009632110595703125 9.637392044067383 0.0095367431640625 9.635242462158203 C 0.009527206420898438 9.635041236877441 0.009517669677734375 9.634857177734375 0.009510040283203125 9.634655952453613 C 0.009412765502929688 9.632506370544434 0.009309768676757812 9.630203247070312 0.009214401245117188 9.628052711486816 C 0.009191513061523438 9.627551078796387 0.0091705322265625 9.627082824707031 0.009149551391601562 9.626581192016602 C 0.009054183959960938 9.624430656433105 0.008970260620117188 9.622540473937988 0.008876800537109375 9.620388984680176 C 0.008867263793945312 9.62018871307373 0.008867263793945312 9.62019157409668 0.008859634399414062 9.619990348815918 C 0.00876617431640625 9.617839813232422 0.0086669921875 9.615548133850098 0.008575439453125 9.613396644592285 C 0.00855255126953125 9.612895011901855 0.008531570434570312 9.612375259399414 0.008508682250976562 9.611872673034668 C 0.008417129516601562 9.609721183776855 0.008327484130859375 9.607602119445801 0.008237838745117188 9.605450630187988 C 0.008237838745117188 9.605450630187988 0.00823211669921875 9.605301856994629 0.00823211669921875 9.605301856994629 C 0.00814056396484375 9.603150367736816 0.008039474487304688 9.600723266601562 0.007951736450195312 9.59857177734375 C 0.0079345703125 9.598169326782227 0.007913589477539062 9.597663879394531 0.007898330688476562 9.597262382507324 C 0.007720947265625 9.592957496643066 0.007534027099609375 9.588315963745117 0.00736236572265625 9.584010124206543 C 0.007341384887695312 9.583507537841797 0.007320404052734375 9.58296012878418 0.00730133056640625 9.582457542419434 C 0.007215499877929688 9.580305099487305 0.007129669189453125 9.578120231628418 0.007045745849609375 9.575966835021973 C 0.007038116455078125 9.575765609741211 0.00702667236328125 9.575448989868164 0.00701904296875 9.575247764587402 C 0.006946563720703125 9.573402404785156 0.006864547729492188 9.571231842041016 0.006793975830078125 9.569385528564453 C 0.006771087646484375 9.568781852722168 0.00675201416015625 9.568305015563965 0.0067291259765625 9.56770133972168 C 0.006647109985351562 9.565547943115234 0.006565093994140625 9.563339233398438 0.0064849853515625 9.561185836791992 C 0.006481170654296875 9.561084747314453 0.006465911865234375 9.560691833496094 0.00646209716796875 9.560591697692871 C 0.006381988525390625 9.558437347412109 0.006320953369140625 9.556769371032715 0.006242752075195312 9.554615020751953 C 0.006219863891601562 9.554010391235352 0.006206512451171875 9.553627014160156 0.006183624267578125 9.553022384643555 C 0.006105422973632812 9.550868034362793 0.006029129028320312 9.548759460449219 0.005952835083007812 9.546604156494141 C 0.005941390991210938 9.546302795410156 0.005924224853515625 9.54580020904541 0.00591278076171875 9.545498847961426 C 0.005847930908203125 9.543651580810547 0.00577545166015625 9.541604995727539 0.005710601806640625 9.53975772857666 C 0.005693435668945312 9.539254188537598 0.005680084228515625 9.538876533508301 0.005662918090820312 9.538372993469238 C 0.005588531494140625 9.53621768951416 0.005510330200195312 9.533951759338379 0.005435943603515625 9.531795501708984 C 0.005426406860351562 9.531494140625 0.005414962768554688 9.531157493591309 0.005405426025390625 9.530856132507324 C 0.005342483520507812 9.529007911682129 0.00525665283203125 9.526477813720703 0.00519561767578125 9.524629592895508 C 0.00518798828125 9.524428367614746 0.005176544189453125 9.524067878723145 0.005168914794921875 9.523866653442383 C 0.005086898803710938 9.521402359008789 0.005025863647460938 9.519522666931152 0.004945755004882812 9.517057418823242 C 0.00493621826171875 9.516755104064941 0.004924774169921875 9.516400337219238 0.004915237426757812 9.516098022460938 C 0.004854202270507812 9.514249801635742 0.004777908325195312 9.511837959289551 0.004718780517578125 9.509989738464355 C 0.004709243774414062 9.509687423706055 0.004697799682617188 9.509320259094238 0.004688262939453125 9.509017944335938 C 0.004610061645507812 9.506552696228027 0.00455474853515625 9.504779815673828 0.00447845458984375 9.502314567565918 C 0.004465103149414062 9.501911163330078 0.004459381103515625 9.501718521118164 0.00444793701171875 9.501316070556641 C 0.004381179809570312 9.499157905578613 0.004312515258789062 9.496891975402832 0.004247665405273438 9.494734764099121 C 0.004243850708007812 9.494633674621582 0.004241943359375 9.494542121887207 0.004238128662109375 9.494441032409668 C 0.00417327880859375 9.492282867431641 0.004100799560546875 9.489848136901855 0.004037857055664062 9.487689971923828 C 0.004026412963867188 9.487286567687988 0.0040130615234375 9.486824035644531 0.003999710083007812 9.486420631408691 C 0.003936767578125 9.484262466430664 0.003875732421875 9.482146263122559 0.003814697265625 9.479988098144531 C 0.003810882568359375 9.479887008666992 0.00380706787109375 9.479735374450684 0.003805160522460938 9.479634284973145 C 0.003742218017578125 9.477475166320801 0.003679275512695312 9.475226402282715 0.003620147705078125 9.473067283630371 C 0.003604888916015625 9.472562789916992 0.003589630126953125 9.471982002258301 0.003576278686523438 9.471477508544922 C 0.003515243530273438 9.469318389892578 0.003454208374023438 9.467072486877441 0.003396987915039062 9.464913368225098 C 0.003396987915039062 9.464913368225098 0.00339508056640625 9.464876174926758 0.00339508056640625 9.464876174926758 C 0.003337860107421875 9.462716102600098 0.003276824951171875 9.460453987121582 0.003221511840820312 9.458293914794922 C 0.003208160400390625 9.457789421081543 0.003194808959960938 9.457292556762695 0.00318145751953125 9.456788063049316 C 0.003126144409179688 9.454627990722656 0.003068923950195312 9.452425003051758 0.00301361083984375 9.450264930725098 C 0.0030059814453125 9.44996166229248 0.003002166748046875 9.449827194213867 0.002994537353515625 9.44952392578125 C 0.002948760986328125 9.447671890258789 0.002889633178710938 9.445328712463379 0.002843856811523438 9.443476676940918 C 0.002832412719726562 9.442972183227539 0.002820968627929688 9.442548751831055 0.002809524536132812 9.442044258117676 C 0.002756118774414062 9.439883232116699 0.002702713012695312 9.437690734863281 0.002651214599609375 9.435529708862305 C 0.002643585205078125 9.435226440429688 0.002634048461914062 9.434818267822266 0.002628326416015625 9.434514999389648 C 0.002584457397460938 9.432662963867188 0.002536773681640625 9.430641174316406 0.00249481201171875 9.428788185119629 C 0.002481460571289062 9.42828369140625 0.002470016479492188 9.427767753601074 0.002458572387695312 9.427263259887695 C 0.002408981323242188 9.425101280212402 0.002361297607421875 9.422983169555664 0.002313613891601562 9.420820236206055 C 0.0023040771484375 9.420416831970215 0.002294540405273438 9.420000076293945 0.002286911010742188 9.419595718383789 C 0.002244949340820312 9.417742729187012 0.002201080322265625 9.415705680847168 0.002161026000976562 9.413851737976074 C 0.002153396606445312 9.413448333740234 0.002141952514648438 9.412975311279297 0.002134323120117188 9.412570953369141 C 0.002088546752929688 9.410408020019531 0.002038955688476562 9.408111572265625 0.001995086669921875 9.405948638916016 C 0.001989364624023438 9.405645370483398 0.001979827880859375 9.405223846435547 0.001974105834960938 9.40492057800293 C 0.001935958862304688 9.403066635131836 0.00189208984375 9.40090274810791 0.001855850219726562 9.399048805236816 C 0.001848220825195312 9.39864444732666 0.00183868408203125 9.398200035095215 0.0018310546875 9.397795677185059 C 0.001787185668945312 9.395631790161133 0.001745223999023438 9.393478393554688 0.001705169677734375 9.391314506530762 C 0.001695632934570312 9.390809059143066 0.001688003540039062 9.390403747558594 0.001678466796875 9.389898300170898 C 0.001642227172851562 9.388043403625488 0.001604080200195312 9.385950088500977 0.001569747924804688 9.384095191955566 C 0.00156402587890625 9.383791923522949 0.001556396484375 9.383373260498047 0.001550674438476562 9.38306999206543 C 0.001512527465820312 9.380905151367188 0.001470565795898438 9.378608703613281 0.001432418823242188 9.376444816589355 C 0.00142669677734375 9.376039505004883 0.0014190673828125 9.375602722167969 0.00141143798828125 9.375198364257812 C 0.001373291015625 9.37303352355957 0.001337051391601562 9.370870590209961 0.001300811767578125 9.368704795837402 C 0.001300811767578125 9.368704795837402 0.0012969970703125 9.368467330932617 0.0012969970703125 9.368467330932617 C 0.001260757446289062 9.366302490234375 0.001222610473632812 9.363903045654297 0.001188278198242188 9.361737251281738 C 0.001180648803710938 9.361332893371582 0.001171112060546875 9.360722541809082 0.001165390014648438 9.360317230224609 C 0.001131057739257812 9.358152389526367 0.001096725463867188 9.35596752166748 0.001064300537109375 9.353800773620605 C 0.001064300537109375 9.353800773620605 0.001062393188476562 9.353639602661133 0.001062393188476562 9.353639602661133 C 0.00102996826171875 9.351472854614258 0.000995635986328125 9.349159240722656 0.000965118408203125 9.346993446350098 C 0.000957489013671875 9.346487045288086 0.000949859619140625 9.345915794372559 0.000942230224609375 9.345410346984863 C 0.000881195068359375 9.341076850891113 0.0008220672607421875 9.336699485778809 0.000766754150390625 9.332365036010742 C 0.000759124755859375 9.331757545471191 0.000751495361328125 9.331131935119629 0.000743865966796875 9.330524444580078 C 0.0007171630859375 9.328357696533203 0.000690460205078125 9.326294898986816 0.0006656646728515625 9.324127197265625 C 0.0006618499755859375 9.323822975158691 0.000659942626953125 9.323636054992676 0.0006561279296875 9.323332786560059 C 0.00063323974609375 9.321474075317383 0.0006103515625 9.31934642791748 0.0005893707275390625 9.317487716674805 C 0.0005817413330078125 9.316880226135254 0.000576019287109375 9.316374778747559 0.0005702972412109375 9.315767288208008 C 0.000545501708984375 9.313599586486816 0.000522613525390625 9.311515808105469 0.0005016326904296875 9.309348106384277 C 0.00049591064453125 9.308942794799805 0.0004940032958984375 9.308701515197754 0.0004901885986328125 9.308296203613281 C 0.0004711151123046875 9.306437492370605 0.0004520416259765625 9.30451774597168 0.00043487548828125 9.302659034729004 C 0.0004291534423828125 9.302050590515137 0.0004253387451171875 9.301615715026855 0.00041961669921875 9.301007270812988 C 0.0003986358642578125 9.29883861541748 0.000377655029296875 9.296631813049316 0.00035858154296875 9.294462203979492 C 0.0003566741943359375 9.294158935546875 0.000354766845703125 9.29395580291748 0.0003528594970703125 9.293651580810547 C 0.000335693359375 9.291791915893555 0.000316619873046875 9.289425849914551 0.000301361083984375 9.287566184997559 C 0.00029754638671875 9.287160873413086 0.000293731689453125 9.286706924438477 0.0002918243408203125 9.286301612854004 C 0.000274658203125 9.28413200378418 0.0002574920654296875 9.281820297241211 0.0002422332763671875 9.27964973449707 C 0.0002384185791015625 9.279346466064453 0.00023651123046875 9.278972625732422 0.0002346038818359375 9.278668403625488 C 0.00022125244140625 9.276808738708496 0.0002079010009765625 9.274649620056152 0.000194549560546875 9.27278995513916 C 0.0001926422119140625 9.272282600402832 0.0001888275146484375 9.271979331970215 0.000186920166015625 9.271471977233887 C 0.0001735687255859375 9.269301414489746 0.00016021728515625 9.26710033416748 0.0001468658447265625 9.26492977142334 C 0.00014495849609375 9.264524459838867 0.0001430511474609375 9.264072418212891 0.000141143798828125 9.263667106628418 C 0.0001316070556640625 9.261806488037109 0.0001201629638671875 9.259613990783691 0.000110626220703125 9.257752418518066 C 0.0001087188720703125 9.257448196411133 0.0001068115234375 9.257004737854004 0.0001049041748046875 9.25670051574707 C 9.34600830078125e-05 9.254219055175781 8.58306884765625e-05 9.252476692199707 7.62939453125e-05 9.249994277954102 C 7.43865966796875e-05 9.249588966369629 7.2479248046875e-05 9.249302864074707 7.2479248046875e-05 9.248897552490234 C 6.4849853515625e-05 9.247035980224609 5.53131103515625e-05 9.244593620300293 4.9591064453125e-05 9.242732048034668 C 4.9591064453125e-05 9.242426872253418 4.76837158203125e-05 9.242218017578125 4.76837158203125e-05 9.241913795471191 C 4.00543212890625e-05 9.239741325378418 3.4332275390625e-05 9.237567901611328 2.86102294921875e-05 9.235395431518555 C 2.6702880859375e-05 9.234786987304688 2.6702880859375e-05 9.234386444091797 2.47955322265625e-05 9.23377799987793 C 2.09808349609375e-05 9.231915473937988 1.71661376953125e-05 9.229783058166504 1.33514404296875e-05 9.227920532226562 C 1.33514404296875e-05 9.227615356445312 1.33514404296875e-05 9.227327346801758 1.1444091796875e-05 9.227023124694824 C 7.62939453125e-06 9.224849700927734 5.7220458984375e-06 9.222716331481934 3.814697265625e-06 9.220542907714844 C 3.814697265625e-06 9.220034599304199 3.814697265625e-06 9.219474792480469 3.814697265625e-06 9.218967437744141 C 1.9073486328125e-06 9.216794013977051 0 9.214493751525879 0 9.212320327758789 C 0 9.212320327758789 0 9.212320327758789 0 9.212320327758789 C 0 9.210146903991699 1.9073486328125e-06 9.207714080810547 3.814697265625e-06 9.205540657043457 C 3.814697265625e-06 9.20494556427002 3.814697265625e-06 9.204177856445312 5.7220458984375e-06 9.203583717346191 C 5.7220458984375e-06 9.201720237731934 7.62939453125e-06 9.200599670410156 1.1444091796875e-05 9.198737144470215 C 1.1444091796875e-05 9.198022842407227 1.33514404296875e-05 9.19718074798584 1.33514404296875e-05 9.196467399597168 C 1.71661376953125e-05 9.194604873657227 2.09808349609375e-05 9.192770004272461 2.47955322265625e-05 9.19090747833252 C 2.6702880859375e-05 9.190550804138184 2.6702880859375e-05 9.190123558044434 2.6702880859375e-05 9.189766883850098 C 3.24249267578125e-05 9.187594413757324 4.00543212890625e-05 9.185288429260254 4.57763671875e-05 9.183116912841797 C 4.76837158203125e-05 9.182403564453125 4.9591064453125e-05 9.181771278381348 5.340576171875e-05 9.181057929992676 C 5.7220458984375e-05 9.179506301879883 6.4849853515625e-05 9.177726745605469 7.05718994140625e-05 9.176175117492676 C 7.2479248046875e-05 9.175461769104004 7.62939453125e-05 9.174784660339355 7.82012939453125e-05 9.174071311950684 C 8.58306884765625e-05 9.172209739685059 9.34600830078125e-05 9.170642852783203 0.0001010894775390625 9.168781280517578 C 0.000102996826171875 9.168306350708008 0.0001068115234375 9.167728424072266 0.0001087188720703125 9.167253494262695 C 0.0001201629638671875 9.164772033691406 0.00012969970703125 9.162961959838867 0.0001430511474609375 9.160481452941895 C 0.0001468658447265625 9.160005569458008 0.000148773193359375 9.159551620483398 0.0001506805419921875 9.159075736999512 C 0.0001621246337890625 9.157215118408203 0.000171661376953125 9.155669212341309 0.00018310546875 9.15380859375 C 0.000186920166015625 9.152976036071777 0.0001926422119140625 9.15227222442627 0.0001983642578125 9.151440620422363 C 0.000209808349609375 9.149580001831055 0.000217437744140625 9.148395538330078 0.0002307891845703125 9.14653491973877 C 0.0002346038818359375 9.145940780639648 0.0002384185791015625 9.145364761352539 0.0002422332763671875 9.144770622253418 C 0.0002593994140625 9.142601013183594 0.0002765655517578125 9.140188217163086 0.000293731689453125 9.138018608093262 C 0.00029754638671875 9.137662887573242 0.0002994537353515625 9.137407302856445 0.000301361083984375 9.137050628662109 C 0.000316619873046875 9.135190963745117 0.000335693359375 9.133020401000977 0.0003509521484375 9.131160736083984 C 0.0003566741943359375 9.130566596984863 0.000362396240234375 9.12983226776123 0.0003681182861328125 9.129238128662109 C 0.0003833770751953125 9.127378463745117 0.000396728515625 9.126066207885742 0.0004138946533203125 9.12420654296875 C 0.00041961669921875 9.123494148254395 0.0004253387451171875 9.12297248840332 0.000431060791015625 9.122260093688965 C 0.0004520416259765625 9.120090484619141 0.0004711151123046875 9.118171691894531 0.0004940032958984375 9.116003036499023 C 0.00049591064453125 9.115765571594238 0.00049591064453125 9.115704536437988 0.000499725341796875 9.115467071533203 C 0.0005207061767578125 9.113298416137695 0.0005474090576171875 9.110899925231934 0.0005702972412109375 9.108732223510742 C 0.0005779266357421875 9.108138084411621 0.0005855560302734375 9.107359886169434 0.0005931854248046875 9.106766700744629 C 0.000614166259765625 9.104907989501953 0.0006275177001953125 9.103786468505859 0.00064849853515625 9.1019287109375 C 0.0006561279296875 9.101216316223145 0.000667572021484375 9.100391387939453 0.000675201416015625 9.099679946899414 C 0.00069427490234375 9.09813117980957 0.0007152557373046875 9.096378326416016 0.0007343292236328125 9.094829559326172 C 0.0007419586181640625 9.094236373901367 0.0007534027099609375 9.093381881713867 0.0007610321044921875 9.092788696289062 C 0.0007915496826171875 9.090312004089355 0.000812530517578125 9.088689804077148 0.00084686279296875 9.086213111877441 C 0.0008525848388671875 9.085739135742188 0.0008602142333984375 9.085165023803711 0.0008678436279296875 9.084690093994141 C 0.00089263916015625 9.082833290100098 0.0009136199951171875 9.081274032592773 0.0009403228759765625 9.07941722869873 C 0.000949859619140625 9.078704833984375 0.0009593963623046875 9.07804012298584 0.00096893310546875 9.077328681945801 C 0.000995635986328125 9.075471878051758 0.001016616821289062 9.074034690856934 0.00104522705078125 9.072177886962891 C 0.001054763793945312 9.071585655212402 0.00106048583984375 9.071081161499023 0.001070022583007812 9.070488929748535 C 0.001102447509765625 9.06832218170166 0.001138687133789062 9.065979957580566 0.001173019409179688 9.063814163208008 C 0.001180648803710938 9.063340187072754 0.001188278198242188 9.062830924987793 0.001195907592773438 9.062355995178223 C 0.001226425170898438 9.060500144958496 0.001253128051757812 9.058902740478516 0.001283645629882812 9.057046890258789 C 0.001295089721679688 9.056336402893066 0.001306533813476562 9.055627822875977 0.001317977905273438 9.054916381835938 C 0.00135040283203125 9.053060531616211 0.001371383666992188 9.05179500579834 0.00140380859375 9.049939155578613 C 0.001413345336914062 9.049346923828125 0.00142669677734375 9.048617362976074 0.001436233520507812 9.048025131225586 C 0.001512527465820312 9.043695449829102 0.001598358154296875 9.038915634155273 0.001680374145507812 9.034587860107422 C 0.0016937255859375 9.033876419067383 0.001705169677734375 9.03331184387207 0.001718521118164062 9.032601356506348 C 0.0017547607421875 9.030746459960938 0.001779556274414062 9.029438972473145 0.0018157958984375 9.027584075927734 C 0.001827239990234375 9.026991844177246 0.001844406127929688 9.026195526123047 0.001855850219726562 9.025603294372559 C 0.00189208984375 9.023749351501465 0.0019378662109375 9.02146053314209 0.00197601318359375 9.01960563659668 C 0.001981735229492188 9.019369125366211 0.001985549926757812 9.019135475158691 0.00199127197265625 9.018898963928223 C 0.002035140991210938 9.016735076904297 0.002086639404296875 9.014253616333008 0.002134323120117188 9.012090682983398 C 0.002145767211914062 9.01149845123291 0.002157211303710938 9.01097297668457 0.002170562744140625 9.010380744934082 C 0.002210617065429688 9.008526802062988 0.002237319946289062 9.007244110107422 0.002279281616210938 9.005391120910645 C 0.00229644775390625 9.004562377929688 0.002313613891601562 9.003863334655762 0.002330780029296875 9.003034591674805 C 0.0023651123046875 9.001490592956543 0.002405166625976562 8.999743461608887 0.002439498901367188 8.998198509216309 C 0.0024566650390625 8.997488975524902 0.002466201782226562 8.997047424316406 0.002483367919921875 8.996336936950684 C 0.002538681030273438 8.993866920471191 0.002576828002929688 8.992246627807617 0.002635955810546875 8.989776611328125 C 0.002649307250976562 8.989184379577637 0.00266265869140625 8.988633155822754 0.00267791748046875 8.988041877746582 C 0.002721786499023438 8.986188888549805 0.002756118774414062 8.984783172607422 0.00279998779296875 8.982931137084961 C 0.002820968627929688 8.982102394104004 0.002834320068359375 8.981573104858398 0.002855300903320312 8.980745315551758 C 0.002893447875976562 8.979201316833496 0.002931594848632812 8.977636337280273 0.002969741821289062 8.976092338562012 C 0.002988815307617188 8.975382804870605 0.003009796142578125 8.974549293518066 0.003026962280273438 8.973838806152344 C 0.003082275390625 8.971678733825684 0.003139495849609375 8.969457626342773 0.00319671630859375 8.967297554016113 C 0.003204345703125 8.96694278717041 0.00321197509765625 8.966705322265625 0.003221511840820312 8.966350555419922 C 0.003269195556640625 8.964498519897461 0.003324508666992188 8.962405204772949 0.003374099731445312 8.960554122924805 C 0.003393173217773438 8.959844589233398 0.003414154052734375 8.959114074707031 0.0034332275390625 8.958404541015625 C 0.003482818603515625 8.95655345916748 0.003511428833007812 8.955480575561523 0.00356292724609375 8.953629493713379 C 0.003582000732421875 8.952919960021973 0.003599166870117188 8.952300071716309 0.003620147705078125 8.951590538024902 C 0.003679275512695312 8.949431419372559 0.003732681274414062 8.947522163391113 0.003793716430664062 8.94536304473877 C 0.003801345825195312 8.945126533508301 0.003801345825195312 8.945089340209961 0.003808975219726562 8.944853782653809 C 0.003870010375976562 8.942694664001465 0.003942489624023438 8.94019603729248 0.00400543212890625 8.938037872314453 C 0.004018783569335938 8.937564849853516 0.004039764404296875 8.936907768249512 0.004053115844726562 8.936434745788574 C 0.004106521606445312 8.934584617614746 0.004154205322265625 8.933019638061523 0.004209518432617188 8.931170463562012 C 0.004230499267578125 8.930460929870605 0.004243850708007812 8.930027008056641 0.00426483154296875 8.929318428039551 C 0.004320144653320312 8.927469253540039 0.0043792724609375 8.925531387329102 0.004436492919921875 8.92368221282959 C 0.004451751708984375 8.923209190368652 0.004459381103515625 8.922930717468262 0.004474639892578125 8.922457695007324 C 0.004550933837890625 8.919991493225098 0.004606246948242188 8.918224334716797 0.0046844482421875 8.915759086608887 C 0.004697799682617188 8.915286064147949 0.004720687866210938 8.91458797454834 0.004735946655273438 8.914115905761719 C 0.004795074462890625 8.912266731262207 0.004842758178710938 8.910773277282715 0.004901885986328125 8.908924102783203 C 0.004924774169921875 8.90821647644043 0.00494384765625 8.907638549804688 0.00496673583984375 8.906929969787598 C 0.00502777099609375 8.905081748962402 0.005077362060546875 8.90355110168457 0.005138397216796875 8.901702880859375 C 0.005159378051757812 8.90111255645752 0.0051727294921875 8.900700569152832 0.005191802978515625 8.900110244750977 C 0.005273818969726562 8.897645950317383 0.005338668823242188 8.895731925964355 0.005422592163085938 8.893267631530762 C 0.005430221557617188 8.893032073974609 0.005437850952148438 8.892822265625 0.005445480346679688 8.892586708068848 C 0.0055084228515625 8.890738487243652 0.005582809448242188 8.888565063476562 0.005647659301757812 8.886717796325684 C 0.005672454833984375 8.886009216308594 0.005701065063476562 8.885195732116699 0.005725860595703125 8.884488105773926 C 0.00579071044921875 8.882640838623047 0.005830764770507812 8.881484031677246 0.005895614624023438 8.879636764526367 C 0.005916595458984375 8.879046440124512 0.00594329833984375 8.878325462341309 0.005964279174804688 8.877735137939453 C 0.006040573120117188 8.875580787658691 0.006128311157226562 8.873142242431641 0.006206512451171875 8.870987892150879 C 0.006206512451171875 8.870987892150879 0.006208419799804688 8.87094783782959 0.006208419799804688 8.87094783782959 C 0.00628662109375 8.868793487548828 0.006372451782226562 8.866459846496582 0.006452560424804688 8.864306449890137 C 0.006479263305664062 8.863598823547363 0.006498336791992188 8.863095283508301 0.006525039672851562 8.862387657165527 C 0.006593704223632812 8.860541343688965 0.006641387939453125 8.859262466430664 0.006711959838867188 8.857416152954102 C 0.006732940673828125 8.856826782226562 0.00676727294921875 8.85595703125 0.006788253784179688 8.855367660522461 C 0.006870269775390625 8.853214263916016 0.006948471069335938 8.851199150085449 0.007032394409179688 8.849045753479004 C 0.007036209106445312 8.84892749786377 0.007038116455078125 8.848906517028809 0.00704193115234375 8.848788261413574 C 0.0071258544921875 8.846634864807129 0.00722503662109375 8.844113349914551 0.0073089599609375 8.841960906982422 C 0.00733184814453125 8.841371536254883 0.007354736328125 8.840826034545898 0.00737762451171875 8.840237617492676 C 0.007452011108398438 8.83839225769043 0.007511138916015625 8.836915016174316 0.007585525512695312 8.83506965637207 C 0.007608413696289062 8.834480285644531 0.00763702392578125 8.833759307861328 0.007661819458007812 8.833169937133789 C 0.0077362060546875 8.831325531005859 0.007810592651367188 8.829506874084473 0.007884979248046875 8.827662467956543 C 0.007900238037109375 8.827308654785156 0.007923126220703125 8.826741218566895 0.007938385009765625 8.826387405395508 C 0.008039474487304688 8.823927879333496 0.008115768432617188 8.822111129760742 0.008218765258789062 8.81965160369873 C 0.008243560791015625 8.819063186645508 0.008256912231445312 8.81873893737793 0.008281707763671875 8.818149566650391 C 0.008359909057617188 8.816305160522461 0.0084228515625 8.814781188964844 0.008502960205078125 8.81293773651123 C 0.00853729248046875 8.812113761901855 0.0085601806640625 8.811583518981934 0.008596420288085938 8.810758590698242 C 0.00867462158203125 8.808915138244629 0.008731842041015625 8.80759334564209 0.00881195068359375 8.805749893188477 C 0.008832931518554688 8.805278778076172 0.008863449096679688 8.804543495178223 0.008884429931640625 8.804072380065918 C 0.008977890014648438 8.801921844482422 0.009082794189453125 8.799533843994141 0.00917816162109375 8.797383308410645 C 0.009199142456054688 8.79691219329834 0.00920867919921875 8.796714782714844 0.009229660034179688 8.796243667602539 C 0.009311676025390625 8.794401168823242 0.0093994140625 8.79244327545166 0.009481430053710938 8.790600776672363 C 0.009508132934570312 8.790012359619141 0.009546279907226562 8.789180755615234 0.009572982788085938 8.788592338562012 C 0.009656906127929688 8.786749839782715 0.009710311889648438 8.785561561584473 0.009794235229492188 8.783719062805176 C 0.00982666015625 8.783013343811035 0.009853363037109375 8.782432556152344 0.009885787963867188 8.781726837158203 C 0.009984970092773438 8.779577255249023 0.01008224487304688 8.777487754821777 0.01018142700195312 8.775338172912598 C 0.01018714904785156 8.77522087097168 0.01019096374511719 8.775149345397949 0.01019668579101562 8.775032043457031 C 0.01029586791992188 8.772883415222168 0.01040840148925781 8.770503044128418 0.01051139831542969 8.768354415893555 C 0.01053810119628906 8.767765998840332 0.0105743408203125 8.767034530639648 0.01060104370117188 8.766446113586426 C 0.01068878173828125 8.764604568481445 0.01075935363769531 8.763162612915039 0.01084709167480469 8.761321067810059 C 0.01086997985839844 8.76085090637207 0.01090621948242188 8.760105133056641 0.01092910766601562 8.759634971618652 C 0.011016845703125 8.757793426513672 0.01113128662109375 8.755455017089844 0.01122093200683594 8.75361442565918 C 0.01124000549316406 8.753261566162109 0.01124382019042969 8.753158569335938 0.011260986328125 8.752805709838867 C 0.01138114929199219 8.750350952148438 0.01147651672363281 8.748456001281738 0.01159858703613281 8.746002197265625 C 0.01162147521972656 8.745532035827637 0.01165199279785156 8.744931221008301 0.01167488098144531 8.744461059570312 C 0.01176643371582031 8.742620468139648 0.01183700561523438 8.741229057312012 0.01193046569824219 8.739389419555664 C 0.01196670532226562 8.738683700561523 0.0120086669921875 8.737854957580566 0.01204299926757812 8.737149238586426 C 0.01213645935058594 8.735309600830078 0.01220321655273438 8.734015464782715 0.012298583984375 8.732175827026367 C 0.0123291015625 8.731588363647461 0.01235771179199219 8.730997085571289 0.01238822937011719 8.730408668518066 C 0.01249885559082031 8.728262901306152 0.01262092590332031 8.725920677185059 0.01273345947265625 8.723774909973145 C 0.01275062561035156 8.723422050476074 0.01277732849121094 8.72293758392334 0.01279449462890625 8.72258472442627 C 0.01289176940917969 8.720746040344238 0.01299095153808594 8.718874931335449 0.01308822631835938 8.717036247253418 C 0.01312446594238281 8.716331481933594 0.013153076171875 8.71580982208252 0.01318931579589844 8.715105056762695 C 0.01328849792480469 8.713266372680664 0.01335906982421875 8.711919784545898 0.013458251953125 8.710082054138184 C 0.01349067687988281 8.709494590759277 0.01353073120117188 8.708748817443848 0.01356124877929688 8.708161354064941 C 0.01367759704589844 8.706016540527344 0.01380729675292969 8.703630447387695 0.01392364501953125 8.701486587524414 C 0.0139312744140625 8.701369285583496 0.01392364501953125 8.701511383056641 0.01392936706542969 8.701393127441406 C 0.01404571533203125 8.699249267578125 0.01417160034179688 8.696985244750977 0.01428985595703125 8.694841384887695 C 0.01432991027832031 8.694136619567871 0.01435661315917969 8.693628311157227 0.01439666748046875 8.692923545837402 C 0.01449775695800781 8.691085815429688 0.0145721435546875 8.689770698547363 0.01467514038085938 8.687933921813965 C 0.01470756530761719 8.687346458435059 0.01475334167480469 8.686553955078125 0.0147857666015625 8.685966491699219 C 0.01488876342773438 8.68412971496582 0.01500511169433594 8.682094573974609 0.01510810852050781 8.680256843566895 C 0.0151214599609375 8.680022239685059 0.0151519775390625 8.67949390411377 0.01516532897949219 8.679259300231934 C 0.0153045654296875 8.676810264587402 0.015411376953125 8.67495059967041 0.01555442810058594 8.672501564025879 C 0.01558113098144531 8.672032356262207 0.01560592651367188 8.671608924865723 0.01563262939453125 8.671139717102051 C 0.01573944091796875 8.669302940368652 0.01584243774414062 8.667527198791504 0.01594924926757812 8.665691375732422 C 0.01598358154296875 8.665104866027832 0.01602363586425781 8.664426803588867 0.01605796813964844 8.663840293884277 C 0.01616668701171875 8.662004470825195 0.01626968383789062 8.660246849060059 0.01637840270996094 8.658411026000977 C 0.01639938354492188 8.658059120178223 0.01643753051757812 8.657425880432129 0.01645851135253906 8.657073974609375 C 0.01660346984863281 8.654626846313477 0.01670646667480469 8.652901649475098 0.01685333251953125 8.650454521179199 C 0.01688957214355469 8.649868011474609 0.01691818237304688 8.649391174316406 0.01695442199707031 8.648804664611816 C 0.01706504821777344 8.646969795227051 0.01715469360351562 8.645486831665039 0.01726531982421875 8.643651962280273 C 0.01730918884277344 8.642948150634766 0.0173492431640625 8.642278671264648 0.01739311218261719 8.641574859619141 C 0.01750564575195312 8.639739990234375 0.01758003234863281 8.638521194458008 0.01769256591796875 8.636686325073242 C 0.01773643493652344 8.635983467102051 0.01776504516601562 8.63552188873291 0.0178070068359375 8.634819030761719 C 0.01794052124023438 8.632678031921387 0.01808357238769531 8.630367279052734 0.01821708679199219 8.628227233886719 C 0.01823997497558594 8.627876281738281 0.01826667785644531 8.627456665039062 0.01828765869140625 8.627105712890625 C 0.01840400695800781 8.625271797180176 0.01852226257324219 8.623393058776855 0.01863861083984375 8.621559143066406 C 0.01869010925292969 8.620738983154297 0.01872634887695312 8.62015438079834 0.01877975463867188 8.61933422088623 C 0.01889610290527344 8.617500305175781 0.01895904541015625 8.616498947143555 0.01907730102539062 8.614665985107422 C 0.01912117004394531 8.61396312713623 0.01916122436523438 8.613340377807617 0.01920700073242188 8.612637519836426 C 0.01932525634765625 8.610804557800293 0.01947975158691406 8.60840892791748 0.01959800720214844 8.606575965881348 C 0.01961326599121094 8.606342315673828 0.01962471008300781 8.606165885925293 0.01963996887207031 8.605932235717773 C 0.01977920532226562 8.603793144226074 0.01993179321289062 8.601454734802246 0.02007293701171875 8.599316596984863 C 0.020111083984375 8.59873104095459 0.02016067504882812 8.597973823547363 0.02019882202148438 8.59738826751709 C 0.02031898498535156 8.595556259155273 0.02038764953613281 8.594520568847656 0.02050971984863281 8.59268856048584 C 0.02055740356445312 8.591986656188965 0.02061843872070312 8.591066360473633 0.02066421508789062 8.590363502502441 C 0.02078628540039062 8.588531494140625 0.02090263366699219 8.586803436279297 0.021026611328125 8.58497142791748 C 0.02104949951171875 8.584620475769043 0.02108001708984375 8.584165573120117 0.0211029052734375 8.58381462097168 C 0.02126884460449219 8.581372261047363 0.02139472961425781 8.579502105712891 0.0215606689453125 8.577059745788574 C 0.02159309387207031 8.576591491699219 0.02162551879882812 8.576112747192383 0.02165794372558594 8.575645446777344 C 0.02178192138671875 8.573813438415527 0.02188873291015625 8.572274208068848 0.02201461791992188 8.570443153381348 C 0.02206230163574219 8.569741249084473 0.0221099853515625 8.569052696228027 0.02215766906738281 8.568350791931152 C 0.02228546142578125 8.566519737243652 0.02237701416015625 8.56519889831543 0.02250480651855469 8.56336784362793 C 0.02254486083984375 8.562783241271973 0.02258682250976562 8.562161445617676 0.0226287841796875 8.561576843261719 C 0.02277755737304688 8.559441566467285 0.02295112609863281 8.556976318359375 0.023101806640625 8.554841041564941 C 0.02310943603515625 8.55472469329834 0.02313232421875 8.554400444030762 0.02313995361328125 8.55428409576416 C 0.02329063415527344 8.552148818969727 0.02341651916503906 8.55039119720459 0.02356719970703125 8.548255920410156 C 0.02361869812011719 8.547554969787598 0.02365493774414062 8.547035217285156 0.02370452880859375 8.546334266662598 C 0.02383613586425781 8.544504165649414 0.02394485473632812 8.542994499206543 0.02407646179199219 8.541164398193359 C 0.02410888671875 8.54069709777832 0.02415657043457031 8.540030479431152 0.02419090270996094 8.539563179016113 C 0.02449989318847656 8.535294532775879 0.02486228942871094 8.53031063079834 0.025177001953125 8.526043891906738 C 0.02521133422851562 8.525576591491699 0.02525901794433594 8.524941444396973 0.02529335021972656 8.524474143981934 C 0.02542686462402344 8.522645950317383 0.02554512023925781 8.521049499511719 0.02568244934082031 8.519221305847168 C 0.025726318359375 8.518637657165527 0.0257720947265625 8.518002510070801 0.02581596374511719 8.51741886138916 C 0.02597427368164062 8.515285491943359 0.02613258361816406 8.513184547424316 0.02629280090332031 8.511051177978516 C 0.02630233764648438 8.510934829711914 0.02630424499511719 8.510909080505371 0.02631187438964844 8.51079273223877 C 0.02647209167480469 8.508659362792969 0.02665328979492188 8.506272315979004 0.02681541442871094 8.50413990020752 C 0.02686882019042969 8.503439903259277 0.02690887451171875 8.5029296875 0.0269622802734375 8.502228736877441 C 0.02710151672363281 8.500401496887207 0.02721214294433594 8.498944282531738 0.02735328674316406 8.497117042541504 C 0.02739715576171875 8.496533393859863 0.027435302734375 8.496034622192383 0.0274810791015625 8.495450973510742 C 0.02762222290039062 8.493623733520508 0.02779579162597656 8.491371154785156 0.02793693542480469 8.489544868469238 C 0.02795600891113281 8.489311218261719 0.02798271179199219 8.488955497741699 0.0279998779296875 8.48872184753418 C 0.02819061279296875 8.486286163330078 0.02833938598632812 8.484378814697266 0.02853012084960938 8.48194408416748 C 0.02856636047363281 8.481477737426758 0.02860450744628906 8.480998992919922 0.0286407470703125 8.480532646179199 C 0.02878379821777344 8.478706359863281 0.02891159057617188 8.477090835571289 0.02905654907226562 8.475264549255371 C 0.02911186218261719 8.474565505981445 0.02916526794433594 8.473884582519531 0.0292205810546875 8.473184585571289 C 0.02936553955078125 8.471358299255371 0.02947807312011719 8.469954490661621 0.02962493896484375 8.46812915802002 C 0.02967071533203125 8.467546463012695 0.02970504760742188 8.467123985290527 0.02975082397460938 8.466540336608887 C 0.02994537353515625 8.464106559753418 0.03009033203125 8.462311744689941 0.03028678894042969 8.459877967834473 C 0.03031539916992188 8.459528923034668 0.03034210205078125 8.459197044372559 0.03037071228027344 8.458847999572754 C 0.030517578125 8.457022666931152 0.0306854248046875 8.454977989196777 0.03083419799804688 8.453153610229492 C 0.03088188171386719 8.452570915222168 0.03094291687011719 8.451802253723145 0.0309906005859375 8.45121955871582 C 0.03114128112792969 8.449395179748535 0.03123664855957031 8.448220252990723 0.0313873291015625 8.446395874023438 C 0.03144454956054688 8.445696830749512 0.031494140625 8.445090293884277 0.03155136108398438 8.444391250610352 C 0.03190422058105469 8.440134048461914 0.03229331970214844 8.435463905334473 0.03264999389648438 8.431209564208984 C 0.03270912170410156 8.430510520935059 0.03276824951171875 8.429801940917969 0.03282737731933594 8.429102897644043 C 0.03295516967773438 8.427582740783691 0.0330963134765625 8.425898551940918 0.03322601318359375 8.424378395080566 C 0.03328514099121094 8.423680305480957 0.03333473205566406 8.423083305358887 0.03339576721191406 8.422384262084961 C 0.03355026245117188 8.420560836791992 0.03368377685546875 8.41899299621582 0.03384017944335938 8.417169570922852 C 0.03387832641601562 8.416704177856445 0.033935546875 8.416035652160645 0.03397560119628906 8.415570259094238 C 0.03415870666503906 8.413443565368652 0.0343475341796875 8.411235809326172 0.03453254699707031 8.409110069274902 C 0.03458213806152344 8.408527374267578 0.034637451171875 8.407898902893066 0.03468704223632812 8.407317161560059 C 0.03484535217285156 8.405494689941406 0.03495216369628906 8.404248237609863 0.03511238098144531 8.402425765991211 C 0.0351715087890625 8.401727676391602 0.03523063659667969 8.401049613952637 0.03529167175292969 8.400350570678711 C 0.03545188903808594 8.398529052734375 0.03559303283691406 8.396918296813965 0.03575325012207031 8.395096778869629 C 0.03579330444335938 8.394631385803223 0.03583717346191406 8.394124984741211 0.03587913513183594 8.393659591674805 C 0.03609275817871094 8.391231536865234 0.0362396240234375 8.38957691192627 0.03645515441894531 8.387148857116699 C 0.03650665283203125 8.386567115783691 0.03657150268554688 8.385828018188477 0.03662490844726562 8.385246276855469 C 0.0367584228515625 8.38372802734375 0.03690910339355469 8.382058143615723 0.03704452514648438 8.38054084777832 C 0.03710556030273438 8.379842758178711 0.03719139099121094 8.378901481628418 0.03725433349609375 8.378203392028809 C 0.03738975524902344 8.376686096191406 0.0375213623046875 8.375221252441406 0.03765869140625 8.373703956604004 C 0.03773117065429688 8.372890472412109 0.03778839111328125 8.372252464294434 0.03786277770996094 8.371438980102539 C 0.038055419921875 8.369315147399902 0.03825187683105469 8.36713981628418 0.03844642639160156 8.365016937255859 C 0.03847694396972656 8.364667892456055 0.03848457336425781 8.364591598510742 0.03851699829101562 8.364242553710938 C 0.03868293762207031 8.362422943115234 0.03887176513671875 8.360353469848633 0.03903961181640625 8.35853385925293 C 0.03910446166992188 8.357836723327637 0.03918647766113281 8.356942176818848 0.03925132751464844 8.356245040893555 C 0.03939056396484375 8.354728698730469 0.0395355224609375 8.353164672851562 0.03967475891113281 8.351648330688477 C 0.03973960876464844 8.350951194763184 0.0398101806640625 8.350193023681641 0.03987503051757812 8.349495887756348 C 0.04004287719726562 8.347677230834961 0.04020881652832031 8.34591007232666 0.04037857055664062 8.344091415405273 C 0.04042243957519531 8.343626976013184 0.04044723510742188 8.343361854553223 0.04048919677734375 8.342897415161133 C 0.04068756103515625 8.340775489807129 0.04088973999023438 8.33863353729248 0.04109001159667969 8.336511611938477 C 0.04115486145019531 8.3358154296875 0.041229248046875 8.335041046142578 0.04129409790039062 8.334343910217285 C 0.04143905639648438 8.332828521728516 0.04157829284667969 8.331342697143555 0.04172325134277344 8.329827308654785 C 0.04179954528808594 8.329014778137207 0.04187583923339844 8.328220367431641 0.04195404052734375 8.327407836914062 C 0.04212570190429688 8.325590133666992 0.04225730895996094 8.32421875 0.04243278503417969 8.32240104675293 C 0.04247665405273438 8.32193660736084 0.04252243041992188 8.321444511413574 0.04256820678710938 8.320981025695801 C 0.0428009033203125 8.318557739257812 0.04296493530273438 8.316845893859863 0.04319953918457031 8.314423561096191 C 0.04325675964355469 8.3138427734375 0.043304443359375 8.313349723815918 0.04335975646972656 8.312768936157227 C 0.04353713989257812 8.310952186584473 0.043670654296875 8.309576034545898 0.04384613037109375 8.307759284973145 C 0.043914794921875 8.307063102722168 0.04400253295898438 8.306166648864746 0.04407119750976562 8.30547046661377 C 0.04421806335449219 8.303956985473633 0.04434776306152344 8.302635192871094 0.04449653625488281 8.301121711730957 C 0.04456520080566406 8.30042552947998 0.04465293884277344 8.299518585205078 0.04472160339355469 8.298822402954102 C 0.04493141174316406 8.296703338623047 0.045135498046875 8.294625282287598 0.04534530639648438 8.292506217956543 C 0.04540443420410156 8.291926383972168 0.04545021057128906 8.29145622253418 0.04550743103027344 8.290876388549805 C 0.04568862915039062 8.289060592651367 0.04581832885742188 8.287753105163574 0.04599952697753906 8.285937309265137 C 0.04609298706054688 8.285009384155273 0.04616546630859375 8.284287452697754 0.04625701904296875 8.283360481262207 C 0.04637908935546875 8.282150268554688 0.04652214050292969 8.280714988708496 0.04664421081542969 8.279504776000977 C 0.0467376708984375 8.27857780456543 0.04683685302734375 8.27759838104248 0.04693031311035156 8.276670455932617 C 0.04708290100097656 8.275157928466797 0.04721641540527344 8.273841857910156 0.04736900329589844 8.272329330444336 C 0.04749870300292969 8.271054267883301 0.04759788513183594 8.270071029663086 0.04772758483886719 8.268795967102051 C 0.047882080078125 8.26728343963623 0.048065185546875 8.265487670898438 0.04821968078613281 8.263975143432617 C 0.04830169677734375 8.263164520263672 0.04838180541992188 8.262383460998535 0.04846572875976562 8.261571884155273 C 0.04858970642089844 8.26036262512207 0.04876708984375 8.258625030517578 0.04889297485351562 8.257415771484375 C 0.04898834228515625 8.256488800048828 0.04906272888183594 8.255754470825195 0.04916000366210938 8.254827499389648 C 0.04931449890136719 8.253315925598145 0.04948234558105469 8.251711845397949 0.04963874816894531 8.250201225280762 C 0.04971122741699219 8.249505996704102 0.04976463317871094 8.24898624420166 0.04983711242675781 8.248291015625 C 0.05005645751953125 8.246175765991211 0.05027580261230469 8.244083404541016 0.05049705505371094 8.241968154907227 C 0.05056953430175781 8.241272926330566 0.0506439208984375 8.240557670593262 0.05071640014648438 8.239863395690918 C 0.05087471008300781 8.23835277557373 0.05102920532226562 8.236895561218262 0.05118751525878906 8.235385894775391 C 0.05127334594726562 8.234575271606445 0.05135154724121094 8.233832359313965 0.0514373779296875 8.23302173614502 C 0.05159759521484375 8.231512069702148 0.05177497863769531 8.229833602905273 0.05193519592285156 8.228322982788086 C 0.05200958251953125 8.227628707885742 0.05206489562988281 8.227104187011719 0.05213737487792969 8.226408958435059 C 0.05236244201660156 8.224295616149902 0.05260276794433594 8.222043037414551 0.05282974243164062 8.219929695129395 C 0.05287933349609375 8.219467163085938 0.0529327392578125 8.218968391418457 0.05298233032226562 8.218505859375 C 0.0531768798828125 8.216693878173828 0.05332565307617188 8.21530818939209 0.05352210998535156 8.213497161865234 C 0.05360794067382812 8.212687492370605 0.05369758605957031 8.21186351776123 0.05378532409667969 8.211053848266602 C 0.05394744873046875 8.209545135498047 0.05408668518066406 8.20826244354248 0.05425071716308594 8.206753730773926 C 0.05433845520019531 8.205944061279297 0.05441856384277344 8.205206871032715 0.05450630187988281 8.204397201538086 C 0.05473709106445312 8.202284812927246 0.05496978759765625 8.200148582458496 0.05520057678222656 8.198037147521973 C 0.05523872375488281 8.197690010070801 0.05525588989257812 8.197529792785645 0.05529403686523438 8.197182655334473 C 0.05549240112304688 8.195372581481934 0.05571365356445312 8.193367958068848 0.05591392517089844 8.191557884216309 C 0.05600166320800781 8.19074821472168 0.05608367919921875 8.190018653869629 0.05617332458496094 8.189208984375 C 0.05630683898925781 8.188002586364746 0.05650711059570312 8.18619441986084 0.056640625 8.184988021850586 C 0.05673027038574219 8.184178352355957 0.05683135986328125 8.183259010314941 0.05692100524902344 8.182450294494629 C 0.05712318420410156 8.18064022064209 0.05732536315917969 8.178828239440918 0.05752754211425781 8.177019119262695 C 0.05756568908691406 8.17667293548584 0.05759429931640625 8.176421165466309 0.0576324462890625 8.176074981689453 C 0.05786895751953125 8.17396354675293 0.0581207275390625 8.171720504760742 0.05835914611816406 8.169610023498535 C 0.05843734741210938 8.168917655944824 0.05851173400878906 8.16826057434082 0.05858993530273438 8.167567253112793 C 0.05875968933105469 8.166060447692871 0.05894088745117188 8.164461135864258 0.05911064147949219 8.162954330444336 C 0.05919075012207031 8.162261962890625 0.05928802490234375 8.161406517028809 0.05936622619628906 8.160713195800781 C 0.05957221984863281 8.158905029296875 0.059722900390625 8.157581329345703 0.05992889404296875 8.155774116516113 C 0.05999374389648438 8.155196189880371 0.06004714965820312 8.15473461151123 0.06011390686035156 8.154156684875488 C 0.06038856506347656 8.15174674987793 0.06058311462402344 8.150050163269043 0.06086158752441406 8.147640228271484 C 0.0609130859375 8.147177696228027 0.06097793579101562 8.146628379821777 0.06103134155273438 8.14616584777832 C 0.06123924255371094 8.14435863494873 0.06141853332519531 8.142802238464355 0.06162834167480469 8.140995025634766 C 0.06170845031738281 8.140302658081055 0.06177711486816406 8.139711380004883 0.06185722351074219 8.139018058776855 C 0.06206703186035156 8.137211799621582 0.06228256225585938 8.135363578796387 0.06249237060546875 8.133556365966797 C 0.06253242492675781 8.133210182189941 0.06256866455078125 8.132898330688477 0.06260871887207031 8.132552146911621 C 0.06289100646972656 8.130143165588379 0.06313133239746094 8.128083229064941 0.06341552734375 8.125675201416016 C 0.06344223022460938 8.125444412231445 0.06344795227050781 8.125388145446777 0.0634765625 8.125157356262207 C 0.06372451782226562 8.123050689697266 0.06393814086914062 8.121227264404297 0.06418800354003906 8.119120597839355 C 0.06425666809082031 8.11854362487793 0.06433868408203125 8.117850303649902 0.0644073486328125 8.117273330688477 C 0.0646209716796875 8.11546802520752 0.06479072570800781 8.114027976989746 0.06500625610351562 8.112222671508789 C 0.06507492065429688 8.111645698547363 0.06513214111328125 8.111161231994629 0.0652008056640625 8.110584259033203 C 0.06570243835449219 8.106371879577637 0.06627464294433594 8.101603507995605 0.06678199768066406 8.097392082214355 C 0.06686592102050781 8.096700668334961 0.06693649291992188 8.096118927001953 0.06702041625976562 8.095426559448242 C 0.06723785400390625 8.093622207641602 0.06739044189453125 8.09235954284668 0.06760978698730469 8.090555191040039 C 0.06768035888671875 8.08997917175293 0.06775856018066406 8.089332580566406 0.06782913208007812 8.088756561279297 C 0.06804847717285156 8.086952209472656 0.06831550598144531 8.084757804870605 0.06853675842285156 8.082954406738281 C 0.06856536865234375 8.082723617553711 0.06861114501953125 8.082333564758301 0.06863975524902344 8.082103729248047 C 0.06889724731445312 8.079998970031738 0.06919097900390625 8.077614784240723 0.06944847106933594 8.07551097869873 C 0.06951904296875 8.074934959411621 0.06958198547363281 8.074428558349609 0.06965446472167969 8.0738525390625 C 0.06987571716308594 8.072049140930176 0.07003402709960938 8.070766448974609 0.07025718688964844 8.068964004516602 C 0.070343017578125 8.068273544311523 0.0704345703125 8.067535400390625 0.07052040100097656 8.06684398651123 C 0.07074356079101562 8.065041542053223 0.07092475891113281 8.063584327697754 0.07114982604980469 8.061782836914062 C 0.07120704650878906 8.061322212219238 0.07128524780273438 8.060691833496094 0.07134246826171875 8.06023120880127 C 0.07164192199707031 8.057827949523926 0.07185173034667969 8.056155204772949 0.07215309143066406 8.053751945495605 C 0.07222557067871094 8.053176879882812 0.07226753234863281 8.052834510803223 0.07234001159667969 8.05225944519043 C 0.07256698608398438 8.050457000732422 0.07275581359863281 8.048954010009766 0.0729827880859375 8.047152519226074 C 0.07307052612304688 8.046462059020996 0.07315444946289062 8.045807838439941 0.07324028015136719 8.04511833190918 C 0.07346916198730469 8.043316841125488 0.07362556457519531 8.042080879211426 0.07385444641113281 8.040279388427734 C 0.07394218444824219 8.039588928222656 0.07400321960449219 8.039104461669922 0.07409095764160156 8.038414001464844 C 0.07435798645019531 8.036313056945801 0.07465171813964844 8.034014701843262 0.07491874694824219 8.031913757324219 C 0.07496452331542969 8.03156852722168 0.0749664306640625 8.031548500061035 0.07501029968261719 8.031203269958496 C 0.0752410888671875 8.029402732849121 0.07552528381347656 8.027190208435059 0.07575607299804688 8.025390625 C 0.07584571838378906 8.024700164794922 0.07592010498046875 8.024121284484863 0.07600975036621094 8.023430824279785 C 0.07624053955078125 8.021631240844727 0.076416015625 8.020277976989746 0.07664871215820312 8.018478393554688 C 0.07672309875488281 8.017903327941895 0.0767974853515625 8.01732349395752 0.07687187194824219 8.016749382019043 C 0.07710647583007812 8.014949798583984 0.07741546630859375 8.012568473815918 0.07765007019042969 8.010768890380859 C 0.07767868041992188 8.010539054870605 0.07770538330078125 8.010333061218262 0.07773590087890625 8.010103225708008 C 0.07800865173339844 8.008004188537598 0.0783233642578125 8.005609512329102 0.0785980224609375 8.003510475158691 C 0.07865715026855469 8.003050804138184 0.07873153686523438 8.002486228942871 0.07879257202148438 8.002026557922363 C 0.07902908325195312 8.000227928161621 0.079254150390625 7.998516082763672 0.07949066162109375 7.99671745300293 C 0.07955169677734375 7.996257781982422 0.07964706420898438 7.995529174804688 0.07970809936523438 7.99506950378418 C 0.07994461059570312 7.993270874023438 0.08021926879882812 7.991202354431152 0.08045768737792969 7.989404678344727 C 0.08050346374511719 7.989059448242188 0.08054351806640625 7.988751411437988 0.08058929443359375 7.988406181335449 C 0.08090782165527344 7.98600959777832 0.081146240234375 7.984214782714844 0.0814666748046875 7.981818199157715 C 0.0815277099609375 7.981358528137207 0.08160018920898438 7.980825424194336 0.08166122436523438 7.980366706848145 C 0.08190155029296875 7.978569030761719 0.08212471008300781 7.976901054382324 0.08236503601074219 7.975104331970215 C 0.0824432373046875 7.974530220031738 0.08252525329589844 7.973922729492188 0.08260154724121094 7.973348617553711 C 0.08284378051757812 7.971550941467285 0.08308029174804688 7.969786643981934 0.08332443237304688 7.967989921569824 C 0.08338546752929688 7.967531204223633 0.08343505859375 7.967162132263184 0.08349800109863281 7.966703414916992 C 0.08382034301757812 7.96430778503418 0.08408927917480469 7.96232795715332 0.08441543579101562 7.959933280944824 C 0.08443069458007812 7.959818840026855 0.08444404602050781 7.959724426269531 0.08445930480957031 7.959609985351562 C 0.08474349975585938 7.957513809204102 0.08502769470214844 7.955427169799805 0.08531379699707031 7.95333194732666 C 0.08537673950195312 7.952873229980469 0.0854644775390625 7.952239990234375 0.08552742004394531 7.951781272888184 C 0.08577346801757812 7.949985504150391 0.08601188659667969 7.948236465454102 0.08625984191894531 7.946440696716309 C 0.08632278442382812 7.945981979370117 0.08639335632324219 7.945466041564941 0.086456298828125 7.94500732421875 C 0.08703231811523438 7.940817832946777 0.08770561218261719 7.935953140258789 0.088287353515625 7.931765556335449 C 0.08838272094726562 7.93107795715332 0.08844757080078125 7.930617332458496 0.08854293823242188 7.929929733276367 C 0.08875083923339844 7.928434371948242 0.0889739990234375 7.92684268951416 0.08918380737304688 7.925347328186035 C 0.08929443359375 7.924545288085938 0.08939933776855469 7.923801422119141 0.08951187133789062 7.922999382019043 C 0.08976173400878906 7.921205520629883 0.09001541137695312 7.919402122497559 0.09026718139648438 7.917609214782715 C 0.0903167724609375 7.917264938354492 0.09035682678222656 7.91697883605957 0.09040451049804688 7.916634559631348 C 0.0906982421875 7.914542198181152 0.09102821350097656 7.912209510803223 0.0913238525390625 7.910117149353027 C 0.09140586853027344 7.909544944763184 0.09148597717285156 7.908977508544922 0.0915679931640625 7.908404350280762 C 0.09182167053222656 7.906611442565918 0.092010498046875 7.905275344848633 0.09226608276367188 7.903482437133789 C 0.09234809875488281 7.902910232543945 0.09246635437011719 7.902072906494141 0.09254837036132812 7.901500701904297 C 0.092803955078125 7.899707794189453 0.09300422668457031 7.898298263549805 0.09326171875 7.896505355834961 C 0.09334373474121094 7.895933151245117 0.09341049194335938 7.895456314086914 0.09349250793457031 7.894883155822754 C 0.09383583068847656 7.892494201660156 0.09409141540527344 7.890704154968262 0.0944366455078125 7.888315200805664 C 0.09446907043457031 7.888086318969727 0.09453964233398438 7.887590408325195 0.094573974609375 7.887361526489258 C 0.09483146667480469 7.88556957244873 0.0951080322265625 7.883657455444336 0.095367431640625 7.881865501403809 C 0.09546661376953125 7.881178855895996 0.09555816650390625 7.880537033081055 0.09565925598144531 7.879850387573242 C 0.09591865539550781 7.878059387207031 0.0961151123046875 7.876699447631836 0.09637641906738281 7.874908447265625 C 0.09644317626953125 7.87445068359375 0.09654426574707031 7.873746871948242 0.09661102294921875 7.873290061950684 C 0.09691619873046875 7.871200561523438 0.09726715087890625 7.868788719177246 0.09757232666015625 7.866700172424316 C 0.09758949279785156 7.866585731506348 0.09764289855957031 7.866227149963379 0.09765815734863281 7.866113662719727 C 0.09792137145996094 7.864322662353516 0.0982513427734375 7.862077713012695 0.09851455688476562 7.860287666320801 C 0.09863090515136719 7.859487533569336 0.09870719909667969 7.858969688415527 0.09882545471191406 7.858169555664062 C 0.09908866882324219 7.856379508972168 0.09923362731933594 7.855402946472168 0.09949874877929688 7.853612899780273 C 0.09959983825683594 7.852927207946777 0.09970664978027344 7.852206230163574 0.0998077392578125 7.851520538330078 C 0.1001167297363281 7.849432945251465 0.1004428863525391 7.847236633300781 0.1007537841796875 7.845149040222168 C 0.1007537841796875 7.845149040222168 0.1007556915283203 7.845134735107422 0.1007556915283203 7.845134735107422 C 0.1010665893554688 7.843047142028809 0.1014003753662109 7.840810775756836 0.1017112731933594 7.838723182678223 C 0.1018314361572266 7.837924003601074 0.1019287109375 7.837272644042969 0.1020488739013672 7.83647346496582 C 0.1022720336914062 7.834982872009277 0.1024799346923828 7.833596229553223 0.1027030944824219 7.83210563659668 C 0.1028060913085938 7.831419944763184 0.1029415130615234 7.830526351928711 0.1030445098876953 7.829840660095215 C 0.1033134460449219 7.828052520751953 0.1035213470458984 7.826671600341797 0.1037921905517578 7.824884414672852 C 0.1038608551025391 7.824427604675293 0.1039505004882812 7.823831558227539 0.1040191650390625 7.823375701904297 C 0.1043338775634766 7.8212890625 0.1046695709228516 7.819082260131836 0.1049861907958984 7.816996574401855 C 0.105072021484375 7.816426277160645 0.1051731109619141 7.815768241882324 0.1052608489990234 7.815196990966797 C 0.1055316925048828 7.813409805297852 0.1056995391845703 7.812313079833984 0.1059722900390625 7.810525894165039 C 0.1060771942138672 7.809841156005859 0.1062202453613281 7.808896064758301 0.1063251495361328 7.808211326599121 C 0.106597900390625 7.806424140930176 0.1067886352539062 7.80518913269043 0.1070613861083984 7.803401947021484 C 0.1071491241455078 7.802831649780273 0.1072177886962891 7.802393913269043 0.1073036193847656 7.801823616027832 C 0.1076698303222656 7.799441337585449 0.1079387664794922 7.797701835632324 0.108306884765625 7.795320510864258 C 0.1083583831787109 7.794978141784668 0.1084327697753906 7.794503211975098 0.1084861755371094 7.794161796569824 C 0.1087608337402344 7.792375564575195 0.1090526580810547 7.790493965148926 0.1093292236328125 7.788707733154297 C 0.1094188690185547 7.788137435913086 0.1095161437988281 7.787510871887207 0.1096038818359375 7.786940574645996 C 0.1098804473876953 7.785155296325684 0.1101284027099609 7.783565521240234 0.1104068756103516 7.781781196594238 C 0.1104774475097656 7.78132438659668 0.1105613708496094 7.780791282653809 0.1106319427490234 7.780335426330566 C 0.1112823486328125 7.77617073059082 0.1120491027832031 7.77127742767334 0.1127052307128906 7.767114639282227 C 0.1127948760986328 7.766544342041016 0.1128768920898438 7.766030311584473 0.1129665374755859 7.765460014343262 C 0.1132469177246094 7.763676643371582 0.1134910583496094 7.762140274047852 0.1137733459472656 7.760356903076172 C 0.1138629913330078 7.759786605834961 0.1139354705810547 7.759333610534668 0.1140251159667969 7.758763313293457 C 0.1143074035644531 7.756979942321777 0.1146945953369141 7.754539489746094 0.1149787902832031 7.752756118774414 C 0.1150150299072266 7.752528190612793 0.1150398254394531 7.75236988067627 0.1150760650634766 7.752141952514648 C 0.1154556274414062 7.749764442443848 0.1157665252685547 7.747814178466797 0.1161479949951172 7.745436668395996 C 0.1162014007568359 7.745095252990723 0.1162643432617188 7.744710922241211 0.1163177490234375 7.744369506835938 C 0.1166038513183594 7.742587089538574 0.1169242858886719 7.740593910217285 0.1172103881835938 7.738811492919922 C 0.1173019409179688 7.738242149353027 0.1173629760742188 7.737859725952148 0.1174545288085938 7.737290382385254 C 0.1177425384521484 7.735507965087891 0.1181068420410156 7.733238220214844 0.1183948516845703 7.731456756591797 C 0.1184329986572266 7.731228828430176 0.1184864044189453 7.730895042419434 0.1185226440429688 7.730667114257812 C 0.1189079284667969 7.728291511535645 0.1192226409912109 7.726347923278809 0.1196079254150391 7.723973274230957 C 0.1196632385253906 7.723631858825684 0.1197242736816406 7.723259925842285 0.1197795867919922 7.722919464111328 C 0.1200695037841797 7.721138000488281 0.1203880310058594 7.719178199768066 0.1206779479980469 7.717397689819336 C 0.1207714080810547 7.716828346252441 0.1208572387695312 7.71630859375 0.1209487915039062 7.715740203857422 C 0.1212406158447266 7.713959693908691 0.1215152740478516 7.712273597717285 0.1218070983886719 7.710493087768555 C 0.1218833923339844 7.710038185119629 0.1219711303710938 7.709493637084961 0.1220474243164062 7.709038734436035 C 0.1223869323730469 7.706960678100586 0.1227970123291016 7.704466819763184 0.1231403350830078 7.702389717102051 C 0.1231403350830078 7.702389717102051 0.1231651306152344 7.702241897583008 0.1231651306152344 7.702241897583008 C 0.1235065460205078 7.700164794921875 0.1238574981689453 7.698044776916504 0.1242008209228516 7.695968627929688 C 0.1242961883544922 7.695400238037109 0.1243953704833984 7.694799423217773 0.1244888305664062 7.694231033325195 C 0.1247844696044922 7.692451477050781 0.1250705718994141 7.690731048583984 0.1253662109375 7.68895149230957 C 0.1254425048828125 7.688496589660645 0.1255073547363281 7.688098907470703 0.1255836486816406 7.687644004821777 C 0.1262760162353516 7.683493614196777 0.1270980834960938 7.678573608398438 0.1277961730957031 7.674424171447754 C 0.1278724670410156 7.673970222473145 0.1279754638671875 7.673354148864746 0.1280517578125 7.672900199890137 C 0.1283512115478516 7.671121597290039 0.1286144256591797 7.669554710388184 0.1289157867431641 7.667777061462402 C 0.1290111541748047 7.667208671569824 0.1291179656982422 7.666576385498047 0.1292133331298828 7.666008949279785 C 0.1295146942138672 7.664231300354004 0.1299037933349609 7.661937713623047 0.1302051544189453 7.660160064697266 C 0.1302433013916016 7.659933090209961 0.1302680969238281 7.659788131713867 0.1303062438964844 7.659561157226562 C 0.1307086944580078 7.657191276550293 0.1310272216796875 7.65532112121582 0.1314296722412109 7.652951240539551 C 0.1315078735351562 7.652497291564941 0.1315898895263672 7.652018547058105 0.1316661834716797 7.651564598083496 C 0.1319694519042969 7.649787902832031 0.1322593688964844 7.648094177246094 0.1325645446777344 7.646317481994629 C 0.1326618194580078 7.645750045776367 0.1327648162841797 7.645149230957031 0.1328620910644531 7.64458179473877 C 0.1331653594970703 7.642805099487305 0.1334266662597656 7.64128589630127 0.1337318420410156 7.639510154724121 C 0.1338100433349609 7.639056205749512 0.1339244842529297 7.63839054107666 0.134002685546875 7.637936592102051 C 0.1344108581542969 7.635568618774414 0.1347198486328125 7.633774757385254 0.1351299285888672 7.631407737731934 C 0.1351890563964844 7.631067276000977 0.1351985931396484 7.631011962890625 0.1352577209472656 7.630672454833984 C 0.1355648040771484 7.628896713256836 0.1359481811523438 7.626688003540039 0.1362552642822266 7.624913215637207 C 0.1363735198974609 7.624232292175293 0.1364479064941406 7.623808860778809 0.136566162109375 7.623128890991211 C 0.1368751525878906 7.621354103088379 0.1371345520019531 7.619864463806152 0.1374435424804688 7.61808967590332 C 0.137542724609375 7.617523193359375 0.1376113891601562 7.617130279541016 0.1377086639404297 7.61656379699707 C 0.1380710601806641 7.614493370056152 0.1384944915771484 7.612071990966797 0.1388568878173828 7.610001564025879 C 0.1388778686523438 7.609888076782227 0.138885498046875 7.609843254089355 0.1389045715332031 7.60973072052002 C 0.1392688751220703 7.607660293579102 0.1396598815917969 7.605429649353027 0.1400260925292969 7.603361129760742 C 0.1401042938232422 7.602907180786133 0.1401939392089844 7.602407455444336 0.1402740478515625 7.601954460144043 C 0.1405849456787109 7.600180625915527 0.1408634185791016 7.598613739013672 0.1411762237548828 7.596839904785156 C 0.1412773132324219 7.596273422241211 0.1413917541503906 7.595619201660156 0.1414928436279297 7.595053672790527 C 0.1418590545654297 7.592985153198242 0.1421794891357422 7.591178894042969 0.142547607421875 7.589110374450684 C 0.1425666809082031 7.588996887207031 0.1426181793212891 7.588705062866211 0.14263916015625 7.588591575622559 C 0.1430072784423828 7.586523056030273 0.1434078216552734 7.584269523620605 0.1437778472900391 7.582201957702637 C 0.1438980102539062 7.581522941589355 0.1440219879150391 7.580831527709961 0.1441440582275391 7.580151557922363 C 0.1444072723388672 7.578675270080566 0.1446895599365234 7.577101707458496 0.1449546813964844 7.575625419616699 C 0.1450958251953125 7.574832916259766 0.1451835632324219 7.574346542358398 0.1453266143798828 7.573554992675781 C 0.1455917358398438 7.572077751159668 0.1459197998046875 7.570247650146484 0.1461868286132812 7.568771362304688 C 0.1462879180908203 7.568205833435059 0.1464042663574219 7.567554473876953 0.1465072631835938 7.566988945007324 C 0.1469326019287109 7.564627647399902 0.1472339630126953 7.562955856323242 0.1476631164550781 7.56059455871582 C 0.1477241516113281 7.560256004333496 0.1478195190429688 7.559727668762207 0.1478805541992188 7.559388160705566 C 0.1482009887695312 7.557618141174316 0.1484794616699219 7.556086540222168 0.1488018035888672 7.554316520690918 C 0.1489448547363281 7.553524971008301 0.1490688323974609 7.552844047546387 0.1492137908935547 7.552053451538086 C 0.1495342254638672 7.55028247833252 0.1497325897216797 7.549197196960449 0.1500568389892578 7.547427177429199 C 0.1501598358154297 7.546862602233887 0.1502761840820312 7.54621696472168 0.1503810882568359 7.545651435852051 C 0.1507568359375 7.543586730957031 0.1511783599853516 7.541284561157227 0.1515579223632812 7.539219856262207 C 0.1515998840332031 7.538993835449219 0.1516590118408203 7.538671493530273 0.1517009735107422 7.538445472717285 C 0.1520252227783203 7.536676406860352 0.1524181365966797 7.534533500671387 0.1527442932128906 7.532764434814453 C 0.1528491973876953 7.532199859619141 0.1529827117919922 7.531474113464355 0.1530876159667969 7.530909538269043 C 0.1534137725830078 7.529140472412109 0.1536293029785156 7.527969360351562 0.1539554595947266 7.526201248168945 C 0.1540813446044922 7.52552318572998 0.1541976928710938 7.524899482727051 0.1543235778808594 7.524221420288086 C 0.1550865173339844 7.520094871520996 0.1559352874755859 7.515536308288574 0.1567039489746094 7.511411666870117 C 0.1568088531494141 7.510847091674805 0.1569290161132812 7.510204315185547 0.1570358276367188 7.509640693664551 C 0.1573657989501953 7.507872581481934 0.1576309204101562 7.50645923614502 0.1579608917236328 7.504692077636719 C 0.1580677032470703 7.504127502441406 0.158172607421875 7.503560066223145 0.1582794189453125 7.502996444702148 C 0.1586112976074219 7.501229286193848 0.1589908599853516 7.499204635620117 0.1593246459960938 7.497438430786133 C 0.1593875885009766 7.497099876403809 0.1594429016113281 7.496805191040039 0.1595058441162109 7.496466636657715 C 0.1598949432373047 7.494405746459961 0.1603279113769531 7.49211597442627 0.1607170104980469 7.490056037902832 C 0.1608238220214844 7.48949146270752 0.1609344482421875 7.488908767700195 0.161041259765625 7.488345146179199 C 0.1613750457763672 7.486578941345215 0.1615924835205078 7.48543643951416 0.16192626953125 7.483670234680176 C 0.1620769500732422 7.482880592346191 0.1622142791748047 7.482157707214355 0.1623649597167969 7.481369018554688 C 0.1626434326171875 7.479897499084473 0.1629543304443359 7.478267669677734 0.1632347106933594 7.47679615020752 C 0.1633415222167969 7.476232528686523 0.1634616851806641 7.475604057312012 0.1635684967041016 7.475040435791016 C 0.1640186309814453 7.472686767578125 0.1643047332763672 7.471186637878418 0.1647567749023438 7.468832969665527 C 0.1648635864257812 7.468270301818848 0.1650238037109375 7.467433929443359 0.1651325225830078 7.46687126159668 C 0.1654148101806641 7.465400695800781 0.1657123565673828 7.463844299316406 0.1659965515136719 7.462373733520508 C 0.1661472320556641 7.46158504486084 0.1662960052490234 7.460818290710449 0.1664466857910156 7.460029602050781 C 0.1667308807373047 7.458559036254883 0.1669864654541016 7.457230567932129 0.1672706604003906 7.455760955810547 C 0.1674232482910156 7.454972267150879 0.1675300598144531 7.454409599304199 0.1676826477050781 7.453620910644531 C 0.1680812835693359 7.451563835144043 0.1684913635253906 7.449447631835938 0.1688899993896484 7.447390556335449 C 0.1689548492431641 7.447052955627441 0.1690177917480469 7.44672966003418 0.1690845489501953 7.446391105651855 C 0.1694259643554688 7.444628715515137 0.1697921752929688 7.442743301391602 0.170135498046875 7.440980911254883 C 0.1702671051025391 7.440304756164551 0.1704139709472656 7.439553260803223 0.1705455780029297 7.438878059387207 C 0.1708889007568359 7.437115669250488 0.1710968017578125 7.436059951782227 0.1714401245117188 7.434297561645508 C 0.1715507507324219 7.433733940124512 0.17169189453125 7.433012008666992 0.1718025207519531 7.432449340820312 C 0.1721477508544922 7.430686950683594 0.1725902557373047 7.42842960357666 0.1729354858398438 7.426667213439941 C 0.1729793548583984 7.42644214630127 0.1730270385742188 7.426198959350586 0.1730709075927734 7.425973892211914 C 0.1734752655029297 7.423918724060059 0.1739158630371094 7.421683311462402 0.1743202209472656 7.419628143310547 C 0.1744537353515625 7.418952941894531 0.1745777130126953 7.41832447052002 0.1747112274169922 7.417649269104004 C 0.1749439239501953 7.416475296020508 0.1753349304199219 7.414496421813965 0.175567626953125 7.413322448730469 C 0.1757240295410156 7.412534713745117 0.1758880615234375 7.41170597076416 0.1760425567626953 7.410918235778809 C 0.1763343811035156 7.409451484680176 0.176666259765625 7.407779693603516 0.1769580841064453 7.406312942504883 C 0.1770687103271484 7.405750274658203 0.1771926879882812 7.405128479003906 0.1773052215576172 7.404565811157227 C 0.1777725219726562 7.402218818664551 0.1780738830566406 7.400701522827148 0.1785430908203125 7.398355484008789 C 0.1786766052246094 7.39768123626709 0.1787776947021484 7.397178649902344 0.1789131164550781 7.396504402160645 C 0.1792068481445312 7.395037651062012 0.1795253753662109 7.39344310760498 0.1798191070556641 7.391977310180664 C 0.1799774169921875 7.391190528869629 0.1801109313964844 7.390519142150879 0.1802692413330078 7.389732360839844 C 0.1805629730224609 7.388266563415527 0.1808567047119141 7.386811256408691 0.1811504364013672 7.385345458984375 C 0.1812858581542969 7.384671211242676 0.1814498901367188 7.383858680725098 0.1815853118896484 7.383184432983398 C 0.1819992065429688 7.381133079528809 0.1823997497558594 7.379144668579102 0.1828155517578125 7.377093315124512 C 0.1829280853271484 7.376531600952148 0.1830348968505859 7.376007080078125 0.1831474304199219 7.375446319580078 C 0.1834449768066406 7.373980522155762 0.1837921142578125 7.372261047363281 0.1840896606445312 7.370795249938965 C 0.1842498779296875 7.370009422302246 0.1844310760498047 7.369119644165039 0.1845893859863281 7.368332862854004 C 0.1848278045654297 7.367161750793457 0.1851539611816406 7.365558624267578 0.1853923797607422 7.364387512207031 C 0.1855754852294922 7.363489151000977 0.1857433319091797 7.362669944763184 0.1859264373779297 7.361772537231445 C 0.1862239837646484 7.360307693481445 0.1865043640136719 7.35893726348877 0.1868038177490234 7.357473373413086 C 0.1870098114013672 7.356463432312012 0.1872673034667969 7.355204582214355 0.1874752044677734 7.354194641113281 C 0.187774658203125 7.352730751037598 0.1881313323974609 7.350984573364258 0.1884326934814453 7.349520683288574 C 0.1885948181152344 7.348734855651855 0.1887302398681641 7.34807014465332 0.1888923645019531 7.347284317016602 C 0.1891937255859375 7.345821380615234 0.1894874572753906 7.344395637512207 0.189788818359375 7.34293270111084 C 0.1899509429931641 7.342147827148438 0.1900920867919922 7.34145450592041 0.1902542114257812 7.340668678283691 C 0.1905574798583984 7.339205741882324 0.1908912658691406 7.33758544921875 0.1911945343017578 7.336122512817383 C 0.1913337707519531 7.335450172424316 0.1914329528808594 7.33497428894043 0.1915721893310547 7.334300994873047 C 0.1919956207275391 7.332253456115723 0.1923866271972656 7.330366134643555 0.1928119659423828 7.32831859588623 C 0.1929759979248047 7.327533721923828 0.1931438446044922 7.326730728149414 0.1933059692382812 7.325945854187012 C 0.1936111450195312 7.324483871459961 0.1938514709472656 7.323329925537109 0.1941566467285156 7.321867942810059 C 0.1943187713623047 7.321083068847656 0.1945228576660156 7.320112228393555 0.1946849822998047 7.319328308105469 C 0.1949920654296875 7.317866325378418 0.1952037811279297 7.316845893859863 0.1955108642578125 7.315383911132812 C 0.1956748962402344 7.31459903717041 0.1958465576171875 7.313776016235352 0.1960105895996094 7.312992095947266 C 0.1963787078857422 7.311237335205078 0.1968975067138672 7.308768272399902 0.197265625 7.307015419006348 C 0.1974067687988281 7.306343078613281 0.1975421905517578 7.305704116821289 0.1976833343505859 7.305031776428223 C 0.1979904174804688 7.303570747375488 0.1982975006103516 7.302115440368652 0.1986064910888672 7.300654411315918 C 0.1987724304199219 7.299870491027832 0.1989707946777344 7.298931121826172 0.1991348266601562 7.298148155212402 C 0.1993827819824219 7.296978950500488 0.1997489929199219 7.295244216918945 0.1999969482421875 7.294075012207031 C 0.2001628875732422 7.293292045593262 0.2002983093261719 7.292649269104004 0.2004661560058594 7.291866302490234 C 0.200836181640625 7.29011344909668 0.2013301849365234 7.287792205810547 0.2017021179199219 7.286040306091309 C 0.2018222808837891 7.285480499267578 0.2019519805908203 7.284869194030762 0.2020721435546875 7.284309387207031 C 0.2024440765380859 7.282557487487793 0.2027626037597656 7.281062126159668 0.2031364440917969 7.27931022644043 C 0.2033042907714844 7.27852725982666 0.2034263610839844 7.277953147888184 0.2035942077636719 7.277170181274414 C 0.2038440704345703 7.276002883911133 0.2042407989501953 7.274149894714355 0.2044906616210938 7.272981643676758 C 0.2046585083007812 7.272198677062988 0.2048244476318359 7.271425247192383 0.2049922943115234 7.270642280578613 C 0.2053680419921875 7.268891334533691 0.2057342529296875 7.267182350158691 0.2061119079589844 7.265432357788086 C 0.2061843872070312 7.265096664428711 0.2062702178955078 7.264698028564453 0.2063407897949219 7.264362335205078 C 0.2067813873291016 7.262320518493652 0.2072677612304688 7.260068893432617 0.2077083587646484 7.258027076721191 C 0.2078285217285156 7.257468223571777 0.2079505920410156 7.256902694702148 0.2080726623535156 7.256343841552734 C 0.2084503173828125 7.254593849182129 0.2087020874023438 7.253434181213379 0.2090797424316406 7.25168514251709 C 0.2092266082763672 7.251014709472656 0.2093982696533203 7.250221252441406 0.2095432281494141 7.249550819396973 C 0.2098598480224609 7.248092651367188 0.2102031707763672 7.246509552001953 0.2105216979980469 7.245052337646484 C 0.2106666564941406 7.244381904602051 0.2108078002929688 7.243731498718262 0.2109546661376953 7.243061065673828 C 0.2113990783691406 7.241020202636719 0.2118434906005859 7.238980293273926 0.2122898101806641 7.236940383911133 C 0.2124118804931641 7.236382484436035 0.2125320434570312 7.235830307006836 0.2126541137695312 7.235271453857422 C 0.2130355834960938 7.233523368835449 0.2133007049560547 7.232316970825195 0.21368408203125 7.230568885803223 C 0.2138309478759766 7.229899406433105 0.2140007019042969 7.229124069213867 0.2141475677490234 7.22845458984375 C 0.2145309448242188 7.226706504821777 0.2147846221923828 7.225552558898926 0.2151699066162109 7.223804473876953 C 0.2152919769287109 7.223246574401855 0.2154350280761719 7.222592353820801 0.2155590057373047 7.222034454345703 C 0.2160072326660156 7.219995498657227 0.2164993286132812 7.217765808105469 0.216949462890625 7.215727806091309 C 0.2170238494873047 7.21539306640625 0.2171039581298828 7.215029716491699 0.2171783447265625 7.214694976806641 C 0.2175636291503906 7.212947845458984 0.2179508209228516 7.21120548248291 0.2183380126953125 7.20945930480957 C 0.2184867858886719 7.208789825439453 0.2186546325683594 7.208027839660645 0.2188034057617188 7.207358360290527 C 0.2191905975341797 7.205612182617188 0.2194137573242188 7.204608917236328 0.2198028564453125 7.202862739562988 C 0.2199516296386719 7.202193260192871 0.2200908660888672 7.201562881469727 0.2202415466308594 7.200894355773926 C 0.2211475372314453 7.196819305419922 0.2221260070800781 7.192442893981934 0.2230396270751953 7.188370704650879 C 0.2231884002685547 7.187702178955078 0.2233524322509766 7.186971664428711 0.2235031127929688 7.18630313873291 C 0.2238292694091797 7.184848785400391 0.2242088317871094 7.183161735534668 0.2245349884033203 7.181707382202148 C 0.2246608734130859 7.181150436401367 0.2248191833496094 7.180444717407227 0.224945068359375 7.179887771606445 C 0.22540283203125 7.177852630615234 0.2258090972900391 7.176052093505859 0.2262687683105469 7.174016952514648 C 0.2262935638427734 7.173906326293945 0.2263412475585938 7.173697471618652 0.2263660430908203 7.173585891723633 C 0.2268257141113281 7.171551704406738 0.2273597717285156 7.169191360473633 0.2278213500976562 7.167157173156738 C 0.2279472351074219 7.166600227355957 0.2280635833740234 7.166091918945312 0.2281894683837891 7.165534973144531 C 0.2285842895507812 7.163790702819824 0.2288818359375 7.162484169006348 0.229278564453125 7.160741806030273 C 0.2294292449951172 7.160073280334473 0.2295856475830078 7.159390449523926 0.229736328125 7.158721923828125 C 0.230133056640625 7.156979560852051 0.2304725646972656 7.15549373626709 0.2308712005615234 7.153750419616699 C 0.2309722900390625 7.153306007385254 0.2310714721679688 7.152871131896973 0.2311725616455078 7.152425765991211 C 0.2317028045654297 7.150102615356445 0.232086181640625 7.148428916931152 0.2326183319091797 7.146105766296387 C 0.2327461242675781 7.145549774169922 0.2328300476074219 7.145184516906738 0.2329578399658203 7.144628524780273 C 0.2333564758300781 7.142886161804199 0.2336826324462891 7.141464233398438 0.2340831756591797 7.13972282409668 C 0.2342624664306641 7.138944625854492 0.2343921661376953 7.138381004333496 0.2345714569091797 7.137602806091309 C 0.2349052429199219 7.136151313781738 0.2352218627929688 7.134783744812012 0.2355556488037109 7.133332252502441 C 0.2357349395751953 7.132554054260254 0.2358722686767578 7.131961822509766 0.2360515594482422 7.131183624267578 C 0.2365207672119141 7.129152297973633 0.2370014190673828 7.127074241638184 0.2374706268310547 7.125043869018555 C 0.237548828125 7.124710083007812 0.2376327514648438 7.124348640441895 0.2377090454101562 7.124015808105469 C 0.2381134033203125 7.122274398803711 0.2385501861572266 7.120393753051758 0.2389545440673828 7.118654251098633 C 0.2391090393066406 7.117986679077148 0.2392406463623047 7.117425918579102 0.2393951416015625 7.116759300231934 C 0.2397994995117188 7.115018844604492 0.2401027679443359 7.11371898651123 0.2405071258544922 7.111979484558105 C 0.2406368255615234 7.111423492431641 0.2407588958740234 7.110902786254883 0.2408885955810547 7.110346794128418 C 0.2412948608398438 7.108607292175293 0.2417926788330078 7.106473922729492 0.2422008514404297 7.104735374450684 C 0.242279052734375 7.104401588439941 0.2423305511474609 7.104179382324219 0.2424087524414062 7.103845596313477 C 0.2428836822509766 7.10181713104248 0.243408203125 7.099579811096191 0.2438850402832031 7.097551345825195 C 0.2440147399902344 7.09699535369873 0.244140625 7.096462249755859 0.2442703247070312 7.095907211303711 C 0.2446804046630859 7.094168663024902 0.2449836730957031 7.092878341674805 0.2453937530517578 7.091139793395996 C 0.2455234527587891 7.090584754943848 0.2456932067871094 7.089868545532227 0.2458229064941406 7.089313507080078 C 0.2462329864501953 7.08757495880127 0.2466793060302734 7.085688591003418 0.2470893859863281 7.083950996398926 C 0.2471694946289062 7.0836181640625 0.2472629547119141 7.083221435546875 0.2473411560058594 7.082888603210449 C 0.2478904724121094 7.080571174621582 0.2482814788818359 7.078915596008301 0.2488327026367188 7.07659912109375 C 0.2489643096923828 7.076044082641602 0.2490978240966797 7.075480461120605 0.2492294311523438 7.074926376342773 C 0.2496414184570312 7.073188781738281 0.2499313354492188 7.07197093963623 0.2503452301025391 7.070234298706055 C 0.2505035400390625 7.069568634033203 0.2506599426269531 7.068918228149414 0.2508182525634766 7.068252563476562 C 0.2512321472167969 7.066515922546387 0.2515316009521484 7.065258026123047 0.2519474029541016 7.063522338867188 C 0.2520790100097656 7.062967300415039 0.252227783203125 7.062343597412109 0.2523612976074219 7.061788558959961 C 0.2528457641601562 7.05976390838623 0.2533740997314453 7.057554244995117 0.2538604736328125 7.055529594421387 C 0.2539405822753906 7.055196762084961 0.2540283203125 7.054832458496094 0.2541065216064453 7.054499626159668 C 0.2545242309570312 7.052763938903809 0.2549247741699219 7.051095962524414 0.2553424835205078 7.049360275268555 C 0.2555027008056641 7.04869556427002 0.255706787109375 7.047844886779785 0.2558670043945312 7.04718017578125 C 0.2562847137451172 7.045445442199707 0.2565212249755859 7.044468879699707 0.2569389343261719 7.042734146118164 C 0.2570991516113281 7.042069435119629 0.2572555541992188 7.041424751281738 0.257415771484375 7.040760040283203 C 0.2579059600830078 7.038736343383789 0.2583484649658203 7.036899566650391 0.2588405609130859 7.034876823425293 C 0.2589206695556641 7.034543991088867 0.2589950561523438 7.034233093261719 0.2590770721435547 7.033900260925293 C 0.2594966888427734 7.032166481018066 0.259979248046875 7.030180931091309 0.2604007720947266 7.028447151184082 C 0.260589599609375 7.02767276763916 0.2607746124267578 7.026910781860352 0.2609634399414062 7.026135444641113 C 0.2612438201904297 7.024979591369629 0.2616596221923828 7.023272514343262 0.2619419097900391 7.022117614746094 C 0.2621307373046875 7.021342277526855 0.2623691558837891 7.020365715026855 0.2625579833984375 7.019590377807617 C 0.2629814147949219 7.017857551574707 0.2632884979248047 7.0166015625 0.2637119293212891 7.014869689941406 C 0.2638206481933594 7.014426231384277 0.2639312744140625 7.013974189758301 0.2640399932861328 7.013531684875488 C 0.2645339965820312 7.011509895324707 0.2650470733642578 7.009424209594727 0.2655429840087891 7.007403373718262 C 0.2657051086425781 7.006739616394043 0.2658462524414062 7.006171226501465 0.2660083770751953 7.005507469177246 C 0.2663631439208984 7.004063606262207 0.2667808532714844 7.002364158630371 0.2671375274658203 7.000921249389648 C 0.2673282623291016 7.000146865844727 0.2674598693847656 6.999613761901855 0.2676506042480469 6.998839378356934 C 0.2680072784423828 6.997396469116211 0.2683887481689453 6.995844841003418 0.2687454223632812 6.994402885437012 C 0.2689094543457031 6.993739128112793 0.2690525054931641 6.9931640625 0.2692165374755859 6.992501258850098 C 0.2697162628173828 6.990481376647949 0.2702178955078125 6.98845386505127 0.2707195281982422 6.986434936523438 C 0.2708568572998047 6.985881805419922 0.2710037231445312 6.985292434692383 0.2711391448974609 6.984739303588867 C 0.2715702056884766 6.983009338378906 0.2718620300292969 6.981834411621094 0.2722930908203125 6.980104446411133 C 0.2724838256835938 6.979331016540527 0.2726573944091797 6.978635787963867 0.2728500366210938 6.977862358093262 C 0.2732105255126953 6.976420402526855 0.273529052734375 6.975143432617188 0.2738876342773438 6.973702430725098 C 0.2740802764892578 6.972929000854492 0.2742385864257812 6.972299575805664 0.2744312286376953 6.971526145935059 C 0.2749347686767578 6.969509124755859 0.2754173278808594 6.967580795288086 0.2759227752685547 6.965563774108887 C 0.27606201171875 6.965011596679688 0.2761726379394531 6.964567184448242 0.2763118743896484 6.964015960693359 C 0.2767448425292969 6.962286949157715 0.2771015167236328 6.960867881774902 0.2775363922119141 6.959138870239258 C 0.2777309417724609 6.958366394042969 0.2778739929199219 6.957794189453125 0.2780685424804688 6.957021713256836 C 0.2783584594726562 6.955869674682617 0.27880859375 6.954086303710938 0.2790985107421875 6.952934265136719 C 0.2792930603027344 6.95216178894043 0.2794914245605469 6.951377868652344 0.2796859741210938 6.950605392456055 C 0.2801227569580078 6.948877334594727 0.2806205749511719 6.946908950805664 0.2810573577880859 6.945180892944336 C 0.2811126708984375 6.944960594177246 0.28118896484375 6.944658279418945 0.2812442779541016 6.944437026977539 C 0.2817554473876953 6.942421913146973 0.2822895050048828 6.940311431884766 0.2828025817871094 6.938297271728516 C 0.2829971313476562 6.937524795532227 0.2831478118896484 6.936938285827637 0.2833423614501953 6.936166763305664 C 0.2837085723876953 6.934727668762207 0.2840404510498047 6.933424949645996 0.2844066619873047 6.931985855102539 C 0.2846031188964844 6.931214332580566 0.2847728729248047 6.930549621582031 0.2849693298339844 6.929777145385742 C 0.2853355407714844 6.928339004516602 0.2857551574707031 6.926698684692383 0.2861213684082031 6.925260543823242 C 0.2862625122070312 6.924709320068359 0.2864532470703125 6.923962593078613 0.2865943908691406 6.92341136932373 C 0.2871818542480469 6.921110153198242 0.2875633239746094 6.919619560241699 0.2881546020507812 6.917318344116211 C 0.2882957458496094 6.916768074035645 0.2884349822998047 6.916224479675293 0.2885761260986328 6.915674209594727 C 0.2890186309814453 6.913949012756348 0.2892780303955078 6.91294002532959 0.2897224426269531 6.911215782165527 C 0.289947509765625 6.910334587097168 0.2901210784912109 6.909662246704102 0.2903480529785156 6.908781051635742 C 0.2907180786132812 6.907343864440918 0.2910556793212891 6.906032562255859 0.2914257049560547 6.904596328735352 C 0.2915973663330078 6.903935432434082 0.2917766571044922 6.903238296508789 0.2919464111328125 6.90257740020752 C 0.2924671173095703 6.900566101074219 0.292999267578125 6.898506164550781 0.2935199737548828 6.896495819091797 C 0.2936344146728516 6.896055221557617 0.2937641143798828 6.895553588867188 0.2938785552978516 6.895113945007324 C 0.2943248748779297 6.893389701843262 0.2947006225585938 6.891938209533691 0.2951488494873047 6.890214920043945 C 0.2953205108642578 6.889554977416992 0.2955169677734375 6.888797760009766 0.2956886291503906 6.888136863708496 C 0.2960605621337891 6.886701583862305 0.2964229583740234 6.885313987731934 0.2967967987060547 6.883878707885742 C 0.2969970703125 6.883108139038086 0.2971668243408203 6.882452964782715 0.2973690032958984 6.881682395935059 C 0.2984161376953125 6.877662658691406 0.2995338439941406 6.873390197753906 0.3005867004394531 6.869373321533203 C 0.3007602691650391 6.86871337890625 0.3009319305419922 6.86805534362793 0.3011054992675781 6.867395401000977 C 0.3015575408935547 6.865674018859863 0.3017997741699219 6.864750862121582 0.3022518157958984 6.863030433654785 C 0.3024539947509766 6.862260818481445 0.3026103973388672 6.861665725708008 0.3028144836425781 6.860896110534668 C 0.3032665252685547 6.859174728393555 0.3036556243896484 6.857702255249023 0.3041095733642578 6.855981826782227 C 0.3042240142822266 6.855542182922363 0.3043346405029297 6.855122566223145 0.3044509887695312 6.854682922363281 C 0.3049812316894531 6.852675437927246 0.3055343627929688 6.850583076477051 0.3060646057128906 6.848576545715332 C 0.3062400817871094 6.847916603088379 0.3063735961914062 6.847412109375 0.306549072265625 6.846753120422363 C 0.3070049285888672 6.845032691955566 0.3072948455810547 6.843935012817383 0.3077526092529297 6.842215538024902 C 0.3079566955566406 6.841446876525879 0.3080997467041016 6.840908050537109 0.3083038330078125 6.840139389038086 C 0.3087596893310547 6.838419914245605 0.3091449737548828 6.83697509765625 0.3096027374267578 6.835256576538086 C 0.3097190856933594 6.834816932678223 0.3098258972167969 6.834415435791016 0.3099422454833984 6.833975791931152 C 0.3104782104492188 6.83197021484375 0.3110389709472656 6.829868316650391 0.3115749359130859 6.827863693237305 C 0.3117504119873047 6.827205657958984 0.3119182586669922 6.826577186584473 0.3120937347412109 6.825918197631836 C 0.3124771118164062 6.82448673248291 0.3129081726074219 6.822874069213867 0.31329345703125 6.821442604064941 C 0.3134689331054688 6.820784568786621 0.3136520385742188 6.820101737976074 0.3138294219970703 6.819442749023438 C 0.3142890930175781 6.81772518157959 0.3145999908447266 6.816570281982422 0.3150615692138672 6.814853668212891 C 0.3152389526367188 6.814194679260254 0.3153495788574219 6.813777923583984 0.3155269622802734 6.813119888305664 C 0.3160667419433594 6.811116218566895 0.316650390625 6.808948516845703 0.3171901702880859 6.80694580078125 C 0.3173084259033203 6.806507110595703 0.3173751831054688 6.80626106262207 0.3174934387207031 6.805822372436523 C 0.3179569244384766 6.804104804992676 0.3183727264404297 6.802563667297363 0.3188381195068359 6.800847053527832 C 0.3190460205078125 6.800080299377441 0.3192729949951172 6.799242973327637 0.3194789886474609 6.79847526550293 C 0.3198680877685547 6.79704475402832 0.3202419281005859 6.79566478729248 0.3206291198730469 6.794235229492188 C 0.3208084106445312 6.793577194213867 0.3209419250488281 6.793085098266602 0.3211193084716797 6.792428016662598 C 0.3215847015380859 6.790712356567383 0.3221683502197266 6.788567543029785 0.3226356506347656 6.786852836608887 C 0.3226947784423828 6.786633491516113 0.3227920532226562 6.786273002624512 0.3228530883789062 6.786053657531738 C 0.3233966827392578 6.784052848815918 0.3239974975585938 6.781856536865234 0.3245429992675781 6.779855728149414 C 0.3246917724609375 6.779308319091797 0.3248310089111328 6.778800010681152 0.324981689453125 6.778252601623535 C 0.3254489898681641 6.776537895202637 0.3258190155029297 6.775188446044922 0.3262882232666016 6.77347469329834 C 0.3264675140380859 6.772817611694336 0.3265914916992188 6.772367477416992 0.3267707824707031 6.771710395812988 C 0.327239990234375 6.76999568939209 0.3278064727783203 6.767934799194336 0.328277587890625 6.766221046447754 C 0.3283672332763672 6.76589298248291 0.328399658203125 6.765775680541992 0.3284893035888672 6.765447616577148 C 0.3291168212890625 6.763162612915039 0.3296184539794922 6.761343955993652 0.3302478790283203 6.759059906005859 C 0.3303680419921875 6.758622169494629 0.3304367065429688 6.75837516784668 0.3305568695068359 6.757937431335449 C 0.3310298919677734 6.756224632263184 0.3315486907958984 6.754342079162598 0.3320217132568359 6.752629280090332 C 0.3321437835693359 6.752191543579102 0.3323020935058594 6.751620292663574 0.3324222564697266 6.751182556152344 C 0.3328952789306641 6.749469757080078 0.3333892822265625 6.747690200805664 0.3338642120361328 6.745977401733398 C 0.333984375 6.745540618896484 0.3341007232666016 6.745121955871582 0.3342208862304688 6.744684219360352 C 0.3348541259765625 6.742401123046875 0.3353328704833984 6.740677833557129 0.335968017578125 6.738395690917969 C 0.3360595703125 6.738067626953125 0.3361473083496094 6.737751960754395 0.3362388610839844 6.737423896789551 C 0.3367156982421875 6.735713005065918 0.3372154235839844 6.733919143676758 0.3376922607421875 6.732208251953125 C 0.3378753662109375 6.731552124023438 0.3380489349365234 6.730930328369141 0.3382301330566406 6.73027515411377 C 0.3387088775634766 6.728564262390137 0.33905029296875 6.72734260559082 0.3395290374755859 6.725631713867188 C 0.3396816253662109 6.725085258483887 0.3398456573486328 6.724495887756348 0.3399982452392578 6.723950386047363 C 0.3405570983886719 6.721954345703125 0.3411808013916016 6.719730377197266 0.3417396545410156 6.71773624420166 C 0.3417701721191406 6.717626571655273 0.3418312072753906 6.717413902282715 0.3418617248535156 6.717304229736328 C 0.3424205780029297 6.715310096740723 0.3429298400878906 6.713502883911133 0.3434906005859375 6.711508750915527 C 0.3436756134033203 6.710853576660156 0.3438091278076172 6.710375785827637 0.343994140625 6.709720611572266 C 0.3444747924804688 6.708011627197266 0.3448162078857422 6.706803321838379 0.3452987670898438 6.705094337463379 C 0.3454513549804688 6.704548835754395 0.3456649780273438 6.70379638671875 0.3458194732666016 6.703250885009766 C 0.3463020324707031 6.701541900634766 0.3468132019042969 6.699729919433594 0.3472976684570312 6.69802188873291 C 0.3474197387695312 6.697585105895996 0.3474655151367188 6.697425842285156 0.3475894927978516 6.696990013122559 C 0.3481540679931641 6.69499683380127 0.3487167358398438 6.69300651550293 0.3492832183837891 6.691014289855957 C 0.3494682312011719 6.690360069274902 0.3497123718261719 6.689506530761719 0.3498973846435547 6.688852310180664 C 0.3503017425537109 6.687429428100586 0.3506851196289062 6.686081886291504 0.3510913848876953 6.684659004211426 C 0.3513069152832031 6.683896064758301 0.3514957427978516 6.683232307434082 0.3517131805419922 6.682469367980957 C 0.3521194458007812 6.681046485900879 0.3525772094726562 6.679443359375 0.3529834747314453 6.678020477294922 C 0.3531703948974609 6.677366256713867 0.3532905578613281 6.676943778991699 0.3534774780273438 6.676290512084961 C 0.3541278839111328 6.674014091491699 0.3545475006103516 6.67254638671875 0.3551998138427734 6.670271873474121 C 0.3553543090820312 6.669727325439453 0.3555183410644531 6.669157028198242 0.3556747436523438 6.668612480163574 C 0.3561630249023438 6.666906356811523 0.3565139770507812 6.665683746337891 0.3570041656494141 6.663978576660156 C 0.3572235107421875 6.663215637207031 0.3573856353759766 6.662654876708984 0.35760498046875 6.661892890930176 C 0.3580131530761719 6.660470962524414 0.3584461212158203 6.658967018127441 0.3588542938232422 6.657546043395996 C 0.3590431213378906 6.656892776489258 0.3592128753662109 6.656302452087402 0.3594017028808594 6.655649185180664 C 0.3599758148193359 6.653659820556641 0.3605785369873047 6.651568412780762 0.3611526489257812 6.649580001831055 C 0.3612480163574219 6.649253845214844 0.3613758087158203 6.648808479309082 0.3614711761474609 6.648482322692871 C 0.3619632720947266 6.646777153015137 0.3624534606933594 6.645085334777832 0.3629474639892578 6.64338207244873 C 0.3631687164306641 6.642620086669922 0.3633365631103516 6.642041206359863 0.3635578155517578 6.641279220581055 C 0.3638858795166016 6.640143394470215 0.3643989562988281 6.638374328613281 0.3647289276123047 6.637239456176758 C 0.3649501800537109 6.636477470397949 0.3651771545410156 6.635696411132812 0.3654003143310547 6.634934425354004 C 0.3658943176269531 6.633231163024902 0.3664398193359375 6.631359100341797 0.3669357299804688 6.629656791687012 C 0.3670005798339844 6.629439353942871 0.3671379089355469 6.628965377807617 0.3672008514404297 6.628747940063477 C 0.3677806854248047 6.626760482788086 0.3683815002441406 6.624702453613281 0.3689613342285156 6.622715950012207 C 0.3691215515136719 6.622172355651855 0.3693294525146484 6.621460914611816 0.3694877624511719 6.620917320251465 C 0.3699855804443359 6.61921501159668 0.370269775390625 6.618244171142578 0.3707695007324219 6.616542816162109 C 0.3709602355957031 6.615890502929688 0.3712043762207031 6.615057945251465 0.3713970184326172 6.614405632019043 C 0.3718948364257812 6.612704277038574 0.3722877502441406 6.611369132995605 0.3727874755859375 6.609667778015137 C 0.3729152679443359 6.609232902526855 0.373077392578125 6.608680725097656 0.3732051849365234 6.608245849609375 C 0.3738727569580078 6.605978012084961 0.3743400573730469 6.604393005371094 0.3750076293945312 6.60212516784668 C 0.3751678466796875 6.601582527160645 0.3753070831298828 6.601115226745605 0.3754673004150391 6.600571632385254 C 0.3759689331054688 6.598871231079102 0.3763179779052734 6.597684860229492 0.3768215179443359 6.59598445892334 C 0.37701416015625 6.595333099365234 0.3772563934326172 6.594515800476074 0.3774490356445312 6.593864440917969 C 0.3779506683349609 6.592164993286133 0.3782958984375 6.590999603271484 0.3788013458251953 6.589300155639648 C 0.3789615631103516 6.588757514953613 0.3791141510009766 6.588244438171387 0.3792743682861328 6.587701797485352 C 0.3799476623535156 6.585434913635254 0.3804054260253906 6.583889961242676 0.3810787200927734 6.581624984741211 C 0.3812408447265625 6.581082344055176 0.3813934326171875 6.580570220947266 0.3815555572509766 6.58002758026123 C 0.3819770812988281 6.578611373901367 0.3824787139892578 6.576925277709961 0.3829002380371094 6.575510025024414 C 0.3830947875976562 6.574858665466309 0.3833637237548828 6.573958396911621 0.3835582733154297 6.573307991027832 C 0.3839797973632812 6.571892738342285 0.3843955993652344 6.570502281188965 0.3848190307617188 6.569087028503418 C 0.3850460052490234 6.568327903747559 0.3851966857910156 6.567826271057129 0.3854236602783203 6.56706714630127 C 0.3860149383544922 6.565086364746094 0.3866539001464844 6.562954902648926 0.3872489929199219 6.56097412109375 C 0.3872814178466797 6.560866355895996 0.3873004913330078 6.560803413391113 0.3873329162597656 6.560694694519043 C 0.3879261016845703 6.558714866638184 0.3884677886962891 6.556914329528809 0.3890628814697266 6.554934501647949 C 0.3892593383789062 6.55428409576416 0.3894939422607422 6.553501129150391 0.3896903991699219 6.552850723266602 C 0.3901157379150391 6.551437377929688 0.3905525207519531 6.549984931945801 0.3909797668457031 6.548571586608887 C 0.3911762237548828 6.547921180725098 0.3913860321044922 6.547224044799805 0.3915824890136719 6.546573638916016 C 0.3920936584472656 6.544878005981445 0.3926887512207031 6.542910575866699 0.3932018280029297 6.541213989257812 C 0.3932666778564453 6.540997505187988 0.3933620452880859 6.540682792663574 0.3934268951416016 6.540465354919434 C 0.3940258026123047 6.538487434387207 0.3946704864501953 6.536359786987305 0.3952713012695312 6.534381866455078 C 0.3954677581787109 6.533732414245605 0.3956813812255859 6.533026695251465 0.3958797454833984 6.532377243041992 C 0.3962230682373047 6.531247138977051 0.3967456817626953 6.529526710510254 0.3970890045166016 6.528397560119629 C 0.3973522186279297 6.527531623840332 0.3976078033447266 6.526691436767578 0.3978710174560547 6.525825500488281 C 0.3983001708984375 6.524413108825684 0.3987331390380859 6.522991180419922 0.3991641998291016 6.521578788757324 C 0.3993301391601562 6.521038055419922 0.3995113372802734 6.520439147949219 0.3996772766113281 6.5198974609375 C 0.4002799987792969 6.517921447753906 0.4008922576904297 6.515920639038086 0.4014949798583984 6.513945579528809 C 0.4016933441162109 6.513296127319336 0.4018802642822266 6.512687683105469 0.4020786285400391 6.512038230895996 C 0.4025115966796875 6.510627746582031 0.4029865264892578 6.509078979492188 0.4034175872802734 6.507668495178223 C 0.4036502838134766 6.506911277770996 0.4038181304931641 6.506365776062012 0.4040508270263672 6.505608558654785 C 0.4044837951660156 6.50419807434082 0.4049472808837891 6.502688407897949 0.4053802490234375 6.501277923583984 C 0.4055805206298828 6.500629425048828 0.4057674407958984 6.500016212463379 0.4059677124023438 6.499367713928223 C 0.4065761566162109 6.497393608093262 0.4071941375732422 6.495382308959961 0.4078044891357422 6.493408203125 C 0.4079704284667969 6.492868423461914 0.4081001281738281 6.492446899414062 0.4082679748535156 6.49190616607666 C 0.4087886810302734 6.490214347839355 0.4091472625732422 6.489054679870605 0.4096717834472656 6.487362861633301 C 0.4099388122558594 6.486498832702637 0.4101505279541016 6.485812187194824 0.4104175567626953 6.48494815826416 C 0.4107666015625 6.483819961547852 0.4112625122070312 6.482218742370605 0.4116134643554688 6.481091499328613 C 0.4118480682373047 6.480335235595703 0.412078857421875 6.479584693908691 0.4123134613037109 6.478829383850098 C 0.4127521514892578 6.477419853210449 0.4132595062255859 6.475784301757812 0.4136981964111328 6.47437572479248 C 0.4139995574951172 6.473403930664062 0.4143180847167969 6.472381591796875 0.4146194458007812 6.471409797668457 C 0.4150581359863281 6.470001220703125 0.4155941009521484 6.468282699584961 0.4160327911376953 6.466875076293945 C 0.4162673950195312 6.466118812561035 0.4164905548095703 6.465404510498047 0.4167270660400391 6.464649200439453 C 0.4171657562255859 6.463241577148438 0.4175624847412109 6.461972236633301 0.4180030822753906 6.460564613342285 C 0.4182052612304688 6.459917068481445 0.4184436798095703 6.459150314331055 0.4186477661132812 6.458502769470215 C 0.4191761016845703 6.456813812255859 0.4196491241455078 6.455302238464355 0.4201774597167969 6.453614234924316 C 0.4203128814697266 6.453182220458984 0.4204463958740234 6.452755928039551 0.4205818176269531 6.452324867248535 C 0.4211997985839844 6.45035457611084 0.4218215942382812 6.44837474822998 0.4224414825439453 6.446405410766602 C 0.4226436614990234 6.445757865905762 0.4228839874267578 6.444998741149902 0.4230861663818359 6.444352149963379 C 0.4235286712646484 6.44294548034668 0.4239654541015625 6.441557884216309 0.4244098663330078 6.440151214599609 C 0.4246139526367188 6.439504623413086 0.4248466491699219 6.438763618469238 0.4250507354736328 6.438117027282715 C 0.4255828857421875 6.436429023742676 0.4259662628173828 6.435215950012207 0.4264984130859375 6.433528900146484 C 0.4266700744628906 6.432990074157715 0.4268417358398438 6.432442665100098 0.4270133972167969 6.431903839111328 C 0.4276351928710938 6.429935455322266 0.428314208984375 6.427791595458984 0.4289379119873047 6.425824165344238 C 0.4290733337402344 6.425393104553223 0.4291763305664062 6.425068855285645 0.4293136596679688 6.424637794494629 C 0.4298477172851562 6.422951698303223 0.4303188323974609 6.421467781066895 0.4308547973632812 6.419781684875488 C 0.431060791015625 6.419136047363281 0.4312953948974609 6.418395042419434 0.4315013885498047 6.417749404907227 C 0.432037353515625 6.41606330871582 0.4324378967285156 6.41480541229248 0.4329738616943359 6.413119316101074 C 0.4331111907958984 6.412689208984375 0.4333171844482422 6.412043571472168 0.4334545135498047 6.411613464355469 C 0.43408203125 6.409646987915039 0.4348030090332031 6.40738582611084 0.4354305267333984 6.405420303344727 C 0.4354991912841797 6.405205726623535 0.4354896545410156 6.405233383178711 0.4355602264404297 6.40501880645752 C 0.436187744140625 6.403053283691406 0.4367637634277344 6.401249885559082 0.4373931884765625 6.399285316467285 C 0.4376010894775391 6.398639678955078 0.4377460479736328 6.398183822631836 0.4379539489746094 6.397538185119629 C 0.4384937286376953 6.395854949951172 0.4388294219970703 6.394806861877441 0.4393692016601562 6.393123626708984 C 0.4395771026611328 6.392477989196777 0.4398269653320312 6.391697883605957 0.4400348663330078 6.391053199768066 C 0.4405765533447266 6.389369010925293 0.4411334991455078 6.387639999389648 0.4416751861572266 6.385956764221191 C 0.4417781829833984 6.385634422302246 0.4418735504150391 6.385340690612793 0.4419765472412109 6.385018348693848 C 0.4426097869873047 6.383054733276367 0.4432582855224609 6.381040573120117 0.4438915252685547 6.379077911376953 C 0.4441013336181641 6.378433227539062 0.4443302154541016 6.377721786499023 0.4445381164550781 6.377077102661133 C 0.4449920654296875 6.375675201416016 0.4454612731933594 6.374225616455078 0.4459133148193359 6.372824668884277 C 0.4461574554443359 6.372072219848633 0.4463596343994141 6.37144660949707 0.4466037750244141 6.370695114135742 C 0.4470577239990234 6.369293212890625 0.4475002288818359 6.367928504943848 0.4479541778564453 6.366527557373047 C 0.4481639862060547 6.365882873535156 0.448394775390625 6.365169525146484 0.4486045837402344 6.364524841308594 C 0.4491500854492188 6.36284351348877 0.4499359130859375 6.360427856445312 0.4504833221435547 6.358747482299805 C 0.4506931304931641 6.358102798461914 0.4508895874023438 6.35749626159668 0.4510993957519531 6.356852531433105 C 0.4515571594238281 6.355452537536621 0.4520492553710938 6.353938102722168 0.4525070190429688 6.352538108825684 C 0.4527511596679688 6.351787567138672 0.4529533386230469 6.351171493530273 0.4531974792480469 6.350419998168945 C 0.4537467956542969 6.348740577697754 0.4540863037109375 6.347701072692871 0.4546356201171875 6.34602165222168 C 0.4548110961914062 6.345485687255859 0.4550113677978516 6.344871520996094 0.4551868438720703 6.344335556030273 C 0.4558277130126953 6.342375755310059 0.4565200805664062 6.34026050567627 0.4571628570556641 6.338301658630371 C 0.4572696685791016 6.337980270385742 0.4574146270751953 6.337540626525879 0.45751953125 6.33721923828125 C 0.4580707550048828 6.335539817810059 0.4586505889892578 6.333775520324707 0.4592018127441406 6.332097053527832 C 0.4594135284423828 6.331454277038574 0.4595985412597656 6.330893516540527 0.4598102569580078 6.330249786376953 C 0.4602699279785156 6.328851699829102 0.4607334136962891 6.327445983886719 0.4611930847167969 6.326046943664551 C 0.4614067077636719 6.325404167175293 0.4616889953613281 6.324542999267578 0.4619026184082031 6.32390022277832 C 0.4624557495117188 6.322222709655762 0.463165283203125 6.320073127746582 0.4637203216552734 6.318395614624023 C 0.4637908935546875 6.318181037902832 0.4639110565185547 6.317816734313965 0.4639816284179688 6.31760311126709 C 0.4646282196044922 6.315646171569824 0.4651908874511719 6.313946723937988 0.4658393859863281 6.311990737915039 C 0.4660530090332031 6.311347961425781 0.4663467407226562 6.310462951660156 0.4665603637695312 6.309820175170898 C 0.4670238494873047 6.308423042297363 0.4674568176269531 6.30711841583252 0.4679203033447266 6.305721282958984 C 0.4681339263916016 6.305079460144043 0.4683647155761719 6.304384231567383 0.4685783386230469 6.303741455078125 C 0.4691352844238281 6.302065849304199 0.4696388244628906 6.300552368164062 0.4701976776123047 6.298876762390137 C 0.4703407287597656 6.29844856262207 0.4704856872558594 6.298012733459473 0.4706287384033203 6.297584533691406 C 0.4713726043701172 6.295350074768066 0.4719123840332031 6.293731689453125 0.4726581573486328 6.291498184204102 C 0.4728374481201172 6.290963172912598 0.4729537963867188 6.29061222076416 0.4731330871582031 6.290077209472656 C 0.47369384765625 6.288402557373047 0.4740619659423828 6.28730297088623 0.4746208190917969 6.285628318786621 C 0.4748725891113281 6.284879684448242 0.4751605987548828 6.284021377563477 0.4754104614257812 6.283272743225098 C 0.4758777618408203 6.281877517700195 0.4762706756591797 6.280708312988281 0.4767379760742188 6.279313087463379 C 0.47698974609375 6.278565406799316 0.4771938323974609 6.2779541015625 0.4774456024169922 6.277205467224121 C 0.4781017303466797 6.275253295898438 0.4787521362304688 6.273320198059082 0.4794082641601562 6.271368026733398 C 0.4795894622802734 6.270833015441895 0.4797439575195312 6.270376205444336 0.4799232482910156 6.269841194152832 C 0.4804878234863281 6.268168449401855 0.4808883666992188 6.266977310180664 0.4814529418945312 6.265304565429688 C 0.4817047119140625 6.264556884765625 0.4819202423095703 6.263920783996582 0.4821720123291016 6.26317310333252 C 0.4826431274414062 6.261778831481934 0.4830226898193359 6.260656356811523 0.4834938049316406 6.259263038635254 C 0.4837474822998047 6.258515357971191 0.4840526580810547 6.257611274719238 0.4843044281005859 6.256863594055176 C 0.4849662780761719 6.254913330078125 0.4855937957763672 6.253060340881348 0.4862556457519531 6.251111030578613 C 0.4863986968994141 6.250683784484863 0.4864597320556641 6.250505447387695 0.4866046905517578 6.250078201293945 C 0.4871730804443359 6.248407363891602 0.4876861572265625 6.246896743774414 0.4882545471191406 6.24522590637207 C 0.4885082244873047 6.244478225708008 0.4888019561767578 6.24361515045166 0.4890556335449219 6.242868423461914 C 0.4894351959228516 6.241754531860352 0.4900131225585938 6.240057945251465 0.4903926849365234 6.238944053649902 C 0.4906463623046875 6.238197326660156 0.4908847808837891 6.23750114440918 0.4911384582519531 6.236753463745117 C 0.4917087554931641 6.23508358001709 0.4921741485595703 6.233721733093262 0.4927444458007812 6.232051849365234 C 0.492889404296875 6.231624603271484 0.4930458068847656 6.231165885925293 0.4931926727294922 6.230738639831543 C 0.4938583374023438 6.228790283203125 0.4945125579833984 6.226880073547363 0.4951801300048828 6.224932670593262 C 0.4954357147216797 6.224185943603516 0.4956512451171875 6.223555564880371 0.4959087371826172 6.222808837890625 C 0.4962902069091797 6.221696853637695 0.4968910217285156 6.219941139221191 0.4972743988037109 6.218828201293945 C 0.4975299835205078 6.218082427978516 0.4977951049804688 6.217311859130859 0.4980506896972656 6.216565132141113 C 0.4986248016357422 6.214897155761719 0.4990501403808594 6.21366024017334 0.4996242523193359 6.211992263793945 C 0.4997711181640625 6.211565971374512 0.4999504089355469 6.211043357849121 0.5000972747802734 6.210617065429688 C 0.5007686614990234 6.208670616149902 0.5014114379882812 6.206804275512695 0.5020828247070312 6.204858779907227 C 0.5023784637451172 6.204006195068359 0.5026416778564453 6.203241348266602 0.5029373168945312 6.202388763427734 C 0.5033206939697266 6.201277732849121 0.5037879943847656 6.199925422668457 0.5041732788085938 6.198814392089844 C 0.5044670104980469 6.197961807250977 0.5047874450683594 6.197039604187012 0.5050811767578125 6.196187973022461 C 0.5054664611816406 6.195076942443848 0.5059070587158203 6.193808555603027 0.5062923431396484 6.192697525024414 C 0.5065879821777344 6.191845893859863 0.5068531036376953 6.191078186035156 0.5071487426757812 6.190227508544922 C 0.5078239440917969 6.18828296661377 0.5084209442138672 6.186563491821289 0.5090961456298828 6.184619903564453 C 0.5093555450439453 6.18387508392334 0.5095767974853516 6.183239936828613 0.5098361968994141 6.1824951171875 C 0.5103206634521484 6.181107521057129 0.51068115234375 6.18006706237793 0.5111656188964844 6.178679466247559 C 0.5114631652832031 6.177828788757324 0.5117855072021484 6.176900863647461 0.5120830535888672 6.176050186157227 C 0.5124702453613281 6.17494010925293 0.5129814147949219 6.173478126525879 0.5133686065673828 6.172368049621582 C 0.5135917663574219 6.171730041503906 0.5139045715332031 6.170835494995117 0.5141277313232422 6.170197486877441 C 0.51470947265625 6.168532371520996 0.5152053833007812 6.167117118835449 0.5157871246337891 6.16545295715332 C 0.5161228179931641 6.164496421813965 0.5164031982421875 6.163695335388184 0.5167388916015625 6.162738800048828 C 0.5172252655029297 6.161352157592773 0.5177383422851562 6.159892082214355 0.5182247161865234 6.158505439758301 C 0.5185604095458984 6.157549858093262 0.5188522338867188 6.156720161437988 0.5191879272460938 6.155763626098633 C 0.5195789337158203 6.154654502868652 0.5198612213134766 6.153849601745605 0.5202522277832031 6.152741432189941 C 0.5206260681152344 6.151679039001465 0.5209312438964844 6.150810241699219 0.5213069915771484 6.149747848510742 C 0.5217933654785156 6.148362159729004 0.5220832824707031 6.147542953491211 0.5225715637207031 6.146157264709473 C 0.5228347778320312 6.145414352416992 0.5231094360351562 6.144632339477539 0.5233726501464844 6.143889427185059 C 0.5240573883056641 6.141949653625488 0.5246047973632812 6.140399932861328 0.5252914428710938 6.138461112976074 C 0.5256290435791016 6.137505531311035 0.5259571075439453 6.136581420898438 0.5262947082519531 6.135625839233398 C 0.5266876220703125 6.134518623352051 0.52703857421875 6.133528709411621 0.5274314880371094 6.132421493530273 C 0.5277328491210938 6.131572723388672 0.5280704498291016 6.130617141723633 0.5283718109130859 6.129768371582031 C 0.5288639068603516 6.128384590148926 0.5290985107421875 6.127725601196289 0.5295906066894531 6.126340866088867 C 0.5299301147460938 6.125386238098145 0.5302143096923828 6.124584197998047 0.5305538177490234 6.123629570007324 C 0.5311450958251953 6.121969223022461 0.5318603515625 6.119960784912109 0.5324516296386719 6.118300437927246 C 0.5330886840820312 6.116517066955566 0.5339221954345703 6.114182472229004 0.5345573425292969 6.112399101257324 C 0.5348987579345703 6.111445426940918 0.5352230072021484 6.110540390014648 0.5355644226074219 6.109585762023926 C 0.5359592437744141 6.108479499816895 0.5363540649414062 6.107378959655762 0.5367488861083984 6.10627269744873 C 0.5370903015136719 6.105319023132324 0.537384033203125 6.10450267791748 0.5377254486083984 6.103549003601074 C 0.5382213592529297 6.102167129516602 0.5385913848876953 6.101134300231934 0.5390872955322266 6.099752426147461 C 0.5395431518554688 6.098481178283691 0.5400314331054688 6.097119331359863 0.5404891967773438 6.095848083496094 C 0.540985107421875 6.094467163085938 0.5413475036621094 6.093459129333496 0.5418453216552734 6.092077255249023 C 0.5421123504638672 6.091336250305176 0.5423755645751953 6.090602874755859 0.5426425933837891 6.089861869812012 C 0.5430412292480469 6.088756561279297 0.5436515808105469 6.087062835693359 0.5440502166748047 6.085957527160645 C 0.5443553924560547 6.085110664367676 0.5445938110351562 6.084449768066406 0.5449008941650391 6.083602905273438 C 0.5453987121582031 6.082221984863281 0.5458698272705078 6.080916404724121 0.5463695526123047 6.079536437988281 C 0.5467529296875 6.07847785949707 0.5470428466796875 6.077672958374023 0.5474262237548828 6.076614379882812 C 0.5479259490966797 6.075234413146973 0.5485134124755859 6.073615074157715 0.5490131378173828 6.072235107421875 C 0.5493583679199219 6.071283340454102 0.5497074127197266 6.07032299041748 0.5500526428222656 6.069371223449707 C 0.5504531860351562 6.068267822265625 0.5507717132568359 6.067387580871582 0.5511722564697266 6.0662841796875 C 0.5515575408935547 6.065226554870605 0.5518817901611328 6.064334869384766 0.5522651672363281 6.063278198242188 C 0.5525665283203125 6.062450408935547 0.5529537200927734 6.061385154724121 0.5532550811767578 6.060558319091797 C 0.5539474487304688 6.058655738830566 0.5545921325683594 6.056890487670898 0.5552864074707031 6.054987907409668 C 0.5555877685546875 6.054161071777344 0.5559616088867188 6.053133964538574 0.5562629699707031 6.05230712890625 C 0.5566501617431641 6.051250457763672 0.5570411682128906 6.050180435180664 0.5574264526367188 6.049124717712402 C 0.5577297210693359 6.048296928405762 0.5581378936767578 6.047178268432617 0.558441162109375 6.046351432800293 C 0.5588264465332031 6.045295715332031 0.559234619140625 6.044182777404785 0.5596218109130859 6.043126106262207 C 0.5599250793457031 6.042300224304199 0.5602474212646484 6.041418075561523 0.5605506896972656 6.040591239929199 C 0.5609378814697266 6.039535522460938 0.5613632202148438 6.038374900817871 0.5617523193359375 6.037319183349609 C 0.5620555877685547 6.036492347717285 0.5624122619628906 6.035519599914551 0.5627155303955078 6.034692764282227 C 0.5633754730224609 6.032898902893066 0.5640735626220703 6.031001091003418 0.5647354125976562 6.029207229614258 C 0.5651397705078125 6.028105735778809 0.5654296875 6.027317047119141 0.5658359527587891 6.026215553283691 C 0.5664901733398438 6.024439811706543 0.5672950744628906 6.022259712219238 0.5679512023925781 6.020483016967773 C 0.5683422088623047 6.019428253173828 0.5687217712402344 6.018400192260742 0.5691108703613281 6.017345428466797 C 0.5696201324462891 6.015970230102539 0.57000732421875 6.014921188354492 0.5705165863037109 6.013545036315918 C 0.5709075927734375 6.012491226196289 0.5713596343994141 6.011269569396973 0.5717506408691406 6.010214805603027 C 0.5722618103027344 6.00883960723877 0.5727691650390625 6.007471084594727 0.5732803344726562 6.006095886230469 C 0.5736312866210938 6.005146980285645 0.5739402770996094 6.004315376281738 0.5742931365966797 6.00336742401123 C 0.5747013092041016 6.002267837524414 0.5750045776367188 6.00145435333252 0.5754127502441406 6.000354766845703 C 0.5758056640625 5.999300956726074 0.5761451721191406 5.998386383056641 0.5765380859375 5.997333526611328 C 0.5768451690673828 5.996508598327637 0.5772724151611328 5.995362281799316 0.5775794982910156 5.994537353515625 C 0.5781688690185547 5.992958068847656 0.5787696838378906 5.991349220275879 0.5793590545654297 5.98976993560791 C 0.5797691345214844 5.988670349121094 0.5802974700927734 5.98725414276123 0.5807094573974609 5.98615550994873 C 0.5810642242431641 5.985208511352539 0.5813713073730469 5.98438549041748 0.58172607421875 5.983438491821289 C 0.5821380615234375 5.982339859008789 0.5824680328369141 5.981454849243164 0.5828800201416016 5.980356216430664 C 0.5835437774658203 5.978586196899414 0.5844516754150391 5.976169586181641 0.5851154327392578 5.974397659301758 C 0.58551025390625 5.973345756530762 0.5858116149902344 5.972541809082031 0.5862064361572266 5.971489906311035 C 0.5868263244628906 5.969842910766602 0.5875740051269531 5.967856407165527 0.5881938934326172 5.96621036529541 C 0.5888614654541016 5.96444034576416 0.5897216796875 5.962162017822266 0.5903873443603516 5.960391998291016 C 0.5910549163818359 5.958622932434082 0.5919284820556641 5.956310272216797 0.5925960540771484 5.954543113708496 C 0.5929946899414062 5.953492164611816 0.5933551788330078 5.952535629272461 0.5937538146972656 5.951484680175781 C 0.5941677093505859 5.95038890838623 0.59466552734375 5.949076652526855 0.5950813293457031 5.947979927062988 C 0.5956783294677734 5.946404457092285 0.5962333679199219 5.944941520690918 0.596832275390625 5.943366050720215 C 0.5971431732177734 5.942543983459473 0.5975666046142578 5.941428184509277 0.5978794097900391 5.940606117248535 C 0.5982780456542969 5.939556121826172 0.59869384765625 5.93846607208252 0.5990924835205078 5.937416076660156 C 0.5995101928710938 5.936320304870605 0.5997524261474609 5.935680389404297 0.6001701354980469 5.934584617614746 C 0.6005306243896484 5.933639526367188 0.6009426116943359 5.932555198669434 0.6013031005859375 5.931611061096191 C 0.6018257141113281 5.930241584777832 0.6021728515625 5.929329872131348 0.6026954650878906 5.927961349487305 C 0.6030559539794922 5.927016258239746 0.603485107421875 5.92589282989502 0.6038455963134766 5.924948692321777 C 0.6044731140136719 5.923305511474609 0.6049060821533203 5.922177314758301 0.6055335998535156 5.920535087585449 C 0.6059360504150391 5.919486045837402 0.6062374114990234 5.918699264526367 0.6066379547119141 5.917651176452637 C 0.6070575714111328 5.916556358337402 0.6073398590087891 5.915820121765137 0.6077594757080078 5.914725303649902 C 0.6081619262695312 5.913677215576172 0.6085529327392578 5.912656784057617 0.6089553833007812 5.911608695983887 C 0.6096305847167969 5.909843444824219 0.6104679107666016 5.907669067382812 0.6111469268798828 5.905905723571777 C 0.6117782592773438 5.904264450073242 0.6125106811523438 5.902361869812012 0.6131420135498047 5.900721549987793 C 0.6135463714599609 5.899673461914062 0.6139488220214844 5.898629188537598 0.6143531799316406 5.897582054138184 C 0.6146697998046875 5.896761894226074 0.6150684356689453 5.895730972290039 0.6153850555419922 5.89491081237793 C 0.6157894134521484 5.893863677978516 0.6162490844726562 5.892669677734375 0.6166553497314453 5.891622543334961 C 0.6169719696044922 5.890803337097168 0.6172504425048828 5.890080451965332 0.6175689697265625 5.889260292053223 C 0.6180133819580078 5.88810920715332 0.6184139251708984 5.887072563171387 0.6188602447509766 5.885921478271484 C 0.6191787719726562 5.885101318359375 0.6196250915527344 5.883947372436523 0.6199436187744141 5.88312816619873 C 0.6206741333007812 5.881243705749512 0.621368408203125 5.879456520080566 0.6220989227294922 5.877573013305664 C 0.6224174499511719 5.876753807067871 0.6227474212646484 5.875905990600586 0.6230659484863281 5.875086784362793 C 0.6235122680664062 5.873935699462891 0.6239185333251953 5.872895240783691 0.6243667602539062 5.871744155883789 C 0.6246852874755859 5.870925903320312 0.625 5.870116233825684 0.6253185272216797 5.869297981262207 C 0.6257667541503906 5.868147850036621 0.6261367797851562 5.867197036743164 0.6265850067138672 5.866046905517578 C 0.6269054412841797 5.865228652954102 0.6271381378173828 5.864633560180664 0.6274566650390625 5.863815307617188 C 0.6282730102539062 5.861723899841309 0.629150390625 5.859479904174805 0.6299686431884766 5.857389450073242 C 0.6302890777587891 5.856571197509766 0.6305027008056641 5.856020927429199 0.6308231353759766 5.855203628540039 C 0.6312732696533203 5.854053497314453 0.6316375732421875 5.853124618530273 0.6320877075195312 5.851975440979004 C 0.6325149536132812 5.850884437561035 0.6326789855957031 5.850465774536133 0.6331081390380859 5.849374771118164 C 0.6335163116455078 5.848330497741699 0.6339187622070312 5.847308158874512 0.6343288421630859 5.84626293182373 C 0.6347560882568359 5.845172882080078 0.6349735260009766 5.844622611999512 0.6354026794433594 5.843532562255859 C 0.6363468170166016 5.841130256652832 0.6373271942138672 5.838634490966797 0.638275146484375 5.836234092712402 C 0.6383819580078125 5.83596134185791 0.6384410858154297 5.835810661315918 0.6385478973388672 5.835538864135742 C 0.6390838623046875 5.834181785583496 0.6395740509033203 5.832935333251953 0.6401100158691406 5.831579208374023 C 0.6403255462646484 5.831033706665039 0.6405563354492188 5.830449104309082 0.6407718658447266 5.829904556274414 C 0.641265869140625 5.828652381896973 0.6417350769042969 5.82746410369873 0.6422309875488281 5.826211929321289 C 0.6425533294677734 5.825395584106445 0.6427268981933594 5.824958801269531 0.6430492401123047 5.824142456054688 C 0.6446189880371094 5.820178031921387 0.6462898254394531 5.815962791442871 0.6478633880615234 5.81200122833252 C 0.6481876373291016 5.811184883117676 0.6483612060546875 5.810749053955078 0.6486854553222656 5.809933662414551 C 0.6491413116455078 5.808786392211914 0.6495933532714844 5.807649612426758 0.6500511169433594 5.806503295898438 C 0.6503753662109375 5.805686950683594 0.6507167816162109 5.804829597473145 0.6510429382324219 5.804014205932617 C 0.6514568328857422 5.802971839904785 0.6519374847412109 5.801765441894531 0.6523532867431641 5.800724029541016 C 0.652679443359375 5.799908638000488 0.6528987884521484 5.799359321594238 0.6532230377197266 5.798543930053711 C 0.6540546417236328 5.79646110534668 0.6548805236816406 5.794397354125977 0.6557121276855469 5.792314529418945 C 0.6559295654296875 5.791770935058594 0.6563930511474609 5.790614128112793 0.6566104888916016 5.790070533752441 C 0.6570281982421875 5.789029121398926 0.6575508117675781 5.787725448608398 0.6579666137695312 5.786684989929199 C 0.658294677734375 5.785869598388672 0.6585597991943359 5.785207748413086 0.6588859558105469 5.784393310546875 C 0.6593456268310547 5.783247947692871 0.6598701477050781 5.781940460205078 0.6603298187255859 5.780796051025391 C 0.6605472564697266 5.780252456665039 0.6608409881591797 5.779523849487305 0.6610584259033203 5.77898120880127 C 0.6618537902832031 5.777004241943359 0.6626873016357422 5.774934768676758 0.663482666015625 5.772957801818848 C 0.6639194488525391 5.771872520446777 0.6641407012939453 5.771324157714844 0.6645774841308594 5.770238876342773 C 0.6649971008300781 5.769198417663574 0.6653900146484375 5.768223762512207 0.6658096313476562 5.767183303833008 C 0.6662483215332031 5.766098022460938 0.6665382385253906 5.765380859375 0.6669750213623047 5.76429557800293 C 0.6673946380615234 5.763256072998047 0.6678047180175781 5.762243270874023 0.6682243347167969 5.761203765869141 C 0.6685543060302734 5.760390281677246 0.6687984466552734 5.759788513183594 0.6691265106201172 5.758975028991699 C 0.6707248687744141 5.755024909973145 0.6723194122314453 5.751092910766602 0.6739215850830078 5.747145652770996 C 0.6742534637451172 5.746333122253418 0.6745529174804688 5.74559211730957 0.6748847961425781 5.744779586791992 C 0.6753482818603516 5.743637084960938 0.6758136749267578 5.742493629455566 0.6762790679931641 5.741351127624512 C 0.6766090393066406 5.740538597106934 0.6767711639404297 5.740142822265625 0.6771011352539062 5.739330291748047 C 0.6787929534912109 5.735177993774414 0.6804542541503906 5.731109619140625 0.6821498870849609 5.726960182189941 C 0.682373046875 5.726418495178223 0.6826553344726562 5.725726127624512 0.6828765869140625 5.725185394287109 C 0.6833858489990234 5.723939895629883 0.6838912963867188 5.722705841064453 0.6844024658203125 5.721461296081543 C 0.6846237182617188 5.720919609069824 0.6848735809326172 5.720312118530273 0.6850948333740234 5.719770431518555 C 0.6856479644775391 5.718422889709473 0.6861476898193359 5.717203140258789 0.6867008209228516 5.715855598449707 C 0.6868114471435547 5.715584754943848 0.6869888305664062 5.715155601501465 0.6870994567871094 5.714885711669922 C 0.6881637573242188 5.71229362487793 0.6892318725585938 5.709694862365723 0.6902999877929688 5.707104682922363 C 0.690521240234375 5.706563949584961 0.6907310485839844 5.706056594848633 0.6909542083740234 5.70551586151123 C 0.6915092468261719 5.704168319702148 0.6920185089111328 5.70293140411377 0.6925735473632812 5.701584815979004 C 0.6927967071533203 5.701044082641602 0.692962646484375 5.700643539428711 0.6931858062744141 5.700102806091309 C 0.6937408447265625 5.698756217956543 0.6942672729492188 5.697481155395508 0.69482421875 5.696135520935059 C 0.6950473785400391 5.695594787597656 0.6951465606689453 5.695355415344238 0.6953697204589844 5.694814682006836 C 0.6964836120605469 5.692122459411621 0.6975002288818359 5.689666748046875 0.6986160278320312 5.686975479125977 C 0.6988391876220703 5.686435699462891 0.6988906860351562 5.686309814453125 0.6991157531738281 5.685770034790039 C 0.6996726989746094 5.68442440032959 0.7002029418945312 5.683148384094238 0.7007617950439453 5.681803703308105 C 0.7008743286132812 5.681533813476562 0.7012100219726562 5.680722236633301 0.7013225555419922 5.680452346801758 C 0.7018814086914062 5.679107666015625 0.7024726867675781 5.67768669128418 0.7030315399169922 5.676342010498047 C 0.7032566070556641 5.675802230834961 0.7033214569091797 5.675644874572754 0.7035465240478516 5.675105094909668 C 0.7053966522216797 5.670658111572266 0.7072963714599609 5.666103363037109 0.7091541290283203 5.661660194396973 C 0.7092666625976562 5.66139030456543 0.7093849182128906 5.661109924316406 0.7094974517822266 5.660840034484863 C 0.7101020812988281 5.659393310546875 0.7106704711914062 5.658034324645996 0.7112770080566406 5.656588554382324 C 0.7113895416259766 5.656318664550781 0.7116527557373047 5.655688285827637 0.7117671966552734 5.655418395996094 C 0.713714599609375 5.65077018737793 0.7156085968017578 5.646257400512695 0.7175636291503906 5.64161205291748 C 0.7176761627197266 5.641343116760254 0.7176361083984375 5.641439437866211 0.7177486419677734 5.641170501708984 C 0.7183570861816406 5.639725685119629 0.7189884185791016 5.638227462768555 0.7195968627929688 5.636782646179199 C 0.7197113037109375 5.636513710021973 0.7199134826660156 5.636034965515137 0.7200260162353516 5.63576602935791 C 0.7226371765136719 5.629574775695801 0.7252998352050781 5.623276710510254 0.7279243469238281 5.617093086242676 C 0.7280387878417969 5.616824150085449 0.7282562255859375 5.61630916595459 0.7283706665039062 5.616040229797363 C 0.7289829254150391 5.614597320556641 0.7295989990234375 5.613149642944336 0.7302112579345703 5.611706733703613 C 0.7303256988525391 5.611437797546387 0.730377197265625 5.611318588256836 0.7304916381835938 5.611049652099609 C 0.7324180603027344 5.60651683807373 0.734344482421875 5.601996421813965 0.7362785339355469 5.597467422485352 C 0.7363929748535156 5.597198486328125 0.7366695404052734 5.596548080444336 0.736785888671875 5.596280097961426 C 0.7374000549316406 5.594839096069336 0.7378940582275391 5.593684196472168 0.7385101318359375 5.592244148254395 C 0.7387409210205078 5.591706275939941 0.7388515472412109 5.591446876525879 0.7390804290771484 5.590909957885742 C 0.7409744262695312 5.586484909057617 0.7429561614990234 5.581865310668945 0.7448558807373047 5.577444076538086 C 0.7449722290039062 5.577176094055176 0.7450084686279297 5.577092170715332 0.7451229095458984 5.576824188232422 C 0.7457427978515625 5.575385093688965 0.7464084625244141 5.573835372924805 0.7470283508300781 5.572396278381348 C 0.7471427917480469 5.572128295898438 0.7472915649414062 5.571783065795898 0.7474079132080078 5.571515083312988 C 0.7480278015136719 5.570075988769531 0.7486782073974609 5.568563461303711 0.7493000030517578 5.56712532043457 C 0.7493000030517578 5.56712532043457 0.7493381500244141 5.567037582397461 0.7493381500244141 5.567037582397461 C 0.7507553100585938 5.563749313354492 0.7521095275878906 5.560615539550781 0.7535305023193359 5.557329177856445 C 0.7535305023193359 5.557329177856445 0.7535724639892578 5.557234764099121 0.7535724639892578 5.557234764099121 C 0.7542381286621094 5.555694580078125 0.7548675537109375 5.554244041442871 0.7555351257324219 5.552703857421875 C 0.7556495666503906 5.552435874938965 0.7557392120361328 5.55223274230957 0.7558536529541016 5.55196475982666 C 0.7565212249755859 5.550424575805664 0.7571372985839844 5.549003601074219 0.7578067779541016 5.547464370727539 C 0.7579231262207031 5.547196388244629 0.7578392028808594 5.547390937805176 0.7579555511474609 5.547122955322266 C 0.7599601745605469 5.54250431060791 0.7620086669921875 5.537796974182129 0.7640209197998047 5.533183097839355 C 0.7641372680664062 5.532915115356445 0.7643203735351562 5.532493591308594 0.7644386291503906 5.532225608825684 C 0.7650642395019531 5.530790328979492 0.7656383514404297 5.529474258422852 0.766265869140625 5.52803897857666 C 0.7664985656738281 5.527503967285156 0.7664833068847656 5.527538299560547 0.7667179107666016 5.527003288269043 C 0.7687339782714844 5.522391319274902 0.7707252502441406 5.517845153808594 0.7727470397949219 5.513236045837402 C 0.7727470397949219 5.513236045837402 0.7729282379150391 5.512823104858398 0.7729282379150391 5.512823104858398 C 0.7736034393310547 5.511286735534668 0.7741966247558594 5.50993537902832 0.774871826171875 5.508399963378906 C 0.7749881744384766 5.508132934570312 0.7751941680908203 5.507667541503906 0.7753105163574219 5.507400512695312 C 0.7773361206054688 5.50279426574707 0.7793292999267578 5.498273849487305 0.7813625335693359 5.493670463562012 C 0.7814807891845703 5.493403434753418 0.7814655303955078 5.493437767028809 0.7815837860107422 5.493170738220215 C 0.7822151184082031 5.491739273071289 0.7827816009521484 5.490458488464355 0.7834148406982422 5.48902702331543 C 0.7836513519287109 5.488492965698242 0.7837772369384766 5.488207817077637 0.7840137481689453 5.487674713134766 C 0.7867279052734375 5.481539726257324 0.7894382476806641 5.475436210632324 0.7921657562255859 5.469308853149414 C 0.7922840118408203 5.46904182434082 0.7925033569335938 5.468550682067871 0.7926216125488281 5.468284606933594 C 0.793212890625 5.466957092285156 0.7939262390136719 5.465357780456543 0.7945175170898438 5.464031219482422 C 0.7946357727050781 5.463764190673828 0.7946910858154297 5.463642120361328 0.7948093414306641 5.463376045227051 C 0.7967681884765625 5.458985328674316 0.7987766265869141 5.454492568969727 0.8007411956787109 5.450106620788574 C 0.8008594512939453 5.449840545654297 0.8012638092041016 5.448940277099609 0.8013820648193359 5.448674201965332 C 0.8019771575927734 5.447347640991211 0.8026351928710938 5.445878982543945 0.8032302856445312 5.444553375244141 C 0.8033504486083984 5.444287300109863 0.8034915924072266 5.443971633911133 0.8036117553710938 5.443705558776855 C 0.8056716918945312 5.439117431640625 0.8077869415283203 5.434413909912109 0.8098545074462891 5.429829597473145 C 0.8098545074462891 5.429829597473145 0.8099956512451172 5.42951488494873 0.8099956512451172 5.42951488494873 C 0.81072998046875 5.427885055541992 0.8113899230957031 5.426422119140625 0.8121261596679688 5.424792289733887 C 0.8122463226318359 5.424526214599609 0.8120899200439453 5.424872398376465 0.8122100830078125 5.424606323242188 C 0.8151092529296875 5.418190002441406 0.8179988861083984 5.411809921264648 0.8209114074707031 5.405401229858398 C 0.8209114074707031 5.405401229858398 0.8211212158203125 5.404936790466309 0.8211212158203125 5.404936790466309 C 0.8239879608154297 5.398630142211914 0.8269557952880859 5.392121315002441 0.8298358917236328 5.385822296142578 C 0.8299560546875 5.385557174682617 0.8298397064208984 5.385810852050781 0.8299617767333984 5.38554573059082 C 0.8328876495361328 5.379145622253418 0.8357982635498047 5.372798919677734 0.8387393951416016 5.366406440734863 C 0.8387393951416016 5.366406440734863 0.8389110565185547 5.366033554077148 0.8389110565185547 5.366033554077148 C 0.8396110534667969 5.364511489868164 0.8402957916259766 5.363025665283203 0.8409976959228516 5.361504554748535 C 0.8411178588867188 5.361239433288574 0.8411312103271484 5.361210823059082 0.8412532806396484 5.360945701599121 C 0.8441982269287109 5.354556083679199 0.8470649719238281 5.348353385925293 0.8500251770019531 5.341970443725586 C 0.8500251770019531 5.341970443725586 0.8501739501953125 5.341650009155273 0.8501739501953125 5.341650009155273 C 0.85614013671875 5.328783988952637 0.8621425628662109 5.315914154052734 0.8681659698486328 5.303081512451172 C 0.8681659698486328 5.303081512451172 0.8682479858398438 5.302906036376953 0.8682479858398438 5.302906036376953 C 1.088302612304688 4.834033966064453 1.346683502197266 4.386805534362793 1.639410018920898 3.965129852294922 C 1.736541748046875 3.825210571289062 1.892251968383789 3.750198364257812 2.050588607788086 3.750211715698242 Z" stroke="none" fill="#032e61"/>
                                                            </g>
                                                        </g>
                                                        </g>
                                                    </svg>
                                                </span>
                                                <span *ngIf="source.eco_name">
                                                    <svg [matTooltip]="source.eco_name" style="margin-left: 10px;"
                                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17" height="18" viewBox="0 0 17 18">
                                                        <defs>
                                                        <clipPath id="clip-ecosystem">
                                                            <rect width="17" height="18"/>
                                                        </clipPath>
                                                        </defs>
                                                        <g id="ecosystem" clip-path="url(#clip-ecosystem)">
                                                        <g id="Group_5545" data-name="Group 5545" transform="translate(16966 3851)">
                                                            <g id="Ellipse_109" data-name="Ellipse 109" transform="translate(-16962.891 -3843.929)" fill="#67beeb" stroke="#3f3679" stroke-width="1">
                                                            <circle cx="3.214" cy="3.214" r="3.214" stroke="none"/>
                                                            <circle cx="3.214" cy="3.214" r="2.714" fill="none"/>
                                                            </g>
                                                            <g id="Ellipse_111" data-name="Ellipse 111" transform="translate(-16953.5 -3846.5)" fill="#f8af23" stroke="#3f3679" stroke-width="1">
                                                            <circle cx="2.25" cy="2.25" r="2.25" stroke="none"/>
                                                            <circle cx="2.25" cy="2.25" r="1.75" fill="none"/>
                                                            </g>
                                                            <g id="Ellipse_112" data-name="Ellipse 112" transform="translate(-16962.256 -3851)" fill="#fff" stroke="#3f3679" stroke-width="1">
                                                            <circle cx="2.571" cy="2.571" r="2.571" stroke="none"/>
                                                            <circle cx="2.571" cy="2.571" r="2.071" fill="none"/>
                                                            </g>
                                                            <g id="Ellipse_113" data-name="Ellipse 113" transform="translate(-16966 -3837.5)" fill="#f46275" stroke="#3f3679" stroke-width="1">
                                                            <circle cx="2.25" cy="2.25" r="2.25" stroke="none"/>
                                                            <circle cx="2.25" cy="2.25" r="1.75" fill="none"/>
                                                            </g>
                                                            <line id="Line_85" data-name="Line 85" y2="2.571" transform="translate(-16959.703 -3846.179)" fill="none" stroke="#3f3679" stroke-width="1"/>
                                                            <line id="Line_88" data-name="Line 88" x1="1.286" y2="1.286" transform="translate(-16962.229 -3838.143)" fill="none" stroke="#3f3679" stroke-width="1"/>
                                                            <line id="Line_86" data-name="Line 86" x1="4.5" y2="1.286" transform="translate(-16957.25 -3843.286)" fill="none" stroke="#3f3679" stroke-width="1"/>
                                                        </g>
                                                        </g>
                                                    </svg>
                                                </span>

                                                    <span *ngIf="vectorProgress && vectorProgress[source.uid] && vectorProgress[source.uid].status == 1" class="rotate-loader ml-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style=" shape-rendering: auto; margin-top: -7px;" width="40px" height="40px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
                                                            <g transform="rotate(0 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.9166666666666666s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(30 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.8333333333333334s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(60 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.75s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(90 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.6666666666666666s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(120 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.5833333333333334s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(150 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.5s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(180 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.4166666666666667s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(210 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.3333333333333333s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(240 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.25s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(270 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.16666666666666666s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(300 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="-0.08333333333333333s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g><g transform="rotate(330 50 50)">
                                                            <rect x="47" y="24" rx="3" ry="3.36" width="6" height="12" fill="#0052CC">
                                                            <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1s" begin="0s" repeatCount="indefinite"></animate>
                                                            </rect>
                                                        </g>
                                                        </svg>
                                                        <div class="customized-tooltip">
                                                            <span><strong>Vectorization In Progress</strong>
                                                                <span *ngIf="vectorProgress[source.uid].totalPercentage" 
                                                        [ngClass]="[vectorProgress[source.uid].totalPercentage ? 'showPercentage' : 'hidePercentage']"> 
                                                        <br>  {{ vectorProgress[source.uid].totalPercentage }}% Completed
                                                                </span>
                                                            </span>
                                                        </div>
                                                    </span>
                                                    
                                                    <button *ngIf="haveDropdown[index]" type="button" class="border-0 bg-transparent ml-1 toggle-sc" (click)="toggleExpand(index)">
                                                        <svg [ngClass]="expandedIndex === index ? 'toggle-child' : '' " xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
                                                            <path d="M10.6167 9.32272L6.44967 5.05179L2.28333 9.32272C2.08077 9.51909 1.81151 9.62672 1.53276 9.62276C1.25402 9.61879 0.987776 9.50353 0.790613 9.30148C0.593449 9.09942 0.480911 8.8265 0.476916 8.54071C0.472922 8.25493 0.577785 7.97881 0.769221 7.77105L5.6984 2.718C5.79774 2.61593 5.91574 2.53495 6.04565 2.47969C6.17555 2.42444 6.31481 2.396 6.45545 2.396C6.5961 2.396 6.73536 2.42444 6.86526 2.47969C6.99517 2.53495 7.11317 2.61593 7.21251 2.718L12.1417 7.77105C12.2412 7.8729 12.3202 7.99388 12.3741 8.12707C12.428 8.26026 12.4558 8.40304 12.4558 8.54723C12.4558 8.69143 12.428 8.83421 12.3741 8.9674C12.3202 9.10059 12.2412 9.22157 12.1417 9.32342C11.9376 9.52715 11.6639 9.6411 11.3791 9.64097C11.0942 9.64084 10.8206 9.52664 10.6167 9.32272Z" fill="#4B4B4B" fill-opacity="0.55"/>
                                                        </svg>
                                                    </button>
                                                    <div *ngIf="abTestStatus[index] && abTestStatus[index].status == 1 ">
                                                        <label class="test-running">A/B Testing running</label>
                                                    </div>
                                            </td>
                                            <td class="search-client-detail ad_type-td" *ngIf="source.display">
                                                <img [src]="source.img" alt="{{source.scName}}" class="smallImage" 
                                                matTooltipPosition="below"
                                                [matTooltip]="source.scName === 'Khoros' ? 'Khoros Communities - Classic' : source.scName === 'Aurora' ? 'Khoros Communities - Aurora' : source.scName"
                                                aria-label="Button that displays a tooltip in various positions"/>
                                            </td>
                                            <td class="search-client-detail" *ngIf="source.display" style="width: 18%;">
                                                {{source.created_date.replace('T', ' ').split('.')[0] | timeZone : userTimeZone:"contentSource"}}
                                            </td>
                                            <td class="search-client-detail position-relative" style="line-height: 1.5;width: 18%;padding-left:0" *ngIf="source.display">
                                                <div class="ad_search-uid" matTooltip="{{source.uid}}" matTooltipPosition="below"
                                                    cdkCopyToClipboard="{{source.uid}}">
                                                    <div class="ad_uid-label" matTooltip="UID Copied"
                                                        (mouseenter)="mouseHandler($event)" #tooltip="matTooltip"
                                                        (click)="tooltip.toggle()">{{source.uid}}</div>
                                                </div>
                                            </td>
                                            <!-- done -->
                                            <td class="search-client-detail ad_share-td" *ngIf="source.display">
                                                <div *ngIf="source.sc_creating_status == 0" style="display:flex; gap:6px;flex-wrap:wrap;width:100%;position:relative;">
                                                <a class="pointer a-icon-edit" matTooltip="Edit search client"
                                                        matTooltipPosition="below" [ngClass]="{'ad_btn-disabled': !source.editable || (scOperations && scOperations.status && source.created_date <= calculateDate)}"
                                                    aria-label="Button that displays a tooltip in various positions"
                                                    (click)="source.editable?indexChanged('0',source,source.uid, abTestStatus[index]?.status):null">
                                                    <img class="edit-content content-size">
                                                </a>
                                                <!-- done -->
                                                <a class="pointer" (click)="source.editable?indexChanged('1',source,source.uid):null"
                                                    [ngClass]="{'ad_btn-disabled': !source.editable}" [hidden]="isModerator"
                                                    matTooltip="Download search client" matTooltipPosition="below"
                                                    aria-label="Button that displays a tooltip in various positions">
                                                    <img class="download-content content-size">
                                                </a>

                                                <a class="pointer a-icon-delete" id="delete" *ngIf="!source.eco_name"
                                                [ngClass]="{'ad_btn-disabled': !source.editable || (scOperations && scOperations.status && source.created_date <= calculateDate)}" 
                                                    (click)="
                                                    abTestStatus[index] && abTestStatus[index].status == 0 ? deleteABsearchClient = true :
                                                    abTestStatus[index] && abTestStatus[index].status == 1 ?  testInprogress = true : 
                                                    source.editable ? deleteSearchClient(source) : null; scSource = source" 
                                                    matTooltip="Delete search client"
                                                    matTooltipPosition="below"
                                                    aria-label="Button that displays a tooltip in various positions">
                                                    <img class="delete-content content-size">
                                                </a>
                                      

                                                <a class="pointer" (click)="source.editable ? toggleCloneClient(source) : null;"
                                                    [ngClass]="{'ad_btn-disabled': !source.editable}"
                                                    matTooltip="Clone search client" matTooltipPosition="below"
                                                    aria-label="Button that displays a tooltip in various positions">
                                                    <img class="clone-content content-size">
                                                </a>



                                                <a *ngIf="source.search_client_type_id !== 22"
                                                class="pointer adicon-customize" matTooltip="Customize search client" matTooltipPosition="below" 
                                                [ngClass]="{'ad_btn-disabled':!source.editable || ( source.language == 'lwcSfConsole' && !suUserandGzuser ) || (scOperations && scOperations.status && source.created_date <= calculateDate)}"
                                                aria-label="Button that displays a tooltip in various positions"
                                                (click)="source.editable?indexChanged('18',source,source.uid):null">
                                                <img  [ngClass]= "{'customize-sc' : source.shareAccess && ((isProdInstance && source.enableRollbackBtn && enableSboxProdFeature) || (!isProdInstance && suUserandGzuser && enableSboxProdFeature)) }" class="ad_code-editor content-size" style="margin-left:0px;">
                                            </a>


                                                 <!-- Ellipsis Icon -->
                                                    <a class="pointer d-flex align-items-center" matTooltip="More actions" *ngIf="(instanceType !== 'sandbox' && instanceType !== 'production') && suUserandGzuser"
                                                    matTooltipPosition="below" 
                                                    (click)="toggleMoreIcons(source.uid)" style="fill: #AFB2BC;">
                                                    <svg width="24" height="24" viewBox="0 0 24 24">
                                                        <circle cx="12" cy="5" r="2"></circle>
                                                        <circle cx="12" cy="12" r="2"></circle>
                                                        <circle cx="12" cy="19" r="2"></circle>
                                                    </svg>                                    
                                                </a>


                                                 <!-- Remaining Icons -->
                                <div [ngClass]="{'expanded-icons': true, 'show': expandedIcons[source.uid]}"    style="display:flex; gap:6px; flex-wrap:wrap;">
                                    <a *ngIf="!isProdInstance && suUserandGzuser && enableSboxProdFeature" class="pointer"   matTooltip="Migrate to Production" matTooltipPosition="below"
                                    [ngClass]="{'ad_btn-disabled':!source.editable || isMigrationSend==='In progress'}"
                                    aria-label="Button that displays a tooltip in various positions"
                                    (click)="source.editable?indexChanged('22',source):null">
                                    <img class="sandboxToMigration  content-size">
                                </a>                           
                                <a *ngIf="isProdInstance && source.enableRollbackBtn && enableSboxProdFeature" 
                                class="pointer a-icon-edit"
                                    matTooltip="Rollback"
                                    matTooltipPosition="below"
                                    (click)="showRollbackScreen(source.uid, source.id)"
                                    aria-label="Button that displays a tooltip in various positions">
                                    <img class="rollback content-size">
                                </a>

                                <span *ngIf="source.eco_name" matTooltip="Exists in an Ecosystem" matTooltipPosition="below">
                                    <a class="pointer a-icon-delete" id="delete"
                                        [ngClass]="{'ad_btn-disabled': source.eco_name}">
                                        <img class="delete-content content-size">
                                    </a>
                                </span>

                                <a class="pointer a-icon-share" data-toggle="modal"
                                data-target="#shareSettingModal" aria-hidden="true" *ngIf="source.shareAccess"
                                [ngClass]="{'ad_btn-disabled': !source.editable}" matTooltip="Share search client"
                                matTooltipPosition="below"
                                (click)="bringShareAccessSettings(source); openModalShare =true"
                                aria-label="Button that displays a tooltip in various positions">
                                <svg class="ad_share-svg" width="21.843" height="21.843"
                                    viewBox="0 0 21.843 21.843">
                                    <path d="M21.843,0H0V21.843H21.843Z" fill="none" />
                                    <path class="ad_share-fill"
                                        d="M13.419,8.767V7.32a.913.913,0,0,1,1.556-.646l4.177,4.177a.906.906,0,0,1,0,1.283l-4.177,4.177a.91.91,0,0,1-1.556-.637V14.137c-4.551,0-7.736,1.456-10.011,4.642C4.318,14.228,7.048,9.677,13.419,8.767Z"
                                        transform="translate(-0.677 -0.576)" /></svg>
                            </a>

                            <a class="pointer a-icon-edit" *ngIf="source.createdFormula" (click)="clearSearchPermissions()"
                            matTooltip="Clear cache to refresh search permissions" matTooltipPosition="below"
                            aria-label="Button that displays a tooltip in various positions">
                            <!-- <i class="far fa-edit" aria-hidden="true"></i> -->
                            <img class="clear-cache content-size">
                        </a>
                        <a *ngIf="(instanceType !== 'sandbox' && instanceType !== 'production') && suUserandGzuser" class="pointer" [@opened]="" (click)="source.editable?ResetScCode(source):null;"
                        [ngClass]="{'ad_btn-disabled': !source.editable}" 
                        matTooltip="Reset search client" matTooltipPosition="below"
                        aria-label="Button that displays a tooltip in various positions">
                        <img class="reset-sc content-size">
                        </a>
                        <a class="pointer" *ngIf="source.search_client_type_id == 6 && suUserandGzuser" (click)="serveScCloudfront(source.uid)"
                        [ngClass]="{'ad_btn-disabled': !source.editable}" 
                        matTooltip="Serve Search Client from CDN" matTooltipPosition="below"
                        aria-label="Button that displays a tooltip in various positions">
                        <img class="serve-sc content-size">
                        </a>
                                </div>
                                               

                                
                                        
                                            
                                             
                                                <!-- done -->
                                              
                                                </div>

                                                <div class="display-flex align-items-center justify-content" *ngIf="source.sc_creating_status != 0">
                                                    <bounce-loader *ngIf="source.sc_creating_status == 1 && !shouldShowCleanupIcon(source.created_date)" matTooltip="Creating Search Client..." ></bounce-loader>
                                                    <span *ngIf="source.sc_creating_status == 2 || shouldShowCleanupIcon(source.created_date)" >
                                                        Failed to create search client
                                                        <a class="pointer a-icon-delete" id="delete" *ngIf="!source.eco_name"
                                                                [ngClass]="{'ad_btn-disabled': !source.editable || (scOperations && scOperations.status && source.created_date <= calculateDate)}"
                                                            (click)="source.editable?deleteSearchClient(source):null" matTooltip="Delete search client"
                                                            matTooltipPosition="below"
                                                            >
                                                            <img alt="" class="delete-content content-size">
                                                        </a>
                                                    </span>
                                                </div>
                                            </td>
    
                                        </tr>

                                        <!-- testing SC starts -->
                                        <ng-container *ngIf="expandedIndex === index">

                                            <ng-container *ngFor="let childs of childSC">
    
                                                <tr *ngIf="childs.ab_test_parent == source.uid" [ngClass]="childs.ab_test_parent == source.uid ? 'child-sc' : '' ">
                                                    <td colspan="2" class="search-client-detail su_name_alignment ad_position-relative" style="word-break: break-word;">
                                                        <div class="ab-test-sc">
                                                        <span class="label" [ngClass]="childs.scLabelClass">{{childs.scLabel}}</span>
                                                        <span>{{childs.searchTerm}}</span>
                                                        </div>
                                                    </td>
                                                    <td class="search-client-detail" style="width: 18%;"> {{childs.created_date.replace('T', ' ').split('.')[0] | timeZone : userTimeZone:"contentSource"}}</td>
                                                    <td class="search-client-detail position-relative" style="line-height: 1.5;width: 18%;padding-left:0">
                                                        <div class="ad_search-uid" matTooltip="{{childs.uid}}" matTooltipPosition="below"
                                                        cdkCopyToClipboard="{{childs.uid}}">
                                                        <div class="ad_uid-label" matTooltip="UID Copied" (mouseenter)="mouseHandler($event)" #tooltip="matTooltip"
                                                            (click)="tooltip.toggle();">{{childs.uid}}</div>
                                                        </div>
                                                    </td>
                                                    <td class="search-client-detail ad_share-td">
                                                        <div style="display:flex; gap:8px;flex-wrap:wrap;width:95%;">
                                                        <a class="pointer a-icon-edit" matTooltip="Edit search client" matTooltipPosition="below"
                                                            aria-label="Button that displays a tooltip in various positions"
                                                            (click)="childs.editable?indexChanged('0',childs,childs.uid, abTestStatus[index]?.status):null"><img class="edit-content content-size"></a>
                                                
                                                        <a class="pointer" [ngClass]="abTestStatus[index] && abTestStatus[index].status == 1 ? 'ad_btn-disabled' : '' " (click)="childs.editable?indexChanged('1',childs,childs.uid):null"
                                                            aria-label="Button that displays a tooltip in various positions" matTooltip="Download search client"
                                                            matTooltipPosition="below">
                                                            <img class="download-content content-size"></a>
                                                
                                                        <a class="pointer" [ngClass]="abTestStatus[index] && abTestStatus[index].status == 1 ? 'ad_btn-disabled' : '' " (click)="childs.editable ? toggleCloneClient(childs) : null;"
                                                            aria-label="Button that displays a tooltip in various positions" matTooltip="Clone search client"
                                                            matTooltipPosition="below"><img class="clone-content content-size"></a>
                                                
                                                        </div>
                                                    </td>
                                                </tr>

                                            </ng-container>
                                        </ng-container>
                                        <!-- testing SC ends -->
                                    </table>
                                </td>

                            </ng-container>
                        </tr>
                          
                    </tbody>
                </table>
                <div class="overlay" *ngIf="allowClone">
                    <div class="shadowDiv animate-div-clone" [@opened]="">
                        <div class="row parent-div">
                            <div class="su-tableHead clone-bold">
                                Clone your search client
                            </div>
                            <table class="table clone-table" style="margin-bottom: 0px;">
                                <tr>
                                    <td>
                                        <mat-form-field>
                                            <input matInput [(ngModel)]="cloneParameters.name" placeholder="Enter name" (ngModelChange)="addUniqueSearchClient(cloneParameters)">
                                        </mat-form-field>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <mat-form-field>
                                            <mat-select class="select-search-client" #select [(ngModel)]="cloneParameters.search_client_type_id"
                                                placeholder="Select search client"
                                                (selectionChange)="cloneAngularDefault()">
                                                <mat-option *ngFor="let platform of platformTypes"
                                                    [value]="platform.id">
                                                    {{platform.name}}</mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </td>
                                </tr>
                                <tr *ngIf="cloneParameters.defaultType == cloneParameters.search_client_type_id">
                                    <td>
                                        <label class="margin-clone-toggle" >Reset to default template</label>
                                        <mat-slide-toggle [checked]="checked"
                                            [(ngModel)]="cloneParameters.restoreSettings">
                                        </mat-slide-toggle>
                                    </td>
                                </tr>
                                <tr *ngIf="cloneParameters.defaultType == cloneParameters.search_client_type_id">
                                    <td class="hide">
                                        <label>Reset to default UID</label>
                                        <mat-slide-toggle [checked]="checked" [(ngModel)]="cloneParameters.restoreUid">
                                        </mat-slide-toggle>
                                    </td>
                                </tr>
                                <tr>
                                    <p class="italicText">
                                        <span class="noteColor">Note:</span>
                                        <span>As you clone your search client, there may be a slight delay while the backend processes the versioning of the content source fields.</span>
                                    </p>
                                </tr>
                                <tr>
                                    <!-- done -->
                                    <td style="text-align: center;">
                                        <button type="button" class="buttonPrimary margin-right-5px"
                                            [disabled]="!cloneParameters.name || !cloneParameters.search_client_type_id || !uniqueSearchClient || cloneButtonClicked"
                                            (click)="indexChanged('2',cloneParameters.name,cloneParameters.uid,'clone')">Clone</button>
                                        <button type="button" class="buttonPrimary marginleft " 
                                            (click)="allowClone = !allowClone;cloneParameters.name='';cloneParameters.search_client_type_id=''; removeClassToBody();"
                                            style="margin-top: 10px;">Cancel</button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="overlay" *ngIf="allowReset">
                    <div class="shadowDiv animate-div-clone reset-sc-popup" [@opened]="">
                        <div class="row parent-div">
                            <div class="su-tableHead clone-bold">
                                <mat-radio-group [(ngModel)]="resetSCOption.value" class="display-flex flex-direction-column ad_line-height-n">
                                    <mat-radio-button value="1">Reset your search client codebase</mat-radio-button>
                                    <mat-radio-button value="2">Swap your search client codebase with another search client UID</mat-radio-button>
                                </mat-radio-group>
                            </div>
                            <p *ngIf="resetSCOption.value == 1" class="note mb-0 reset-para">
                                <span class="note">Note: </span> 
                                This feature is for the QA use only. Resetting the <br> search client will reset all the customisations done
                            </p>
                            <mat-form-field *ngIf="resetSCOption.value == 2">
                                <!-- <div class="migrationPopupFormH1">Select a Search Client Migration Request</div> -->
                                <mat-select class="select-search-client" panelClass="migrationClass"  panelClass="matSCMigrations" (selectionChange)="scResetCodebase($event)" #select placeholder="Select search client">
                                    <ng-container *ngFor="let source of contentSources;">
                                        <mat-option 
                                            [value]="source.uid"
                                            *ngIf="source.search_client_type_id == cloneParameters.search_client_type_id && source.uid != cloneParameters.uid"
                                            >
                                            <span>
                                                <strong>
                                                    {{ source.name }}  | {{source.uid}}
                                                </strong>
                                            </span>
                                        </mat-option>
                                    </ng-container>
                                </mat-select>
                            </mat-form-field>
                            <table class="table clone-table" style="margin-bottom: 0px;">
                                <tr>
                                    <td style="text-align: center;">
                                        <button type="button" class="buttonPrimary margin-right-5px"
                                        (click)="indexChanged('24', cloneParameters.name, cloneParameters.uid, resetSCOption)">{{resetSCOption.value == 1 ? 'Reset' : 'Swap'}}</button>
                                        <button type="button" class="buttonPrimary marginleft " 
                                        (click)="allowReset = !allowReset; cloneParameters.name=''; cloneParameters.search_client_type_id=''; cloneParameters.uid=''; removeClassToBody(); resetSCOption.value = '1'; resetSCOption.sourceUid = '';">Cancel</button>
                                    </td>
                                </tr>
                            </table>
                            <table class="table clone-table" style="margin-bottom: 0px;margin-top:30px;" *ngIf="migrationInProgress || migrationStep0 || (migrationStep1 && migrationStep2) || isReprocessMigration">
                                <tr>
                                    <td>
                                        <div>
                                            <div class="row no-margin-padding">
                                                <div class="col-sm-2 no-margin-padding"><strong>#</strong></div>
                                                <div class="col-sm-6 no-margin-padding"><strong>Migration Steps</strong></div>
                                                <div class="col-sm-2 no-margin-padding"><strong>Status</strong></div>
                                                <div class="col-sm-2 no-margin-padding text-center"><strong>Actions</strong></div>
                                            </div>
                                            <div class="row no-margin-padding migration-align-end" style="margin-top: 5px;"  *ngIf="migrateParams.oldNew === 'migrate-new'">
                                                <!-- style="margin-top: 10px;" -->
                                                <div class="col-sm-2 no-margin-padding"><strong>1.</strong></div>
                                                <div class="col-sm-6 no-margin-padding">Creating new search client ...</div>
                                                <div  *ngIf="!migrationStep0 && migrateParams.oldNew === 'migrate-new'" style="display: inline;">
                                                    <!-- class="col-sm-2 center-align" -->
                                                    <div class="migrationInProgress">
                                                    <img src="assets/img/migrationInProgress.svg" alt="loader">                            
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding">
                                                    <!-- <strong *ngIf="migrationStep0" [ngStyle]="{'color': migrationStep0==='Failed' ? 'red' : 'green' }">{{migrationStep0}}</strong> -->
                                                    <!-- -->
                                                    <div  *ngIf="migrationStep0" [ngClass]="{'migrationStepCompleted': migrationStep0 ==='Completed' ,'migrationStepFailed': migrationStep0 ==='Failed'  }" >{{migrationStep0}}
                                                        <span *ngIf="migrationStep0 ==='Failed'" style="margin-left: 4px;" >
                                                            <svg matTooltipClass="migrationFailedTooltip" matTooltip="Note: There are some failed steps, trigger &quot;re-process&quot; from &quot;Actions&quot;."
                                                            matTooltipPosition="below"
                                                            xmlns="http://www.w3.org/2000/svg" width="20.866" height="16" viewBox="0 0 20.866 18.233">
                                                                <g id="Component_19_1" data-name="Component 19 – 1" transform="translate(0.775 0.75)">
                                                                <path id="Path_2613" data-name="Path 2613" d="M10.4,5.236,2.576,18.306a1.849,1.849,0,0,0,1.581,2.773H19.815A1.849,1.849,0,0,0,21.4,18.306L13.566,5.236a1.849,1.849,0,0,0-3.161,0Z" transform="translate(-2.328 -4.346)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                                <path id="Path_2614" data-name="Path 2614" d="M18,13.5v3.7" transform="translate(-8.342 -7.859)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                                <path id="Path_2615" data-name="Path 2615" d="M18,25.5h0" transform="translate(-8.342 -12.464)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                                </g>
                                                            </svg>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding" style="text-align: center;">
                                                    <a *ngIf="migrationStep0 ==='Failed'" class="pointer a-icon-edit display-inline-flex" id="reprocess_migration"
                                                    matTooltip="Reprocess Failed Steps"
                                                    matTooltipPosition="below"
                                                    (click)="reprocessMigration()"
                                                    aria-label="Button that displays a tooltip in various positions">
                                                    <img class="sync-content content-size">
                                                </a>
                                                </div>
                                            </div>
                                            <div class="row no-margin-padding migration-align-end" [ngStyle]="{'margin-top': migrationStep1==='Failed' || migrationStep2==='Failed' ? '15px' : '0px'}">
                                                <div class="col-sm-2 no-margin-padding"><strong>{{migrateParams.oldNew === 'migrate-existing' ? '1.' : '2.'}}</strong></div>
                                                <div class="col-sm-6 no-margin-padding pt-1">Updating search client files and settings...</div>
                                                <div  *ngIf="!migrationStep1"  style="display: inline;">
                                                    <!-- class="col-sm-2 center-align -->
                                                    <div class="migrationInProgress">
                                                        <img src="assets/img/migrationInProgress.svg" alt="loader">                            
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding">
                                                    <!-- <strong *ngIf="migrationStep1" [ngStyle]="{'color': migrationStep1==='Failed' ? 'red' : 'green' }">{{migrationStep1}}</strong> -->
                                                    
                                                    <div  *ngIf="migrationStep1" [ngClass]="{'migrationStepCompleted': migrationStep1 ==='Completed' ,'migrationStepFailed': migrationStep1 ==='Failed'  }" >{{migrationStep1}} 
                                                    <span *ngIf="migrationStep1 ==='Failed'" style="margin-left: 4px;" >
                                                        <svg matTooltipClass="migrationFailedTooltip" matTooltip="Note: There are some failed steps, trigger &quot;re-process&quot; from &quot;Actions&quot;."
                                                        matTooltipPosition="below"
                                                        xmlns="http://www.w3.org/2000/svg" width="20.866" height="16" viewBox="0 0 20.866 18.233">
                                                            <g id="Component_19_1" data-name="Component 19 – 1" transform="translate(0.775 0.75)">
                                                            <path id="Path_2613" data-name="Path 2613" d="M10.4,5.236,2.576,18.306a1.849,1.849,0,0,0,1.581,2.773H19.815A1.849,1.849,0,0,0,21.4,18.306L13.566,5.236a1.849,1.849,0,0,0-3.161,0Z" transform="translate(-2.328 -4.346)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                            <path id="Path_2614" data-name="Path 2614" d="M18,13.5v3.7" transform="translate(-8.342 -7.859)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                            <path id="Path_2615" data-name="Path 2615" d="M18,25.5h0" transform="translate(-8.342 -12.464)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                            </g>
                                                        </svg>
                                                    </span>
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding" style="text-align: center;">
                                                    <!-- [hidden]="migrationStep1!=='Failed' && migrationStep2!=='Failed'" -->
                                                    <a  [hidden]="migrationStep1!=='Failed' && migrationStep2!=='Failed'" class="pointer a-icon-edit display-inline-flex" id="reprocess_migration"
                                                        matTooltip="Reprocess Failed Steps"
                                                        matTooltipPosition="below"
                                                        (click)="reprocessMigration()"
                                                        aria-label="Button that displays a tooltip in various positions">
                                                        <img class="sync-content content-size">
                                                    </a>
                                                </div>
                                            </div>
                                    </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: center;">
                                        <button  type="button" class="buttonPrimary migratePopupCanelBtn"
                                        [disabled]="migrationInProgress || migrationStep2 ==='Failed'"
                                        (click)="closeMigrationOverlay();
                                        getSearchClientsFromSandbox();
                                        openMigrationOverlay();"
                                        style="margin-right: 13px;">Ok</button>

                                        <button type="button" 
                                        class="buttonPrimary migratePopupMigrateBtn" (click)="closeMigrationOverlay()">Cancel</button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="overlay" *ngIf="enableRollback">
                    <div class="shadowDiv animate-div-clone migration-overlay" [@opened]="">
                        <div class="row parent-div migrationReqPopup">  
                            <div>
                                <div class="backupText" colspan="3">
                                    Search Client Migration Rollback
                                </div>
                            </div>
                            <div class="backupTableDiv"  *ngIf="!loadingBackupFiles">
                                
                                <table *ngIf="backupFilesList.length >= 1" class="backupTable">
                                
                                    <!-- <br> -->
                                    <tr *ngFor="let file of backupFilesList; let ind = index">
                                        <td class="backupRadioBtn pb-1">
                                            <mat-radio-group [(ngModel)]="rollbackRadioBtn">
                                                <mat-radio-button [value]="file.name" name="file.name"></mat-radio-button>
                                            </mat-radio-group>   
                                            <span class="backupName" 
                                               matTooltip="{{ getTooltipText(file) }}"
                                                matTooltipPosition="below"> 
                                                {{file.name.split('__')[1]}} 
                                            </span>
                                        </td>
                                        <td class="backupDeleteBtn pb-1">
                                            <a class="pointer a-icon-delete" id="delete" style="margin-left: 8px;" aria-label="Button that displays a tooltip in various positions"
                                            [ngClass] = "{'disable-delete' : isDeleteDisabled()}"
                                            [attr.data-disabled]="isDeleteDisabled() ? true : 'null'"
                                            [class.disabled]="isDeleteDisabled()"
                                            (click)="isDeleteDisabled() ? null : deleteBackup(ind, file.uid_prod, file.name)">
                                                <img class="delete-content content-size"
                                                [attr.data-disabled]="isDeleteDisabled() ? true : 'null'"
                                                [class.disabled]="isDeleteDisabled()"
                                                [ngClass] = "{'disable-delete' : isDeleteDisabled()}"
                                                >
                                            </a>
                                        </td>
                                    </tr>
                                
                                    <!-- <br> -->
                                    <!-- <tr class="backupBtns">
                                        <td colspan="3" class="buttonContainer">
                                            <button type="button" class="buttonPrimary migratePopupCanelBtn" (click)="cancelRollback()" style="margin-right: 13px;">Cancel</button>
                                            <button type="button" 
                                            [disabled] = "!rollbackRadioBtn || rollbackInProgress"
                                            class="buttonPrimary migratePopupMigrateBtn" (click)="rollbackFile()">Rollback</button>
                                        </td>
                                    </tr> -->
                                </table>

                                <p *ngIf="backupFilesList.length == 0">No backups found</p>
                            </div>
                            <div class="backupBtns"  *ngIf="!loadingBackupFiles">
                                <div colspan="3" class="buttonContainer">
                                    <button type="button" class="buttonPrimary migratePopupCanelBtn" (click)="cancelRollback()" style="margin-right: 13px;">Cancel</button>
                                    <button type="button" 
                                    [disabled] = "!rollbackRadioBtn || rollbackInProgress ||backupFilesList.length ==0"
                                    class="buttonPrimary migratePopupMigrateBtn" (click)="!(!rollbackRadioBtn || rollbackInProgress) && rollbackFile()">Rollback</button>
                                </div>
                            </div>
                        </div>
                        <div class="loadBackupFiles" *ngIf="loadingBackupFiles">
                            Loading Backup Files .....
                        </div>
                    </div>
                </div>

                <div class="overlay" *ngIf="initiateMigration && selectedForMigration">
                    <div class="shadowDiv animate-div-clone initiate-migration-overlay" [@opened]="">
                        <div class="row parent-div">
                            <div class="migrationHeading" matTooltipPosition="above" matTooltip="Migrate search client to production">
                                Migrate Search Client
                            </div>
                            <div class="migrationSubHeading" *ngIf="isMigrationSend !== 'Completed'">
                                Do you want to migrate search client with below UID to production ?
                            </div>
                            <div class="migrationSubHeadingUID">{{selectedForMigration.uid}}</div>
                            <div *ngIf="isMigrationSend" [ngClass]="{'migrationCompleted': isMigrationSend ==='Completed' || isMigrationSend === 'In progress' , 'migrationFailed': isMigrationSend ==='Failed' }"  >Status : {{isMigrationSend}}</div>
                            <!-- *ngIf="isMigrationSend ==='Completed'" -->
                            <div *ngIf="isMigrationSend ==='Completed'"  class="migrationCompletedContent">Search Client migration request has been sent to Production. Please login to production instance for further processing</div>
                            <!-- <bounce-loader [hidden]="isMigrationSend !== 'In Progress'"></bounce-loader> -->
                            <div class="migrationInProgress" [hidden]="isMigrationSend !== 'In progress'">
                                <img src="assets/img/migrationInProgress.svg" alt="loader">
                            </div>
                            <button *ngIf="!isMigrationSend"
                            type="button" class="button buttonPrimary ecoSystem migrationProceed"
                            (click)="sendMigrationRequestToProduction()" >Proceed</button>
                            <button *ngIf="!isMigrationSend" type="button" class="button buttonPrimary migrationCancel"
                            (click)="initiateMigration=false;" >Cancel</button>
                            <button *ngIf="isMigrationSend === 'Failed' || isMigrationSend ==='Completed'" type="button" class="button buttonPrimary"
                            (click)="initiateMigration=false;"
                            >OK</button>
                        </div>
                    </div>
                </div>
                <div class="overlay discard-migration" *ngIf="dltMigrationRequest">
                    <div class="shadowDiv animate-div-clone initiate-migration-overlay migrationDltPopup" [@opened]="">
                        <div class="row parent-div">
                            <div class="migrationDltHeading">
                                Are you sure you want to discard this search client migration request?
                            </div>     
                            <button 
                            type="button" class="button buttonPrimary ecoSystem migrationProceed"
                            (click)="dltMigrationRequest = false;">Cancel</button>
                            <button type="button" class="button buttonPrimary migrationCancel" (click)="deleteMigrationRequest(migrationUIDForDeletion);dltMigrationRequest = false;"
                            >Discard</button>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="isLoadingAdded" class="loadingScreen center-align" style="width: 100%; display: inline-flex;">
                <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                </div>
            </div>
                <div class="overlay" *ngIf="isMigrated">
                    <div class="shadowDiv animate-div-clone migration-overlay" [@opened]="">
                        <div class="row parent-div migrationReqPopup">
                            <div class="migrationPopupHeading">
                                Search Client Migration Requests
                            </div>
                            <table class="table clone-table" style="margin-bottom: 0px;" *ngIf="!(migrationInProgress || migrationStep0 || (migrationStep1 && migrationStep2) || isReprocessMigration)">
                                <tr >
                                    <td>
                                        <mat-form-field>
                                            <div class="migrationPopupFormH1">Select a Search Client Migration Request</div>
                                            <!-- placeholder="Select search client from sandbox" -->
                                            <mat-select class="select-search-client" panelClass="migrationClass"  panelClass="matSCMigrations"  #select
                                                [disabled]="migrationInProgress">
                                                <mat-option 
                                                    *ngFor="let source of searchClientsFromSandbox;"
                                                    [value]="source.uid"
                                                    (click)="selectSearchClientForMigration(source)"
                                                   
                                                    >
                                                    
                                                    <span>
                                                        <strong>
                                                            {{ source.name.length > 20 ? source.name.substring(0,20) + '...' : ' ( '+source.name+' ) ' }} {{ source.uid }}
                                                        </strong>
                                                    </span>
                                                    <span class="sectionButton" matTooltip="Discard" (click)="dltMigrationRequest = true; migrationUIDForDeletion=source.uid;">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="9.872" height="9.872" viewBox="0 0 9.872 9.872">
                                                            <path id="Path_3733" data-name="Path 3733" d="M15.06,5.634a.746.746,0,0,0-1.056,0L10.343,9.288,6.682,5.626A.746.746,0,0,0,5.627,6.682l3.661,3.661L5.626,14A.746.746,0,0,0,6.682,15.06L10.343,11.4,14,15.06A.746.746,0,0,0,15.06,14L11.4,10.343,15.06,6.682A.75.75,0,0,0,15.06,5.634Z" transform="translate(-5.407 -5.408)" fill="#757575"/>
                                                        </svg>
                                                    </span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </td>
                                </tr>
                                <tr >
                                    <td class="clone-language">
                                        <div class="clone-language-inner">
                                            <label for="new or existing" class="migrationPopupFormH1">Migrate as "new" OR merge with existing search client?</label>
                                            <mat-radio-group class="language-dropdown" aria-label="new or existing" (change)="migrationToggle()" [(ngModel)]="migrateParams.oldNew" [disabled]="migrationInProgress">
                                                <mat-radio-button value="migrate-new"><span class="migrationPopupFormH1">New</span></mat-radio-button>
                                                <mat-radio-button value="migrate-existing">
                                                    <span class="migrationPopupFormH1">Existing</span>                                                
                                                </mat-radio-button>
                                            </mat-radio-group>
                                        </div>
                                    </td>
                                </tr>
                                <tr *ngIf="migrateParams.oldNew == 'migrate-new'">
                                    <td class="migrationPopUpPadding">
                                        <mat-form-field>
                                            <div class="migrationPopupFormH1">Enter New Search Client Name* </div>
                                            <!-- placeholder="Enter New Platform Name" -->
                                            <input matInput name="new_migration_platform" [(ngModel)]="newPlatform.name"
                                                class="clientDetails"
                                                [disabled]="migrationInProgress"
                                                (ngModelChange)="addUniqueSearchClient(newPlatform);resetMigrationSteps();" required>
                                        </mat-form-field>
                                    </td>
                                </tr>
                                <tr *ngIf="migrateParams.oldNew == 'migrate-new'">
                                    <td class="migrationPopUpPadding">
                                        <mat-form-field>
                                            <div class="migrationPopupFormH1">Enter New Search Client Base URL* </div>
                                            <!-- placeholder="Enter new platform base url" -->
                                            <input matInput name="new_migration_client_href" [(ngModel)]="newPlatform.client_href"
                                                [disabled]="migrationInProgress"
                                                (ngModelChange)="resetMigrationSteps();"
                                                class="clientDetails" required>
                                        </mat-form-field>
                                    </td>
                                </tr>
                                <tr *ngIf="migrateParams.oldNew == 'migrate-existing'">
                                    <td class="migrationPopUpPadding">
                                        <mat-form-field>
                                            <div class="migrationPopupFormH1">Select Existing Search Client to Replace </div>
                                            <mat-select class="select-search-client" panelClass="migrationClass" #select  
                                                [disabled]="migrationInProgress">
                                                <!-- *ngIf="source.search_client_type_id == migrateParams.search_client_type_id && source.language == migrateParams.language" -->
                                                <ng-container *ngFor="let source of contentSources;">
                                                    <mat-option *ngIf="source.search_client_type_id == migrateParams.search_client_type_id && source.language == migrateParams.language"
                                                    [value]="source.uid"
                                                    (click)="selectExistingSearchClient(source)">
                                                    <strong>
                                                    {{ source.name.length > 20 ? source.name.substring(0,20) + '...' : source.name }} {{ ' { '+source.uid+' } ' }}</strong>
                                                    </mat-option>
                                                </ng-container>
                                            </mat-select>
                                        </mat-form-field>
                                    </td>
                                </tr>
                                <tr *ngIf="migrateParams.oldNew == 'migrate-existing'">
                                    <td class="migrationPopUpPadding">
                                        <mat-form-field>
                                            <div class="migrationPopupFormH1">Enter Backup Name* </div>
                                            <!-- placeholder="Enter New Platform Name" -->
                                            <input matInput name="backup_name" [(ngModel)]="migrateParams.backupFileName"
                                                class="clientDetails"
                                                required>
                                        </mat-form-field>
                                    </td>
                                </tr>
                                <tr>
                                    <div id="backupWarning" class="mb-1 mt-2" *ngIf="!enableMigrateButton">
                                        <!-- <mat-icon class="warning mr-2">warning</mat-icon> -->
                                        <svg style="margin-right: 0.7rem;" xmlns="http://www.w3.org/2000/svg" width="20.866" height="18.233" viewBox="0 0 20.866 18.233">
                                          <g id="Icon_feather-alert-triangle" data-name="Icon feather-alert-triangle" transform="translate(0.775 0.75)">
                                            <path id="Path_2613" data-name="Path 2613" d="M10.4,5.236,2.576,18.306a1.849,1.849,0,0,0,1.581,2.773H19.815A1.849,1.849,0,0,0,21.4,18.306L13.566,5.236a1.849,1.849,0,0,0-3.161,0Z" transform="translate(-2.328 -4.346)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                            <path id="Path_2614" data-name="Path 2614" d="M18,13.5v3.7" transform="translate(-8.342 -7.859)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                            <path id="Path_2615" data-name="Path 2615" d="M18,25.5h0" transform="translate(-8.342 -12.464)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                          </g>
                                        </svg>

                                        <span id="backupText"> {{backupErrorMessage}}</span>
                                    </div>
                                </tr>
                                <tr>
                                    <td *ngIf="migrateParams.oldNew == 'migrate-new' || migrateParams.oldNew == 'migrate-existing' " style="text-align: center;">
                                        <button type="button" class="buttonPrimary migratePopupCanelBtn"
                                            [disabled]="migrationInProgress"
                                            (click)="closeMigrationOverlay()"
                                            style="margin-right: 13px;">Cancel</button>
                                        <button type="button" class="buttonPrimary migratePopupMigrateBtn"
                                            [disabled]="isMigrationButtonDisabled(migrateParams, enableMigrateButton, newPlatform, migrationInProgress, uniqueSearchClient)"
                                            (click)="!isMigrationButtonDisabled(migrateParams, enableMigrateButton, newPlatform, migrationInProgress, uniqueSearchClient) && indexChanged('23', migrateParams.name)">Migrate</button>
                                    </td>
                                </tr>
                            </table>
                            <table class="table clone-table" style="margin-bottom: 0px;margin-top:30px;" *ngIf="migrationInProgress || migrationStep0 || (migrationStep1 && migrationStep2) || isReprocessMigration">
                                <tr>
                                    <td>
                                        <div>
                                            <div class="row no-margin-padding">
                                                <div class="col-sm-2 no-margin-padding"><strong>#</strong></div>
                                                <div class="col-sm-6 no-margin-padding"><strong>Migration Steps</strong></div>
                                                <div class="col-sm-2 no-margin-padding"><strong>Status</strong></div>
                                                <div class="col-sm-2 no-margin-padding text-center"><strong>Actions</strong></div>
                                            </div>
                                            <div class="row no-margin-padding migration-align-end" style="margin-top: 5px;"  *ngIf="migrateParams.oldNew === 'migrate-new'">
                                                <!-- style="margin-top: 10px;" -->
                                                <div class="col-sm-2 no-margin-padding"><strong>1.</strong></div>
                                                <div class="col-sm-6 no-margin-padding">Creating new search client ...</div>
                                                <div  *ngIf="!migrationStep0 && migrateParams.oldNew === 'migrate-new'" style="display: inline;">
                                                    <!-- class="col-sm-2 center-align" -->
                                                    <div class="migrationInProgress">
                                                    <img src="assets/img/migrationInProgress.svg" alt="loader">                            
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding">
                                                    <!-- <strong *ngIf="migrationStep0" [ngStyle]="{'color': migrationStep0==='Failed' ? 'red' : 'green' }">{{migrationStep0}}</strong> -->
                                                    <!-- -->
                                                    <div  *ngIf="migrationStep0" [ngClass]="{'migrationStepCompleted': migrationStep0 ==='Completed' ,'migrationStepFailed': migrationStep0 ==='Failed'  }" >{{migrationStep0}}
                                                        <span *ngIf="migrationStep0 ==='Failed'" style="margin-left: 4px;" >
                                                            <svg matTooltipClass="migrationFailedTooltip" matTooltip="Note: There are some failed steps, trigger &quot;re-process&quot; from &quot;Actions&quot;."
                                                            matTooltipPosition="below"
                                                            xmlns="http://www.w3.org/2000/svg" width="20.866" height="16" viewBox="0 0 20.866 18.233">
                                                                <g id="Component_19_1" data-name="Component 19 – 1" transform="translate(0.775 0.75)">
                                                                <path id="Path_2613" data-name="Path 2613" d="M10.4,5.236,2.576,18.306a1.849,1.849,0,0,0,1.581,2.773H19.815A1.849,1.849,0,0,0,21.4,18.306L13.566,5.236a1.849,1.849,0,0,0-3.161,0Z" transform="translate(-2.328 -4.346)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                                <path id="Path_2614" data-name="Path 2614" d="M18,13.5v3.7" transform="translate(-8.342 -7.859)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                                <path id="Path_2615" data-name="Path 2615" d="M18,25.5h0" transform="translate(-8.342 -12.464)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                                </g>
                                                            </svg>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding" style="text-align: center;">
                                                    <a *ngIf="migrationStep0 ==='Failed'" class="pointer a-icon-edit display-inline-flex" id="reprocess_migration"
                                                    matTooltip="Reprocess Failed Steps"
                                                    matTooltipPosition="below"
                                                    (click)="reprocessMigration()"
                                                    aria-label="Button that displays a tooltip in various positions">
                                                    <img class="sync-content content-size">
                                                </a>
                                                </div>
                                            </div>
                                            <div class="row no-margin-padding migration-align-end" [ngStyle]="{'margin-top': migrationStep1==='Failed' || migrationStep2==='Failed' ? '15px' : '0px'}">
                                                <div class="col-sm-2 no-margin-padding"><strong>{{migrateParams.oldNew === 'migrate-existing' ? '1.' : '2.'}}</strong></div>
                                                <div class="col-sm-6 no-margin-padding pt-1">Updating search client files ...</div>
                                                <div  *ngIf="!migrationStep1"  style="display: inline;">
                                                    <!-- class="col-sm-2 center-align -->
                                                    <div class="migrationInProgress">
                                                        <img src="assets/img/migrationInProgress.svg" alt="loader">                            
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding">
                                                    <!-- <strong *ngIf="migrationStep1" [ngStyle]="{'color': migrationStep1==='Failed' ? 'red' : 'green' }">{{migrationStep1}}</strong> -->
                                                    
                                                    <div  *ngIf="migrationStep1" [ngClass]="{'migrationStepCompleted': migrationStep1 ==='Completed' ,'migrationStepFailed': migrationStep1 ==='Failed'  }" >{{migrationStep1}} 
                                                    <span *ngIf="migrationStep1 ==='Failed'" style="margin-left: 4px;" >
                                                        <svg matTooltipClass="migrationFailedTooltip" matTooltip="Note: There are some failed steps, trigger &quot;re-process&quot; from &quot;Actions&quot;."
                                                    matTooltipPosition="below"
                                                    xmlns="http://www.w3.org/2000/svg" width="20.866" height="16" viewBox="0 0 20.866 18.233">
                                                        <g id="Component_19_1" data-name="Component 19 – 1" transform="translate(0.775 0.75)">
                                                            <path id="Path_2613" data-name="Path 2613" d="M10.4,5.236,2.576,18.306a1.849,1.849,0,0,0,1.581,2.773H19.815A1.849,1.849,0,0,0,21.4,18.306L13.566,5.236a1.849,1.849,0,0,0-3.161,0Z" transform="translate(-2.328 -4.346)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                            <path id="Path_2614" data-name="Path 2614" d="M18,13.5v3.7" transform="translate(-8.342 -7.859)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                            <path id="Path_2615" data-name="Path 2615" d="M18,25.5h0" transform="translate(-8.342 -12.464)" fill="none" stroke="#d10505" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                                                            </g>
                                                        </svg>
                                                    </span>
                                                    </div>
                                                </div>
                                                <div class="col-sm-2 no-margin-padding" style="text-align: center;">
                                                    <!-- [hidden]="migrationStep1!=='Failed' && migrationStep2!=='Failed'" -->
                                                    <a  [hidden]="migrationStep1!=='Failed' && migrationStep2!=='Failed'" class="pointer a-icon-edit display-inline-flex" id="reprocess_migration"
                                                        matTooltip="Reprocess Failed Steps"
                                                        matTooltipPosition="below"
                                                        (click)="reprocessMigration()"
                                                        aria-label="Button that displays a tooltip in various positions">
                                                        <img class="sync-content content-size">
                                                    </a>
                                                </div>
                                            </div>
                                    </div>
                                    </td>
                                </tr>
                                <tr *ngIf="!(migrationInProgress || migrationStep2 ==='Failed')">
                                    <td class="note-sc-edit">Note: Please edit/save the search client once</td>
                                </tr>
                                <tr>
                                    <td style="text-align: center;">
                                        <button  type="button" class="buttonPrimary migratePopupCanelBtn"
                                        [disabled]="migrationInProgress || migrationStep2 ==='Failed'"
                                        (click)="closeMigrationOverlay();
                                        getSearchClientsFromSandbox();
                                        "
                                        style="margin-right: 13px;">Ok</button>

                                        <button type="button" 
                                        class="buttonPrimary migratePopupMigrateBtn" [disabled]="migrationInProgress || migrationStep2 ==='Failed'" (click)="closeMigrationOverlay()">Cancel</button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>

<div class="topHeading display-flex" *ngIf="(contentTypeHide || editMode) && !isLoading_small && !ecoSystem" [hidden]="designerActive">
    <div class="heading-source margin-TB-auto-LR-0 ad_heading-txt-top" >
        Manage 
        <span matTooltip="{{selectedClient.name}}">
            <ng-container *ngIf="!clonedSelectedClientName">
            {{selectedClient.name.length> 50 ? selectedClient.name.substring(0,50) + '...' : selectedClient.name }}
            </ng-container>
            <ng-container *ngIf="clonedSelectedClientName && clonedSelectedClientName.length">
                {{clonedSelectedClientName.length> 50 ? clonedSelectedClientName.substring(0,50) + '...' : clonedSelectedClientName }}
            </ng-container>
        </span> Search Client
        <div class="info-headings">
            Before we proceed, make sure you have added the relevant content sources in your panel.
        </div>
    </div>
    <div class="margin-left-auto ad_btn-back">
        <button type="button" (click)="toggleEditMode();searchText='';selectedSCtype='All';getContentSources()" class="buttonSecondary white-space" routerLinkActive="router-link-active"
            routerLink="/dashboard/generate-search-client">Back To Search Client</button>
    </div>
</div>
<div class="sectionDiv margin-bottom-0px" *ngIf="(contentTypeHide || editMode) && !isLoading_small && !ecoSystem" [hidden]="designerActive">
    <div id="searches">
        <nav mat-tab-nav-bar>
            <span matTooltipClass="abTestTooltip" [matTooltip]="isEditingChildSC && isABTestRunning == 1 ? 'This tab is temporarily unavailable during the A/B test. Please wait until the process is complete.' : isEditingChildSC && isABTestRunning == 0 ? 'This tab is temporarily unavailable due to the A/B Test.' : '' " matTooltipPosition="below">
                <a mat-tab-link routerLinkActive="router-link-active"
                    routerLink="/dashboard/generate-search-client/Configurations" [disabled]="isEditingChildSC">
                    Configurations
                </a>
            </span>
            <span matTooltipClass="abTestTooltip" [matTooltip]="(isEditingChildSC && isABTestRunning == 1) || (ifParentSCHaveChild && isABTestRunning == 1)  ? 'This tab is temporarily unavailable during the A/B test. Please wait until the process is complete.' : 
            (isEditingChildSC && isABTestRunning == 0) || (ifParentSCHaveChild && isABTestRunning == 0) ? 'This tab is temporarily unavailable due to the A/B Test.' : '' " matTooltipPosition="below">
                <a mat-tab-link routerLinkActive="router-link-active"
                    routerLink="/dashboard/generate-search-client/ContentSources" [disabled]="isEditingChildSC || ifParentSCHaveChild">
                    Content Sources
                </a>
            </span>
            <!-- <a *ngIf="suUserandGzuser && settings.client.s3_supported == 1 && (settings.client.search_client_type_id == 8 || settings.client.search_client_type_id == 7)" mat-tab-link routerLinkActive="router-link-active"
            routerLink="/dashboard/generate-search-client/SFPackageConfig">
            SF Package Config
            </a> -->
            <span matTooltipClass="abTestTooltip" [matTooltip]="(ifParentSCHaveChild && isABTestRunning == 1)  ? 'This tab is temporarily unavailable during the A/B test. Please wait until the process is complete.' : 
            (ifParentSCHaveChild && isABTestRunning == 0) ? 'This tab is temporarily unavailable due to the A/B Test.' : '' " matTooltipPosition="below">
                <a mat-tab-link routerLinkActive="router-link-active"
                    routerLink="/dashboard/generate-search-client/Relevancy" [disabled]="isEditingChildSC || ifParentSCHaveChild">
                    Relevancy
                </a>
            </span>
            <!-- <a mat-tab-link routerLinkActive="router-link-active"
                routerLink="/dashboard/generate-search-client/Template"
                [disabled]="settings.client.search_client_type_id == 7 || settings.client.search_client_type_id == 13 || settings.client.search_client_type_id == 19 || settings.client.search_client_type_id == 22 || settings.client.search_client_type_id == 26 || settings.client.search_client_type_id == 28">
                Template
            </a> -->
            <!-- <a mat-tab-link routerLinkActive="router-link-active"
                routerLink="/dashboard/generate-search-client/Designer"
                [disabled]="settings.client.search_client_type_id == 13 || settings.client.search_client_type_id == 22">
                Designer
            </a> -->
            <span matTooltipClass="abTestTooltip" [matTooltip]="isEditingChildSC && isABTestRunning == 1 ? 'This tab is temporarily unavailable during the A/B test. Please wait until the process is complete.' : isEditingChildSC && isABTestRunning == 0 ? 'This tab is temporarily unavailable due to the A/B Test.' : '' " matTooltipPosition="below">
                <a mat-tab-link routerLinkActive="router-link-active"
                    routerLink="/dashboard/generate-search-client/AnalyticsSettings" [disabled]="isEditingChildSC">
                    Analytics Settings
                </a>
            </span>
            <!-- <a mat-tab-link routerLinkActive="router-link-active"
                routerLink="/dashboard/generate-search-client/AgentHelper"
                [disabled]="true">
                *ngIf="settings.client.search_client_type_id == 7 && allAddons[10]">
                Agent Helper
            </a> -->
            <span matTooltipClass="abTestTooltip" [matTooltip]="isEditingChildSC && isABTestRunning == 1 ? 'This tab is temporarily unavailable during the A/B test. Please wait until the process is complete.' : isEditingChildSC && isABTestRunning == 0 ? 'This tab is temporarily unavailable due to the A/B Test.' : '' " matTooltipPosition="below">
                <a mat-tab-link routerLinkActive="router-link-active"
                    routerLink="/dashboard/generate-search-client/Advertisements"
                    [disabled]="settings.client.search_client_type_id == 7 || settings.client.search_client_type_id == 16 || settings.client.search_client_type_id == 18 || isEditingChildSC">
                    Advertisements
                </a>
            </span>
            <span matTooltipClass="abTestTooltip" [matTooltip]="isEditingChildSC && isABTestRunning == 1 ? 'This tab is temporarily unavailable during the A/B test. Please wait until the process is complete.' : isEditingChildSC && isABTestRunning == 0 ? 'This tab is temporarily unavailable due to the A/B Test.' : '' " matTooltipPosition="below">
                <a mat-tab-link routerLinkActive="router-link-active"
                    routerLink="/dashboard/generate-search-client/PageRating"
                    [disabled]="settings.client.search_client_type_id == 22 || settings.client.search_client_type_id == 7 || isEditingChildSC">
                    End-User Feedback
                </a>
            </span>
            <span matTooltipClass="abTestTooltip" [matTooltip]="(isEditingChildSC && isABTestRunning == 1) || (ifParentSCHaveChild && isABTestRunning == 1)  ? 'This tab is temporarily unavailable during the A/B test. Please wait until the process is complete.' : 
            (isEditingChildSC && isABTestRunning == 0) || (ifParentSCHaveChild && isABTestRunning == 0) ? 'This tab is temporarily unavailable due to the A/B Test.' : '' " matTooltipPosition="below">
                <a mat-tab-link routerLinkActive="router-link-active"
                    routerLink="/dashboard/generate-search-client/LanguageManager" [disabled]="isEditingChildSC || ifParentSCHaveChild">
                    Language Manager
                </a>
            </span>
        </nav>
    </div>
</div>

<div class="su__sectionDiv-search-client ">
    <router-outlet></router-outlet>
</div>

<div *ngIf="openModalShare" class="ad_share-modal-row display-flex center-align">
    <div class="modal fade" id="shareSettingModal" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header px-0 py-1 border-0">
                    <div class="modal-header border-0 close-dialog" data-dismiss="modal">
                        <div class="ad_share-header-main display-flex">
                            <div class="ad_share-header display-flex">
                                <div class="ad_share-icon-row ad_darkmode-bg1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                        <path d="M0,0H24V24H0Z" fill="none" />
                                        <path
                                            d="M7,10H5V8A1,1,0,0,0,3,8v2H1a1,1,0,0,0,0,2H3v2a1,1,0,0,0,2,0V12H7a1,1,0,0,0,0-2Zm11,1a3,3,0,1,0-.91-5.86A4.934,4.934,0,0,1,17.99,8a5.031,5.031,0,0,1-.9,2.86A2.99,2.99,0,0,0,18,11Zm-5,0a3,3,0,1,0-3-3A2.987,2.987,0,0,0,13,11Zm0,2c-2,0-6,1-6,3v1a1,1,0,0,0,1,1H18a1,1,0,0,0,1-1V16C19,14,15,13,13,13Zm6.62.16A3.7,3.7,0,0,1,21,16v1.5a2.734,2.734,0,0,1-.05.5H23.5a.5.5,0,0,0,.5-.5V16C24,14.46,21.63,13.51,19.62,13.16Z"
                                            fill="#fff" /></svg>
                                </div>
                                <div class="ad_share-txt-row ml-2">
                                    <div class="ad_lable-title ad_darkmode-color">Share with Admins and Moderators</div>
                                    <div class="ad_lable-desc ad_darkmode-color">Only added users can change setting for the search client
                                    </div>
                                </div>
                            </div>
                            <div class="ad_close-icon-block pointer" click="openModalShare=false; closeModal()">
                                <i class="material-icons ad_darkmode-color">
                                    close<br>
                                </i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="modal-user-content w-100 d-block">
                        <div class="ad_share-user-row">
                            <div class="ad_share-modal-title text-left px-2 ad_darkmode-color">Admin</div>
                            <div class="ad_share-user-inner px-2 py-2 display-flex" *ngIf="shareModalData.admin?.length">
                                <div *ngFor="let user of shareModalData.admin"
                                    class="ad_share-user-info pointer mb-3 position-relative ad_darkmode-bg1">
                                    <span *ngIf="shareModalData.accessDetails.ownerEmail == user.email"
                                        class="ad_shared-owner ad_darkmode-color">Owner</span>
                                    <div [ngClass]="{'ad_share-active': user.shareAccess || shareModalData.accessDetails.ownerEmail == user.email}"
                                        (click)="user.shareAccess= adminSession.email == user.email || shareModalData.accessDetails.ownerEmail == user.email?user.shareAccess:!user.shareAccess;updateShareSettings(user.email)"
                                        class="ad_share-inner-block w-100 ad_padding-10">
                                        <div class="ad_user-info display-flex">
                                            <div class="ad_share-user-content">
                                                <div class="ad_share-user-icon">
                                                    <div class="ad_font-600 ad_color-blue text-capitalize">{{user.name[0]||user.email[0]}}</div>
                                                </div>
                                            </div>
                                            <div class="ad_share-user-info-row ml-2 text-left">
                                                <div class="ad_share-user-title ad_font-600">{{user.name}}</div>
                                                <div class="ad_share-user-desc ad_font-12">{{user.email}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class ="p-3 ad_font-12 ad_font-500 text-left ad_darkmode-color" *ngIf="shareModalData.admin?.length==0">No Admin user exist.</div>
                        </div>

                        <div class="ad_share-user-row">
                            <div class="ad_share-modal-title px-2 text-left ad_darkmode-color">Moderators</div>
                            <div class="ad_share-user-inner px-2 py-2 display-flex" *ngIf="shareModalData.moderator?.length!=0">
                                <div *ngFor="let user of shareModalData.moderator"
                                    class="ad_share-user-info pointer mb-3 position-relative ad_darkmode-bg1">
                                    <span *ngIf="shareModalData.accessDetails.ownerEmail == user.email"
                                        class="ad_shared-owner ad_darkmode-color">Owner</span>
                                    <div [ngClass]="{'ad_share-active': user.shareAccess}" class="ad_share-inner-block w-100 ad_padding-10"
                                    (click)="user.shareAccess= adminSession.email == user.email || shareModalData.accessDetails.ownerEmail == user.email?user.shareAccess:!user.shareAccess;updateShareSettings(user.email)" >
                                        <div class="ad_user-info display-flex">
                                            <div class="ad_share-user-content">
                                                <div class="ad_share-user-icon">
                                                    <div class="ad_font-600 ad_color-blue text-capitalize">{{user.name[0] ||user.email[0]}}</div>
                                                </div>
                                            </div>
                                            <div class="ad_share-user-info-row ml-2 text-left">
                                                <div class="ad_share-user-title ad_font-600">{{user.name}}</div>
                                                <div class="ad_share-user-desc ad_font-12">{{user.email}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class ="p-3 ad_font-12 ad_font-500 text-left ad_darkmode-color" *ngIf="shareModalData.moderator?.length==0">No Moderator user exist.</div>
                            <div class="ad_share-user-row text-center my-2">
                                <button class="buttonPrimary"
                                    (click)="saveShareSettingsForSC(shareModalData.id, shareModalData.name); openModalShare=false; ;closeModal()">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="overlay" *ngIf="scMigratePopup">
    <div class="shadowDiv animate-div-clone migrateScPopup" [@opened]="">
        <div class="row parent-div">
            <div class="migrateScPopupHeading">
                Would you like to migrate the search client to cloud front ?
            </div>
            <div class="migrateScPopupContent">
                <span><span class="migrateScPopupNote">Note : </span>During this process there will be a downtime as a
                        reprocessing/reinstallation depending on type of search client
                        may be required.<br> 
                        <span>
                        <span class="migrateScPopupHeading">Prerequisite :</span>
                        <ol>
                            <li>
                                Latest build of admin & SC services is deployed
                            </li>
                            <li>
                                All customizations are available in this SC's codebase ( copied from a CDN SC ).
                            </li>
                        </ol>
                        Once migrated, please follow the instructions mentioned in <strong>ReadMe</strong> file after downloading the search client.
                        </span>
                </span>
            </div>
            <div class="display-flex align-items-center justify-content padding-top-15px migrateScPopupBtns">
                <button class="buttonPrimary margin-right-10px"
                    (click)="scMigratePopup=false;">Cancel</button>
                <button class="buttonPrimary"
                    (click)="migrateScToCloudFront(scConfigBackup);">Migrate to M23</button>
            </div>
        </div>
    </div>
</div>

<!-- stop the A/B Test alert starts -->
<div *ngIf="deleteABsearchClient" class="delete-sc-alert outer-overlay d-flex align-items-center justify-content-center position-fixed w-100 h-100 text-center">
    <div class="dialog-body position-relative"> 
        <h2 class="mb-2">Are you sure you want to delete {{scSource.name}} ?</h2>
        <p class="note mb-1 pb-1 pt-1"><span>Note: </span> Deleting this client will permanently remove all A/B test data and<br> reports linked to it.</p>
        <!-- <mat-checkbox><span class="retain-check">Retain A/B test reports.</span></mat-checkbox> -->
        <div class="w-100 mt-3 text-center">
            <button class="buttonPrimary mr-2" (click)="deleteABsearchClient = false">Cancel</button>
            <button class="buttonPrimary" (click)="scSource.editable ? deleteSearchClient(scSource, deleteParentABTest = true) : null; deleteABsearchClient = false; ">Proceed</button>
        </div>
    </div> 
  </div>
  <!-- stop the A/B Test alert ends -->

<!-- stop the A/B Test alert starts -->
<div *ngIf="testInprogress" class="test-inprogress-alert outer-overlay d-flex align-items-center justify-content-center position-fixed w-100 h-100 text-center">
    <div class="dialog-body position-relative">
        <figure class="mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                <mask id="mask0_522_3391" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="38" height="38">
                  <path d="M37.6657 0.217773H0.335938V37.5512H37.6657V0.217773Z" fill="white"/>
                </mask>
                <g mask="url(#mask0_522_3391)">
                  <path d="M15.3329 8.7303C15.3459 9.20418 15.2033 9.66927 14.927 10.0544C14.6506 10.4396 14.2556 10.7236 13.8025 10.863C13.3156 11 12.7971 10.9734 12.3267 10.7873C11.8564 10.6013 11.46 10.2661 11.1985 9.83305C10.5798 8.82856 10.0084 7.79678 9.41698 6.77592C9.23501 6.44655 9.02758 6.12082 8.8547 5.78417C8.60809 5.32108 8.52426 4.78854 8.61666 4.27207C8.70906 3.75561 8.97234 3.28517 9.36422 2.93631C9.61934 2.73342 9.91527 2.58802 10.2318 2.51005C10.5483 2.43208 10.8779 2.42338 11.198 2.48455C11.5182 2.54571 11.8214 2.6753 12.0869 2.86445C12.3523 3.0536 12.5738 3.29784 12.7362 3.58049C13.3621 4.58861 13.9354 5.63131 14.5268 6.65946C14.7546 7.02047 14.9606 7.39479 15.1436 7.78041C15.268 8.08273 15.3299 8.40707 15.3256 8.73394" fill="#32AA34"/>
                  <path d="M2.54884 10.652C2.54774 10.3301 2.61785 10.0119 2.75412 9.72025C2.89039 9.42858 3.08947 9.17066 3.3371 8.96494C3.58473 8.75922 3.87479 8.6108 4.18649 8.53031C4.4982 8.44981 4.82385 8.43922 5.14012 8.49932C5.44753 8.54921 5.74379 8.65268 6.01541 8.80503C7.27951 9.53292 8.543 10.2608 9.80589 10.9887C10.1617 11.1783 10.4632 11.4557 10.6817 11.7946C10.9001 12.1334 11.0284 12.5225 11.0542 12.9249C11.0751 13.2365 11.028 13.5489 10.9163 13.8406C10.8045 14.1322 10.6308 14.3961 10.407 14.6139C10.1832 14.8318 9.91484 14.9985 9.62033 15.1024C9.32583 15.2064 9.01226 15.2451 8.70132 15.2159C8.28897 15.1867 7.88905 15.0621 7.53306 14.852C6.29565 14.1386 5.05884 13.4241 3.82265 12.7083C3.44827 12.5055 3.13363 12.2081 2.91011 11.8457C2.6866 11.4832 2.56203 11.0686 2.54884 10.643" fill="#64DB66"/>
                  <path d="M21.2415 4.81627C21.2415 5.57874 21.2688 6.3412 21.2415 7.10184C21.2448 7.55297 21.1143 7.99497 20.8665 8.37197C20.6188 8.74898 20.2648 9.04409 19.8493 9.22C19.5372 9.34729 19.2001 9.40177 18.8638 9.37928C18.5274 9.3568 18.2007 9.25795 17.9082 9.09025C17.6158 8.92254 17.3654 8.69039 17.1761 8.41143C16.9869 8.13248 16.8637 7.81405 16.8159 7.48035C16.7896 7.34292 16.775 7.20352 16.7722 7.06363C16.7722 5.55508 16.7486 4.04653 16.7722 2.5398C16.7593 2.08675 16.8831 1.64029 17.1275 1.25859C17.3719 0.876888 17.7256 0.577636 18.1425 0.399814C18.4379 0.272733 18.7576 0.212128 19.079 0.222287C19.4005 0.232445 19.7157 0.313119 20.0025 0.458599C20.2893 0.60408 20.5406 0.810812 20.7387 1.06417C20.9367 1.31752 21.0767 1.61131 21.1487 1.92474C21.2149 2.17278 21.2473 2.42865 21.2451 2.68538C21.2451 3.39325 21.2451 4.09931 21.2451 4.80718L21.2415 4.81627Z" fill="#32AA34"/>
                  <path d="M8.8617 22.5529C9.28103 22.5392 9.695 22.6502 10.0513 22.8717C10.4076 23.0932 10.6902 23.4154 10.8634 23.7976C11.0477 24.1873 11.1125 24.6228 11.0497 25.0493C10.9868 25.4758 10.7992 25.8741 10.5104 26.1941C10.3031 26.4474 10.0492 26.6587 9.76246 26.8165C8.54507 27.5189 7.33131 28.2268 6.11028 28.9219C5.70923 29.1909 5.23484 29.3293 4.75207 29.3182C4.26929 29.3071 3.80175 29.1471 3.41345 28.86C3.1711 28.6764 2.9698 28.444 2.82242 28.178C2.67504 27.912 2.58484 27.6182 2.55761 27.3153C2.53037 27.0125 2.56669 26.7073 2.66424 26.4192C2.7618 26.1312 2.91844 25.8667 3.12412 25.6428C3.33102 25.4028 3.57714 25.1998 3.85201 25.0422L7.50237 22.9332C7.90967 22.6851 8.3775 22.5541 8.85442 22.5547" fill="#77E079"/>
                  <path d="M15.3273 29.1189C15.3232 29.5646 15.1974 30.0007 14.9633 30.38C14.2573 31.6046 13.5567 32.8347 12.8397 34.0539C12.6347 34.4521 12.3203 34.7834 11.9335 35.009C11.5467 35.2346 11.1035 35.3453 10.6561 35.3278C10.1178 35.3118 9.60538 35.0932 9.22141 34.7157C8.83743 34.3381 8.61021 33.8294 8.58522 33.2915C8.55911 32.7524 8.69403 32.2178 8.97281 31.7556C9.67037 30.5522 10.3667 29.3476 11.0619 28.1417C11.2502 27.772 11.5307 27.457 11.8762 27.2272C12.2217 26.9974 12.6206 26.8605 13.0344 26.8297C13.3398 26.8088 13.6461 26.8535 13.9329 26.9607C14.2196 27.0678 14.4801 27.235 14.6968 27.4511C14.9136 27.6672 15.0817 27.9272 15.1898 28.2136C15.2979 28.5 15.3436 28.8061 15.3236 29.1116" fill="#77E079"/>
                  <path d="M4.92504 21.1287C4.1553 21.1287 3.38556 21.1414 2.61582 21.1287C2.13103 21.117 1.66156 20.9566 1.27097 20.6691C0.880388 20.3817 0.587564 19.9812 0.432155 19.5219C0.345698 19.2162 0.325292 18.8957 0.372294 18.5815C0.419295 18.2674 0.532627 17.9668 0.704757 17.6998C0.876887 17.4329 1.10387 17.2056 1.37062 17.0331C1.63737 16.8606 1.93778 16.7469 2.25188 16.6995C2.36557 16.6745 2.48129 16.6599 2.59762 16.6559C4.15348 16.6559 5.70934 16.6231 7.2652 16.6667C7.70987 16.6646 8.14493 16.796 8.51411 17.0439C8.8833 17.2917 9.16962 17.6447 9.33605 18.057C9.45763 18.351 9.51397 18.6678 9.50118 18.9857C9.48839 19.3035 9.40677 19.6147 9.26196 19.898C9.11715 20.1812 8.91259 20.4296 8.66242 20.6261C8.41225 20.8225 8.12242 20.9624 7.81294 21.0359C7.56541 21.1049 7.30924 21.138 7.0523 21.1342H4.92868" fill="#77E079"/>
                  <path d="M22.6786 29.1042C22.6582 28.8005 22.703 28.4959 22.81 28.2109C22.917 27.9259 23.0837 27.6671 23.299 27.4519C23.5143 27.2366 23.7731 27.0699 24.0581 26.9629C24.3431 26.8559 24.6477 26.8111 24.9514 26.8315C25.3695 26.8604 25.7729 26.9976 26.1219 27.2295C26.4709 27.4615 26.7536 27.7802 26.9422 28.1544C27.6373 29.3505 28.3289 30.5485 29.0167 31.7483C29.2734 32.1282 29.4147 32.5743 29.4238 33.0328C29.4328 33.4912 29.3091 33.9426 29.0676 34.3324C28.9091 34.5855 28.6999 34.803 28.4532 34.9712C28.2064 35.1395 27.9275 35.2549 27.634 35.3101C27.3406 35.3653 27.0388 35.3591 26.7478 35.292C26.4568 35.2249 26.1829 35.0983 25.9432 34.9201C25.4599 34.5145 25.0717 34.0075 24.8059 33.4352C24.2333 32.4465 23.6619 31.4572 23.0917 30.4672C22.8224 30.0636 22.6781 29.5895 22.6768 29.1042" fill="#77E079"/>
                  <path d="M29.3662 15.2166C28.8908 15.2613 28.4135 15.1541 28.0028 14.9105C27.5922 14.667 27.2693 14.2995 27.0805 13.8609C26.9467 13.4804 26.913 13.0718 26.9824 12.6745C27.0519 12.2772 27.2223 11.9043 27.4773 11.5917C27.9287 11.1561 28.4465 10.7949 29.0113 10.5217C29.9976 9.954 30.9826 9.38381 31.9665 8.81121C32.4136 8.54761 32.9336 8.43483 33.4498 8.48956C33.9659 8.54428 34.4507 8.7636 34.8325 9.11509C35.1716 9.47127 35.3826 9.9302 35.432 10.4195C35.4814 10.9088 35.3665 11.4006 35.1055 11.8174C34.8065 12.2632 34.3979 12.6247 33.919 12.8674C32.7198 13.5461 31.5298 14.2431 30.3324 14.9236C30.043 15.1059 29.7081 15.2071 29.3662 15.2166Z" fill="#CAFDCB"/>
                  <path d="M29.214 22.5715C29.8283 22.5916 30.4214 22.8014 30.9118 23.1721L34.0982 25.0118C34.5512 25.23 34.9232 25.5862 35.161 26.0293C35.3987 26.4724 35.4898 26.9794 35.4211 27.4775C35.3637 27.9133 35.1695 28.3196 34.8663 28.6378C34.5631 28.956 34.1667 29.1697 33.7342 29.2481C33.3626 29.3111 32.9821 29.3 32.6149 29.2153C32.2476 29.1307 31.9007 28.9742 31.5942 28.755C30.5023 28.1071 29.4105 27.4811 28.3078 26.8552C27.9218 26.6657 27.5922 26.3783 27.3521 26.0217C27.1119 25.665 26.9696 25.2514 26.9393 24.8225C26.9213 24.5203 26.9678 24.2177 27.076 23.935C27.1841 23.6522 27.3513 23.3958 27.5664 23.1828C27.7815 22.9698 28.0396 22.805 28.3233 22.6996C28.6071 22.5942 28.912 22.5505 29.214 22.5715Z" fill="#BCF9BD"/>
                  <path d="M21.2399 32.9855C21.2399 33.748 21.2508 34.5104 21.2399 35.271C21.227 35.7599 21.0637 36.2329 20.7722 36.6256C20.4808 37.0183 20.0754 37.3117 19.6113 37.4657C19.307 37.5486 18.9887 37.5662 18.6771 37.5175C18.3656 37.4687 18.0679 37.3547 17.8035 37.1829C17.5391 37.0111 17.3139 36.7854 17.1429 36.5205C16.9718 36.2557 16.8586 35.9577 16.8107 35.646C16.7834 35.5087 16.7682 35.3692 16.7652 35.2293C16.7652 33.7207 16.7398 32.2122 16.7652 30.7036C16.7523 30.2472 16.879 29.7978 17.1284 29.4154C17.3778 29.0329 17.7381 28.7357 18.161 28.5636C18.4543 28.4393 18.7712 28.3807 19.0896 28.392C19.4079 28.4032 19.72 28.4841 20.0038 28.6287C20.2877 28.7733 20.5364 28.9782 20.7327 29.2291C20.9289 29.48 21.0679 29.7709 21.1398 30.0812C21.2054 30.3183 21.239 30.5631 21.2399 30.8091V32.9783V32.9855Z" fill="#77E079"/>
                  <path d="M33.0762 21.1282C32.3211 21.1282 31.5659 21.1482 30.8126 21.1282C30.3818 21.1317 29.9586 21.0153 29.5903 20.7919C29.222 20.5686 28.9231 20.2471 28.7272 19.8635C28.5975 19.6091 28.5197 19.3316 28.4983 19.0469C28.4768 18.7622 28.5122 18.4761 28.6022 18.2052C28.6923 17.9343 28.8353 17.684 29.023 17.4689C29.2106 17.2537 29.4391 17.078 29.6952 16.9519C30.0666 16.7548 30.4812 16.6535 30.9017 16.6571H35.218C35.6475 16.6427 36.073 16.743 36.4508 16.9477C36.8285 17.1524 37.1448 17.4541 37.3672 17.8218C37.5898 18.2062 37.6875 18.6503 37.6467 19.0927C37.606 19.535 37.4288 19.9538 37.1397 20.2911C36.8945 20.5681 36.5913 20.7877 36.2516 20.9343C35.912 21.0808 35.5442 21.1508 35.1744 21.1391H33.0745" fill="#CAFDCB"/>
                  <path d="M26.1969 10.143C25.7945 10.3934 25.3211 10.5052 24.8492 10.4612C24.3773 10.4171 23.9328 10.2197 23.5837 9.89919C23.2196 9.54803 22.9807 9.0871 22.9038 8.58713C22.8269 8.08716 22.9162 7.57576 23.1579 7.13139C23.7038 6.09051 24.3152 5.07511 24.8975 4.04878C25.0795 3.7176 25.2615 3.38277 25.4708 3.0625C25.7459 2.61535 26.1634 2.27371 26.6562 2.09251C27.1489 1.91132 27.6883 1.90109 28.1876 2.06347C28.4915 2.18126 28.7663 2.36327 28.9934 2.59707C29.2205 2.83087 29.3944 3.11095 29.5032 3.41815C29.6121 3.72535 29.6533 4.05242 29.6241 4.37702C29.5949 4.70162 29.496 5.01609 29.334 5.29893C28.7881 6.34891 28.1694 7.36978 27.5798 8.39246C27.3834 8.76912 27.1646 9.13371 26.9247 9.48429C26.728 9.74593 26.4805 9.96505 26.1969 10.1285" fill="#E9FFE9"/>
                </g>
              </svg>
        </figure>
        <h2 class="mb-1">A/B Testing is in Progress</h2>
        <p class="note mb-3 pb-1 pt-1">Cannot delete Search Client <span>{{scSource.name}}</span>.</p>
        <div class="w-100 mt-1 text-center">
            <button class="buttonPrimary mr-2" (click)="testInprogress = false">Okay</button>
        </div>
    </div> 
  </div>
  <!-- stop the A/B Test alert ends -->
