declare var ace: any;
import {SC_IDS} from '../../../constants/searchClientType.constants';
import {
  Component,
  OnInit,
  ViewEncapsulation,
  Input,
  HostListener,
} from "@angular/core";
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from "@angular/animations";
import {
  trigger as triggers,
  style as styles,
  transition as transitions,
  animate as animations,
  query,
  stagger,
} from "@angular/animations";
import { DomSanitizer } from '@angular/platform-browser';
import { CookieService } from "../../../services/cookie.service";
import { Router, Event, NavigationEnd } from "@angular/router";
import { PaginationService } from "ng2-pagination";
import { ContentSourceService } from "../../../services/contentSource.service";
import { SearchClientService } from "../../../services/searchClient.service";
import { ToastyService, ToastOptions } from "ng2-toasty";
import { AdminAnalyticsService } from "../../../services/adminAnaytics.service";
import { Subject } from "rxjs";
import { FormControl } from "@angular/forms";
import { AnalyticsSetting } from "./AnalyticsSetting";
import { SearchTuningService } from "app/services/searchTuning.service";
import { CategoryPipe } from '../../../pipes/category';
import { TimeZonePipe } from '../../../pipes/timezone.pipe';
import { TimezoneService } from 'app/services/timezone.service';
import { EcosystemService } from '../../../services/ecoSystem.service';
import { UserManagementService } from '../../../services/userManagement.service';
import { DEFAULT_ATTACHMENTS, DEFAULT_RELEVANCY, Helper } from "app/variables/contants";
import { addonsService } from '../../../services/addons.service';
import * as socketIo from 'socket.io-client';
import { Variables } from '../../../variables/contants';
import { abTestingService } from "../../../services/abTesting.service";

@Component({
  selector: "generate-search-client",
  templateUrl: "generate_search_client.html",
  providers: [
    ContentSourceService,
    PaginationService,
    ToastyService,
    SearchClientService,
    SearchTuningService,
    AdminAnalyticsService,
    CategoryPipe,
    EcosystemService,
    addonsService,
    abTestingService
  ],
  animations: [
    trigger("opened", [
      state(
        "void",
        style({
          transform: "translateY(-100%)",
        })
      ),
      transition("no => yes", [
        animate(200),
        style({ transform: "translate(100%)" }),
      ]),
      transition("yes => no", [
        animate(200),
        style({ transform: "translate(-100%)" }),
      ]),
      transition("void => *", [animate(200)]),
      transition("* => void", [animate(200)]),
    ]),
    trigger("previewTemplate", [
      state(
        "void",
        style({
          transform: "translateY(-100%)",
        })
      ),
      transition("void => *", [animate(200)]),
      transition("* => void", [animate(200)]),
    ]),
    triggers("listAnimation", [
      transitions("* <=> *", [
        query(
          ":enter",
          [
            styles({ opacity: 0, transform: "translateX(-15px)" }),
            stagger(
              "50ms",
              animations(
                "550ms ease-out",
                styles({ opacity: 1, transform: "translateX(0px)" })
              )
            ),
          ],
          { optional: true }
        ),
        query(":leave", animations("50ms", styles({ opacity: 0 })), {
          optional: true,
        }),
      ]),
    ]),
  ],
  styleUrls: ["generate_search_client.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class GenerateSearchClientComponent implements OnInit {
  private selectedDrupalVersion: string = '';
  private selectedKhorosVersion: string = '';
  
  private customUIDfield: boolean = false;
  private isValidUID: boolean = false;
  private customUID: string;
  public updatedContentSource: boolean;
  public contentSources: any;
  private selectedSCtype: any;
  private allSupportedSearchClientCategories:any;
  private allCategories: any;
  private isLoading_small: boolean;
  public filterPreferenceList: any[];
  public filterPreferenceListCopy: any[];
  private preferencesLoading: boolean;
  selectPlatform: boolean;
  private newPlatform: any;
  private isDrupalSc: boolean = false;
  private  isKhoros: boolean = false;
  private platformTypes: any;
  public contentTypeHide: boolean;
  public ecoSystem: boolean;
  public editMode: boolean;
  public selectedClient: any;
  public selectedObject: any;
  public settings: any;
  public desigerTabConfig: any;
  public agentHelperSettings;
  public filtersArray: any;
  private editClient: boolean;
  private currentTabClient: string;
  private excludeLoading: boolean;
  private cloneParameters: any;
  private allowClone: boolean;
  private allowReset: boolean;
  private checkStatus;

  /** Migration Variables - START*/
  private isMigrated: boolean = false;
  private migrationStep0: string;
  private migrationStep1: string;
  private migrationStep2: string;
  private migrateParams: any;
  private currentMigrationSource : any;
  private migrationInProgress: boolean = false;
  private isMigrationSend: string = '';
  private isReprocessMigration : boolean = false;
  private dltMigrationRequest: boolean = false;
  private migrationUIDForDeletion: string = '';
  /** Migration Variables - END*/
  expandedIcons: { [key: string]: boolean } = {}; // Track expanded state by UID
  deleteFilters: any;
  private selectedIndex: number;
  private addSearchClient: boolean;
  private addingSc: string;
  private isLoadingAdded: boolean;
  private allAddons: any;
  private uniqueSearchClient: boolean;
  private caseDeflectionEnabled: any;
  private optionsVal = {
    onUpdate: (event: any) => {},
    group: "test",
    animation: 150,
    handle: ".sortImage",
    "ui-floating": "auto",
    delay: 0,
    ghostClass: "sortable-ghost",
    chosenClass: "sortable-chosen",
    dragClass: "sortable-drag",
    scrollSpeed: 5,
    scrollSensitivity: 30,
    connectWith: ".sortImage",
  };
  private toggleSummary: boolean;
  public summaryPreferences: any;
  private matLoader: boolean;
  private showMergeFilters: boolean;
  private mergedFilters: any;
  private disableMerge: any;
  private aggregations: any;
  private clientUid: any;
  public channelExists: boolean;
  public subject = new Subject<any>();
  private isAdmin: boolean;
  private isModerator: boolean;
  private shareModalData: any = {};
  private userNewFeatureData: any = {};
  private currentSelectedUserForShare: any = [];
  private backup: any = [];
  private addArray: any = [];
  private removeArray: any = [];
  @Input() adminSession: any;
  userEmail: any;
  activeClientId: any;
  public analyticsSettingsCopy: any;
  public deflectionSettingsCopy: any;
  public contentSourceCopy: any;
  public contentSourceCopyNew:any;
  public copyAnalyticsProperties: any;
  public copyAnalyticsPropertiesNew:any
  public changeSearchContentSource:boolean;
  public changeAnalyticsProperties:boolean;
  public newFeatureEcoSystemProperty:boolean = true;
  private cloneButtonClicked: boolean = false;
  private newSCSaveButtonClicked: boolean = false;
  public suUserandGzuser: boolean;
  private searchClientsFromSandbox: any;
  private instanceType: any;
  private isProdInstance : boolean = false;
  private initiateMigration;
  private selectedForMigration;

  private drupalPlatforms: string[] = ['Drupal 7','Drupal 10']; 
  private khorosPlatforms: string[] = ['Khoros','Aurora']; 
  private drupalVersions: string[] = [];
  private khorosVersions: string[] = [];

  public defaultSorting: any;
  public designerActive: boolean = false;
  userTimeZone : any = 'UTC';
  private finalHiddenfacet: any = [];
  public scMigratePopup: boolean;

  private migrateButton = {};
  private fileCount = 5;
  private enableRollback = false;
  private backupFilesList = [];
  private enableMigrateButton = true;
  private backupErrorMessage = '';
  private rollbackRadioBtn: string;
  // private prodUid: string;
  private rollbackInProgress = false;
  private loadingBackupFiles = true;
  private backupIds = {
    prodUid: "",
    prodSCId: null
  }
  calculateDate: any = new Date();
  private scOperations: any = {};
  public vectorProgress = {};
  private socket: any;  
  private ScCloudfrontUrl: any = '';
  private enableSboxProdFeature = true;

  private ssoTypes: any = [
    { name: 'Google' },
    { name: 'Okta' }
  ];
  private SC_IDS = SC_IDS;
  private SC_POPULAR_CATEGORY = Object.values({
    SALESFORCE_LIGHTNING_COMMUNITY:8,
    SALESFORCE_SERVICE_CONSOLE:7,
    HIGHER_LOGIC_THRIVE: 23,
    KHOROS: 2,
    WEB_APP: 6,
    ZENDESK_SUPPORT: 12,
    FRESHSERVICE:34,
  })

  private resetSCOption: any = {
    value: '1',
    sourceUid: ''
  };

  private childSC: any = [];
  private haveDropdown: any = [];
  private isEditingChildSC: boolean = false;
  private ifParentSCHaveChild: boolean = false;
  private testReportsData: any = [];
  private isABTestRunning: number = 0;
  private abTestStatus: any = [];
  private readonly DIFF_MINUTES = 15;

  constructor(
    private cookieService: CookieService,
    private router: Router,
    private contentSourceService: ContentSourceService,
    private toastyService: ToastyService,
    private searchClientService: SearchClientService,
    private searchTuningService: SearchTuningService,
    private adminAnalyticsService: AdminAnalyticsService,
    private category: CategoryPipe,
    private TimezoneService: TimezoneService,
    private EcosystemService:EcosystemService,
    private userManagementService: UserManagementService,
    private addonsService: addonsService,
    private domSanitizer: DomSanitizer,
    private abTestingService: abTestingService
  ) {
    TimezoneService.getUserTimeZone().subscribe((data)=>{
			this.userTimeZone = data;
	  });
    this.router.events.subscribe((event: Event) => {
      if (event instanceof NavigationEnd) {
        if(event.url === "/dashboard/generate-search-client") {
          this.editMode = false;
          this.selectedObject = false;
          this.contentTypeHide = false;
        }
      }            
    });
  }
  objFilterPreferences: any = {};

  /**
   * @function shouldShowCleanupIcon
   * @param createdDateISOString - created date in ISO string format
   * @returns {boolean} - true if the cleanup icon should be shown, false otherwise
   */
  shouldShowCleanupIcon(createdDateISOString: string): boolean {
    const createdDate = new Date(createdDateISOString);
    const now = new Date();
    const diffMs = now.getTime() - createdDate.getTime();
    const diffMinutes = diffMs / (60 * 1000);
    return diffMinutes >= this.DIFF_MINUTES;
  }

  /**
   * OnInit
   */
  ngOnInit() {
    this.getTestReports();
    this.cookieService.checkSCOperations().then((result) => {
      this.scOperations = result;
      this.calculateDate.setDate(new Date().getDate() - this.scOperations.days);
      this.calculateDate = this.calculateDate.toISOString();
    });
    this.router.navigate(["dashboard/generate-search-client"]);
    this.scMigratePopup = false;
    this.contentSources = [];
    this.platformTypes = [];
    this.searchClientsFromSandbox = [];
    this.isLoadingAdded = false;
    this.selectedSCtype = 'All';
    this.getContentSources();
    this.getPlatformTypes();
    this.getSessionDetails();
    this.setAddedSearchClient();
    this.getAllSupportedSearchClientCategories();
    this.getInstanceInfo();
    let paramters = {type : 'get'}
    
    this.searchClientService.getPlatformTypes().then((result) => {
       this.drupalVersions = result.map(data=>{
        if(this.drupalPlatforms.includes(data.name)){
          return {...data}
        }
        return null;
      }).filter(Boolean);
    });
    this.searchClientService.getPlatformTypes().then((result) => {
      this.khorosVersions = result.map(data=>{
       if(this.khorosPlatforms.includes(data.name)){
        let displayName = data.name;
          if (data.name === "Khoros") {
           displayName = "Khoros Communities - Classic"
          } else if (data.name === "Aurora") {
            displayName = "Khoros Communities - Aurora";
          }

          return {...data ,
            displayName,
          };
        }
        return null;
      }).filter(Boolean);
    });
    this.checkEntriesInSboxProdMappingTable();
    this.userManagementService.userSpecificSettings(paramters).then((res) => {
      if(res && res.data && res.data.newFeatureActions) {
        this.userNewFeatureData = JSON.parse(res.data.newFeatureActions);
        if (this.userNewFeatureData && this.userNewFeatureData.ecosystem) {
          if(this.userNewFeatureData.ecosystem === true) {
            this.newFeatureEcoSystemProperty = true;
          } else {
            this.newFeatureEcoSystemProperty = false;
          }
        } else {
          this.newFeatureEcoSystemProperty = false;
        }
      } else {
        this.newFeatureEcoSystemProperty = false;
      }
    });
    this.addingSc = "";
    this.allAddons = [];
    this.getAddonsStatus();
    this.currentTabClient = "config";
    this.selectPlatform = false;
    this.contentTypeHide = false;
    
    this.newPlatform = {
      customUID: "",
      name: "",
      platformId: undefined,
      client_href: "",
      recommendations: 0,
      rec_widget_regex:"",
      rec_widget_redirect_url:"",
      recentsearchescount: 0,
      recentSearches: 0,
      knowledgeGraph: 0,
      autocomplete: 0,
      channel_id: null,
      didYouMean: 0,
      didYouMeanDictType: 0,
      similarSearchRecommendation: 0,
      allowSpecialCharSearch: 1,
      ViewedResults: 0,
      attachment_preview:0,
      email: "",
      language: "react",
      relevancy:'{"searchOperator": "OR","advanceQuery":[{"id":0,"setName":"master","querySnippetName":"multi_match","fields":["copy_fields","exclude_copy_fields", "include_title_fields"]},{"id":1,"setName":"default","querySnippetName":"multi_match","type":"phrase","boost":3,"fields":["exclude_copy_fields"]}], "relevancyScores": { \"enabled\": 1, \"dataRetention\": \"\", \"searchType\": 1}}',
      smartFacet:0,
      minFacetSelection:0,
      gpt: '{\"name\": \"\", \"active\": false, \"modelType\": \"\", \"contextLimit\": 2000}',
      summarize: '{\"name\": \"\", \"active\": false, \"modelType\": \"\"}',
      gpt_feedback: 0,
      highlight:'{"type": "unified", "order": "score", "fragmentSize": 100, "numberOfFragments": 5}',
      scToggles:'{\"htAccess\": 0}',
      default_results_sorting:'{"sortPreference":{"default":"_score","keyLabelMapping":{"click":"Popularity","_score":"Relevance","post_time":"Created Date"}}}',
      ssoConfig: {
        type: 'Google'
      },
      domainPermission: '{\"enabled\": false, \"default\": true, \"customDomains\": []}',
      isDisabled: false
    };
    this.selectedClient = {};
    this.filtersArray = [];
    this.editClient = false;
    this.excludeLoading = false;
    this.allowClone = false;
    this.allowReset = false;
    this.cloneParameters = {};
    this.cloneParameters.search_client_type_id = "";
    this.cloneParameters.language = "react";
    this.isMigrated = false;
    this.migrateParams = {};
    this.migrateParams.search_client_type_id = "";
    this.migrateParams.oldNew = "new"
    this.migrateParams.backupFileName = '';
    this.deleteFilters = [];
    this.selectedIndex = 0;
    this.addSearchClient = false;
    this.uniqueSearchClient = false;
    this.summaryPreferences = [];
    this.toggleSummary = false;
    this.mergedFilters = [];
    this.disableMerge = true;
    this.aggregations = [];
    this.changeSearchContentSource = false;
    this.changeAnalyticsProperties = false;
    this.socket = socketIo(`${window.location.origin}`, {
      path: `${Variables.baseHref}/index-service/socket.io`
    });
    this.listenMovKafka();
  }

  @HostListener("mouseenter"["$event"])
  mouseHandler(event) {
    event.stopImmediatePropagation();
  }

  ngOnDestroy() {
    Helper.allowSearchClientChildRoutes = false;
    try {
      this.socket.close();
    } catch (e) {
    }
  }

   // Method to toggle expanded icons for a specific item
   toggleMoreIcons(uid: string): void {
    this.expandedIcons[uid] = !this.expandedIcons[uid];
  }
  
  getSessionDetails() {
    this.cookieService.getSearchUnifySession().then((result) => {
      this.newPlatform.email = result.email;
      this.adminSession = result;
      this.userEmail = result.email;
      this.isAdmin = this.adminSession.role == 1 ? true : false;
      this.isModerator = this.adminSession.role == 2 ? true : false;
      this.suUserandGzuser = this.userEmail.includes(result.suDomains[0]) || this.userEmail.includes(result.suDomains[1]);   
    });
  }

  getAddonsStatus() {
    this.addonsService.getAddonsStatus().then(result => {
      localStorage.setItem("allAddons", JSON.stringify(result));
      this.allAddons = result;
    });
  }

  indexChanged(option, target, uid, status?) {
    this.ifParentSCHaveChild = this.testReportsData.some(
        item => uid === item.searchClient.uid && (item.status === 0 || item.status === 1)
    );
    this.isEditingChildSC = target && target.ab_test_parent != null ? true : false;
    this.isABTestRunning = status;
    const source = (target && typeof(target) === 'string' ? target : target.name) || ''; 
    if (option || option == 0) {
      let info;
      if (option == 0) {
        info = `Clicked Edit configuration on ${source} > ${uid}` ;
        this.activeClientId = target.id;
        this.clientChanged(target);
      } else if (option == 1) {
        info = `Downloaded ${source} > ${uid}`;
        this.downloadSearchClient(target);
      } else if (option == 2) {
        info = `Cloned ${source} > ${uid}`;
        this.cloneSearchClient();
      } else if (option == 3) {
        info = `Deleted ${source} > ${uid}`;
      } else if (option == 4) {
        info = `Added ${source} > ${uid}`;
        this.savePlatform();
      } else if (option == 5) {
        info = `Updated ${source} > ${uid}`;
      } else if (option == 6) {
        //uid not required in this case as its not generated at the moment
        info = 'Clicked Add a Search Client';
      } else if (option == 7) {
          info = `Clicked Update in Designer settings on ${source} > ${uid}`;
      } else if (option == 8) {
          info = `Updated Page rating settings on ${source} > ${uid}`;
      } else if (option == 9) {
          info = `Updated Language Manager settings on ${source} > ${uid}`;
      } else if (option == 10) {
          info = `Updated Advertisements settings on ${source} > ${uid}`;
      } else if (option == 11) {
          info = `Updated Analytics reports under Analytics settings on ${source} > ${uid}`;
      } else if (option == 15) {
          info = `Updated Analytics properties under Analytics settings on ${source} > ${uid}`
      } else if (option == 16) {
          info = `Edited Content source Configurations on ${source} > ${uid}`;
      } else if (option == 17) {
          info = `Updated Analytics properties under Analytics settings on ${source} > ${uid}`
      } else if (option == 18) {
        info = `Customize ${source} > ${uid}`;
        this.openDesigner(target);
      } else if (option == 19) {
        info = `Added Merge Facets on ${source} > ${uid}`
      } else if (option == 20) {
        info = `Edited Merge Facets on ${source} > ${uid}`;
      } else if (option == 21) {
        info = `Deleted Merge Facets on ${source} > ${uid}`;
      } else if (option == 22) {
        info = 'Migration Request from Sandbox' + source;
        this.initiatePlatformMigration(target);
      } else if (option == 23) {
        info = 'Migrate to Production' + source;
        this.resetMigrationSteps(true);
        if(this.migrateParams.oldNew == "migrate-new"){
          this.migrateNewSearchClientToProd();
        }
        else if(this.migrateParams.oldNew == "migrate-existing"){
          this.migrateExistingSearchClientToProd();
        };
      }
      else if (option == 24) {
        info = `Reset ${source} > ${uid}`;
        this.resetSearchClient();
      } 

      let options = {
        info: "Search Client > " + info,
        object: "Search Client",
      };
      //console.log(options);
      this.adminAnalyticsService.trackAdminAnalytics(options);
    }
  }

  /**
   * Customize search client.
   */
   openDesigner(source) {
    // this.searchClientService.getSearchClient(source.id).then((result) => {
      this.designerActive = true;
      // this.settings = result;
      this.desigerTabConfig = source;
      Helper.allowSearchClientChildRoutes = true;
      this.router.navigate(["dashboard/generate-search-client/Designer"]);
    // });
  }

  // migrateScToCloudFront() {
  //   this.settings.client.s3_supported = 1;
  //   /**
  //    * To migrate the SC from docker to CDN, we need to update the "language" in DB for deprecated SCs
  //    */
  //   if(this.settings.client.language === 'angular' && this.settings.client.search_client_type_id  != 8){
  //     if(this.settings.client.search_client_type_id == 7){
  //       this.settings.client.language = 'lwcSfConsole';
  //     }
  //     else{
  //       this.settings.client.language = 'react';
  //     }
  //   } else if(this.settings.client.language === 'angular' && this.settings.client.search_client_type_id === 8) {
  //     this.settings.client.language = 'lwcSfLightning';
  //   }
  //   this.updateSearchClient();
  //   this.scMigratePopup = false;
  //   this.router.navigate(["dashboard/generate-search-client"]);
  // }

  /**
   * Gets fresh data on client select element change.
   */
  private clonedSelectedClientName: string = '';
  
  extractClientName(name: string): string {
    const regex = /^(.+?)_([A-Z][a-z]+[A-Z][a-z]+)/;
    const match = name.match(regex);
  
    if (match) {
      const clientName = match[1];
      const camelCasePart = match[2].replace(/([a-z])([A-Z])/g, '$1 $2');
      return `${clientName} (${camelCasePart})`;
    }
  
    return name;
  }

  clientChanged(source) {

    if (source && source.ab_test_parent) {
      this.clonedSelectedClientName = this.extractClientName(source.name);
    } else {
      this.clonedSelectedClientName = '';
    }
    
    // this.clonedSelectedClientName = source?.ab_test_parent ? this.extractClientName(source.name) : '' ;

    this.selectedClient = Object.assign({}, source);
    this.matLoader = true;
    this.ecoSystem = false;
    this.searchClientService.getSearchClient(source.id).then((result) => {
      this.matLoader = false;
      this.editMode = this.isMigrated ? false : true;
      this.selectedIndex = source.type == "enableAnalytics" ? 4 : 0; // added to enable analytics via clicking inside analytics
      this.contentTypeHide = this.isMigrated ? false : true;;
      this.channelExists = false;
      this.settings = result;
      Helper.allowSearchClientChildRoutes = true;
      this.settings.client.hidden_facet = JSON.parse(this.settings.client.hidden_facet || '[]');
      this.settings.client.relevancy = (() => {
        try {
          return JSON.stringify(JSON.parse(this.settings.client.relevancy));
        } catch (error) {
          console.error('Error while parsing relevancy. Storing default value. : ', error);
          return DEFAULT_RELEVANCY;
        }
      })();

      this.agentHelperSettings = JSON.parse(JSON.stringify(this.settings));
      this.contentSourceCopy = JSON.parse(JSON.stringify(this.settings.sources));
      this.copyAnalyticsProperties = JSON.parse(JSON.stringify(this.settings));
      this.defaultSorting = JSON.parse(result.client.default_results_sorting);
      var temp = [];
      if (this.settings.sources) {
        for (let i = 0; i < this.settings.sources.length; i++) {
          if (
            this.settings.sources[i].content_source_type_id === 3 ||
            this.settings.sources[i].content_source_type_id === "3"
          ) {
            temp.push(this.settings.sources[i]);
          }
        }
      }
      this.agentHelperSettings.sources = temp;
      this.settings.analyticsSetting = new AnalyticsSetting(
        result.analyticsReports
      );
      this.analyticsSettingsCopy = JSON.parse(JSON.stringify(this.settings.analyticsSetting));
      this.deflectionSettingsCopy = this.settings.deflection_formula;
      this.addFilterPreferences(true, false);
      this.getMergedFilters();
      this.disableMerge = true;
      this.searchTuningService
        .search("", 0, 1, this.settings.client.uid, undefined, undefined, false , undefined ,undefined ,undefined ,undefined ,undefined,undefined , 0)
        .then((response) => {
          
          this.aggregations = response.aggregationsArray && response.aggregationsArray.filter(function (o) {
            return !o.key.includes("_nested") && !o.key.includes("_navigation");
          });
          this.disableMerge = this.aggregations && this.aggregations.length ? false : true;
          this.clientUid = this.settings.client.uid;
        });
      this.caseDeflectionStatus();
      this.getSearchClientLanguages();
      this.changeSearchContentSource = false;
      this.changeAnalyticsProperties = false;
      if (this.isEditingChildSC) {
          this.router.navigate(["dashboard/generate-search-client/Relevancy"]);
      } else if (!this.isMigrated) {
          this.router.navigate(["dashboard/generate-search-client/Configurations"]);
      }
    });
  }

  // fetches languages selected for a search client for validating activation of Neural Search along with multilingual search
  getSearchClientLanguages() {
    if (this.settings && this.settings.client && this.settings.client.uid) {
      this.searchClientService.getSearchClientLanguage(this.settings.client.uid).then((res) => {
        this.settings.client.languagesSelected = [];
        if (res && res.config && res.config.selectedLanguages) {
          this.settings.client.languagesSelected = res.config.selectedLanguages;
        }
      });
    }
  }

  /**
   * get caseDeflection status
   */
  caseDeflectionStatus() {
    if (this.settings && this.settings.client && this.settings.client.uid) {
      this.searchClientService
        .caseDeflectionStatus(this.settings.client.uid, false, "")
        .then((result) => {
          if (result.status == 200 || result.status == "200") {
            this.caseDeflectionEnabled =
              result.caseDeflectionStatus[0].is_case_deflected_shown;
          }
        });
    }
  }
  bringShareAccessSettings(source) {
    this.currentSelectedUserForShare = [];
    let data = { id: source.id, task: "get" };
    this.searchClientService.shareAccessSettings(data).then((result) => {
      if (result.status == 200 || result.status == "200") {
        if (result.data.owner[0] && result.data.owner[0].roleId == 1) {
          result.data.admin.unshift(result.data.owner[0]);
        } else if (result.data.owner[0] && result.data.owner[0].roleId == 2) {
          result.data.moderator.unshift(result.data.owner[0]);
        }
        this.shareModalData = result.data;
        this.shareModalData.name = source.name;
        this.currentSelectedUserForShare = JSON.parse(
          result.data.accessDetails.sharedAccess
        );
        this.backup = JSON.parse(
          JSON.stringify(this.currentSelectedUserForShare)
        );
      }
    });
  }

  closeModal() {
    this.shareModalData = {};
    this.currentSelectedUserForShare = [];
    this.backup = [];
  }
  updateShareSettings(data) {
    if (
      this.adminSession.email != data &&
      this.shareModalData.accessDetails.ownerEmail != data
    ) {
      if (this.currentSelectedUserForShare.includes(data)) {
        this.currentSelectedUserForShare.splice(
          this.currentSelectedUserForShare.indexOf(data),
          1
        );
      } else {
        this.currentSelectedUserForShare.push(data);
      }
    }
  }

  saveShareSettingsForSC(id, scName) {
    this.addArray = this.currentSelectedUserForShare.filter(
      (x) => this.backup.indexOf(x) === -1
    );
    this.removeArray = this.backup.filter(
      (x) => this.currentSelectedUserForShare.indexOf(x) === -1
    );

    let data = {
      id: id,
      task: "updateAray",
      accessArray: this.currentSelectedUserForShare,
    };
    this.searchClientService.shareAccessSettings(data).then((result) => {
      if (result.status == 200 || result.status == "200") {
        this.showToasty(
          "Access Settings Changed!",
          "Search client access updated",
          "success"
        );
      } else {
        this.showToasty("Something went wrong", `${result.data}`, "error");
      }
    });
    /** Track Admin  hit for all newly added users*/
    this.addArray.map((e) => {
      let options = {
        info: `Search Client ${scName} > Share > Access Given to ${e}`,
        object: `Search Client`,
      };
      this.adminAnalyticsService.trackAdminAnalytics(options);
    });
    /** Track Admin  hit for all removed users*/
    this.removeArray.map((e) => {
      let options = {
        info: ` Search Client ${scName} > Share > Access revoked to ${e}`,
        object: `Search Client`,
      };
      this.adminAnalyticsService.trackAdminAnalytics(options);
    });
  }

    addFilterPreferences(opened, close) {
        if (opened) {
            this.searchClientService.getAggregateFilters(this.settings.client.uid).then(result => {
                this.filterPreferenceList = result.length ? result : [];
                this.filterPreferenceList = this.filterPreferenceList.concat(this.filtersArray);

                if(this.settings.client.sourcePriority > this.filterPreferenceList.length)
                    this.filterPreferenceList.push({"label": "Sources","name": "_type"});
                else{
                  this.filterPreferenceList.splice(this.settings.client.sourcePriority-1, 0, {"label": "Sources","name": "_type"});
                } 
                   this.settings.sources.map(u => {
                      if(u.enabled){
                        u.objects.map(v=> {
                          this.filterPreferenceList.map( k => {
                            const fieldIndex =  v.fields.findIndex(f => f.name == k.name);
                            if(fieldIndex != -1){
                              if(v.fields[fieldIndex].use.auto_learning && this.selectedClient.smart_facet){
                                k.disabled = true;
                                const index = this.settings.client.hidden_facet.findIndex(u => u == k.name);
                                if(index != -1){
                                 this.settings.client.hidden_facet.splice(index, 1);
                                }
                             }else if(v.fields[fieldIndex].use.auto_learning && !this.selectedClient.smart_facet){
                               k.disabled = false;
                             }
                            }
                            return k
                          })
                        })
                      }
                      return u
                    })

                    if(this.settings.client.hideAllContentSources == true && this.settings.client.hidden_facet && this.settings.client.hidden_facet.length == 0){
                      this.filterPreferenceList.map((e) => {
                        if(this.settings.client.hideAllContentSources == true){
                          e.checked = true; 
                        }
                        return e 
                     })
                     this.settings.client.hidden_facet  = this.filterPreferenceList.filter((r)=> r.checked ? r.name : '').map(({name}) => name);
                    }
                this.filterPreferenceListCopy = JSON.parse(JSON.stringify(this.filterPreferenceList))
            }); 
        } else {
              if (this.filterPreferenceList && this.filterPreferenceList.length > 0) {
                this.finalHiddenfacet = [];
                this.filterPreferenceList.map((v) => {
                    const index = this.settings.client.hidden_facet.findIndex(
                        (u) => u == v.name
                    );
                    if (index > -1) {
                      const finalIndex =  this.finalHiddenfacet.findIndex( u => u == this.settings.client.hidden_facet[index])
                      if(finalIndex == -1){
                        this.finalHiddenfacet.push(
                            this.settings.client.hidden_facet[index]
                        );
                      }

                    }
                    return v;
                });
                this.settings.client.hidden_facet = this.finalHiddenfacet;
                if(this.settings.client.hidden_facet.length != this.filterPreferenceList.length){
                  this.settings.client.hideAllContentSources = false;
                }else{
                  this.settings.client.hideAllContentSources = true;
                }
            }
            if (this.settings.client.horizontalTabEnabled 
                && this.settings.client.horizontalTabFacet == "_type" 
                && this.filterPreferenceList.findIndex(item => item.label == 'Sources') > -1
                )
                this.filterPreferenceList.splice(
                    this.filterPreferenceList.findIndex(item => item.label == 'Sources') , 1
                )
            
            this.filtersArray.map(x => {
                if (this.filterPreferenceList.findIndex(f => f.name == x.name) == -1)
                    this.filterPreferenceList.push(x);
            })
            this.filterPreferenceList.filter((f, index) => {
                if (f.label == 'Sources') this.settings.client.sourcePriority = index + 1;
                f.priority = index + 1;
                return f;
            })
            if (this.filterPreferenceList.some(item => item.label == 'Sources') && this.settings.client.sourcePriority > -1)
                this.filterPreferenceList.splice(this.settings.client.sourcePriority-1, 1);
            if (close) {
                this.objFilterPreferences.sourcePriority = this.settings.client.sourcePriority;
                let [indicesOrder, objectsOrder] = this.setPreference(JSON.parse(this.settings.client.horizontalTabOrder) || []
                    , JSON.parse(this.settings.client.sourceOrder) || []);
                this.mergedFilters = JSON.parse(this.settings.client.merged_facets || '[]')
                this.createMFFromDB(indicesOrder, '_index');
                this.createMFFromDB(objectsOrder,'_type');
                this.settings.client.merged_facets = JSON.stringify(this.mergedFilters);
                this.settings.client.horizontalTabOrder = JSON.stringify(indicesOrder.map(f => f.name));
                this.settings.client.sourceOrder = JSON.stringify(objectsOrder.map(f => f.name));
                // let contentTag = JSON.parse(this.settings.client.contentTag || "{}");
                let contentTag 
                try{
                  contentTag = JSON.parse(this.settings.client.contentTag)
                } catch(e){
                  contentTag =  {"enabled": true, "type": "index"};
                }
                contentTag.enabled = !contentTag.enabled || contentTag.enabled == "false" ? false : true;
                contentTag.type = contentTag.type || 'index';
                this.objFilterPreferences = {
                    hideAllContentSources: this.settings.client.hideAllContentSources,
                    hidden_facet:         this.settings.client.hidden_facet || [],
                    horizontalTabEnabled: this.settings.client.horizontalTabEnabled,
                    horizontalTabFacet:   this.settings.client.horizontalTabFacet,
                    horizontalTabOrder:   this.settings.client.horizontalTabOrder,
                    sourceSortBy:         this.settings.client.sourceSortBy,
                    sourceOrderBy:        this.settings.client.sourceOrderBy,
                    sourceOrder:          this.settings.client.sourceOrder,
                    sourcePriority:       this.settings.client.sourcePriority,
                    collapsibleSummary :  this.settings.client.collapsibleSummary == true ? 1 : 0,
                    minSummaryLength:     this.settings.client.minSummaryLength,
                    maxSummaryLength :    this.settings.client.maxSummaryLength,
                    contentTag:           JSON.stringify(contentTag) || '',
                    minDocCount:          this.settings.client.minDocCount
                } 

                this.searchClientService.saveFilterPriority(this.filterPreferenceList, this.settings.client.uid, this.deleteFilters, this.activeClientId, this.objFilterPreferences,this.summaryPreferences, this.defaultSorting).then(result => { })
            }       
        }
    }

  getContentSources() {
    this.isLoadingAdded = true;
    this.getMeanVectorProgress();
    let clonnedAbTestSc = true;
    this.contentSourceService.getContentSources(clonnedAbTestSc).then((result) => {
      var contentSources = result.message;
      const inProgressSearchClients = [];
      for (let i = 0; i < contentSources.length; i++) {
        if (contentSources[i].sc_creating_status == 0) {
          inProgressSearchClients.push(contentSources[i]);
          // contentSources[i].intervalId = setInterval(() => {
          //   this.searchClientService
          //     .getSCCreatedStatus(contentSources[i].id, contentSources[i].uid)
          //     .then((result) => {
          //       if (result.status == 200 && result && result.data.sc_creating_status == 0) {
          //         clearInterval(contentSources[i].intervalId);
          //         this.contentSources[i].sc_creating_status = result.data.sc_creating_status;
          //       }
          //     })
          //     .catch((error) => {
          //       clearInterval(contentSources[i].intervalId);
          //       console.error(`Error fetching status for uid: ${contentSources[i].uid}`, error);
          //     });
          // }, 1000);
        }
      }

      if (inProgressSearchClients.length > 0) {
        clearInterval(this.checkStatus);
        this.checkStatus = setInterval(() => {
          for (let i = 0; i < inProgressSearchClients.length; i++) {
            this.searchClientService
              .getSCCreatedStatus(inProgressSearchClients[i].id, inProgressSearchClients[i].uid)
              .then((result) => {
                if (result.status == 200 && result && result.data.sc_creating_status != 1) {
                  this.getContentSources();
                }
              })
              .catch((error) => {
                clearInterval(this.checkStatus);
                console.error(`Error fetching status for uid: ${inProgressSearchClients[i].uid}`, error);
              });
          }
        }, 1000);
      }

      this.contentSources = [];
      this.EcosystemService.getAddedSearchClients().then((result) => {
        this.contentSources = contentSources.filter(client => {
          return !result.find(sc => {
            if (client.id === sc.search_client_id) {
              client.eco_name = sc.eco_name;
            }
          })
        });
      });
      this.isLoadingAdded = false;


      let apiResponse = contentSources;
      // Create a Set of parent UIDs and mark children in a single pass
      const abTestParents = new Set();
      this.childSC = [];
      this.haveDropdown = [];
      const regex = /.*?_([A-Za-z]+)(Search)_/;
      apiResponse.forEach(item => {
        // Extract the search term using the regex
        const match = item.name.match(regex);
        if (match) {
          item.searchTerm = `${match[1]} ${match[2]}`; // Add the extracted term to the item
        } else {
          item.searchTerm = null; // If no match, set as null or handle as needed
        }
        // Process ab_test_parent logic
        if (item.ab_test_parent) {
          this.childSC.push(item);
          abTestParents.add(item.ab_test_parent);
        }
        // Check and mark child status in the same loop
        item.child = abTestParents.has(item.uid);
        this.haveDropdown.push(item.child);
      });

      // Assign scLabel (A, B, C) per unique ab_test_parent
      const labelCounters = {}; // To keep track of the label counters for each ab_test_parent
      this.childSC.forEach(item => {
        const parent = item.ab_test_parent;
        if (!labelCounters[parent]) {
          labelCounters[parent] = 0; // Initialize the counter for this ab_test_parent
        }
        // Assign the label based on the current counter
        const label = String.fromCharCode(65 + labelCounters[parent]);
        item.scLabel = label; // Assign scLabel
        item.scLabelClass = `label-${label}`; // Assign scLabelClass (e.g., label-A, label-B)

        labelCounters[parent]++; // Increment the counter for this ab_test_parent
      });
      this.abTestStatus = this.compareArrays(apiResponse, this.testReportsData);




    });
  }

  compareArrays(A, B) {
    return A.map(aItem => {
      const matchingB = B.find(bItem => bItem.searchClient.uid === aItem.uid && bItem.status != 3 && bItem.status != 2);
      return matchingB ? { uid: aItem.uid, status: matchingB.status } : null;
    });
  }


  isDeleteDisabled(){
    return this.rollbackInProgress;
  }

  async getBackupCount(uid) {
    let backupObj =  {
      errorMessage: "",
      enable: true
    };
    return this.searchClientService.getBackupCount(uid).then((result)=>{
      const fileCount =  result;
      if(fileCount[0]['COUNT(*)'] >= this.fileCount){
        backupObj.errorMessage = "The backup limit for 5 Search Clients has been reached. Please delete any existing backup to migrate a new Search Client.";
        backupObj.enable = false; 
      }
      return backupObj;
    })
  }

  getSearchClientsFromSandbox(){
    this.searchClientService.getSearchClientsFromSandbox().then((result) => {
      this.searchClientsFromSandbox = result.message;
      // this.searchClientsFromSandbox.forEach((item)=>{
      // this.getBackupCount(item.uid).then((res)=>{
      //     console.log("response", res);
      //     this.migrateButton[item.uid] = res;
      //     console.log("migrateButton---", this.migrateButton);
      //   });
      // })
    });
  }

  getBackAndUpdateSC() {
    // this.contentTypeHide = !this.contentTypeHide;
    // this.editMode = !this.editMode;
    this.selectedObject = !this.selectedObject;
    this.currentTabClient = "config";
    this.getContentSources();
  }

  selectPlatformPage() {
    this.selectPlatform = !this.selectPlatform;
    this.newPlatform.customUID = "";
    this.customUIDfield = false;
    this.newPlatform.platformId = "";
    this.newPlatform.name = "";
    this.newPlatform.client_href = "";
    this.newPlatform.recommendations = 0;
    this.newPlatform.recentsearchescount = 0;
    this.newPlatform.rec_widget_regex="",
    this.newPlatform.rec_widget_redirect_url="",
    this.newPlatform.recentSearches = 0;
    this.newPlatform.knowledgeGraph = 0;
    this.newPlatform.autocomplete = 0;
    this.addSearchClient = false;
    this.addingSc = "";
    this.newPlatform.didYouMean = 0;
    this.newPlatform.didYouMeanDictType = 0;
    this.newPlatform.similarSearchRecommendation = 0;
    this.newPlatform.allowSpecialCharSearch = 1;
    this.newPlatform.ViewedResults = 0;
    this.newPlatform.channel_id = null;
    this.uniqueSearchClient = false;
    this.newSCSaveButtonClicked = false;
    this.newPlatform.language = 'react';
    this.newPlatform.relevancy = DEFAULT_RELEVANCY;
    this.newPlatform.smartFacet=0;
    this.newPlatform.minFacetSelection = 0;
    this.newPlatform.ssoConfig = {
      type: 'Google'
    }
  }

  ecoSystemScreen() {
    Helper.allowSearchClientChildRoutes = true;
    this.contentTypeHide = true;
    this.ecoSystem = true;
    this.editMode = false;
    this.userNewFeatureData = {
        type: "set",
        newFeatures: true,
        newFeatureActions: {"ecosystem":true}
    };
    this.userManagementService.userSpecificSettings(this.userNewFeatureData).then(result => {
      if (result && result.data && result.data.newFeatureActions) {
        this.userNewFeatureData = JSON.parse(result.data.newFeatureActions);
      }
    })
    this.newFeatureEcoSystemProperty = true;
    this.router.navigate(["dashboard/generate-search-client/ecosystems"]);
  }

  // While cloning select react by default
  cloneAngularDefault(){
    this.cloneParameters.language = "react";
  }

  /**
     * Check whether the search client is added in the instance
     */
  setAddedSearchClient() {
    setTimeout(() => {
        this.platformTypes.forEach(element => {
            this.contentSources.some(addedSC => addedSC.search_client_type_id == element.id) ? element.installed = true : element.installed = false;
        });
    }, 500);
  }

  getAllSupportedSearchClientCategories() {
    this.allSupportedSearchClientCategories = [
      {type: "Content Management System", id: 1},
      {type: "Community Platforms", id: 2},
      {type: "CRM and Support Systems", id: 3},
      {type: "Internal Platforms", id: 5},
      {type: "Others", id: 4}
    ];
    this.allCategories = [...this.allSupportedSearchClientCategories];
    this.allSupportedSearchClientCategories.map(v => ({ ...v, showCategory: true }))
    this.allCategories.unshift({ type: 'All', id: 0 }); //Add All as a new category
  }

  getPlatformTypes(): void {
    this.searchClientService.getPlatformTypes().then((result) => {
      const allPlatformTypes = result.map(data=>{
        if(this.drupalPlatforms.includes(data.name)){
          return {...data, group:'Drupal',showDrupal: data.name === 'Drupal 7'}
        }
        return data;
      })
      this.platformTypes = allPlatformTypes;
    });
  }

  toggleAddClient(id, name) {
    this.addSearchClient = !this.addSearchClient;
    if(name != 'Drupal'  ){
      this.isDrupalSc = false;
      this.newPlatform.platformId = id;
      this.addingSc = name;
    }else{
      this.isDrupalSc = true;
      this.selectedDrupalVersion = '';
    }
    
    if(name != 'Khoros'){
      this.isKhoros =  false
      this.newPlatform.platformId = id;
      this.addingSc = name;
    }else{
       this.isKhoros = true;
       this.selectedKhorosVersion = ''
    }
    
  }

  drupalSelectionChange(e){
    const { value } = e;
    this.addingSc = value;

    if(value === 'Drupal 7'){
      this.newPlatform.platformId = 15;
    }
    else if( value === 'Drupal 10'){
      this.newPlatform.platformId = 17;
    }
  }
  khorosSelectionChange(e){
    const { value } = e;
    this.addingSc = value;

    if(value === 'Khoros'){
      this.newPlatform.platformId = 2;
    }
    else if( value === 'Aurora'){
      this.newPlatform.platformId = 33;
    }
  }

  serveScCloudfront(uuid){
      this.searchClientService.previewPath(uuid, true).then(result => {
            if(result.status == 200){// Open the URL in a new tab
                this.ScCloudfrontUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(result.data);
                window.open(result.data, '_blank');
            }else{
                this.toastyService.error({
                    title: `Something went wrong`,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default',
                    msg: `Unable to fetch CDN URL for SC`
                })
            }
        }).catch((e)=>{
            this.toastyService.error({
                title: `Something went wrong`,
                showClose: true,
                timeout: 3000,
                theme: 'default',
                msg: `Error`
            })
      })
  }

  savePlatform() {
    this.newSCSaveButtonClicked = true;
    this.searchClientService.savePlatform(this.newPlatform).then((result) => {
      if (result.message == "Done") {
        this.getContentSources();
        this.selectPlatformPage();
        this.showToasty(
          "Platform saved!",
          "Search client platform saved successfully",
          "success"
        );
      } else {
        this.newSCSaveButtonClicked = false;
        this.showToasty("Cannot save platform", `${result.message}`, "error");
      }
    });
  }

  deleteSearchClient(searchClient, deleteParentABTest?) {
    let confirmed = !deleteParentABTest ? confirm(`Are you sure you want to delete ${searchClient.name} ?`) : '';
    if (confirmed || deleteParentABTest) {
      this.isLoading_small = true;
      this.isLoadingAdded=true;
      this.searchClientService
        .deleteSearchClient(searchClient)
        .then((result) => {
          if (result.flag == 401) {
            this.showToasty(
              "Unauthorized",
              `Search client not deleted`,
              "error"
            );
          } else if (result.flag == 200) {
            this.isLoading_small = false;
            this.isLoadingAdded=false;
            this.indexChanged("3", searchClient.name, searchClient.uid);
            this.showToasty(
              "Search Client deleted!",
              "Search Client deleted successfully",
              "success"
            );
            this.getContentSources();
          }
        });
    }
  }

  downloadSearchClient(searchClient) {
    this.searchClientService.downloadSearchClient(searchClient.id);
  }


  private isRequestFromSearchUnifyGPTPopup: any;

  callGetSearchClientForFeedback(source) {
    this.searchClientService.getSearchClient(source.id).then((result) => {
    });

  }

  updateSearchClient() {
    /**
     * Update three tables search_clients,
     * search_client_to_content_objects
     * search_client_filter
     */
    // 1. this.settings.client => search_client As it is
    // 2. Delete entries for this.settings.client.id and New Entries for search_clients_to
    this.isLoadingAdded = true;
    
    if (this.settings.deflection_formula.external_user_enabled) {
      if (
        this.settings.deflection_formula.account_name &&
        this.settings.deflection_formula.account_name.length &&
        !/^(\w[a-zA-Z0-9\.]*){1}(\,\w[a-zA-Z0-9\.]+)*$/.test(
          this.settings.deflection_formula.account_name
        )
      ) {
        this.showToasty("Account Validation failed", `Error`, "error");
        return;
      }
      if (!this.settings.deflection_formula.account_name) {
        this.showToasty(
          "Account Validation failed",
          `Please enter a valid domain name`,
          "error"
        );
        return;
      }
    }
    if (this.selectedObject && this.selectedObject.fields)
      this.toggleSummaryPreference(false);

    let mapping_objects = [];
    if (this.filterPreferenceList) {
      this.filtersArray.map((v,i) => {
       const index = this.filterPreferenceList.findIndex(u => u.name == v.name);
       if(index == -1){
        this.filterPreferenceList.push(this.filtersArray[i])
       }
       return v;
      })
      // this.filterPreferenceList = this.filterPreferenceList.concat(
      //   this.filtersArray
      // );
      this.addFilterPreferences(false, true);
    }
    /* admin logs */ 
    this.copyAnalyticsPropertiesNew = this.settings;
    this.copyAnalyticsProperties.deflection_formula.account_name = this.copyAnalyticsProperties.deflection_formula.account_name == '' ? null : this.copyAnalyticsProperties.deflection_formula.account_name;
    this.copyAnalyticsPropertiesNew.deflection_formula.account_name = this.copyAnalyticsPropertiesNew.deflection_formula.account_name == '' ? null : this.copyAnalyticsPropertiesNew.deflection_formula.account_name;
    this.contentSourceCopyNew = this.settings.sources;
    
    var deflection_formula_includesArray = [
        "viewed_results",
        "email_tracking_enabled",
        "stage1",
        "cumulative",
        "support_url",
        "user_entitlements",
        "session_idle_timeout",
        "account_name",
        "external_user_enabled"
    ];
    if(this.adminAnalyticsService.compareJsonAdminLogs(this.copyAnalyticsPropertiesNew.deflection_formula,this.copyAnalyticsProperties.deflection_formula,deflection_formula_includesArray,[], 'analyticsProperties')){
        this.changeAnalyticsProperties = true;
    }
    if (!this.copyAnalyticsProperties.deflection_formula.directly_viewed_results != !this.copyAnalyticsPropertiesNew.deflection_formula.directly_viewed_results) {
        this.changeAnalyticsProperties = true;
    }
    
    var clientInclude_Array=[
        "mergeSources","SCsalesforceConsoleConfigurations","kcsEnabled"
    ]
    if(this.adminAnalyticsService.compareJsonAdminLogs(this.copyAnalyticsPropertiesNew.client,this.copyAnalyticsProperties.client,clientInclude_Array,[], 'searchClient')){
        this.changeSearchContentSource = true;
    }
    var content_source_includeArray = [
    "use_as","use_value","exclude","search_priority","autosuggestField",
    "base_href","formula", "title_field_id","permission_by_pass","icon","enabled","merge_results", "objects", "fields", "use", "metadata"];

    if(this.adminAnalyticsService.compareJsonAdminLogs(this.contentSourceCopyNew,this.contentSourceCopy,content_source_includeArray,[], 'contentSource')){
        this.changeSearchContentSource = true;
    }

    this.settings.sources.forEach((s) => {
      mapping_objects = mapping_objects.concat(
        s.objects
          .filter((o) => o.enabled)
          .map((o) => {
            let a = this.settings.enabledObjects.find((m) => {
              return (
                m.search_client_id == this.settings.client.id &&
                m.content_source_object_id == o.id
              );
            });
            if (!a) {
              a = {
                search_client_id: this.settings.client.id,
                content_source_object_id: o.id,
              };
            }

            a.base_href = o.base_href;
            a.compose_title = o.compose_title;
            a.title_field_id = o.title_field_id;
            a.formula = o.formula;
            a.icon = o.icon;
            a.permission_by_pass = o.permission_by_pass;
            a.merge_results = o.merge_results;
            a.preview_type = o.preview_type;
            let composeTitleValueIfEmpty;
            a.mapping = o.fields
              .filter((f) => f.use.use_as)
              .map((f) => {
                var temp = f.use;
                temp.content_source_object_id = f.content_source_object_id;
                temp.content_source_object_field_id = f.id;
                if (a.title_field_id && (!a.compose_title || a.compose_title.length === 0) && f.id === a.title_field_id) {
                  composeTitleValueIfEmpty = "{{" + f.name + "}}";
                }
                temp.priority = null;
                if (
                  (f.use.use_as === "Tag" ||
                    f.use.use_as === "Filter" ||
                    f.use.use_as === "SearchFilter" ||
                    f.use.use_as === "SummaryFilter") &&
                  this.filterPreferenceList &&
                  this.filterPreferenceList.length
                )
                  temp.priority = this.filterPreferenceList.find(
                    (x) => x.name === f.name
                  ).priority;
                  // temp.extra_field = f.use.extra_field ? 1 : 0;
                  // temp.track_analytics = f.use.track_analytics ? 1 : 0;
                  // temp.use_as  = temp.use_as == 'Extra' 
                  //   ? temp.use_as : 
                  //     temp.use_as == '' && (temp.extra_field || temp.track_analytics)
                  //     ? 'Extra' : temp.use_as.replace('Extra','');
                return temp;
              });
            if (composeTitleValueIfEmpty) {
                a.compose_title = composeTitleValueIfEmpty;
            }
            a.metadata_mapping = o.fields
              .filter(
                (f) => f.metadata.use_as || f.metadata.autosuggestField == 1
              )
              .map((f) => {
                var temp = f.metadata;
                temp.content_source_object_field_id = f.id;
                temp.autosuggestField = f.metadata.autosuggestField || 0;
                if (f.metadata.use_as == "Metadata") {
                  temp.metaData = 1;
                } else temp.metaData = 0;
                return temp;
              });

            a.preview_mapping = o.fields
              .filter(
                (f) => f.preview.preview_order
              )
              .map((f) => {
                var temp = f.preview;
                temp.content_source_object_field_id = f.id;
                temp.search_client_to_content_object_id = o.id;
                temp.sc_id = this.settings.client.id;

                return temp;
              });
            if ( !a.mapping || !a.mapping.length ) {
              this.changeSearchContentSource = false;
              this.showToasty(
                "Content types cannot be saved!",
                "Please add fields to add content types",
                "warning"
              );
            } else {
              return a;
            }
          })
      );
    });

    /* admin logs */ 
    
    let mergedFacetsNew= JSON.parse(this.copyAnalyticsPropertiesNew.client.merged_facets || '[]');
    let mergedFacetsOld= JSON.parse(this.copyAnalyticsProperties.client.merged_facets || '[]');
    if (mergedFacetsNew.length != mergedFacetsOld.length 
      || this.adminAnalyticsService.compareJsonAdminLogs(mergedFacetsNew, mergedFacetsOld, [], [], 'mergeFacets', true) ) {
      if (mergedFacetsNew.length && !mergedFacetsOld.length )
        this.indexChanged(19, this.settings.client.name, this.settings.client.uid);
      else if (!mergedFacetsNew.length && mergedFacetsOld.length )
        this.indexChanged(21, this.settings.client.name,this.settings.client.uid);
      else 
        this.indexChanged(20, this.settings.client.name,this.settings.client.uid);
      this.changeSearchContentSource = true;
    }
    
    var availableReports_includeArray =[];
    if(this.adminAnalyticsService.compareJsonAdminLogs(this.settings.analyticsSetting.availableReports,this.analyticsSettingsCopy.availableReports,availableReports_includeArray,[], 'analyticsReports')){
        this.indexChanged(11, this.settings.client.name,this.settings.client.uid);
    }
    if (JSON.stringify(this.deflectionSettingsCopy) != JSON.stringify(this.settings.deflection_formula)){
        this.indexChanged(15, this.settings.client.name,this.settings.client.uid);
    }
        
    if (this.changeSearchContentSource) {
        this.indexChanged(16, this.settings.client.name,this.settings.client.uid);
        this.changeSearchContentSource = false;
    }
    if (this.changeAnalyticsProperties) {
        this.indexChanged(17, this.settings.client.name,this.settings.client.uid);
        this.changeAnalyticsProperties = false;
    }

    delete this.settings.client.is_case_deflected_shown;
    this.settings.client.mergeSources = this.setMergeSources();


    this.isRequestFromSearchUnifyGPTPopup = JSON.parse(localStorage.getItem('requetFromSearchUnifyGPTPopup'));
    
    if(this.isRequestFromSearchUnifyGPTPopup) {
      this.settings.analyticsSetting.availableReports.forEach(item => {
          if (item.analytics_report_id === 65) {
              item.is_enabled = JSON.parse(localStorage.getItem('checkFeedbackReport'));
          }
      });
    }

    if(this.settings.client && this.settings.client.relevancy) {
      let parsedRelevancy = JSON.parse(this.settings.client.relevancy);
      parsedRelevancy.defaultAttachments = parseInt(this.settings.client.defaultAttachments || DEFAULT_ATTACHMENTS);

      this.settings.client.relevancy = JSON.stringify(parsedRelevancy);
    }

    delete this.settings.client.languagesSelected;
    delete this.settings.client.defaultAttachments;

    this.searchClientService
      .updateSearchClient({
        client: this.settings.client,
        mapping_objects: mapping_objects,
        css: this.settings.css,
        deflection_formula: this.settings.deflection_formula,
        analyticsReports: this.settings.analyticsSetting.availableReports,
        updatedContentSource: this.updatedContentSource,
        isMigration : this.isMigrated,
        sandbox_uid : this.migrateParams.uid
      })
      .then((result) => {
        if(!this.isRequestFromSearchUnifyGPTPopup) {
          this.editMode = false;
          this.contentTypeHide = false;
          this.summaryPreferences = [];
          this.filterPreferenceList = [];
        } else {
          this.callGetSearchClientForFeedback(this.selectedClient);
        }
        this.getBackAndUpdateSC();
        if (result.flag == 401) {
          this.isMigrated && this.setMigrationStep('step2', 'Failed');
          this.showToasty(
            "Unauthorized",
            "Editing Search client Access denied",
            "error"
          );
        } else {
          this.isMigrated && this.setMigrationStep('step2', 'Completed');
          this.showToasty(
            "Platform saved!",
            "Search client platform saved successfully",
            "success"
          );
        }
        localStorage.setItem('requetFromSearchUnifyGPTPopup', "false");
      });

    this.updateShareOption();
  }

  updateShareOption() {
    let contentSourceArray = [];
    for (let i = 0; i < this.settings.sources.length; i++) {
      if (this.settings.sources[i].enabled == true) {
        let temp = [],
          flag = false;
        for (let j = 0; j < this.settings.sources[i].objects.length; j++) {
          if (this.settings.sources[i].objects[j].enabled == true) {
            temp.push(this.settings.sources[i].objects[j]);
            flag = true;
          }
        }
        if (flag) {
          contentSourceArray.push({
            contentSourceId: this.settings.sources[i].id,
            contentSourceLabel: this.settings.sources[i].label,
            objects: temp,
          });
        } else {
            this.searchClientService.editClient(this.selectedClient).then(result => {
                if (result.flag == 401) {
                    this.showToasty("Unauthorized", "Editing Search client Access denied", "error");
                } else if (result.flag == 200) {
                    // this.getContentSources();
                } else {
                    this.showToasty("Cannot save platform", `${result.message}`, "error");
                }
                // GET SC data after we update configuration ... to get Last updated user
                this.getContentSources();
            });
        }
      }
    }
    this.searchClientService
      .updateShareResults(this.settings.client.id, contentSourceArray)
      .then((res) => {
        if (res.result == "Success") {
        }
      });
  }

  toggleEditMode() {
    this.contentTypeHide = !this.contentTypeHide;
    this.editMode = !this.editMode;
    this.selectedObject = !this.selectedObject;
    this.currentTabClient = "config";
    // this.getContentSources();
  }

  editPlatform() {
    this.selectedClient.id = this.settings.client.id;
    this.selectedClient.search_client_type_id = this.settings.client.search_client_type_id;
    this.selectedClient.name = this.settings.client.name;
    this.selectedClient.client_href = this.settings.client.client_href;
    this.selectedClient.jwt_href = this.settings.client.jwt_href;
    this.selectedClient.jwt_expiry = this.settings.client.jwt_expiry;
    this.selectedClient.template_type_id = this.settings.client.template_type_id
      ? this.settings.client.template_type_id
      : 1;
    this.selectedClient.custom_css = this.settings.client.custom_css;
    this.selectedClient.uid = this.settings.client.uid;
    this.selectedClient.recommendations = this.settings.client.recommendations;
    this.selectedClient.rec_widget_regex=this.settings.client.rec_widget_regex;
    this.selectedClient.rec_widget_redirect_url=this.settings.client.rec_widget_redirect_url;
    this.selectedClient.recentsearchescount = this.settings.client
      .recent_searches_count
      ? this.settings.client.recent_searches_count
      : 0;
    this.selectedClient.recentSearches = this.settings.client.recent_searches;
    this.selectedClient.preview = this.settings.client.preview;
    this.selectedClient.knowledgeGraph = this.settings.client.knowledge_graph;
    this.selectedClient.autocomplete = this.settings.client.autocomplete;
    this.selectedClient.didYouMean = this.settings.client.did_you_mean;
    this.selectedClient.didYouMeanDictType = this.settings.client.did_you_mean_dict_type;
    this.selectedClient.similarSearchRecommendation = this.settings.client.similar_search;
    this.selectedClient.featuredSnippet = this.settings.client.featured_snippet;
    this.selectedClient.featuredSnippetMediaDisabled = this.settings.client.featured_snippet_media_disabled;
    this.selectedClient.gptToggle= this.settings.client.gpt;
    this.selectedClient.summarize = this.settings.client.summarize;
    this.selectedClient.allowSpecialCharSearch = this.settings.client.special_search;
    this.selectedClient.channel_id = this.settings.client.channel_id;
    this.selectedClient.custom_js = this.settings.client.js;
    this.selectedClient.js = this.settings.client.js;
    this.selectedClient.searchboxjs = this.settings.client.searchboxjs;
    this.selectedClient.autocomplete_instant =
      this.settings.client.autocomplete_instant || 0;
    this.selectedClient.pagination =
      this.settings.client.pagination || "page_no_button";
    this.selectedClient.language = this.settings.client.language;
    this.selectedClient.advertisements =
      this.settings.client.advertisements || 0;
    this.selectedClient.redirection_url =
      this.settings.client.redirection_url || "";
    this.selectedClient.languageManager =
      this.settings.client.languageManager || 0;
    this.selectedClient.SCsalesforceConsoleConfigurations = this.settings.client.SCsalesforceConsoleConfigurations;
    this.selectedClient.ViewedResults = this.settings.client.ViewedResults || 0;
    this.selectedClient.mergeSources = this.setMergeSources();
    this.selectedClient.smartFacet = this.settings.client.smart_facet;
    this.selectedClient.minFacetSelection = this.settings.client.min_facet_selection;
    this.selectedClient.relevancy  = this.settings.client.relevancy;
    this.selectedClient.rec_widget = this.settings.client.rec_widget;
    this.selectedClient.gpt_feedback = this.settings.client.gpt_feedback;
    this.selectedClient.neuralSearchEnabledInCurrentSession = this.settings.neuralSearchEnabledInCurrentSession;
    this.selectedClient.enableGPTConfigurations = this.settings.client.enable_additional_configurations;
    this.selectedClient.scToggles = this.settings.client.scToggles;
    this.selectedClient.gptRules = this.settings.client.gptRules;
    this.selectedClient.default_results_sorting = this.settings.client.default_results_sorting;
    this.selectedClient.defaultAttachments = parseInt(this.settings.client.defaultAttachments);
    this.selectedClient.highlight = this.settings.client.highlight;
    this.selectedClient.domainPermission = this.settings.client.domainPermission;
    this.selectedClient.sc_creating_status = this.settings.client.sc_creating_status;
    this.selectedClient.attachment_preview = this.settings.client.attachment_preview || 0;
    this.selectedClient.isDisabled = this.settings.client.isDisabled;
    // console.log('generate sc ------>', this.selectedClient.domainPermission);
    
    if (this.channelExists) {
      this.showToasty(
        "Warning !!",
        "Slack channel id  already exists.",
        "warning"
      );
    } else {
      this.searchClientService
        .editClient(this.selectedClient)
        .then((result) => {
          if (result.flag == 401) {
            this.showToasty(
              "Unauthorized",
              "Editing Search client Access denied",
              "error"
            );
          } else if (result.flag == 200) {
            // this.getContentSources();
          } else {
            this.showToasty(
              "Cannot save platform",
              `${result.message}`,
              "error"
            );
          }
        });
    }
  }

  cloneSearchClient() {
    if(this.cloneParameters.search_client_type_id == 26||this.cloneParameters.search_client_type_id == 28){
        this.cloneParameters.language = 'react';
    }
    this.cloneButtonClicked = true;
    if (this.cloneParameters.defaultType != this.cloneParameters.search_client_type_id) {
      this.cloneParameters.restoreSettings = 1;
      this.cloneParameters.restoreUid = 0;
    }
    this.searchClientService.cloneSearchClient(this.cloneParameters).then((result) => {
      this.cloneButtonClicked = false;
      if (result.flag == 401) {
        this.showToasty("Unauthorized!", `Search client not cloned`, "error");
      } else if (result.flag == 200) {
        this.showToasty(
          "Cloned successfully",
          "Search client cloned successfully",
          "success"
        );
        this.cloneParameters.name = "";
        this.cloneParameters.search_client_type_id = "";
        this.getContentSources();
      } else {
        this.showToasty(
          "Cloning failed",
          `Search client cloning failed`,
          "error"
        );
      }
      this.allowClone = !this.allowClone;
      if (this.allowClone) {
        this.addClassToBody();
      } else {
        this.removeClassToBody();
      }
    });
  }

  toggleCloneClient(source) {
    this.cloneParameters.id = source.id;
    this.cloneParameters.uid = source.uid;
    this.cloneParameters.defaultType = source.search_client_type_id;
    this.cloneParameters.restoreSettings = 1;
    this.cloneParameters.restoreUid = 0;
    this.allowClone = !this.allowClone;
    this.cloneParameters.language = "react";
    if (this.allowClone) {
      this.addClassToBody();
    } else {
      this.removeClassToBody();
    }
  }

  resetSearchClient() {
    this.cloneParameters.resetSCOption = this.resetSCOption;
    if (this.cloneParameters.resetSCOption.value == '2' && (this.cloneParameters.resetSCOption.sourceUid == '' || this.isUIDvalid(this.cloneParameters.resetSCOption.sourceUid))) {
      return this.showToasty(
        "Select a search client",
        "",
        "warning"
      );
    }
    this.searchClientService.cloneSearchClient(this.cloneParameters).then((result) => {
      this.cloneButtonClicked = false;
      if (result.flag == 401) {
        this.showToasty("Unauthorized!", `Search client not cloned`, "error");
      } else if (result.flag == 200) {
        this.showToasty(
          "Reset successfully",
          "Search client resetted successfully",
          "success"
        );
        this.getContentSources();
        this.resetSCOption = {
          value: '1',
          sourceUid: ''
        }
      } else {
        this.showToasty(
          "Reset failed",
          `Search client reset failed`,
          "error"
        );
      }
      this.allowReset = !this.allowReset;
      this.cloneParameters.name = "";
      this.cloneParameters.uid = "";
      this.cloneParameters.search_client_type_id = "";
      this.cloneParameters.defaultType = "";
      this.cloneParameters.restoreSettings = 0;
      this.cloneParameters.restoreUid = 0;
      if (this.allowReset) {
        this.addClassToBody();
      } else {
        this.removeClassToBody();
      }
    });
  }

  ResetScCode(source) {
    this.cloneParameters.id = source.id;
    this.cloneParameters.name = source.name;
    this.cloneParameters.uid = source.uid;
    this.cloneParameters.search_client_type_id = source.search_client_type_id;
    this.cloneParameters.defaultType = source.search_client_type_id;
    this.cloneParameters.restoreSettings = 1;
    this.cloneParameters.restoreUid = 1;
    this.allowReset = !this.allowReset;
    this.cloneParameters.language = "react";
    if (this.allowReset) {
      this.addClassToBody();
    } else {
      this.removeClassToBody();
    }
  }

  getInstanceInfo() {
    this.contentSourceService.getInstanceInfo().then(result => {
      this.isProdInstance = result.data.isProd;
      this.instanceType = result.data.instanceType;
    });
  }

  initiatePlatformMigration(source){
    this.isMigrationSend = '';
    this.initiateMigration = true;
    this.selectedForMigration = source;
  }

  getTooltipText(file: any): string {
    const createdAt = file.name.split('__')[0]; // Extract the timestamp part
    const formattedDate = `${createdAt.substring(0, 4)}-${createdAt.substring(4, 6)}-${createdAt.substring(6, 8)} ${createdAt.substring(8, 10)}:${createdAt.substring(10, 12)}:${createdAt.substring(12, 14)}`;
    
    return `File Name: ${file.name} \n Created At: ${formattedDate}`;
  }

  isMigrationButtonDisabled(migrateParams, enableMigrateButton, newPlatform, migrationInProgress, uniqueSearchClient): boolean{
    return(
      !migrateParams.search_client_type_id 
      || (migrateParams.oldNew === 'migrate-existing' && (!migrateParams.prodUid || !migrateParams.uid )) 
      || (migrateParams.oldNew === 'migrate-new' && (!uniqueSearchClient || !newPlatform.client_href )) 
      || (migrateParams.oldNew === 'migrate-existing' && (!migrateParams.backupFileName))
      || (migrateParams.oldNew === 'migrate-new' && (!migrateParams.name))
      || migrationInProgress 
      || !enableMigrateButton)
  }


  rollbackFile(){
    this.rollbackInProgress = true;
    this.searchClientService.rollbackFile(this.backupIds.prodUid, this.rollbackRadioBtn, this.backupIds.prodSCId).then(result =>{
      this.rollbackInProgress = false;
      if (result.flag == 401) {
        this.enableRollback = false;
        this.showToasty("Unauthorized!", `Rollback Failed`, "error");
      }
      if(result.flag == 200){
        this.searchClientService.getBackupFiles(this.backupIds.prodUid).then(result=>{
          if(result.flag == 200){
            this.enableRollback = false;
            this.backupFilesList = result.data;
              this.showToasty(
                "Success!",
                `Files Rollbacked Successfully!`,
                "success"
              );
            }
        })
      }else{
        this.enableRollback = false;
        this.showToasty(
          "Error!",
          `Files Rollback Unsuccessful!`,
          "error"
        );
      }
    })
  }

  getBackupFiles(uid: string, prodSCId: any){
    // this.prodUid = uid;
    this.backupIds = {
      prodUid: uid,
      prodSCId: prodSCId
    }
    this.searchClientService.getBackupFiles(uid).then(result =>{
      if(result.flag == 200){
        this.loadingBackupFiles = !this.loadingBackupFiles;    
        this.backupFilesList = result.data;
      }
    })
  }

  showRollbackScreen(uid, prodSCId){
    this.enableRollback = !this.enableRollback;
    this.loadingBackupFiles = true
    this.getBackupFiles(uid, prodSCId);
  }

  cancelRollback(){
    this.enableRollback = !this.enableRollback;
    this.rollbackRadioBtn = "";
    this.rollbackInProgress = false;
  }

  deleteBackup(ind: number, uid: string, name: string){
    this.searchClientService.deleteBackup(uid, name).then((result)=>{
      this.rollbackRadioBtn = "";
      if(result.flag === 200){
        this.backupFilesList.splice(ind, 1);
        this.showToasty(
          "Success!",
          "Backup File Deleted Successfully!",
          "success"
        );
      }else{
        this.showToasty(
          "Error",
          `Backup File Cannot be Deleted`,
          "error"
        );
      }
    })
  }

  sendMigrationRequestToProduction(){
    this.isMigrationSend = 'In progress';
    this.searchClientService.sendMigrationRequestToProd(this.selectedForMigration, this.isProdInstance).then((result) => {
      if (result.flag == 200) {
        this.showToasty(
          "Migration Request Sent!",
          "Successfully",
          "success"
        );
        this.isMigrationSend = 'Completed';
      } else {
        this.showToasty("Cannot Sent Migration Request", `${result.message}`, "error");
        this.isMigrationSend = 'Failed';
      }
    })
    .catch(()=>{
      this.isMigrationSend = 'Failed';
      this.showToasty("Cannot Sent Migration Request", `Cannot Sent Migration Request`, "error");
    });
  }

  deleteMigrationRequest(uid){
    this.searchClientService.deleteMigrationRequest(uid).then((result)=>{
      if (result.flag == 200) {
        this.showToasty(
          "Discarded!",
          "Search client migration request discarded successfully",
          "success"
        );
        this.getSearchClientsFromSandbox();
      }
      else{
        this.showToasty("Failed !", `Cannot Delete Migration Request`, "error");
      }
    })
    .catch((err)=>{
      this.showToasty("Failed !", `Cannot Delete Migration Request`, "error");
    })
  }

  openMigrationOverlay(){
    this.migrateParams.restoreSettings = 1;
    this.migrateParams.restoreUid = 0;
    this.isMigrated = true;
    this.migrationInProgress = false;
    this.isReprocessMigration = false;
    this.migrateParams.oldNew = "migrate-new";
    this.migrateParams.prodUid = "";
    this.addClassToBody();
  }

  selectSearchClientForMigration(source){
    this.resetMigrationSteps();
    this.migrateParams.id = source.id;
    this.migrateParams.uid = source.uid;
    this.migrateParams.search_client_type_id = source.search_client_type_id;
    this.migrateParams.language = source.language;
    this.migrateParams.name = source.name;
    this.migrateParams.restoreSettings = 0;
    this.migrateParams.restoreUid = 0;
    this.migrateParams.prodSCId = source.id;
  }

  selectExistingSearchClient(source){
    this.currentMigrationSource = source;
    this.migrateParams.prodUid = source.uid;
    this.migrateParams.prodSCId = source.id;
    this.resetMigrationSteps();
    this.activeClientId = source.id;
    this.clientChanged(source);
    // this.contentSources.forEach((item)=>{
      this.getBackupCount(source.uid).then((res)=>{
          this.migrateButton[source.uid] = res;
          this.enableMigrateButton = res.enable;
          this.backupErrorMessage = res.errorMessage;
      });
    // })

  }

  reprocessMigration(){
    this.isReprocessMigration = true;
    this.migrationInProgress = true;
    this.migrateParams.prodUid = this.currentMigrationSource.uid;
    this.resetMigrationSteps();
    this.activeClientId = this.currentMigrationSource.id;
    this.migrateExistingSearchClientToProd();
  }

  closeMigrationOverlay(){
    this.isMigrated = false;
    this.newPlatform.name = "";
    this.newPlatform.client_href = "";
    this.migrateParams.id = "";
    this.migrateParams.uid = "";
    this.migrateParams.search_client_type_id = "";
    this.migrateParams.language = "";
    this.migrateParams.name = "";
    this.migrateParams.prodUid = "";
    this.migrateParams.backupFileName ="";
    this.settings = null;
    this.resetMigrationSteps(true);
    this.removeClassToBody();
    this.backupErrorMessage = '';
    this.enableMigrateButton = true;
  }
  
  migrateNewSearchClientToProd() {
    this.migrationInProgress = true;
    const payload = {
      name : this.newPlatform.name,
      client_href : this.newPlatform.client_href,
      platformId : this.migrateParams.search_client_type_id,
      language : this.migrateParams.language,
      relevancy:'{"searchOperator": "OR","advanceQuery":[{"id":0,"setName":"master","querySnippetName":"multi_match","fields":["copy_fields","exclude_copy_fields", "include_title_fields"]},{"id":1,"setName":"default","querySnippetName":"multi_match","type":"phrase","boost":3,"fields":["exclude_copy_fields"]}], "relevancyScores": { \"enabled\": 1, \"dataRetention\": \"\", \"searchType\": 1}}',
      customUID: "",
      recommendations: 0,
      rec_widget_regex:"",
      rec_widget_redirect_url:"",
      recentsearchescount: 0,
      recentSearches: 0,
      knowledgeGraph: 0,
      autocomplete: 0,
      channel_id: null,
      didYouMean: 0,
      didYouMeanDictType: 0,
      similarSearchRecommendation: 0,
      allowSpecialCharSearch: 1,
      ViewedResults: 0,
      email: "",
      smartFacet:0,
      minFacetSelection:0,
      gpt: '{\"name\": \"\", \"active\": false, \"modelType\": \"\", \"contextLimit\": 2000}',
      gpt_feedback: 0,
      highlight:'{"type": "unified", "order": "score", "fragmentSize": 250, "numberOfFragments": 5}',
      default_results_sorting:'{"sortPreference":{"default":"_score","keyLabelMapping":{"click":"Popularity","_score":"Relevance","post_time":"Created Date"}}}',
      ssoConfig: {
        type: 'Google'
      },
      domainPermission: '{\"enabled\": false, \"default\": true, \"customDomains\": []}'
    }
    this.currentMigrationSource = null;
    this.searchClientService.savePlatform(payload).then((status) => {
      if (status.message == "Done") {
        this.setMigrationStep('step0', 'Completed');
        this.contentSourceService.getContentSources().then((result) => {
          this.contentSources = result.message;
          const [ source ] = this.contentSources.filter( s => s.uid === status.uid);
          this.currentMigrationSource = source;
          this.migrateParams.prodUid = source.uid;
          this.migrateParams.prodSCId = source.id;
            setTimeout(()=>{
              this.migrateExistingSearchClientToProd();
            },10000);
        });
        this.showToasty(
          "Platform saved!",
          "Search client platform saved successfully",
          "success"
        );
      } else {
        this.setMigrationStep('step0', 'Failed');
        this.setMigrationStep('step1', 'Failed');
        this.setMigrationStep('step2', 'Failed');
        this.showToasty("Cannot save platform", `${status.message}`, "error");
      }
    });
  }

  migrationToggle(){
    this.settings = null;
    this.resetMigrationSteps();
    this.migrateParams.prodUid = "";
  }

  checkEntriesInSboxProdMappingTable(){
    this.enableSboxProdFeature = true;

    // this.enableSboxProdFeature = false;
    // this.searchClientService.checkEntriesInSboxProdMappingTable().then(result=>{
    //   if (result[0]) {
    //     const keys = Object.keys(result[0]);
    //     if(keys.length === 1 && result[0][keys[0]] > 0){
    //       this.enableSboxProdFeature = true;
    //     }
    //   }
    // })
  }

  setMigrationStep(step, value){
    if(value === 'Failed'){
      this.migrationInProgress = false;
    }
    switch (step) {
      case 'step0':
        this.migrationStep0 = value;
        if(value === 'Completed'){
          this.uniqueSearchClient = false;
        }
        break;
      case 'step1':
        this.migrationStep1 = value;
        if(this.migrateParams.oldNew === 'migrate-new'){
          this.migrationInProgress = false;
          if(value === 'Completed'){
            this.getSearchClientsFromSandbox();
          }
        }
        break;
      case 'step2':
        this.migrationStep2 = value;
        this.migrationInProgress = false;
        if(value === 'Completed'){
          this.migrateParams.id = '';
          this.migrateParams.uid = '';
          this.migrateParams.language = '';
          this.migrateParams.name = '';
          this.getSearchClientsFromSandbox();
        }
        break;
      default:
        break;
    }
  }

  resetMigrationSteps(resetAll?){
    (!this.isReprocessMigration || resetAll) && this.setMigrationStep('step0','');
    this.setMigrationStep('step1','');
    this.setMigrationStep('step2','');
  }

  migrateExistingSearchClientToProd(){
    this.migrationInProgress = true;
    this.searchClientService.migrateExistingToProduction(this.migrateParams, this.isProdInstance).then((result) => {
      if (result.flag == 401) {
        this.setMigrationStep('step1', 'Failed');
        this.setMigrationStep('step2', 'Failed');
        this.showToasty("Unauthorized!", `Search client not migrated`, "error");
      } else if (result) {
        this.setMigrationStep('step1', 'Completed');
        this.showToasty(
          "Files updated successfully",
          "Search client files updated",
          "success"
        );

        if(this.migrateParams.oldNew === 'migrate-existing'){
          this.editPlatform();
          this.updateSearchClient();
        }

      } else {
        this.setMigrationStep('step1', 'Failed');
        this.setMigrationStep('step2', 'Failed');
        this.showToasty(
          "Migration failed",
          `Search client migration failed`,
          "error"
        );
      }
    });
  }

  addUniqueSearchClient(parameter) {
    if (
      this.contentSources.find(
        (x) =>
          x.name.toLowerCase().trim() === parameter.name.toLowerCase().trim()
      )
    ) {
      this.uniqueSearchClient = false;
      this.showToasty(
        "Cannot save search client!",
        `Same name already exists`,
        "warning"
      );
    } else {
      this.uniqueSearchClient = true;
    }
  }

  toggleSummaryPreference(open) {
    if (open) {
      this.toggleSummary = true;
      this.addClassToBody();
      this.summaryPreferences = this.selectedObject.fields.filter(
        (x) =>
          x.use.use_as == "Summary" ||
          x.use.use_as == "SearchSummary" ||
          x.use.use_as == "SummaryFilter" ||
          x.use.use_as == "Tag"
      );
      this.summaryPreferences.sort(this.sortPriority);
    } else {
      this.toggleSummary = false;
      this.removeClassToBody();
      this.summaryPreferences =
        this.summaryPreferences && this.summaryPreferences.length
          ? this.summaryPreferences
          : this.selectedObject.fields.filter(
              (x) =>
                x.use.use_as == "Summary" ||
                x.use.use_as == "SearchSummary" ||
                x.use.use_as == "SummaryFilter" ||
                x.use.use_as == "Tag"
            );

      for (
        let counter = 0;
        counter < this.summaryPreferences.length;
        counter++
      ) {
        this.summaryPreferences[counter].search_priority = counter + 1;
      }
      this.summaryPreferences.map((sp) => {
        this.settings.sources.map((s) => {
          s.objects.map((m) =>
            m.fields.filter((x) => {
              if (x.id === sp.id) {
                x.use.search_priority = sp.search_priority;
              }
            })
          );
        });
      });
    }
  }

  sortPriority(a, b) {
    return a.use.search_priority - b.use.search_priority;
  }

  addSearchClientCheck() {
    if (
      this.channelExists
    ) {
      this.selectPlatform = !this.selectPlatform;
      if (this.channelExists) {
        this.showToasty(
          "Warning !!",
          "Slack channel id  already exists.",
          "warning"
        );
      } else {
        this.showToasty("OOPS!!", "Search client cannot be saved.", "warning");
      }
    } else {
      // this.indexChanged(4, this.newPlatform.name);
      this.savePlatform();
    }
    this.channelExists = false;
  }

  getMergedFilters(flag?) {
    this.searchClientService
      .getMergedFilters(this.settings.client.uid)
      .then((result) => {
        this.mergedFilters = JSON.parse(result.merged_facets || "[]");
        if (flag == 1) {
          this.showMergeFilters = true;
        }
      });
  }

  checkSlackChannels(channelId) {
    if (this.settings.slackChannels.find((x) => x.channel_id == channelId)) {
      this.channelExists = true;
    } else this.channelExists = false;
  }

  addClassToBody() {
    var addClass = document.getElementById("desktopview");
    addClass.classList.add("addClass");
  }

  removeClassToBody() {
    var removeClass = document.getElementById("desktopview");
    removeClass.classList.remove("addClass");
  }

  showToasty(title, msg, type) {
    var toastOptions: ToastOptions = {
      title,
      msg,
      showClose: true,
      timeout: 3000,
      theme: "default",
    };
    if (type == "success") {
      this.toastyService.success(toastOptions);
    } else if (type == "error") {
      this.toastyService.error(toastOptions);
    } else if (type == "warning") {
      this.toastyService.warning(toastOptions);
    }
  }

  setMergeSources() {
    var mergeSource = JSON.parse(this.settings.client.mergeSources || "{}");
    var mergeSourceValue = {
      enabled:
        mergeSource && Object.keys(mergeSource).length
          ? mergeSource.enabled
          : false,
      size:
        mergeSource && Object.keys(mergeSource).length ? mergeSource.size : 0,
      configurations: [],
    };
    if (mergeSourceValue.enabled) {
      var customizeSearchClientViewList = this.setCustomizeSearchClientViewList(
        mergeSource
      );
      for (
        let counter = 0;
        counter < customizeSearchClientViewList.length;
        counter++
      ) {
        mergeSourceValue.configurations.push({
          indexName: customizeSearchClientViewList[counter].index,
          sortOrder: counter + 1,
        });
      }
    }
    return JSON.stringify(mergeSourceValue);
  }

  setCustomizeSearchClientViewList(mergeSource) {
    var newEnabled = [];
    var customizeSearchClientViewList = [];
    this.settings.sources.map((x) => {
      let index = -1;
      if (
        x.objects.some((f) => f.enabled) &&
        mergeSource.configurations.some((f, i) => {
          if (f.indexName == x.index) {
            index = i;
            return true;
          }
        })
      ) {
        x.sort_orderNew = mergeSource.configurations[index].sortOrder;
        customizeSearchClientViewList.push(x);
      } else if (x.objects.some((f) => f.enabled)) {
        newEnabled.push(x);
      }
    });
    customizeSearchClientViewList = customizeSearchClientViewList.sort(
      function (a, b) {
        return a.sort_orderNew - b.sort_orderNew;
      }
    );
    customizeSearchClientViewList = customizeSearchClientViewList.concat(
      newEnabled
    );
    return customizeSearchClientViewList;
  }

  clearSearchPermissions() {
    let confirmed = confirm("Are you sure you want to clear the cache to refresh search permissions?");
    if (confirmed) {
        this.searchClientService.clearSearchPermissions().then(result => {
            let toastOptions: ToastOptions = {
                title: "Success!!",
                msg: "Cache for search permissions is cleared",
                showClose: true,
                timeout: 2000,
                theme: 'default'
            };
            this.toastyService.success(toastOptions);
        });
    }
  }

  setPreference(indicesArr,objectsOrder){
    let addedIndices = []
    let removedIndices = []
    let tempArr = [];
    let summarySources = JSON.parse(JSON.stringify(this.settings.sources));

    summarySources =  summarySources.filter(x => {
        // if(x.enabled){
            x.objects =  x.objects.filter((o,oi) => {
                if(o.enabled){
                    o.fields = o.fields.filter((f,fi) =>{
                        if(f.use.use_as == 'Summary' || f.use.use_as == 'SearchSummary' || f.use.use_as == 'SummaryFilter' || f.use.use_as == 'Tag')
                            return true;
                        else
                            return false   
                    })
                    if(o.fields.length > 0){
                        // o.fields.map((f,fi) => {f.use.search_priority = fi +1})
                        o.fields.sort(this.sortPriority);
                        
                        return true;
                    }
                        
                    else
                        return false;
                }

            })
            if(x.objects.length > 0)
                return true
            else
                return false
        // }
    })
    let s = [];
    let ind = this.settingOrder(indicesArr);
    
    this.settings.sources.filter(x => {
      ind.map((item,index) => {
            if((item.name ? item.name : item) == x.index){
                if(!(x.objects.some(f => f.enabled))){
                    removedIndices.push(item);
                } else
                    s.push({label: x.label, name: x.index,order:index})
            }

        });
        // (indicesArr.map(i => (i.name ? i.name : i)).indexOf(x.index))
        if((ind.map(i => (i.name ? i.name : i)).indexOf(x.index))< 0){
            if(x.objects.some(f => f.enabled)){
                addedIndices.push({label: x.label, name: x.index});
            }
        }
    })
    s.sort((a,b) => a.order - b.order);

    let finalSortedArr = s.filter((val,i) => {
         if(!(removedIndices.indexOf(val.name) > -1)){
             return val;
         }
     })
    finalSortedArr = s.concat(addedIndices);

    this.settings.sources.map(x => {
        if(finalSortedArr.map(f=>f.name).indexOf(x.index) > -1){
            x.objects.map(obj => {
                if(obj.enabled)
                tempArr.push({label: obj.label, name: obj.name});
            })
        }
    })
    let d = this.settingOrder(objectsOrder);
    let newadded =  tempArr.filter(val => {
        if(d.indexOf(val.name) < 0)
            return val;
    })

    let updated = d.map(val => {
            if(tempArr.findIndex(f=>  val.name ? f.name == val.name : f.name == val) > -1)
                return tempArr[tempArr.findIndex(f=> val.name ? f.name == val.name : f.name == val)];
        }).filter(f=>f)
    objectsOrder = updated.concat(newadded);

     return [finalSortedArr, objectsOrder,summarySources];

  }

  settingOrder(orderedArray){
    let d = [];
    orderedArray.map(f => {if(f.merged) return f.mergedData.filterList; else return f.name ? f.name : f}).forEach(f => {Array.isArray(f) ? d= d.concat(f) : d.push(f)})
    return d;
  }

  createMFFromDB(arrOrder,selectedTab) {
    let filterArray = JSON.parse(JSON.stringify(arrOrder));
    // console.log(selectedTab, " : before: ", this.mergedFilters);
    this.mergedFilters = this.mergedFilters.filter((f) => {
      if (f.facetName == selectedTab && f.createdDefaultPref) {
          let filterArrayKeys = filterArray.map(g => g.name);
          f.filterList = f.createdDefaultPref 
            ? f.filterList.filter((g) => filterArrayKeys.includes(encodeURIComponent(g))) 
            : [];
          return f.filterList.length < 2 ? false : true;
      }
      else return true;
    });
    // console.log(selectedTab, " : after: ", this.mergedFilters);
  }

  isCustomUID(event) {
    this.customUIDfield = event.checked;
  }

  isUIDvalid(customUIDdata) {
    let uidFormatValidator = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if(customUIDdata.length > 0) {
      if(uidFormatValidator.test(customUIDdata)) {
        this.isValidUID = false;
        return false; 
      }
      else {
        this.isValidUID = true;
        return true;
      }
    }
   
    this.isValidUID = false;
    return false;
  }

  async getMeanVectorProgress(){  
    try {
      const response = await this.searchClientService.getMeanVectorProgress(null);
      if (response && response.scProgress) {
        this.vectorProgress =  response.tenantVectorEnabled || response.tenantHybridEnabled ? response.scProgress : {}; 
        await this.processStatusForUids(this.vectorProgress);
      }
    } catch (error) {
      console.error("Error fetching mean vector progress:", error);
    }
  }

  async processStatusForUids(uidStatusObject) {
    for (const uid in uidStatusObject) {
      if (uidStatusObject.hasOwnProperty(uid)) {
        const { status } = uidStatusObject[uid];
        if (status === 1) {
          await this.triggerEmitScWise(uid);
        }
      }
    }
  }

  async triggerEmitScWise(scUid) {
    const data = {
      scUid: scUid
    }
    this.emitCount(data);
    this.movCountUpdate();
  }

  async listenMovKafka() {
    this.socket.on(`movKafkaEvent`, (data) => {
      this.emitCount(data.data);
    });
    this.movCountUpdate();
  }

  emitCount(data) {
    const t_id = localStorage.getItem('t_id');
    this.socket.emit(`getCount_${t_id}`, data);
  }

  async movCountUpdate(){
    let self = this;
    this.socket.on(`countUpdate_${localStorage.getItem('t_id')}`, function (source) {
      if (self.vectorProgress) {
        self.vectorProgress[source.scUid].totalPercentage = source.totalPercentage;
        self.vectorProgress[source.scUid].status = source.status
      }
    });
  }
  
  scResetCodebase(event): void {
    this.resetSCOption.sourceUid = event.value;
  }
  

  expandedIndex: number | null = null;

  toggleExpand(index: number): void {
    this.expandedIndex = this.expandedIndex === index ? null : index;
  }

  getTestReports() {
    this.abTestingService.testReports().then((reportsData) => {
          this.testReportsData = reportsData.data;
    });
  }

}
