import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ChangeDetectorRef } from '@angular/core';
import { ToastyService, ToastyConfig, ToastOptions, ToastData } from 'ng2-toasty';
import { CookieService } from '../../../../services/cookie.service';
import { environment } from 'environments/environment';
import { Variables, CSID_WITH_FOLDER } from '../../../../variables/contants'
import { ContentSourceService } from '../../../../services/contentSource.service';
import { CrawlerConfigService} from '../../../../services/crawlerConfig.service';
import { StackOverflowCrawlerService } from '../../../../services/stackOverflowCrawler.service';
import { CONTENT_SOURCE_TYPE } from '../../../../variables/contants';
import { MatChipInputEvent } from '@angular/material/chips';
import { ContentSourcesComponent } from '../content_sources';
import { ActivatedRoute } from '@angular/router';
import { AuthTreeComponent } from '../treeComponent/authtreeComponent/authTree';
import { AuthSharedTreeComponent } from '../treeComponent/authSharedTreeComponent/authSharedTree';
import { TreeService } from 'app/services/tree.service';
import { SharedDriveComponent } from '../treeComponent/sharedDriveComponent/sharedDrive';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';

interface fieldsSchema {
	label: string,
	value: string,
	name: string
};

@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  providers: [ToastyService, ToastyConfig, CookieService, CrawlerConfigService, StackOverflowCrawlerService, TreeService],
  styleUrls: ['./auth.component.css']
})

export class AuthComponent implements OnInit {
  private csTypeId: any;
  private languages: any;
  private BASE_HREF: string;
  private currentTab: string;
  private isLoading_small: boolean;
  private uniqueContentSource: boolean = false;
  private isCSConnected: boolean;
  private authenticationMethods: any = [];
  private selectedAuthenticationMethod: any;
  public isFileUploaded: boolean = false;
  public csvFile: string;
  private csvType: boolean = false;
  public urlFilePath: any;
  private isConnected: boolean;
  private isLoading: boolean;
  private connectButton: boolean;
  private validation: any = { valid: true, msgs: [] };
  private enabledProject: any;
  private sendPlaces: any = [];
  private selectedIndex: number;
  private isLinear: boolean;
  private nowhitespace: boolean;
  private organizationTypes: any;
  private selectedOrganizationType: any;
  public selectedDropDownField : any;
  private nextDisabled: boolean = false;
  private languageAllChecked: boolean;
  private halfChecked: boolean;
  private isSubmitted: boolean = false;
  private validUrl: boolean = true;
  private urlPattern: any = new RegExp(this.ContentSourcesComponent.clientURLPattern)
  private downloadInProgress:boolean =false;
  private csWithoutPlaces: any = false;
  private csWithClientCreds: any = false;
  private csWithTenant: any = false;
  private multiSelectAuth: any = false;
  private csWithoutClientUrls: any = false;
  private clientCreds: any = false;
  private spaceLabel: String = "By Spaces";
  private readonlyClientUrl: Boolean = false;
  private clienturl: any = "";
  private dropDownField: any = {};
  private additionalFields: any = [];
  private multiSelectFields: any = [];
  private noApiKeyField: boolean = false;
  private onPremCsType: boolean = false;
  private stackCsType: boolean = false;
  private khorosCsType: boolean = false;
  private passwordPlaceholder: String;
  private usernamePlaceholder: String = "Username";
  private isOauthUpdated: boolean = false;
  private connectingCS  = false;
  private authenticatingCS  = false;
  private isRequired = true;
  private isFormValid = false;
  private selectedAuthenticationTypes: any = [];
  private clientUrlPattern =  this.ContentSourcesComponent.clientURLPattern;
  private authenticatedUser: String;
  private connectedContentSourceAuthData: [];
  private isCsUserDetails: any = false;
  private authError: any = false;
  public isShowAuthenticatedUserButtonOrNot: boolean = false;
  private isRulesTabLoading = true;
  private isSharedDriveLoaded = false;
  private jsWeb: boolean = false;
  private searchUnifyUser: boolean = false;
  private multiStepForm: boolean = false;
  private isWebsiteConnected: boolean = false;
  private saveContentSource: boolean = false;
  public urlArray: any = [];
  public savebuttonDisable: boolean = true;
  private formSubmitted: boolean = false;
	private validThreadCount: number;
	private selectedIndexVal: number = 0;
  private fieldsNotToIncludeInFilter = ['usernameCssSelector', 'passwordCssSelector', 'inputCssSelector'];
	private deleteId: number;
  private additionalToggle: any= [];
	private maxStepsInForm = 5;
	anchorUrlFilter: string = '';
  private multiFormSchemaForm: FormGroup;
	private showDeleteWarning:boolean = false;
	private multiFormControl = new FormControl('');
  private addNewStep : boolean = true;
	public fileName: string;

  Tag: any;
  suggestedOpt: any;
  selectedTags: Array<string>;

  private alreadyNode: any;
  private getDivData: any;
  private isJstreeLoaded: any;
  private outerFolderButton: number;
  private isSharedTreeLoaded: any;
  private folderButton: number;
  private folderBasedCS: boolean = false;
  private changeScopePermissions: boolean;
  private enablePermissions: boolean;
  private getSharedData: any;
   

  @ViewChild(AuthTreeComponent) AuthTreeComponent: AuthTreeComponent;
  @ViewChild(AuthSharedTreeComponent) AuthSharedTreeComponent: AuthSharedTreeComponent;
  @ViewChild(SharedDriveComponent) sharedDriveComponent: SharedDriveComponent;

  @Input() contentSourceData: any;
  @Input() contentSourcess: any;
  @Input() editMode: boolean;
  @Input() instructions: string;
  @Input() syncStartDate: any;
  @Input() userContentSources: any;
  @Input() uniqueCSName: string;
  @Output() submitted = new EventEmitter();
  // @Output() getPlaces = new EventEmitter();
  // @Output() EnabledPlaces = new EventEmitter();
  @Output() reset = new EventEmitter();

  //for mat chips
  visible: boolean = true;
  selectable: boolean = true;
  removable: boolean = true;
  addOnBlur: boolean = true;


  constructor(private ContentSourcesComponent: ContentSourcesComponent, private cookieService: CookieService, private toastyService: ToastyService, private contentSourceService: ContentSourceService,  private crawlerConfigService: CrawlerConfigService, private stackoverflowcrawlerservice: StackOverflowCrawlerService, private _Activatedroute: ActivatedRoute, private treeService: TreeService, private cdr: ChangeDetectorRef, private fb: FormBuilder) {
  }

  ngOnInit() {
    this.csTypeId = this._Activatedroute.snapshot.paramMap.get("id");
    if (this.csTypeId && !isNaN(this.csTypeId))
      this.csTypeId = Number(this.csTypeId);
    this.contentSourceData = this.ContentSourcesComponent.contentSourceData;
    this.selectedAuthenticationMethod = this.contentSourceData.authorization.authorization_type;
    if(this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.vanilla){
      this.selectedAuthenticationMethod = 'OAuth'
    }
    
    this.uniqueCSName = this.ContentSourcesComponent.uniqueCSName;
    this.userContentSources = this.ContentSourcesComponent.userContentSources;
    if (this.csTypeId == 12 || this.csTypeId == 17) {
      this.addCustomOauthValue()
    }
    this.editMode = this.ContentSourcesComponent.editMode;
    this.noApiKeyField = [CONTENT_SOURCE_TYPE.helpScout].includes(this.csTypeId);
    this.onPremCsType = [CONTENT_SOURCE_TYPE.jiraOnprem].includes(this.csTypeId);
    this.stackCsType = [CONTENT_SOURCE_TYPE.stackOverflow].includes(this.csTypeId);
    this.khorosCsType = [CONTENT_SOURCE_TYPE.lithium].includes(this.csTypeId);

    this.jsWeb = CONTENT_SOURCE_TYPE.jsWeb==this.csTypeId;
    
    this.cookieService.getSearchUnifySession().then((result) => {
			this.searchUnifyUser = result.suDomains.includes(result.email.split('@')[1]) ? true : false;
		});

    this.contentSourceService.threadValue().then(response => {
			this.validThreadCount = response;
		});

    if (this.jsWeb) { 
      const defaults = {
          session: false,
          javascript_enabled: false,
          sitemap_enabled: false,
          authenticate_before_crawl: false,
          anchor_url: '',
          anchor_depth: 1
      };
      this.contentSourceData.getWebConf = {
        ...defaults,
        ...(this.contentSourceData.getWebConf || {})
      }
    
    }


    if (CONTENT_SOURCE_TYPE.stackOverflow == this.csTypeId)
      this.contentSourceData.authorization.instanceURL = "https://api.stackexchange.com/";
    this.getLanguages();
    this.currentTab = 'authentication';
    // this.isCSConnected = false;
    this.BASE_HREF = `${window.location.origin}`;
    //Auth-Singleton-change Start
    this.folderBasedCS = CSID_WITH_FOLDER.includes(this.csTypeId);
    this.csWithoutPlaces = !this.folderBasedCS;
    this.isRulesTabLoading = true;


     this.contentSourceData.authorization.organization_user_type = this.contentSourceData.authorization.organization_user_type ? String(this.contentSourceData.authorization.organization_user_type) : "0";
    this.organizationTypes = true ? [] : [{ id: 0, label: "Public" }, { id: 1, label: "Enterprise" }];
    this.selectedOrganizationType = this.contentSourceData.authorization.organization_user_type ? this.contentSourceData.authorization.organization_user_type : "0";

    this.contentSourceData.language = this.contentSourceData.language.length ? this.contentSourceData.language : ['en'];
    this.isConnected = false;
    this.connectButton = true;
    this.isLinear = true;
    this.nowhitespace = true;

    this.isCSConnected = this.contentSourceData.authorization.is_authenticated || false;
    this.contentSourceData.authorization.verifyAuthentication = false;
    this.contentSourceData.authorization.authorization_type = this.contentSourceData.authorization.authorization_type? this.contentSourceData.authorization.authorization_type : '';
    this.isFileUploaded = !!((this.contentSourceData.contentSource.isFileUploaded == undefined) ? this.isFileUploaded : this.contentSourceData.contentSource.isFileUploaded);
    this.contentSourceData.contentSource.isFileUploaded = this.isFileUploaded;

    if(this.isFileUploaded){
			this.ContentSourcesComponent.clientURLPattern = "";
    }
   
    if (this.editMode) {
      // this.isCSConnected = true;
      this.isLinear = false;
      this.nextDisabled= true
      this.isRequired= false
    }
    this.selectedIndex = 0;
    this.Tag = "";
    this.suggestedOpt = [];
    this.selectedTag = [];
    this.alreadyNode = [];
    this.getDivData = [];
    this.isJstreeLoaded = false;
    this.isSharedTreeLoaded = false;
    this.isSharedDriveLoaded = false;
    this.outerFolderButton = 0;
    this.getSharedData = [];
       
    this.folderButton = 1;
    
    if (this.csTypeId == CONTENT_SOURCE_TYPE.stackOverflow) {
      this.stackoverflowcrawlerservice.getSavedTags(this.contentSourceData.contentSource.id).then(result => {
        this.selectedTag = result;
      });
    }
    if (this.csTypeId == CONTENT_SOURCE_TYPE.file) {
      this.csvType = true;
    }
    if (this.editMode && this.uniqueCSName.toLowerCase().trim() === this.contentSourceData.contentSource.label.toLowerCase().trim()) {
      this.uniqueContentSource = true;
    }
    this.fetchAllsources();
    if (this.csTypeId == CONTENT_SOURCE_TYPE.jira || this.csTypeId == CONTENT_SOURCE_TYPE.confluence) {
      this.passwordPlaceholder = 'API token';
    } else if (this.csTypeId == CONTENT_SOURCE_TYPE.zulip) {
      this.usernamePlaceholder = 'Email';
      this.passwordPlaceholder = 'API token';
    } else  if (this.csTypeId == CONTENT_SOURCE_TYPE.uservoice){
      this.passwordPlaceholder = 'Client secret';
      this.usernamePlaceholder = 'Client Id';
      if(this.contentSourceData.authorization.authorization_type == 'OAuth'){
        this.clientCreds = true;
      }
    } else {
      this.passwordPlaceholder = 'Password'
    }
    this.showAuthenticatedUserButtonOrNot();
    this.multiStepForm = this.jsWeb && ( this.contentSourceData.authorization.authorization_type == 'Multi-Step Form') ;

    if(this.jsWeb){
      this.onAnchorUrlFilterChange(null,this.contentSourceData.getWebConf.anchor_url);
      let multFormData = JSON.parse(this.contentSourceData.authorization.multi_form_auth || "[]");
      this.multiFormSchemaForm = this.fb.group({
        multiFormSchema: this.fb.array([])
      });

      if(this.multiStepForm){
        let multFormData = JSON.parse(this.contentSourceData.authorization.multi_form_auth || "[]");
        if(multFormData.length) {
          this.setMultiFormData(multFormData);
        } 
      }
		}
    this.validateForm();
    this.enablePermissions = CONTENT_SOURCE_TYPE.youtube && this.editMode ? true: false;
    this.contentSourceData.authorization.changeScopePermissions = false;
  }

  patchSelectedFields(fields) {
    let selectedFields = [];
    fields.forEach((field) => {
      const [key] = Object.keys(field);
      selectedFields.push(key);
    })
    return selectedFields;
  }

  patchFields(fieldsToBeAdded, multiFormFields, fields) {
    fieldsToBeAdded.forEach(ele => {
      const [key] = Object.keys(ele);
      const fieldSchemaObject: fieldsSchema = {
        label: multiFormFields.controls.find(control => control.get('name').value === key).get('label').value,
        value: ele[key],
        name: key
      };
      fields.push(this.fb.group(fieldSchemaObject));
    });
  }

  onAnchorUrlFilterChange(event: Event, anchorUrlFromDb: string) {
    if (anchorUrlFromDb || event) {
      this.anchorUrlFilter = (anchorUrlFromDb ? anchorUrlFromDb.trim() : "") || (event.target as HTMLInputElement).value.trim();
    }
  }


  connectTab(tabName) {
    this.contentSourceData.tabName = tabName;
  }


  setMultiFormData(multFormData) {
    multFormData.forEach((obj, ind) => {
      this.addNewTab();
      const formArray = this.multiFormSchemaForm.get('multiFormSchema') as FormArray;
      const formGroup = formArray.controls[ind] as FormGroup;
      const multiFormFields = formGroup.get('multiFormFields') as FormArray;
      const fields = formGroup.get('fields') as FormArray;
      const selectedFields = this.patchSelectedFields(obj.fields)
      const res = this.patchFields(obj.fields, multiFormFields, fields)
      let filteredMultiFormFields = formGroup.get('filteredMultiFormFields') as FormArray;

      formGroup.patchValue({
        currentPageUrl: obj.currentPageUrl,
        selectedFields: selectedFields,
        searchTerm: ''
      });
      filteredMultiFormFields.clear();
      multiFormFields.value.forEach(field => {
        let res = this.fieldsNotToIncludeInFilter.filter((notIncludeField) =>
          notIncludeField === field.name
        )
        if (!res.length) {
          filteredMultiFormFields.push(this.createFieldFormGroup(field.name, field.label));
        }
      });
    })
  }


  patchField(fieldsToBeAdded, multiFormFields, fields) {
    fieldsToBeAdded.forEach(ele => {
      if (!fields.controls.some(control => control.get('name').value === ele)) {
        const fieldSchemaObject: fieldsSchema = {
          label: multiFormFields.controls.find(control => control.get('name').value === ele).get('label').value,
          value: '',
          name: ele
        };
        fields.push(this.fb.group(fieldSchemaObject));
      }
    });
  }
  changeFormFields(event, index) {
    const formArray = this.multiFormSchemaForm.get('multiFormSchema') as FormArray;
    const formGroup = formArray.controls[index] as FormGroup;
    if (event.value.length) {
      if (event.value.includes('username') && !event.value.includes('usernameCssSelector')) {
        event.value.push('usernameCssSelector')
      }
      if (!event.value.includes('username') && event.value.includes('usernameCssSelector')) {
        event.value = event.value.filter(item => item !== 'usernameCssSelector');
      }
      if (event.value.includes('password') && !event.value.includes('passwordCssSelector')) {
        event.value.push('passwordCssSelector')
      }
      if (!event.value.includes('password') && event.value.includes('passwordCssSelector')) {
        event.value = event.value.filter(item => item !== 'passwordCssSelector');
      }
      if (event.value.includes('extraInput') && !event.value.includes('inputCssSelector')) {
        event.value.push('inputCssSelector')
      }
      if (!event.value.includes('extraInput') && event.value.includes('inputCssSelector')) {
        event.value = event.value.filter(item => item !== 'inputCssSelector');
      }
    }
    // Update selectedFields in the form group
    formGroup.patchValue({
      selectedFields: event.value
    });
    let fieldsToBeAdded = event.value;

    // Update fields in the form group
    const multiFormFields = formGroup.get('multiFormFields') as FormArray;
    const fields = formGroup.get('fields') as FormArray;

    this.patchField(fieldsToBeAdded, multiFormFields, fields)

    // Remove unselected fields from the form group
    const unselectedFields = fields.controls.filter(control => !event.value.includes(control.get('name').value));
    unselectedFields.forEach(control => {
      const controlIndex = fields.controls.indexOf(control);
      fields.removeAt(controlIndex);
    });
  }

  addNewTab() {
    const newTab = this.createTabFormGroup();
    (this.multiFormSchemaForm.get('multiFormSchema') as FormArray).push(newTab);
    this.selectedIndexVal = (this.multiFormSchemaForm.get('multiFormSchema') as FormArray).length - 1;

  }

  createFieldFormGroup(name: string, label: string): FormGroup {
    return this.fb.group({
      name: [name, Validators.required],
      label: [label, Validators.required]
    });
  }

  createTabFormGroup(): FormGroup {
    // Create and return a FormGroup for a new tab
    return this.fb.group({
      multiFormFields: this.fb.array([
        this.createFieldFormGroup('username', 'Username'),
        this.createFieldFormGroup('password', 'Password'),
        this.createFieldFormGroup('extraInput', 'Add extra input'),
        this.createFieldFormGroup('usernameCssSelector', 'Username CSS Selector'),
        this.createFieldFormGroup('passwordCssSelector', 'Password CSS Selector'),
        this.createFieldFormGroup('buttonCssSelector', 'Button CSS Selector'),
        this.createFieldFormGroup('clickSelector', 'Add a click event'),
        this.createFieldFormGroup('inputCssSelector', 'Extra Input CSS Selector')
      ]),
      fields: this.fb.array([]),
      currentPageUrl: ['', [Validators.required, Validators.pattern(this.ContentSourcesComponent.clientURLPattern)]],
      selectedFields: [[], Validators.required],
      searchTerm: [''],
      filteredMultiFormFields: this.fb.array([
        this.createFieldFormGroup('username', 'Username'),
        this.createFieldFormGroup('password', 'Password'),
        this.createFieldFormGroup('extraInput', 'Add extra input'),
        this.createFieldFormGroup('buttonCssSelector', 'Button CSS Selector'),
        this.createFieldFormGroup('clickSelector', 'Add a click event'),
      ])
    });
  }

  deleteIndex(ind) {
    this.deleteId = ind;
    this.showDeleteWarning = true;
    this.selectedIndexVal = ind;
  }

  deleteIndexV() {
    if (this.deleteId != 0) {
      this.selectedIndexVal = this.deleteId - 1;
    } else {
      this.selectedIndexVal = 0;
    }
    (this.multiFormSchemaForm.get('multiFormSchema') as FormArray).removeAt(this.deleteId);
    this.showDeleteWarning = false;
  }

  cancelDelete() {
    this.selectedIndexVal = this.deleteId;
    this.showDeleteWarning = false;
  }

  nextTab(ind) {
    if ((this.multiFormSchemaForm.get('multiFormSchema') as FormArray).length - 1 > ind) {
      this.selectedIndexVal = ind + 1;
    }
  }

  prevTab(ind) {
    if (ind != 0) {
      this.selectedIndexVal = ind - 1;
    }
  }

  get canAddStep(): boolean {
    const formArray = this.multiFormSchemaForm.get('multiFormSchema') as FormArray;

    if (!formArray || !(formArray instanceof FormArray)) {
      console.warn("multiFormSchema is not a valid FormArray.");
      return false;
    }

    const length = formArray.length || 0;

    return length < this.maxStepsInForm;
  }


  searchFields(event, index) {
    const formArray = this.multiFormSchemaForm.get('multiFormSchema') as FormArray;
    const formGroup = formArray.controls[index] as FormGroup;
    let multiFormFields = formGroup.get('multiFormFields') as FormArray;
    const searchTerm = formGroup.get('searchTerm');
    let filteredMultiFormFields = formGroup.get('filteredMultiFormFields') as FormArray;

    const filteredFields = multiFormFields.value.filter((field) => {
      return field.name.toLowerCase().includes(event.target.value)
    });

    filteredMultiFormFields.clear();

    filteredFields.forEach(field => {
      let res = this.fieldsNotToIncludeInFilter.filter((notIncludeField) =>
        notIncludeField === field.name
      )
      if (!res.length) {
        filteredMultiFormFields.push(this.createFieldFormGroup(field.name, field.label));
      }
    });
  }


  isTabDisabled(index: number): boolean {
    if (index > 0) {
      const formArray = this.multiFormSchemaForm.get('multiFormSchema') as FormArray;
      const formGroup = formArray.controls[index - 1] as FormGroup;

      // Check if the form controls are invalid for the current tab
      return formGroup.get('currentPageUrl').invalid ||
        formGroup.get('selectedFields').value.length === 0 ||
        formGroup.get('fields').invalid;
    } return false;
  }

  onTabChange(event): void {
    // Update the selectedIndexVal when the tab is changed manually
    this.selectedIndexVal = event.index;
  }

  addCustomOauthValue() {
    this.userContentSources.find(obj => obj.id == this.contentSourceData.contentSource.id && (this.isOauthUpdated = obj.isOauthUpdated))
  }

  fetchAllsources() {

    this.contentSourceService.fetchCSConfig(this.csTypeId).then(res => {
      this.isLoading = false;
      if (res.error || (res && res.data && res.data.length == 0)) {
        var toastOptions: ToastOptions = {
          title: "Error fetching CSconfig",
          msg: res.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
      }
      else {
        let content = res.data[0];
        this.selectedAuthenticationTypes = this.contentSourceData.authorization.authorization_type ? this.contentSourceData.authorization.authorization_type.split(',') : [];

        if (content.defaultAuth) {
          this.contentSourceData.authorization.authorization_type = this.contentSourceData.authorization.authorization_type ||  content.defaultAuth;
          this.selectedAuthenticationTypes = this.contentSourceData.authorization.authorization_type ? this.contentSourceData.authorization.authorization_type.split(',') : [];
        }
        if (content.dropDownKey && this.contentSourceData.authorization[this.dropDownField.key]) {
          this.contentSourceData.authorization[content.dropDownKey] = String(this.contentSourceData.authorization[content.dropDownKey]);
        }

        this.csWithoutPlaces = !content.hasPlaces;
        this.csWithClientCreds = content.hasClientCreds;
        this.csWithTenant = content.hasTenant;
        this.csWithoutClientUrls = content.hasClientUrl;
        this.multiSelectAuth = content.is_multi_select_auth;
        this.additionalFields = content.additionalFields ? JSON.parse(content.additionalFields) : [];
        this.additionalToggle = content.additionalToggles ? JSON.parse(content.additionalToggles) : [];
        this.multiSelectFields = content.multiSelectFields ? JSON.parse(content.multiSelectFields) : [];
        this.spaceLabel = content.spaceLabel || "By Spaces";
        this.dropDownField = content.dropDownKey ? {
          key: content.dropDownKey,
          label: content.dropDownLabel,
          options: JSON.parse(content.dropDownOptions)
        } : {};

        this.clientCredsCheck(this.contentSourceData.authorization[content.dropDownKey]);
        this.selectedDropDownField = String(this.contentSourceData.authorization[content.dropDownKey]);
        
        if (content.dropDownKey && (!this.contentSourceData.authorization[this.dropDownField.key] || this.contentSourceData.authorization[this.dropDownField.key] == '') && this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.salesforce) {
          if (this.dropDownField && this.dropDownField.options.length > 0) {
            this.selectedDropDownField = this.contentSourceData.contentSource[this.dropDownField.key] || this.dropDownField.options[0].value;
            this.contentSourceData.contentSource[this.dropDownField.key] = this.selectedDropDownField;
          }
        }

        if (content.allowedAuths && content.allowedAuths.length) {
          content.allowedAuths.split(",").forEach(e => {
            this.authenticationMethods.push({
              id: e, label: e, active: true
            });
          });
        }

        if (content.clientUrl != null) {
          this.contentSourceData.contentSource.url = content.clientUrl;
          this.readonlyClientUrl = true;
        }
        this.isRulesTabLoading = false;
        this.validateForm();
      }
    })

    this.contentSourceService.getCsConfiguration().then(res => {
      if (res.error || !res.data || !(res.data.connectedUserCs)) {
        this.isCsUserDetails = false;
        return;
      }

      let response = res.data.connectedUserCs.filter(cs => cs.id === this.contentSourceData.contentSource.content_source_type_id);
      this.connectedContentSourceAuthData = response[0].userEndPoint;

      if(!this.connectedContentSourceAuthData.length) {
        this.isCsUserDetails = false;
      } else {
        this.isCsUserDetails = true
      }
      
      delete res.data.crawlerDebugSupportedCs;
      delete res.data.totalCountSupportedCs;
    });
  };

  clientCredsCheck(orgType?: any) {
    if (this.csWithClientCreds) {
      this.clientCreds = this.csWithClientCreds;
      if (this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.mindtouch) {
        this.clientCreds = this.selectedAuthenticationMethod == 'Api'  || this.selectedAuthenticationTypes[0] == 'Api';
      }
      if (this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.sharepoint) {
        this.clientCreds = this.selectedAuthenticationMethod == 'OAuth' || this.selectedAuthenticationTypes[0] == 'OAuth';
      }
      if (this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.aha ) {
        this.clientCreds = this.selectedAuthenticationMethod == 'OAuth' || this.selectedAuthenticationTypes[0] == 'OAuth';
      }
      if (this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.lithium) {
        this.clientCreds = this.selectedAuthenticationTypes.length && this.selectedAuthenticationTypes.includes('OAuth');
      }
      if (this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.khorosAurora) {
        this.clientCreds = this.selectedAuthenticationTypes.length && this.selectedAuthenticationTypes.includes('OAuth') && !this.selectedAuthenticationTypes.includes('Client Credentials');
      }
    }
    if(this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.servicenow){
      this.clientCreds = this.selectedAuthenticationMethod === 'OAuth' || this.selectedAuthenticationTypes[0] === 'OAuth';
    }
    if(this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.uservoice  && this.selectedAuthenticationMethod == 'OAuth'){
      this.clientCreds = true;
    }else if(this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.uservoice  && this.selectedAuthenticationMethod == 'Basic'){
      this.clientCreds = false;
    } else if(this.contentSourceData.contentSource.content_source_type_id===CONTENT_SOURCE_TYPE.confluence) {
        this.csWithoutClientUrls = !(this.selectedAuthenticationTypes.includes('OAuth'));
        this.clientCreds = this.selectedAuthenticationTypes.includes('OAuth');
    } else if(this.contentSourceData.contentSource.content_source_type_id===CONTENT_SOURCE_TYPE.jira) {
      this.clientCreds = this.selectedAuthenticationTypes.includes('OAuth');
    } else if(this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.airtable  && this.selectedAuthenticationMethod == 'Api'){
      this.clientCreds = false;
    } else if(this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.zendesk) {
      this.clientCreds = !!this.selectedAuthenticationTypes.includes('OAuth');    
    }
    if (this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.dropbox) {
      if (Number(orgType) == 1) {
        this.clientCreds = true;
      } else {
        this.clientCreds = false;
      }
    }
    //this.clientCreds = Number(orgType) ? (this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.dropbox ? true : false) : this.clientCreds;
  }

  clientUrls() {
    let tempUrl = this.contentSourceData.contentSource.url.split(',');
    this.validUrl = true;
    for (let index = 0; index < tempUrl.length; index++) {
      if (!this.urlPattern.test(tempUrl[index]) || tempUrl[index] == '') {
        this.validUrl = false
        break;
      }
    }
  }

  tabType(event, tabName) {
    if (CSID_WITH_FOLDER.includes(this.csTypeId)) {
      if (tabName === 'outerTab') {
        this.outerFolderButton = 1;
        this.contentSourceService.getContentSourceInRulesTab(this.contentSourceData.contentSource.id).then(result => {
          this.contentSourceData = result.data;
          this.AuthTreeComponent.treeLoad(this.contentSourceData, this.isJstreeLoaded);
          this.isJstreeLoaded = true;
          this.alreadyNode = this.contentSourceData.spacesORboards;
        });
      }
      if (tabName === 'innerTab' && event.index == 0) {
        this.toggleFolder(1);
        if (this.isJstreeLoaded == false) {
          this.contentSourceService.getContentSourceInRulesTab(this.contentSourceData.contentSource.id).then(result => {
            this.contentSourceData = result.data;
            this.AuthTreeComponent.treeLoad(this.contentSourceData, this.isJstreeLoaded);
            this.isJstreeLoaded = true;
            this.alreadyNode = this.contentSourceData.spacesORboards;
          });
        }
      }
      if (tabName === 'innerTab' && event.index == 1) {
        this.toggleFolder(2);
        if (this.isSharedTreeLoaded == false) {
          this.contentSourceService.getContentSourceInRulesTab(this.contentSourceData.contentSource.id).then(result => {
            this.contentSourceData = result.data;
            this.AuthSharedTreeComponent.treeLoad(this.contentSourceData, this.isSharedTreeLoaded);
            this.isSharedTreeLoaded = true;
          });
        }
      }
      if (tabName === 'innerTab' && event.index == 2 && this.csTypeId === 12 ) {
        this.toggleFolder(3);
        if (this.isSharedDriveLoaded == false) {
            this.contentSourceService.getContentSourceInRulesTab(this.contentSourceData.contentSource.id).then(result => {
                this.contentSourceData = result.data;
                this.sharedDriveComponent.treeLoad(this.contentSourceData, this.isSharedDriveLoaded);
                this.isSharedDriveLoaded = true;
            });
        }
    }
    }
  }

  selectAllLanguages(event) {
    this.halfChecked = false;
    if (event.checked) {
      var lang = []
      for (var i = 0; i < this.languages.length; i++) {
        lang.push(this.languages[i].code)
      }
      this.contentSourceData.language = lang;
    } else {
      this.contentSourceData.language = [];
    }
  }

  languageChange(event) {
    this.halfChecked = true;
    this.contentSourceData.language = event.value;
    if (event.value.length == this.languages.length) {
      this.languageAllChecked = true;
      this.halfChecked = false;
    } else if (this.contentSourceData.language.length < 1) {
      this.languageAllChecked = false;
      this.halfChecked = false;
    } else {
      this.halfChecked = true;
      this.languageAllChecked = false;
    }
  }

  savePlaces(spacesORboards) {
    this.contentSourceData.spacesORboards = spacesORboards;
  }

  getLanguages() {
    this.contentSourceService.getLanguages().then((res) => {
      this.languages = res;
      if (this.contentSourceData.language.length == this.languages.length) {
        this.halfChecked = false;
      } else {
        this.halfChecked = true;
      }
    })

  }

  connectContentSource() {
    this.connectButton = false;

    this.contentSourceData.webConf = this.contentSourceData.getWebConf;
    let outputArray = [];

    if (this.multiStepForm) {
      this.multiFormSchemaForm.value.multiFormSchema.map((multiForm) => {
        let outputObject = { fields: [], currentPageUrl: multiForm.currentPageUrl };
        multiForm.fields.forEach((field, ind) => {
          let obj = {}
          obj[field.name] = field.value;
          outputObject.fields.push(obj);
        })
        outputArray.push(outputObject);
      })
      this.contentSourceData.authorization.multi_form_auth = JSON.stringify(outputArray);
    }

    if (this.jsWeb) {
      this.ContentSourcesComponent.authorizationChanged = true;
      if (this.contentSourceData.contentSource.thread > this.validThreadCount) {
        let msg = "Thread value should be between 1 & " + this.validThreadCount;
        if (this.validThreadCount == 1) {
          msg = "Thread count should be 1"
        }
        var toastOptions: ToastOptions = {
          title: "Thread Value",
          msg: msg,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
        this.selectedIndex = 0;
        this.connectButton = true;
        return;
      }
    }
    this.switchTab('authentication');
    this.selectedIndex = 0;
    this.contentSourceData.tabName = this.selectedIndex;
    this.connectingCS = true;
    this.contentSourceData.authorization.verifyAuthentication = true;
    this.contentSourceData.contentSource.isOauthUpdated = this.isOauthUpdated;
    this.authenticatedUser = null;
    this.authError = null;
    if ((this.contentSourceData.contentSource.content_source_type_id == 12 || this.contentSourceData.contentSource.content_source_type_id == 17) && (!this.contentSourceData.authorization.client_id || !this.contentSourceData.authorization.client_secret) && (!this.contentSourceData.contentSource.editing || this.contentSourceData.contentSource.isOauthUpdated)) {
      var toastOptions: ToastOptions = {
        title: "Error saving content source",
        msg: "Please provide valid client ID and Secret",
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.error(toastOptions);
      this.contentSourceData.authorization.verifyAuthentication = false;
      this.connectButton = true;
      this.connectingCS = false;
      return;
    }
    this.contentSourceService.addContentSource(this.contentSourceData).then(result => {
      if(result.status == 403 || result.status == 500){
        var toastOptions: ToastOptions = {
          title: 'Connection Failed!',
          msg: "Please retry",
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
        this.contentSourceData.authorization.verifyAuthentication = false;
        this.connectButton = true;
        this.connectingCS = false;
        return;
      }
      this.contentSourceData = result;
      this.isOauthUpdated = this.contentSourceData.contentSource.isOauthUpdated;
      let id = result.contentSource.id;
      // if (id) this.connectButton = true;
      if ((this.contentSourceData.authorization.authorization_type.includes('OAuth')  && !(this.contentSourceData.contentSource.content_source_type_id == 28) 
        && !(this.contentSourceData.contentSource.content_source_type_id == 55)
        && !(this.contentSourceData.contentSource.content_source_type_id == CONTENT_SOURCE_TYPE.cornerstone))) {
        
          if (!result.oauth || !result.oauth.status) {
            var toastOptions: ToastOptions = {
              title: 'Connection Failed!',
              msg: "Please retry",
              showClose: true,
              timeout: 3000,
              theme: 'default'
            };
            this.toastyService.error(toastOptions);
            this.connectingCS = false;
            if (id) this.connectButton = true;
            this.isRequired= true;
            this.validateForm();
          } else {
          let id = result.contentSource.id;
          if (id) this.connectButton = true;
          var scope = this;
          console.log('dataaaaaa', Variables.baseHref, environment.production);
          if (environment.production) {
            console.log('in production', `${result.oauth.url}&state=https://${window.location.host}${Variables.baseHref}/admin/authorization/oAuthCallbacksu_csid${id}`);
            var newWindow = window.open(`${result.oauth.url}&state=https://${window.location.host}${Variables.baseHref}/admin/authorization/oAuthCallbacksu_csid${id}`, 'name', 'height=400,width=400');
          }
          else {
            console.log('not production url', `${result.oauth.url}&state=http://${window.location.host}/backend/admin/authorization/oAuthCallbacksu_csid${id}`);
            var newWindow = window.open(`${result.oauth.url}&state=http://${window.location.host}/backend/admin/authorization/oAuthCallbacksu_csid${id}`, 'name', 'height=400,width=400');
          }
          this.isRequired= false;
          this.validateForm();
          (function afterCloseChild() {
            try {
              if (newWindow && newWindow.closed) {
                if (newWindow.document) {
                  const h3 = newWindow.document.getElementsByClassName('textError')[0];
                  scope.connectButton = true;
                  if (h3 && (h3 as any).innerText === 'Connection Succeeded') {
                    scope.contentSourceData.contentSource.is_paused = 0;
                    scope.contentSourceData.authorization.is_authenticated = 1;
                    scope.connectingCS = false;
                    scope.isLinear = false;
                    scope.isCSConnected = true;
                    scope.nextDisabled= true;
                    scope.isSubmitted = true;
                    scope.selectedIndex = 1;
                    scope.isRequired= false;
                    scope.validateForm();
                    scope.showSuccessToast();
                  } else {
                    handleConnectionUnsuccessful();
                  }
                } else {
                  handleConnectionUnsuccessful();
                }
              } else {
                // The window is still open. Check again after a delay
                setTimeout(afterCloseChild, 500);
              }
            } catch (err) {
              scope.connectButton = true;
              console.log('Window error', err);
              handleConnectionUnsuccessful();
            }

            function handleConnectionUnsuccessful() {
              if (!scope.contentSourceData.contentSource.editing && !scope.contentSourceData.authorization.connection_status) {
                // Connection unsuccessful. Handle the case when the window is closed without a successful connection
                scope.contentSourceService.deleteUnauthenticatedCS(id, scope.contentSourceData.contentSource.index_name,  scope.contentSourceData.contentSource.content_source_type_id).then((res)=>{
                  scope.isCSConnected = false;
              })
              }
              var toastOptionsOauthError: ToastOptions = {
                title: 'Connection Failed!',
                msg: "Please retry",
                showClose: true,
                timeout: 3000,
                theme: 'default'
              };
              scope.connectingCS = false;
              scope.toastyService.error(toastOptionsOauthError);
              scope.isRequired= true;
              scope.validateForm();
            }
          })();
        }
      }
      else {
        this.isCSConnected = true;
        var toastOptions: ToastOptions = {
          title: "Connected successfully!",
          msg: "Content source connected successfully.",
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.success(toastOptions);
        this.selectedIndex = 1;
        this.isLinear = false;
        this.connectingCS = false;
        this.isSubmitted = true;
        this.isRequired= false
        this.validateForm()
      }
      // this.isRequired= false
      // this.validateForm()
    }, error => {
      var toastOptions: ToastOptions = {
        title: 'Connection Failed!',
        msg: "Please retry",
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.connectingCS = false;
      this.toastyService.error(toastOptions);
      this.isRequired= true
      this.validateForm()
    });
    return;
  }

  addUniqueContentSource() {
    this.isLinear = this.editMode ? false : true;

    if (this.editMode && this.uniqueCSName.toLowerCase().trim() === this.contentSourceData.contentSource.label.toLowerCase().trim()) {
      this.uniqueContentSource = true;

    } else if (this.userContentSources.find(x => x.label.toLowerCase().trim() === this.contentSourceData.contentSource.label.toLowerCase().trim())) {
      this.uniqueContentSource = false;
      var toastOptions: ToastOptions = {
        title: "Cannot save content source!",
        msg: "Same name already exists",
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.error(toastOptions);
      this.isLinear = true;
    }
    else {
      this.uniqueContentSource = true;

    }

  }

  switchTab(tabName) {
    this.currentTab = tabName;
    this.validateForm()
  }

  addUserContentSource(tab) {
    this.contentSourceData.tabName = tab;
    this.ContentSourcesComponent.toggleChildLoading();
    if (CONTENT_SOURCE_TYPE.stackOverflow == this.csTypeId)
      this.stackoverflowcrawlerservice.insertTagsForStackoverflow(
        this.contentSourceData.authorization.content_source_id,
        this.selectedTag
      ).then(result => this.ContentSourcesComponent.addUserContentSource(this.contentSourceData));
    else
      this.ContentSourcesComponent.addUserContentSource(this.contentSourceData);
  }

  saveObjectsAndFields(objectsAndFields) {
    this.contentSourceData.objectsAndFields = objectsAndFields;
  }
  cancel() {
    this.reset.emit();
  }
  getSelectedIndex(index) {
    this.selectedIndex = index.selectedIndex;
  }

  addSelectedTag() {
    if (this.Tag == "") {
      var toastOptions: ToastOptions = {
        title: "Empty Tag Name!",
        msg: "Tag Name cannot be Empty.",
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.error(toastOptions);

    }
    else if (this.selectedTag.includes(this.Tag)) {
      var toastOptions: ToastOptions = {
        title: "Duplicate Tag Name!",
        msg: "Tag Name is Already present.",
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.error(toastOptions);
    }
    else if (!this.suggestedOpt.includes(this.Tag)) {
      var toastOptions: ToastOptions = {
        title: "Invalid Tag Name!",
        msg: "Please Select valid Tag Name from dropdown.",
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.error(toastOptions);
    }
    else {
      this.selectedTag.push(this.Tag);
      this.Tag = "";
    }
  }

  showSuccessToast() {
      var toastOptions: ToastOptions = {
        title: "Connected successfully!",
        msg: "Content source connected successfully.",
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
  }

  onSearchChange(x: string) {
    this.stackoverflowcrawlerservice.getInname(x, this.contentSourceData.contentSource.id).then(result => {
      this.suggestedOpt = result;
    })
  }

  selectedTag = [];

  // By Folders 

  getSelectedFiles(tab) {
    /**
     * On Save
     * get all files and folders from the selected area
     * to save into database
     */

    if (this.isJstreeLoaded == true || this.isSharedTreeLoaded == true) {
      var saveToDb = [];
      this.alreadyNode.forEach(x => {
        const divIndex = this.getDivData.find(o => o.id === x.spaceId);
        const sharedDataIndex = this.getSharedData.find(o => o.id === x.spaceId);
        if (!divIndex && !sharedDataIndex) {
          let obj = {};
          obj = { "id": x.spaceId, "text": x.spaceName, "path": x.spaceUrl, "data": { "IsShared": x.folderType == 'myfile' ? 0 : 1, "type": x.spaceType }, "id_path": x.spaceKey }
          if (x.folderType && CONTENT_SOURCE_TYPE.drive) {
            obj['original'] = { driveId: x.folderType };
          }
          saveToDb.push(obj);
        }
      })
      var entriesToSavedInDb = [];
      entriesToSavedInDb = entriesToSavedInDb.concat(saveToDb);
      entriesToSavedInDb = entriesToSavedInDb.concat(this.getDivData);
      entriesToSavedInDb = entriesToSavedInDb.concat(this.getSharedData);
      this.contentSourceService.getFoldersId(entriesToSavedInDb, this.contentSourceData.contentSource.id, this.contentSourceData.contentSource.content_source_type_id).then(result => {
        this.addUserContentSource(tab);
      });
    }
    else {
      this.addUserContentSource(tab);
    }
  }

  selectAll(folderType) {
    if (folderType === "mine") {
      this.AuthTreeComponent.isTreedRawn((data) => {
        if (data) {
          this.AuthTreeComponent.selectAllNode();
        }
      })
    } else if (folderType === "shared") {
      this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
        if (data) {
          this.AuthSharedTreeComponent.selectAllNode();
        }
      })
    }
    else if (folderType === "drive") {
      this.sharedDriveComponent.isSharedDriveDrawn((data) => {
        if (data) {
          this.sharedDriveComponent.selectAllNode();
        }
      })
    }
    // else {
    //   this.sharedDriveComponent.isTreedRawn((data) => {
    //     if (data) {
    //       this.sharedDriveComponent.selectAllNode();
    //     }
    //   })
    // }
  }

  deSelectAll(folderType) {
    if (folderType === "mine") {
      this.AuthTreeComponent.isTreedRawn((data) => {
        if (data) {
          this.AuthTreeComponent.deSelectAllNode();
        }
      })
    }
    else if (folderType === "shared") {
      this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
        if (data) {
          this.AuthSharedTreeComponent.deSelectAllNode();
        }
      })
    }
    else if (folderType === "drive") {
      this.sharedDriveComponent.isSharedDriveDrawn((data) => {
        if (data) {
          this.sharedDriveComponent.deSelectAllNode();
        }

      })
    }
    // else {
    //   this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
    //     if (data) {
    //       this.AuthSharedTreeComponent.deSelectAllNode();
    //     }
    //   })
    // }

  }

  checkForRenamedFiles = (data) => {
    this.alreadyNode.forEach(al => {
      data = data.filter(div => {
        return (div.id !== al.spaceId || (div.id === al.spaceId && div.text !== al.spaceName));
      });
    });

    return data;
  }

  getintoDiv(isRemove) {
    /**
     * get tree nodes to the selected area
     */
    this.AuthTreeComponent.isTreedRawn((data) => {
      if (data) {
        this.AuthTreeComponent.getTreeJson((err, data1) => {
          this.getDivData = this.checkForRenamedFiles(data1);
          this.checkInPath(this.getDivData);
          if (!isRemove) {
            this.AuthTreeComponent.removeSelectedNode((list) => {
              this.alreadyNode = this.alreadyNode.filter(x => list.indexOf(x.spaceId) == -1);
            })
          }
        })
      }
    })
    this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
      if (data) {
        this.AuthSharedTreeComponent.getTreeJson((err, data2) => {
          this.getSharedData = this.checkForRenamedFiles(data2);
          this.checkInPath(this.getSharedData);
          if (!isRemove) {
            this.AuthSharedTreeComponent.removeSelectedNode((list1) => {
              this.alreadyNode = this.alreadyNode.filter(x => list1.indexOf(x.spaceId) == -1);
            })
          }
        })
      }
    });

    this.sharedDriveComponent.isSharedDriveDrawn((data) => {
      if (data) {
          this.sharedDriveComponent.getTreeJson((err, data3) => {
            data3 = this.checkForRenamedFiles(data3);
            // this.getSharedData = [... this.getSharedData, ...data3];
            this.getSharedData = this.getSharedData ? this.getSharedData.concat(data3) : data3;

            this.checkInPath(this.getSharedData);
            if (!isRemove) {
              this.sharedDriveComponent.removeSelectedNode((list1) => {
                this.alreadyNode = this.alreadyNode.filter(x => list1.indexOf(x.spaceId) == -1);
              })
            }
          });
      }
  });

    if (!isRemove)
      this.ContentSourcesComponent.changeInRulesTab = true;
  }

  checkInPath(data) {
    /**
     *
     * Input data is the selected nodes from the Tree Component which are not present in already Nodes
     * If a node in tree is selected then filter out AlreadyNodes to remove all entries which contain this node in its path
     * To remove all child entries from already Node because now we have selected parent node
     *
    */
    var dataArray = [];

    //To get id array
    data.forEach(x => {
      dataArray.push(x.id);
    })

    //get node saved in array
    var alreadyNodeArray = [];
    var parentArrayDbFolders = [];

    this.alreadyNode.forEach(x => {
      var arr = x.spaceKey.split('/');
      alreadyNodeArray.push(x.spaceId);
      parentArrayDbFolders.push(arr);
    });

    //total array contains path nodes of nodes saved in array.
    var totalArray = [].concat.apply([], parentArrayDbFolders);
    totalArray = totalArray.filter(x => {
      return !alreadyNodeArray.includes(x);
    });

    //those ids from PAth Array (total array) which are present in data array.
    var foldersToRemove = [];
    foldersToRemove = totalArray.filter(x => {
      return dataArray.includes(x);
    });

    // Now remove all entries from `alreadyNodes` in which path contains any entry from `foldersToRemove` array
    this.alreadyNode = this.alreadyNode.filter(x => {
      var p = (x.spaceKey)?x.spaceKey.split('/'):[];
      var zz = !p.some(y => foldersToRemove.includes(y));
      return zz;
    })
    let unselected = [];
    // remove all entries from `alreadyNodes` whose any path node is unslsected from the jstree.
    this.AuthTreeComponent.isTreedRawn((flag) => {
      if (flag) {
        this.AuthTreeComponent.removeUnselectedNodes((data1) => {
          data1.forEach(x => unselected.push(x.id));
          this.alreadyNode = this.alreadyNode.filter(x => {
            var p =(x.spaceKey)?x.spaceKey.split('/'):[];
            var zz = !p.some(y => unselected.includes(y));
            return zz;
          })
        })
      }
    });
    if (this.csTypeId ===  CONTENT_SOURCE_TYPE.drive) {
      this.AuthSharedTreeComponent.isSharedTreeDrawn((f2) => {
        if (f2) {
          this.AuthSharedTreeComponent.removeUnselectedNodes((data2) => {
            data2.forEach(x => unselected.push(x.id));
            this.alreadyNode = this.alreadyNode.filter(x => {
              let p = (x.spaceKey)?x.spaceKey.split('/'):[];
              let unselectedNodes = !p.some(y => unselected.includes(y));
              return unselectedNodes;
            });
          });
        }
      });

      this.sharedDriveComponent.isSharedDriveDrawn((f3) => {
        if (f3) {
          this.sharedDriveComponent.removeUnselectedNodes((data3) => {
            data3.forEach(x => unselected.push(x.id));
            this.alreadyNode = this.alreadyNode.filter(x => {
              let p = (x.spaceKey)?x.spaceKey.split('/'):[];
              let unselectedNodes = !p.some(y => unselected.includes(y));
              return unselectedNodes;
            });
          });
        }
      });
    }

  }

  removeFromTree(folderId, isShared, isAlready) {
    /**
     * Called when we clcik cross in the selected area for any node
     * It delete entry from alreadyNodes( spaces from database)
     * It also uncheck that particular node from the jstree
     */

    switch (this.csTypeId) {
      case CONTENT_SOURCE_TYPE.aem:
        if (isShared == 1) {
          this.AuthTreeComponent.isTreedRawn((data) => {
            if (data) {
              this.AuthTreeComponent.deSelectNode(folderId, () => {
                this.getintoDiv(1);
              })
            }
          });
          if (isAlready == '1') {
            this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
          }
        }
        else {
          this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
            if (data) {
              this.AuthSharedTreeComponent.deSelectNode(folderId, () => {
                this.getintoDiv(1);
              });
            }
          });
          if (isAlready == '1') {
            this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
          }
        }
        break;

      case CONTENT_SOURCE_TYPE.dropbox:
        if(isShared && isShared.length > 1){
          isShared == 'myfile' ? isShared = 0: isShared = 1;     
        }
        if (isShared == 0) {
            this.AuthTreeComponent.isTreedRawn((data) => {
                if (data) {
                    this.AuthTreeComponent.deSelectNode(folderId, () => {
                        this.getintoDiv(1);
                    })
                }
            });
            if (isAlready == '1') {
                this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
            }
        } else {
            this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
                if (data) {
                    this.AuthSharedTreeComponent.deSelectNode(folderId, () => {
                        this.getintoDiv(1);
                    });
                }
            });
            if (isAlready == '1') {
                this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
            }
        }     
        break; 
      
      case CONTENT_SOURCE_TYPE.drive:
        if(isShared && isShared.length > 1){
          isShared == 'myfile' ? isShared = 0: isShared = 1;     
        }
        if (isShared == 0) {
            this.AuthTreeComponent.isTreedRawn((data) => {
                if (data) {
                    this.AuthTreeComponent.deSelectNode(folderId, () => {
                        this.getintoDiv(1);
                    })
                }
            });
            if (isAlready == '1') {
                this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
            }
        } else {
            this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
                if (data) {
                    this.AuthSharedTreeComponent.deSelectNode(folderId, () => {
                        this.getintoDiv(1);
                    });
                }
            });
            if (isAlready == '1') {
                this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
            }
        }     
        if (isShared == 1) {
          this.AuthTreeComponent.isTreedRawn((data) => {
              if (data) {
                  this.AuthTreeComponent.deSelectNode(folderId, () => {
                      this.getintoDiv(1);
                  })
              }
          });
          if (isAlready == '1') {
              this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
          }
      }
      else {
          this.AuthSharedTreeComponent.isSharedTreeDrawn((data) => {
              if (data) {
                  this.AuthSharedTreeComponent.deSelectNode(folderId, () => {
                      this.getintoDiv(1);
                  });
              }
          });
          // todo
          this.sharedDriveComponent.isSharedDriveDrawn((data) => {
              if (data) {
                  this.sharedDriveComponent.deSelectNode(folderId, () => {
                      this.getintoDiv(1);
                  });
              }
          });
          if (isAlready == '1') {
              this.alreadyNode = this.alreadyNode.filter(x => x.spaceId != folderId);
          }
      }
        break; 
      }
  }


  remove(fruit: any): void {
    let index = this.selectedTag.indexOf(fruit);

    if (index >= 0) {
      this.selectedTag.splice(index, 1);
    }
  }

  add(event: MatChipInputEvent): void {
    let input = event.input;
    let value = event.value;

    // Add our fruit
    if ((value || '').trim()) {
      this.selectedTag.push({ name: value.trim() });
    }

    // Reset the input value
    if (input) {
      input.value = '';
    }
  }

  nextPrevButtonOperations(event) {
    if (event === 'previous' && this.selectedIndex !== 0) {
      this.isLinear = false;
      this.selectedIndex = this.selectedIndex - 1;
    } else if (event === 'next' && this.selectedIndex !== 2 && this.uniqueContentSource && this.nextDisabled) {
      this.isLinear = false;
      this.selectedIndex = this.selectedIndex + 1
    }
  }

  toggleFolder(current) {
    if (current !== this.folderButton) {
        // if (this.csTypeId === CONTENT_SOURCE_TYPE.drive) {
            this.folderButton = current;
        // } else {
        //     this.folderButton = (this.folderButton === 1) ? 2 : 1;
        // }
    }
}

  switchAuthenticationType() {
    this.selectedAuthenticationMethod = this.selectedAuthenticationTypes[0] || this.contentSourceData.authorization.authorization_type;
    this.clientCredsCheck();
    // if(this.selectedAuthenticationMethod == 'Api')
    //   this.connectButton = true;
    // else
    //   this.connectButton = false;

    if ((this.contentSourceData.authorization.authorization_type?this.contentSourceData.authorization.authorization_type.includes('No Authentication'):this.selectedAuthenticationTypes.includes('No Authentication'))) {
      if (!(this.contentSourceData.authorization.authorization_type?this.contentSourceData.authorization.authorization_type.includes('htaccess'):this.selectedAuthenticationTypes.includes('htaccess'))) {
        this.contentSourceData.authorization.authorization_type = 'No Authentication';
      }
    }

    this.contentSourceData.authorization.authorization_type = this.selectedAuthenticationTypes.join(',');
    this.multiStepForm = this.jsWeb && ( this.selectedAuthenticationMethod == 'Multi-Step Form') ;

    if(this.jsWeb) {
      if(this.multiStepForm) {
				this.multiFormSchemaForm = this.fb.group({
					multiFormSchema: this.fb.array([])
				});
				let multFormData = JSON.parse(this.contentSourceData.authorization.multi_form_auth || "[]");
				if (multFormData.length) {

					this.setMultiFormData(multFormData);

				} else {
					if( (this.multiFormSchemaForm.get('multiFormSchema') as FormArray).length == 0){
						this.addNewTab();
						this.addNewTab();
					}
				}
				this.selectedIndexVal = 0;
			} 
			else if (!this.isConnected) {
				this.multiFormSchemaForm.reset();
			}
    }

  this.clearUnselectedFields();
  }
  
  uploadCsv(event) {
    let fileList: FileList = event.target.files;
    const fileName = event.target.files[0].name;
    const fileExtension = fileName.toString().slice(fileName.lastIndexOf('.'));
    if(this.contentSourceData.contentSource.content_source_type_id === 69) {
    // const fileTypes = this.additionalFields[0].type;
    const fileTypes = this.additionalFields
      .filter(field => field.type)  
      .map(field => field.type)     
      .flat(); 

      let mimeTypes = {
        csv: "text/csv",
        json: "application/json",
        excel: ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
    };
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10 MB

    if (fileList.length > 0) {
      let file: File = fileList[0];

      if (file.size > MAX_FILE_SIZE) {
        this.toastyService.error({
            title: "File Size Exceeded",
            msg: `The file size exceeds the maximum limit of 10 MB.`,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        });
        return;
    }
      if ([".csv", ".json", ".xls" ,".xlsx"].indexOf(fileExtension) === -1) {
        var toastOptions: ToastOptions = {
          title: "Error uploading file",
          msg: `only csv,json and excel files are supported for uploading `,
          showClose: true,
          timeout: 3000,
          theme: 'default'
      };

      this.toastyService.error(toastOptions);
      return;
      }

      if (fileTypes.includes("csv") && fileExtension === ".csv" && file.type === mimeTypes.csv ){
        this.validateCsvFile(file).then(isValid => {
          if (isValid) {
              this.uploadFile(file);
              event.target.value = '';
          } else {
              this.toastyService.error({ title: "Invalid CSV", msg: `The CSV file is not valid. Please check the format.`, timeout: 3000 });
          }
      });

      }
      else if (fileTypes.includes("json") && fileExtension === ".json" && file.type === mimeTypes.json) {
        this.validateJsonFile(file).then(isValid => {
          if (isValid) {
              this.uploadFile(file);
              event.target.value = '';
          } else {
              this.toastyService.error({ title: "Invalid JSON", msg: `The JSON file is not valid.`, timeout: 3000 });
          }
      });

      }else if (fileTypes.includes("excel") && (fileExtension === ".xls" || fileExtension === ".xlsx") &&  mimeTypes.excel.includes(file.type)) {
              this.uploadFile(file);
      } else {
        this.toastyService.error({ title: "File Type is not valid", msg: `only csv & json files are allowed.`, timeout: 3000 });
        return;
      }
    }
  } else if (this.contentSourceData.contentSource.content_source_type_id === 47 ) {
    if (fileList.length) {
      if([".xml",".txt"].indexOf(this.fileName.toString().slice(-4)) == -1){
        var toastOptions: ToastOptions = {
          title: "Error uploading file",
          msg: `only txt & xml files are supported for uploading sitemap`,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
				return;
      }
      let completeFile: File = fileList[0];
      this.uploadFile(completeFile);
      event.target.value = '';
    }
  }
}

  validateCsvFile(file: File): Promise<boolean> {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const text = e.target.result.trim(); // Remove any leading/trailing whitespace
        const rows = text.split("\n").filter(row => row.trim() !== ""); // Remove empty rows
        if (rows.length < 2) {
          resolve(false);
          return;
        }
        const headerRow = rows[0].split(",").map(col => col.trim()); 
        if (headerRow.length < 2) {
          resolve(false);
          return;
        }
        resolve(true);
      };
  
      reader.readAsText(file);
    });
  }
  validateJsonFile(file: File): Promise<boolean> {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const text = e.target.result;
        try {
          const jsonData = JSON.parse(text);
        if (
          (Array.isArray(jsonData) && jsonData.length === 0) || // Empty array
          (typeof jsonData === "object" && Object.keys(jsonData).length === 0) // Empty object
        ) {
          resolve(false);
          return;
        }

          resolve(true);
        } catch (err) {
          resolve(false);
        }
      };
      reader.readAsText(file);
    });
  }



  uploadFile(file: File) {
    this.csvFile = file.name;

    let formData: FormData = new FormData();
    formData.append('uploadFile', file, this.csvFile);
    formData.append('csTypeId', this.contentSourceData.contentSource.content_source_type_id);

    this.contentSourceService.uploadCsvfile(formData).then(response => {
      if (response.code === 200) {
        this.urlFilePath = response.url;
        this.contentSourceData.contentSource.url = this.urlFilePath;
        this.isFileUploaded = true;
        this.contentSourceData.contentSource.isFileUploaded = this.isFileUploaded;
        this.ContentSourcesComponent.clientURLPattern= "";
        this.toastyService.success({ title: "File uploaded!", msg: `File uploaded successfully!`, timeout: 3000 });
      } else if (response.code == 106) {
        var toastOptions: ToastOptions = {
          title: "Error uploading file!",
          msg: `file has either unsupported extension or has special characters in it's name please verify and then upload`,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.warning(toastOptions);
      }
      else {
        var toastOptions: ToastOptions = {
          title: "There was a problem!",
          msg: `Error uploading file`,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };

        this.toastyService.error(toastOptions);
      }
    });
  }

  downloadUrlFile(data) {
    if (this.isFileUploaded && !this.downloadInProgress) {
      this.downloadInProgress = true;
      const fileName = this.contentSourceData.contentSource.url;
      const contentSourceId = this.contentSourceData.contentSource.id;

      this.contentSourceService.downloadUrlFile(fileName, contentSourceId).then(response => {
        if (response.statusCode === 200) {
          this.toastyService.success({ title: "File downloaded successfully", msg: "", timeout: 3000 });
        } else {
          this.toastyService.error({ title: "Download Failed", msg: "", timeout: 3000 });
        }
        this.downloadInProgress = false;
      });

    } else {
      this.toastyService.warning({ title: "Please wait", msg: "The download is already in progress.", timeout: 3000 });
    }
  }

  onRemoveUploadUrl(): void {
    this.isFileUploaded = false;
    this.csvFile = '';  
    this.ContentSourcesComponent.clientURLPattern = this.clientUrlPattern;
    this.contentSourceData.contentSource.isFileUploaded = this.isFileUploaded;
		this.contentSourceData.contentSource.url = '';
  }

  switchDropDownField(event, key){
    if (this.contentSourceData.contentSource.content_source_type_id===CONTENT_SOURCE_TYPE.salesforce){
      this.contentSourceData.contentSource[this.dropDownField.key] = event.value;
    } else {
      this.contentSourceData.authorization[this.dropDownField.key] = String(event.value);
    }
    this.clientCredsCheck(this.contentSourceData.authorization[this.dropDownField.key]);
    this.validateForm();
    this.cdr.detectChanges();
  }

  switchOrganizationType() {
    this.selectedOrganizationType = this.contentSourceData.authorization.organization_user_type;
  }

  validateForm() {
    const auth = this.contentSourceData.authorization;
    const contentSource = this.contentSourceData.contentSource;
    const authType = this.contentSourceData.authorization.authorization_type || this.selectedAuthenticationTypes;
  
    if (authType.includes('Basic')) {
      this.isFormValid = !!(auth.username && auth.password);
      if (!this.isFormValid) return;
    }
    if (this.clientCreds) {
      this.isFormValid = !!(auth.client_id && auth.client_secret);
      if (!this.isFormValid) return;
    }
  
    if (authType === 'Api' && !this.noApiKeyField) {
      this.isFormValid = !!auth.accessToken;
      if (!this.isFormValid) return;
    }
    if(auth.authorization_type === 'File'|| this.csvType){
      this.isFormValid = true;
      return;

    }
  
    if (this.additionalFields.length > 0) {
      this.isFormValid = this.additionalFields.every(field => {
        if (field.field == 'upload') {
          return !!contentSource.url;
        }
        if (field.required || !field.required) {
          return true;
        }
        if (field.devOnly && !this.searchUnifyUser) {
          return true;
        }
        if (field.authType) {
          if (authType.includes(field.authType)) {
            if (field.parentKey) {
              return !!this.contentSourceData[field.parentKey][field['field']];
            }
            return !!auth[field['field']];

          } else {
            return true;
          }
        }
        if (field.parentKey) {
          return !!this.contentSourceData[field.parentKey][field['field']];
        }
        return !!auth[field['field']];
      });
      if (!this.isFormValid) return;
    }

    if (authType.includes('htaccess')) {
      this.isFormValid = !!(auth.htaccessUsername && auth.htaccessPassword);
      if (!this.isFormValid) return;
    }

    if (authType.includes('Community user')) {
      this.isFormValid = !!(auth.username && auth.password);
      if (!this.isFormValid) return;
    }

    if (authType.includes('Api user')) {
      this.isFormValid = !!(auth.publicKey && auth.privateKey);
      if (!this.isFormValid) return;
    }

    if(this.contentSourceData.contentSource.content_source_type_id === CONTENT_SOURCE_TYPE.khorosAurora){
      if (!authType.includes('Client Credentials') && !authType.includes('OAuth')) {
        this.isFormValid = false;
        return;
      }
    }

    if (auth.authorization_type === 'Multi-Step Form') {
      this.isFormValid = !!(auth.loginlink);

      if (!this.isFormValid) {
        return;
      }

      const formArray = this.multiFormSchemaForm.get('multiFormSchema') as FormArray;

      for (const formGroup of formArray.controls) {
        const currentPageUrl = formGroup.get('currentPageUrl').value;
        if (currentPageUrl === '' || formGroup.get('currentPageUrl').status === 'INVALID') {
          this.isFormValid = false;
          return;
        }

        const fields = formGroup.get('fields') as FormArray;

        const fieldValues = fields.controls.map(control => control.get('value').value);

        if (fieldValues.length === 0 || fieldValues.some(value => value === '')) {
          this.isFormValid = false;
          return;
        }
      }
    }  
    this.isFormValid = true;
  }  

  handleFocus() {
    if(this.csvType){
      this.isRequired =false;
    }else{
      this.isRequired = true;
    }
    
  }

  isTypeSelected(type: string): boolean {
    return this.contentSourceData.authorization.authorization_type ? this.contentSourceData.authorization.authorization_type.includes(type) : this.selectedAuthenticationTypes.includes(type);
  }

  clearUnselectedFields(): void {
    if (!this.contentSourceData.authorization.authorization_type.includes('OAuth')) {
      this.contentSourceData.authorization.client_id = '';
      this.contentSourceData.authorization.client_secret = '';
    }
    if (this.multiSelectFields.length) {
      this.multiSelectFields.forEach((fieldGroup) => {
        const { type, fields } = fieldGroup;
        if (!this.contentSourceData.authorization.authorization_type.includes(type)) {
          // Clear all fields of the unselected type
          fields.forEach((field) => {
            const fieldKey = field.ngModel; 
            if (this.contentSourceData.authorization[fieldKey] !== undefined) {
              this.contentSourceData.authorization[fieldKey] = ''; 
            }
          });
        }
      });
    }
  }

  viewAuthenticatedUser() {
    this.authenticatingCS = true;
    this.contentSourceService.getCsConnectedUser({mysqlId: this.contentSourceData.contentSource.id, query: this.connectedContentSourceAuthData}).then((res)=>{
      if (!res.status || res.status === false) {
        this.authenticatingCS = false;
        this.authError = true;
        return;
      }
      this.authenticatedUser=res.data;
      this.authenticatingCS = false;

    })
  }

  handlePermissions(event){
    this.contentSourceData.authorization.changeScopePermissions = event.checked;
  }

  ngOnDestroy() {
    this.ContentSourcesComponent.resetIndexingSelection();
    this.ContentSourcesComponent.addingContentSource = false;
  }

  showAuthenticatedUserButtonOrNot() {
    if (this.csTypeId === CONTENT_SOURCE_TYPE.lithium) {
      if (this.selectedAuthenticationTypes.length && ( 
        this.selectedAuthenticationTypes.includes('OAuth')
      )) {
        return true;
      } 
    } else if (this.csTypeId === CONTENT_SOURCE_TYPE.khorosAurora) {
      if (this.selectedAuthenticationTypes.length && ( 
        this.selectedAuthenticationTypes.includes('OAuth')
      )) {
        return true;
      } 
    } else if (this.csTypeId === CONTENT_SOURCE_TYPE.vanilla) {
        return true;
    } else if (this.csTypeId === CONTENT_SOURCE_TYPE.monday) {
      return true;
    } else if (this.csTypeId === CONTENT_SOURCE_TYPE.freshservice) {
      return true;
    }else if (this.csTypeId === CONTENT_SOURCE_TYPE.freshdesk) {
      return true;
    } else if(
      (!this.selectedAuthenticationTypes.includes('No Authentication') && 
      !this.selectedAuthenticationTypes.includes('Basic'))) {
          return true;
    }
    return false;
  }


  // Function to extract the key path dynamically
  getToggleKey(curToggle: any): string {

    if (!curToggle || typeof curToggle !== 'object') {
      return "";
    }

    if (typeof curToggle.field === 'string') {
      return `authorization.${curToggle.field}`; // Handle simple string case
    }

    function extractPath(field: any, path: string[] = []): string {
      if (field && typeof field === 'object' && Object.keys(field).length > 0) {
        const key = Object.keys(field)[0];
        path.push(key);
        return extractPath(field[key], path);
      }
      return path.join('.');
    }

    const temp = extractPath(curToggle.field);
    return temp; // Avoid redundant function call
  }


  // Function to get a value from a deeply nested object
  getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((acc, key) => acc && acc[key], obj);
  }

  // Function to set a value in a deeply nested object
  setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    keys.reduce((acc, key, index) => {
      if (index === keys.length - 1) {
        acc[key] = value;
      }
      return acc[key] = acc[key] || {};
    }, obj);
  }

  // Function called when toggle state changes
  onToggleChange(curToggle: any, event: any) {
    const keyPath = this.getToggleKey(curToggle);
    this.setNestedValue(this.contentSourceData, keyPath, event.checked);
  }
}
