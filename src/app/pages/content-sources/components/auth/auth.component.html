<ng2-toasty></ng2-toasty>
<mat-horizontal-stepper [linear]="isLinear" #stepper="matHorizontalStepper" (selectionChange)="getSelectedIndex($event)"
    [selectedIndex]="selectedIndex">
    <mat-step [completed]="authConnectForm.valid && uniqueContentSource && isSubmitted">
        <form role="form" #authConnectForm="ngForm" autocomplete="off">
            <ng-template matStepLabel>Authentication</ng-template>
            <div class="cls-jiveCred">
                <div class="auth-wrapper">
                    <div class="table-left">
                        <span class="table-left-data">
                            <mat-form-field>
                                <input matInput pattern="{{ContentSourcesComponent.clientNamePattern}}" placeholder="Name"
                                    name="clientName" #clientName="ngModel"
                                    [(ngModel)]="contentSourceData.contentSource.label"
                                    (ngModelChange)="addUniqueContentSource()" required
                                    (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                        <span style="width: 20px;display: inline-block;">
                            <span *ngIf="clientName.invalid && (clientName.dirty || clientName.touched)">
                                <span *ngIf="clientName.errors?.required">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        matTooltip="Name is required" matTooltipPosition="below"
                                        aria-label="Button that displays a tooltip in various positions">
                                </span>
                                <span *ngIf="clientName.errors?.pattern">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="special characters are not allowed">
                                </span>

                            </span>
                        </span>
                    </div>
                    <div class="table-right" *ngIf="csWithoutClientUrls">
                        <span class="table-right-data">
                            <mat-form-field>
                                <input  matInput pattern="{{ContentSourcesComponent.clientURLPattern}}" 
                                        placeholder="Client Url" name="clientUrl"
                                        #clientUrl="ngModel" 
                                        [(ngModel)]="contentSourceData.contentSource.url"
                                        (ngModelChange)="clientUrls()"
                                            required [disabled]="readonlyClientUrl"
                                            (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                        <span style="width: 20px;display: inline-block;">
                            <span *ngIf="(clientUrl.invalid && (clientUrl.dirty || clientUrl.touched)) || !validUrl">
                                <span *ngIf="clientUrl.errors?.required && validUrl">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Client Url is required">
                                </span>
                                <span *ngIf="clientUrl.errors?.pattern || !validUrl">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Blank space and incorrect URL are not allowed">
                                </span>
                            </span>
                        </span>
                    </div>
                    <div class="table-right" *ngIf="onPremCsType">
                        <span class="table-left-data">
                            <mat-form-field>
                                <input matInput
                                    pattern="^(?:https?:\/\/(?:www\.)?|www\.)[a-z0-9]+(?:[-.][a-z0-9]+)*\.[a-z]{2,5}(?::[0-9]{1,5})?(?:\/\S*)?$"
                                    placeholder="Searchunify Index Url" name="indexUrl" #indexUrl="ngModel"
                                    [(ngModel)]="contentSourceData.contentSource.searchunifyIndexUrl" required
                                    (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                    </div>
                    <div class="table-left" *ngIf="organizationTypes.length">
                        <span class="table-left-data">
                            <mat-form-field>
                                <mat-select placeholder="Organization Type" name="organizationType"
                                    [(ngModel)]="contentSourceData.authorization.organization_user_type"
                                    (selectionChange)="switchOrganizationType(); validateForm(); handleFocus()">
                                    <mat-option *ngFor="let organizationType of organizationTypes"
                                        value="{{organizationType.id}}">{{organizationType.label}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </span>
                    </div>
                    <div *ngIf='authenticationMethods.length > 1 && multiSelectAuth' class="table-right">
                        <span class="table-right-data">
                            <mat-form-field>
                                <mat-select placeholder="Authentication Type" name="authenticationMethod"
                                    [(ngModel)]="selectedAuthenticationTypes"
                                    (selectionChange)="switchAuthenticationType(); validateForm(); handleFocus()" multiple>
                                    <mat-option *ngFor="let authenticationMethod of authenticationMethods"
                                        value="{{authenticationMethod.id}}">{{authenticationMethod.label}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </span>
                    </div>
                    <div *ngIf='authenticationMethods.length > 1 && !multiSelectAuth' class="table-right">
                        <span class="table-right-data">
                            <mat-form-field>
                                <mat-select placeholder="Authentication Type" name="authenticationMethod"
                                    [(ngModel)]="selectedAuthenticationTypes[0]"
                                    (selectionChange)="switchAuthenticationType(); validateForm(); handleFocus()">
                                    <mat-option *ngFor="let authenticationMethod of authenticationMethods"
                                        value="{{authenticationMethod.id}}">{{authenticationMethod.label}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </span>
                    </div>
                    <div class="table-left" *ngIf="csWithTenant">
                        <span class="table-left-data">
                            <mat-form-field>
                                <input matInput pattern="{{ContentSourcesComponent.clientNamePattern}}" placeholder="Tenant"
                                    name="tenantId" #tenantId="ngModel"
                                    [(ngModel)]="contentSourceData.authorization.tenantId"
                                    (ngModelChange)="addUniqueContentSource()" required>
                            </mat-form-field>
                        </span>
                        <span style="width: 20px;display: inline-block;">
                            <span *ngIf="tenantId.invalid && (tenantId.dirty || tenantId.touched)">
                                <span *ngIf="tenantId.errors?.required">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        matTooltip="Name is required" matTooltipPosition="below"
                                        aria-label="Button that displays a tooltip in various positions">
                                </span>
                                <span *ngIf="tenantId.errors?.pattern">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Blank spaces and special characters are not allowed">
                                </span>

                            </span>
                        </span>
                    </div>
                    <div class="table-left">
                        <span class="table-left-data">
                            <mat-form-field>
                                <mat-select placeholder="Language" (selectionChange)="languageChange($event)"
                                    name="language" [(ngModel)]="contentSourceData.language"
                                    [value]="contentSourceData.language" multiple>
                                    <mat-checkbox class="mat-option languageCheckbox" [checked]="languageAllChecked"
                                        (change)="selectAllLanguages($event)" [indeterminate]="halfChecked">All
                                    </mat-checkbox>
                                    <mat-option *ngFor="let lang of languages" value="{{lang.code}}">
                                        {{lang.label}}-{{lang.code}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </span>
                    </div>
                    <div class="table-left" *ngIf="dropDownField.key">
                        <span class="table-left-data">
                            <mat-form-field>
                                <mat-select placeholder="{{dropDownField.label}}" name="dropDownField" 
                                    [(ngModel)]="selectedDropDownField"
                                    [value]="selectedDropDownField"
                                    (selectionChange)="switchDropDownField($event)">
                                    <mat-option *ngFor="let opt of dropDownField.options" value="{{opt.value}}" >
                                        {{opt.label}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </span>
                    </div>
                    <div *ngIf="contentSourceData.authorization.authorization_type=='Basic'" class="table-left">
                        <span class="table-right-data">
                            <mat-form-field>
                                <input matInput pattern="^[^\s].+[^\s]$" placeholder="{{usernamePlaceholder}}"
                                    name="clientUsername" #clientUsername="ngModel"
                                    [(ngModel)]="contentSourceData.authorization.username" readonly
                                    onfocus="this.removeAttribute('readonly')"[required]="isRequired" (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                        <span style="width: 20px;display: inline-block;">
                            <span
                                *ngIf="clientUsername.invalid && (clientUsername.dirty || clientUsername.touched)">
                                <span *ngIf="clientUsername.errors?.required">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Username is required">
                                </span>
                                <span *ngIf="clientUsername.errors?.pattern">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Blank spaces are not allowed">
                                </span>
                            </span>
                        </span>
                    </div>
                    <div *ngIf="contentSourceData.authorization.authorization_type=='Basic'" class="table-right">
                        <span class="table-left-data">
                            <mat-form-field>
                                <input matInput autocomplete="new-password" type="password" placeholder="{{passwordPlaceholder}}" name="password"
                                    #password="ngModel" [(ngModel)]="contentSourceData.authorization.password"
                                    [required]="isRequired" (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                        <span style="width: 20px;display: inline-block;">
                            <span *ngIf="password.invalid && (password.dirty || password.touched)">
                                <span *ngIf="password.errors?.required">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Password is required">
                                </span>
                            </span>
                        </span>
                    </div>
                    <div *ngIf="clientCreds || showClientId"
                        class="table-left">
                        <span class="table-left-data">
                            <mat-form-field>
                                <input matInput pattern="^[^\s].+[^\s]$" type=text placeholder= "{{(contentSourceData.contentSource.content_source_type_id == '11' && selectedAuthenticationMethod == 'Api')? 'client UserName' : 'client Id'}}" name="clientId"
                                    #clientId="ngModel" [(ngModel)]="contentSourceData.authorization.client_id"
                                    readonly
                                    onfocus="this.removeAttribute('readonly')" [required]="isRequired" (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                        <span style="width: 20px;display: inline-block;">
                            <span *ngIf="clientId.invalid && (clientId.dirty || clientId.touched)">
                                <span *ngIf="clientId.errors?.required">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Client ID is required">
                                </span>
                                <span *ngIf="clientId.errors?.pattern">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Blank spaces are not allowed">
                                </span>
                            </span>
                        </span>
                    </div>
                    <div *ngIf="clientCreds" 
                        class="table-right">
                        <span class="table-right-data">
                            <mat-form-field>
                                <input matInput autocomplete="new-password" type="password" placeholder="Client Secret" name="clientSecret"
                                    #clientSecret="ngModel"
                                    [(ngModel)]="contentSourceData.authorization.client_secret" [required]="isRequired" (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                        <span style="width: 20px;display: inline-block;">
                            <span *ngIf="clientSecret.invalid && (clientSecret.dirty || clientSecret.touched)">
                                <span *ngIf="clientSecret.errors?.required">
                                    <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                        title="Client secret is required">
                                </span>
                            </span>
                        </span>
                    </div>

                                            <!-- Dynamic field  -->
                    <ng-container *ngFor="let fieldGroup of multiSelectFields">
                        <!-- Check if the current authorization_type is selected -->
                        <ng-container *ngIf="isTypeSelected(fieldGroup.type)">
                            <div *ngFor="let field of fieldGroup.fields" [class]="field.type === 'password' ? 'table-right' : 'table-left'">
                                <span [class]="field.type === 'password' ? 'table-right-data' : 'table-left-data'">
                                    <mat-form-field>
                                        <input matInput [autocomplete]="field.autocomplete || 'off'" [type]="field.type"
                                            [placeholder]="field.label" [name]="field.name" #key="ngModel"
                                            [(ngModel)]="contentSourceData.authorization[field.ngModel]" [pattern]="field.pattern || null"
                                            [required]="field.required" readonly onfocus="this.removeAttribute('readonly')"
                                            (input)="validateForm()" (focus)="handleFocus()" />
                                    </mat-form-field>
                                </span>
                                <span style="width: 20px; display: inline-block;">
                                    <span *ngIf="key.invalid && (key.dirty || key.touched)">
                                        <span *ngIf="key.errors?.required">
                                            <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                                title="{{ field.label }} is required" />
                                        </span>
                                        <span *ngIf="key.errors?.pattern">
                                            <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                                title="Invalid input for {{ field.label }}" />
                                        </span>
                                    </span>
                                </span>
                            </div>
                        </ng-container>
                    </ng-container>

                    <div class="table-left" *ngIf="contentSourceData.authorization.authorization_type == 'Api' && !noApiKeyField">
                        <span class="table-left-data">
                            <mat-form-field>
                                <input matInput placeholder="{{(contentSourceData.contentSource.content_source_type_id == '11' && selectedAuthenticationMethod == 'Api')? 'Client Api' : 'API Key'}}" name="key" #key="ngModel"
                                    [(ngModel)]="contentSourceData.authorization.accessToken" readonly
                                    onfocus="this.removeAttribute('readonly')" [required]="isRequired" (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                        </span>
                    </div>
                    <div class="table-left" 
                        *ngFor="let curField of additionalFields;let i=index">
                        <span class="table-left-data" *ngIf="curField.field != 'upload'">
                            <mat-form-field>
                                <input matInput placeholder="{{curField.label}}" name="{{curField.field}}" #key="ngModel"
                                    [(ngModel)]="contentSourceData.authorization[curField['field']]" readonly
                                    onfocus="this.removeAttribute('readonly')" [required]="isRequired && !csvType" (input)="validateForm()" (focus)="handleFocus()">
                            </mat-form-field>
                            <div  *ngIf="curField.infoText" id="info">
                                Enter the field name to be used as the primary key for controlling duplicates.
                              </div>
                        </span>
                        <span *ngIf="curField.field == 'upload'" style="display: flex;padding-bottom: 18px;">
                            <span class="table-left-data">
                                <mat-form-field>
                                    <input matInput placeholder="Enter file Url or Upload Xlsx/CSV/Json file" name="url" #url="ngModel"
                                        pattern="{{ContentSourcesComponent.clientURLPattern}}" [(ngModel)]="contentSourceData.contentSource.url"
                                        [readonly]="isFileUploaded" required>
                                </mat-form-field>
                            </span>
                            <span *ngIf="!isFileUploaded" style="width: 20px; display: inline-block;">
                                <span *ngIf="!isFileUploaded">
                                    <label for="upload">
                                        <span class="material-icons" style="cursor: pointer;">
                                            publish
                                        </span>
                                    </label>
                                </span>
                        
                                <span *ngIf="!isFileUploaded">
                                    <span *ngIf="url.invalid && (url.dirty || url.touched)">
                                        <span *ngIf="url.errors?.pattern">
                                            <img src="assets/img/form-error.svg" class="form-invalid-exclamation" alt=""
                                                matTooltip="Blank space and incorrect URL are not allowed" matTooltipPosition="below"
                                                aria-label="Blank space and incorrect URL are not allowed">
                                        </span>
                                    </span>
                                </span>
                            </span>
                            <span *ngIf="isFileUploaded " style="display: flex; flex-direction: column; cursor: pointer;">
                                <span class="material-icons" (click)="downloadUrlFile($event)" style="transform: rotate(180deg);">
                                    publish
                                </span>
                                <span class="material-icons" (click)="onRemoveUploadUrl()" style="cursor: pointer;">
                                    close
                                </span>
                            </span>
                            <div>
                                <form enctype="multipart/form-data">
                                    <input id="upload" type="file" (change)="uploadCsv($event)" multiple="" style="display:none;">
                                </form>
                            </div>
                        </span>

                    </div>
                    <div class="table-right" *ngIf = "isCSConnected && csTypeId == 17">
                        <span class="table-right-data update-button">
                                <mat-checkbox class="languageCheckbox alignCheckbox" name= "changeScopePermissions"  [value]="contentSourceData.authorization.changeScopePermissions"  #key="ngModel" [(ngModel)]="contentSourceData.authorization.changeScopePermissions" (change)="handlePermissions($event)"> Update Permission
                                </mat-checkbox>                                                                   
                            <span>
                                <i class="fa fa-info-circle tool-tip-info update-permission-info" mat-raised-button matTooltipPosition="right"
                                matTooltip="Allows you to update the permission settings for your YouTube Account."></i>
                            </span>
                        </span>

                    </div>
                </div>
                <div class="inline-block">
                  <!-- connect button -->
                    <button *ngIf="!isCSConnected && !connectingCS && !editMode"
                        [disabled]="!authConnectForm.form.valid || !connectButton || !uniqueContentSource || !validUrl || !isFormValid"
                        class="button-add-content" type="button" (click)="connectContentSource()">
                        <img [ngClass]="{'connected': authConnectForm.form.valid, 'connect': !authConnectForm.form.valid}">Connect
                    </button>

                      <!-- connected button -->
                    <button *ngIf="(isCSConnected) && !connectingCS" class="button-add-content-connected connected-btn mr-1">
                        <img src="assets/img/connected-icon.svg" alt="">                      
                        Connected
                    </button>

                      <!-- Reconnect button -->
                      <button *ngIf="(isCSConnected || editMode) && !connectingCS"
                        [ngClass] = "{'enableConnectButton': authConnectForm.form.valid}"
                        [disabled]="!authConnectForm.form.valid || !isFormValid"
                        class="button-add-content reconnect-btn" type="button" (click)="connectContentSource()">
                        <img [ngClass]="{'connected': authConnectForm.form.valid, 'connect': !authConnectForm.form.valid}">Re-Connect
                    </button>

                    <!-- connecting button -->
                    <button *ngIf="connectingCS" class="connecting-btn connected-btn" style="color: #64DB66">
                    <img src="assets/img/connecting.svg"  class="connecting-icon">
                    <span style="color: #64DB66; margin-left: -6px;">Connecting</span>
                    </button>

                    <button id="btnnextContentSource" type="button" style="display: none;"
                        (click)="isLinear=false; nextDisabled=true;" class="buttonPrimary">Next</button>

                    <div *ngIf="isLoading_fetchObjects" class="small-loader">
                        <div class="caselist">
                            <div class="spinner">
                                <div class="bounce1"></div>
                                <div class="bounce2"></div>
                                <div class="bounce3"></div>
                            </div>
                        </div>
                    </div>
                 </div>
                </div>
                <div *ngIf="isCSConnected && showAuthenticatedUserButtonOrNot() && isCsUserDetails && !connectingCS " class="inline-block">
                    <button *ngIf="!authenticatingCS && !authenticatedUser && !authError" class="button-authenticate-content" (click)="viewAuthenticatedUser()">
                        View Authenticated User
                    </button>
                    <button *ngIf="authenticatingCS" class="connecting-btn connected-btn" style="color: #64DB66">
                        <img src="assets/img/connecting.svg"  class="connecting-icon">
                        <span style="font-family: Montserrat, sans-serif; font-weight: 500; font-size: 12px; color: #43425D; letter-spacing: 0px; line-height: 15px; margin-left: -6px;">Please wait...</span>
                    </button>
                    <div *ngIf = "authenticatedUser" style="padding: 8px 0px 16px 0px">
                        <span class="authenticated-user-content">User: {{authenticatedUser}}</span>
                    </div>
                    <div *ngIf="authError && !authenticatingCS && !authenticatedUser" style="color: #F44336; font-size: 12px; font: normal normal medium 12px/15px Montserrat;padding-top: 16px;padding-bottom: 11px">
                        Couldn't fetch user details. Please <a (click)="viewAuthenticatedUser()" style="text-decoration: underline; cursor: pointer;">try again</a>
                    </div>
                </div>
        </form>
        <div class="reauthenticate" *ngIf="contentSourceData.contentSource.is_paused && editMode">
            <img src="assets/img/warning-triangle.svg" style="width: 19px;
            height: 17px;">
            <span class="ml-1">You need to re-authenticate this content source to continue crawling new content.</span>
        </div>
        <div class="oauthReminder" *ngIf="!isOauthUpdated  && (csTypeId == 12 || csTypeId == 17)">
            <app-svg class="mt-1 alter-svg" name="auth-reminder" width="20.866" height="18.233"></app-svg>
            <span class="oauthReminderText"> You need to set up your Google OAuth to continue using this content source.
                <a class="oauthDocLink" target="_blank" [href]="csTypeId == 12
                        ? 'https://docs.searchunify.com/Content/Content-Sources/Google-Drive-Authentication.htm'
                        : 'https://docs.searchunify.com/Content/Content-Sources/YouTube.htm#OAuth20SetupPending'">
                    Click here to know more
                </a>
            </span>
        </div>
    </mat-step>
    <mat-step>
        <ng-template matStepLabel>Frequency</ng-template>
        <frequency [contentSourceData]="contentSourceData" [content_source_type_id]="csTypeId"
        (saveFrequency)="addUserContentSource('Frequency')">
        </frequency>
    </mat-step>
    <mat-step>
        <ng-template matStepLabel>Rules</ng-template>
        <div *ngIf="isRulesTabLoading" class="text-center">
            <div  class="small-loader">
                <div class="caselist">
                    <div class="spinner">
                        <div class="bounce1"></div>
                        <div class="bounce2"></div>
                        <div class="bounce3"></div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="!isRulesTabLoading">    
            <mat-tab-group class="sub-tabs" (selectedTabChange)="tabType($event, 'outerTab')">
                <mat-tab label="By Content Type">
                    <objects-fields *ngIf="selectedIndex == 2" [sfObjects]="contentSourceData.objectsAndFields"
                        [content_source_id]="contentSourceData.contentSource.id" [content_source_type_id]="csTypeId"
                        (saveObjectsAndFields)="saveObjectsAndFields($event)"></objects-fields>
                </mat-tab>
                <ng-container *ngIf="selectedIndex == 2 && contentSourceData.contentSource.id && !csWithoutPlaces">
                    <mat-tab label="{{spaceLabel}}">
                        <places-boards [placesAndBoards]="contentSourceData.spacesORboards" [csTypeId]="csTypeId"
                            [content_source_id]="contentSourceData.contentSource.id" (savePlaces)="savePlaces($event)">
                        </places-boards>
                    </mat-tab>
                </ng-container>
                <mat-tab label="By Tags" *ngIf="selectedIndex == 2 && contentSourceData.contentSource.id && stackCsType">
                    <form>
                        <mat-form-field class="example-full-width" style="margin-top: 26px;">
                            <input matInput placeholder="Search Tags here" [matAutocomplete]="auto" name="Tag" [(ngModel)]="Tag"
                                (input)="onSearchChange($event.target.value)">
                            <mat-autocomplete #auto="matAutocomplete">
                                <mat-option *ngFor="let opt of suggestedOpt" [value]="opt">
                                    <span>{{ opt }}</span>
                                </mat-option>
                            </mat-autocomplete>
                        </mat-form-field>
                        <button type="submit" value="Add Tag" class="buttonPrimary" (click)="addSelectedTag()"
                            style="margin:3px;">Add Tag</button>
                        <mat-card class="tags" *ngIf=selectedTag.length
                            style="background-color:#cacbf340; margin-top:20px; padding:15px;">
                            <mat-chip-list #chipList>
                                <mat-chip *ngFor="let tag of selectedTag" [selectable]="selectable" [removable]="removable"
                                    (removed)="remove(tag)" style="margin:5px;">
                                    {{tag}}
                                    <i *ngIf="removable" matChipRemove class="material-icons">
                                        close
                                    </i>
                                </mat-chip>
                            </mat-chip-list>
                        </mat-card>
                    </form>
                </mat-tab>
                <mat-tab label="By Folders" *ngIf="selectedIndex == 2 && contentSourceData.contentSource.id && folderBasedCS">
                    <mat-tab-group class="rules-folder-head" (selectedTabChange)="tabType($event, 'innerTab')" [selectedIndex]="0">
                        <mat-tab
                            [label]="contentSourceData.contentSource.content_source_type_id == 24 ? 'Mine Folders': 'My Folders' ">
                        </mat-tab>
                        <mat-tab label="Shared Folders">
                        </mat-tab>
                        <mat-tab *ngIf="contentSourceData.contentSource.content_source_type_id == 12" label="Shared Drives">
                        </mat-tab>
                    </mat-tab-group>
                    <div class="row rules-inner-folder">
                        <div [hidden]="folderButton != '1' || outerFolderButton == '0'"
                            class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
                            <div class="tree-folder">
                                <authTree #authTree [nodesFromDB]="alreadyNode"></authTree>
                            </div>
                            <button class="buttonPrimary btn-action-save" (click)="selectAll('mine')">Select
                                All</button>
                            <button class="buttonPrimary btn-action-save" (click)="deSelectAll('mine')">Unselect
                                All</button>
                        </div>
                        <div [hidden]="folderButton != '2'" class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
                            <div class="tree-folder">
                                <authSharedTree #authSharedTree [nodesFromDB]="alreadyNode"></authSharedTree>
                            </div>
                            <button class="buttonPrimary btn-action-save" (click)="selectAll('shared')">Select
                                All</button>
                            <button class="buttonPrimary btn-action-save" (click)="deSelectAll('shared')">Unselect
                                All</button>
                        </div>
                        <div [hidden]="folderButton != '3'" class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
                            <div class="tree-folder">
                                <!-- <authSharedTree #authSharedTree [nodesFromDB]="alreadyNode"></authSharedTree> -->
                                <sharedDrive #shareddrive [nodesFromDB]="alreadyNode"></sharedDrive>
                            </div>
                            <button class="buttonPrimary btn-action-save" (click)="selectAll('drive')">Select
                                All</button>
                            <button class="buttonPrimary btn-action-save" (click)="deSelectAll('drive')">Unselect
                                All</button>
                        </div>
                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                            <div class="arrow-button">
                                <div class="arrow-btn" (click)="getintoDiv(0)">
                                    <i class="material-icons">
                                        arrow_right_alt
                                    </i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
                            <div class="tree-file-shown">
                                <h6 class="tree-head">Selected Files &amp; Folders: </h6>
                                <mat-card>
                                    <mat-card-title>Saved</mat-card-title>
                                    <mat-card-content class="p-0">
                                        <ul class="select-file-inner">
                                            <li class="white-space" *ngFor="let ad of alreadyNode">
                                                <div id=ad.spaceId>
                                                    <p class="select-file-text">
                                                        <span matTooltipClass="my-tooltip tooltip-content"
                                                            matTooltip="Name: {{ad.spaceName}}&#13;Path: /{{ad.spaceUrl}}">{{ad.spaceName}}
                                                        </span>
                                                    </p>
                                                    <a (click)="removeFromTree(ad.spaceId, ad.isSelected, '1')">
                                                        <img class="delete-content rules-trash">
                                                    </a>
                                                </div>
                                            </li>
                                        </ul>
                                    </mat-card-content>
                                </mat-card>
                                <mat-card>
                                    <mat-card-title>My Folders</mat-card-title>
                                    <mat-card-content class="p-0">
                                        <ul class="select-file-inner">
                                            <li class="white-space" *ngFor="let getdiv of getDivData">
                                                <div id=getdiv.id>
                                                    <p class="select-file-text">
                                                        <span matTooltipClass="my-tooltip tooltip-content"
                                                            matTooltip="Name: {{getdiv.text}}&#13;Path: /{{getdiv.path}}">{{getdiv.text}}
                                                        </span>
                                                    </p>
                                                    <img class="delete-content rules-trash"
                                                        (click)="removeFromTree(getdiv.id, getdiv.data.IsShared, '0')">
                                                </div>
                                            </li>
                                        </ul>
                                    </mat-card-content>
                                </mat-card>
                                <mat-card>
                                    <mat-card-title class="title-card">Shared With Me</mat-card-title>
                                    <mat-card-content style="padding: 0px;">
                                        <ul class="select-file-inner">
                                            <li style=" white-space:nowrap;line-height: 1.4rem;"
                                                *ngFor="let sd of getSharedData">
                                                <div id=sd.id>
                                                    <p class="select-file-text">
                                                        <span matTooltipClass="my-tooltip tooltip-content"
                                                            matTooltip="Name: {{sd.text}}&#13;Path: /{{sd.path}}">
                                                            {{sd.text}}
                                                        </span>
                                                    </p>
                                                    <img class="delete-content rules-trash"
                                                        (click)="removeFromTree(sd.id, sd.data.IsShared, '0')">
        
                                                </div>
                                            </li>
                                        </ul>
                                    </mat-card-content>
                                </mat-card>
                            </div>
                        </div>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
    </mat-step>
</mat-horizontal-stepper>
<div class="footerActions">
    <div class="float-right">
        <button type="submit" [disabled]="(selectedIndex === 0) || (selectedIndex === 1) || ContentSourcesComponent.savingContentSource"
            (click)="getSelectedFiles('Rules')" class="buttonPrimary btn-action-save"
            style="margin: 0px;">Save</button>
    </div>
    <div class="float-left">
        <button class="buttonPrimary btn-action-cancel" [disabled]="selectedIndex===0"
            (click)="nextPrevButtonOperations('previous');"><img class="prev-btn"></button>
        <button class="buttonPrimary btn-action"
            [disabled]="selectedIndex===2 ||!authConnectForm.form.valid || !uniqueContentSource || !nextDisabled"
            (click)="nextPrevButtonOperations('next');"><img class="next-btn"></button>
    </div>
</div>
