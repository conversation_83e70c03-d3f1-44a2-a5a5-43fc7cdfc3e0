.table-left{
    float: none;
    width: 50%;
}
.table-right{
    float: none;
    width: 50%;
    padding: 0 0 2% 2%;
}
.auth-wrapper{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.oauthReminder{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    height: 31px;
    width: auto;
    background-color: #F4EC0048;
    margin: 24px -20px 0 -20px;
    border-radius: 7px;
}

.oauthReminderText{
    color: #43425D;
    font-size: 13px;
    font-weight: 500;
    font-family: "Montserrat";
    
}

.oauthReminder svg {
    margin: 0px 14px;
}

.oauthReminder a, a:link, a:visited, a:hover, a:active {
    text-decoration: underline;
    color: #56C5FF;
    font-weight: 500;
    font-family: 'Montserrat';
}

.white-space {
    white-space:nowrap
}

.no-padding {
    padding: 0px;
}

.mat-card {
    padding: 0px;
    border-radius: 0px;
    background-color: #f4f8f9;
    margin-bottom: 15px;
    border: 1px solid #f4f8f9;
    font-family: "Montserrat";
    box-shadow: none !important;
    font-size: 14px;
}

.mat-card-title,
.mat-card-content {
    margin-bottom: 0px;
    padding: 5px;
    font-size: inherit;
}
.mat-card-content {
    background-color: white;
}
.fa:hover {
    font-size: 15px;
}
.jstree-icon.jstree-themeicon {
    color: #61a5fe;
}
/* .reauthenticate{
    background: #FFEEEE 0% 0% no-repeat padding-box;
    width: 105%;
    height: 31px;
    border-radius: 5px;
    margin: 30px 0 0px -20px;
    display: flex;
    justify-content: center;
    align-items: center;
    font: normal normal 400 13px / 16px Montserrat;
} */

.alter-svg {
    margin-bottom: 5px;
    margin-right: 13px;
}
#info{
    font: normal normal 11px / 16px Montserrat;
}
.alignCheckbox{
    padding: 2% 0 0 0;
    font-family: 'Montserrat'
}

.update-button{
    display: flex;
    align-items: center;
}

.update-permission-info {
    padding: 5px;
    margin-top: 2px;
}