import { Component, OnInit, OnDestroy, Input, Output, ViewChild, EventEmitter, ElementRef, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { trigger, state, style, transition, animate } from "@angular/animations";
import { ToastyService, ToastOptions } from 'ng2-toasty';
import { ContentSourceService } from '../../../../services/contentSource.service';
import { CookieService } from '../../../../services/cookie.service';
import { ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import * as socketIo from 'socket.io-client';
import { OBJECT_CRAWL, Variables } from '../../../../variables/contants';
import { ContentSourcesComponent } from '../../components/content_sources';
import { AdminAnalyticsService } from '../../../../services/adminAnaytics.service';
import { UserManagementService } from '../../../../services/userManagement.service';
import { CONTENT_SOURCE_TYPE } from '../../../../variables/contants';
import { CrawlerConstants } from '../../../../variables/crawler-constants';
import { TaxonomyService } from 'app/services/taxonomy.service';
import * as momentZone from 'moment-timezone';
import { TimezoneService } from 'app/services/timezone.service';
import { ThemeService } from 'app/services/theme.service';

@Component({
    selector: 'objects-fields',
    templateUrl: 'objectsAndFields.html',
    animations: [
        trigger('opened', [
            state('void', style({
                'transform': 'translateY(100%)'
            })),
            transition('no => yes', [animate(200), style({ transform: 'translate(100%)' })]),
            transition('yes => no', [animate(200), style({ transform: 'translate(-100%)' })]),
            transition('void => *', [animate(200)]),
            transition('* => void', [animate(200)])
        ])
    ],
    styleUrls: ['objectAndFields.css'],
    providers: [CookieService, ContentSourceService, ToastyService, UserManagementService, AdminAnalyticsService, TaxonomyService],
    encapsulation: ViewEncapsulation.None
})
export class ObjectsFieldsComponent implements OnInit, OnDestroy {
    @ViewChild('logFileDiv') private myScrollContainer: ElementRef;
    private isOpened: string;
    private newSfObject: any;
    private newField: any;
    private isHere: boolean;
    private currentIndex: number;
    private fieldsArray: any;
    private addedObjectName: any;
    private checkingValidity: boolean;
    private isNewObjectNameValid: boolean;
    private validObjectName: boolean;
    private BASE_HREF: string;
    private found: boolean;
    private fieldList: any;
    private fieldSelectors: any;
    private fillterfieldList: any;
    private fieldListReplace: any;
    private timer: any = [];
    private objectName: string;
    private checkingNewObjectValidity: boolean;
    private isLoadingFields: boolean;
    private validationsArray: any;
    private websiteType: boolean;
    private csvType : boolean;
    private showAddObject : boolean;
    private showPopup: boolean;
    private validationMessage: any;
    private mergeArray: any;
    private mergeFalse: any;
    private editObject: any;
    private editField: boolean;
    private addObjectDiv: boolean;
    private toggleConditions: boolean;
    private fieldConditions: any;
    private replace: any;
    private apiCrawlerType: boolean;
    private json_field_for_api_crawler: any;
    private toggleReplace: boolean;
    private filterFieldArray: any;
    private cronObject: any;
    private activeTheme : string;
    private disableManageObject: any;
    private modalData : any = {
        image : "",
        message : [],
        note : []
    };
    private removeSingleFieldData;
    private reCrawlChoice:any
    private reCrawlOption:boolean;
    private deletedFields: any;
    private isEditing: boolean;
    private disableButton: boolean = false;
    private isAddValidField: boolean;
    private filteredJsonFieldList : any;
    private showLogs: boolean;
    private logFileSource: any;
    public endTime:any;
    public elapsedTime:number;
    public timeElapsed :any;
    public hh = 0;
    public mm = 0;
    public ss = 0;
    public timerId = 0;
    private objectFieldResponse: any;
    private socket: any;
    public indexName: any;
    private scrollInterval: any;
    private disableScrollDown: Boolean = false;
    public objectsDocCount: any;
    private objectSyncList : any = [];
    private editableObjects = [];
    private editableFields : any;
    private objectCrawl : Boolean = false;
    @Input() sfObjects: any;
    @Input() content_source_id: any;
    @Input() content_source_type_id: any;
    @Input() contentSourceData : any ;
    @Output() saveObjectsAndFields = new EventEmitter<boolean>();
    @Input() content_source_pid: boolean;
    @Output() reset = new EventEmitter<boolean>();
    // subscription: any;
    private deleteAnnotation: any;
    private finalfieldsArray: any;
    userTimeZone: any = 'UTC';
    doesPathExists: boolean = true;
    private samePathExists = false;
    private objectPath: any;
    private objectLabel: any;
    private jsWebType: boolean = false;
    private searchUnifyUser: boolean = false;
    private csTypeIdsToShow = [CONTENT_SOURCE_TYPE.salesforce, CONTENT_SOURCE_TYPE.jsWeb];
    fieldToDelete: any;
    deleteFieldIndex: any;
    private crawlingError: any;
    private defaultSection : any = 0;
    private defaultSectionSummary =[
        {
          "name": "Preparation",
          "status": 0
        },
        {
          "name": "Crawling",
          "status": 0
        },
        {
          "name": "Indexing",
          "status": 0
        },
        {
          "name": "Completion",
          "status": 0
        }
      ]
    hasCrawlingStopped: boolean;
    private fieldsElements = []
    tenantISConfig: any;
    // contentSourceData: any;

    constructor(private ContentSourcesComponent: ContentSourcesComponent, private sanitizer: DomSanitizer, private cookieService: CookieService, private router: Router, private contentSourceService: ContentSourceService, private toastyService: ToastyService,private userManagementService: UserManagementService, private adminAnalyticsService: AdminAnalyticsService, private taxonomyService: TaxonomyService, private TimezoneService: TimezoneService, private theme : ThemeService) {
        TimezoneService.getUserTimeZone().subscribe((data)=>{
            this.userTimeZone = data
        })
        this.isOpened = 'no';
        this.reCrawlOption = false;
        this.isHere = false;
        this.fieldsArray = [];
        this.checkingValidity = false;
        this.validationsArray = [];
        this.websiteType = false;
        this.csvType = false ;
        this.showPopup = false;
        this.validationMessage = {
            objectName: [],
            fieldName: []
        };
        this.mergeArray = [];
        this.mergeFalse = [];
        this.deletedFields = [];
        this.editField = true;
        this.editObject = {};
        this.indexName = '';
        this.editObject.index = false;

        this.cronObject = {};
        this.isEditing = false;
        this.isAddValidField = true;
    }
    ngOnInit() {
        this.editableObjects = this.sfObjects;
        this.theme.activeTheme.subscribe(t=>{
            this.activeTheme = t;
        })   
        this.objectsDocCount = {};
        this.finalfieldsArray = [];
        this.BASE_HREF = `${window.location.origin}`;
        this.sfObjects = this.sfObjects ? this.sfObjects : [];
        this.contentSourceData = this.ContentSourcesComponent.contentSourceData;
        if (this.sfObjects && this.sfObjects.length) {
            for (let counter = 0; counter < this.sfObjects.length; counter++) {
                this.sfObjects[counter].valid = true;
                this.validationsArray.push(this.sfObjects[counter].fields);
                
                if (this.sfObjects[counter].object_pid > 0 && this.contentSourceData.contentSource.crawl_status === 5) {
                    this.objectSyncList.push(this.sfObjects[counter].id);             
                }
            }
        }
        if (this.content_source_type_id === 3 || this.content_source_type_id === '3' || this.content_source_type_id === 7 || this.content_source_type_id === '7') {
            this.socket = socketIo(`${window.location.origin}`, {
                path: `${Variables.baseHref}/su-crawler/socket.io`
            });
        }
        this.disableManageObject = this.sfObjects[0] && this.sfObjects[0].fields.length ? false : true;
        this.found = false;
        this.newSfObject = {};
        this.addedObjectName = true;
        this.isNewObjectNameValid = false;
        this.validObjectName = false;
        this.fieldList = [];
        this.fillterfieldList = [];
        this.fieldListReplace = [];
        this.newField = {};
        this.newField.name = '';
        this.newField.label = '';
        this.newField.selector = '';
        this.newField.single_multiple = 'single';
        this.newField.type = '';
        this.newField.isMerged = 0;
        this.checkingNewObjectValidity = false;
        this.objectName = '';
        this.isLoadingFields = false;
        this.addObjectDiv = true;
        this.apiCrawlerType = false;
        this.json_field_for_api_crawler = [];
        this.showLogs = false;
        this.filteredJsonFieldList = [];
        if ([
            CONTENT_SOURCE_TYPE.salesforce,
            CONTENT_SOURCE_TYPE.website,
            CONTENT_SOURCE_TYPE.madcap,
            CONTENT_SOURCE_TYPE.solr,
            CONTENT_SOURCE_TYPE.customContentSource,
            CONTENT_SOURCE_TYPE.jsWeb,
            CONTENT_SOURCE_TYPE.contentful,
            CONTENT_SOURCE_TYPE.file,
            CONTENT_SOURCE_TYPE.zendesk
        ].includes(this.content_source_type_id)){
            this.addObjectDiv = false;
        }
        if ([
            CONTENT_SOURCE_TYPE.website,
            CONTENT_SOURCE_TYPE.jsWeb,
            CONTENT_SOURCE_TYPE.madcap,
            CONTENT_SOURCE_TYPE.solr
        ].includes(this.content_source_type_id)){
            this.websiteType = true;
        }
        if (OBJECT_CRAWL.includes(this.content_source_type_id)) {
            this.objectCrawl = true;
        }

        if([CONTENT_SOURCE_TYPE.file].includes(this.content_source_type_id)){
            this.csvType = true;
        }
        if ([
            CONTENT_SOURCE_TYPE.jsWeb,
        ].includes(this.content_source_type_id)) {
            this.jsWebType = true;
        }   

        if (this.content_source_type_id === 22 || this.content_source_type_id === '22' || this.content_source_type_id === 31 || this.content_source_type_id === '31') {
            this.apiCrawlerType = true;
            this.contentSourceService.getApiJsonFields(this.content_source_id, "response").then((response) => {
                // console.log("response===============", response);
                this.filteredJsonFieldList = response.items;
                this.json_field_for_api_crawler = response.items;
            })
        }


        this.toggleConditions = false;
        this.toggleReplace = false;
        // this.subscription = this.ContentSourcesComponent.getCS().subscribe(CS => {
        //     this.content_source_type_id = CS.contentSource.content_source_type_id;
        //     if (this.content_source_type_id === 3 || this.content_source_type_id === 9 ||  this.content_source_type_id === 10 || this.content_source_type_id == 21 || this.content_source_type_id == 22) {
        //         this.addObjectDiv = false;
        //     }
        //     if (this.content_source_type_id === 9 || this.content_source_type_id === '9' || this.content_source_type_id === 10 || this.content_source_type_id === '10' || this.content_source_type_id === 21 || this.content_source_type_id === '21') {
        //         this.websiteType = true;
        //     }

        //     if (this.content_source_type_id === 22 || this.content_source_type_id === '22' || this.content_source_type_id === 31 || this.content_source_type_id === '31') {
        //         this.apiCrawlerType = true;
        //         this.contentSourceService.getApiJsonFields(this.content_source_id, "response").then((response) => {
        //             // console.log("response===============", response);
        //             this.json_field_for_api_crawler = response.items ;
        //         })
        //     }


        // });
        this.contentSourceService.getContentSourceInRulesTab(this.content_source_id).then(result => {
            this.sfObjects = result.data.objectsAndFields;
            this.indexName = result.data.contentSource.index_name;
            this.saveObjectsAndFields.emit(this.sfObjects);
            if(result.data.addField===false)
            this.isAddValidField=false;
        });

        // if (this.csTypeIdsToShow.includes(Number(this.content_source_type_id))) {
            this.contentSourceService.getObjectsDocCount(this.contentSourceData.contentSource.id, this.contentSourceData.contentSource.index_name).then((response) => {
                if (response && response.length) {
                    const elasticTypes = response.map(el => el.key);
                    response.forEach(data => {
                        this.sfObjects.forEach(object => {
                            if (!elasticTypes.includes(object.name)) {
                                this.objectsDocCount[object.name] = 0;
                            } else if (data.key === object.name) {
                              this.objectsDocCount[object.name] = data.doc_count;
                            }
                        })
                    });
                } else {
                    this.sfObjects.forEach(object => {
                        this.objectsDocCount[object.name] = 0;
                    });
                }
            })
            if (this.content_source_type_id === 3 || this.content_source_type_id === '3' || this.content_source_type_id == 7 || this.content_source_type_id == '7') {
                this.getObjectDocumentCounts();
            }
        // }

        this.cookieService.getSearchUnifySession().then((result) => {
			this.searchUnifyUser = result.suDomains.includes(result.email.split('@')[1]) ? true : false;
		});
        this.showAddObject = this.websiteType || this.apiCrawlerType || this.csvType;
        this.editableObjects = JSON.parse(JSON.stringify(this.sfObjects));
    }

    getlabel(fieldName) {
        this.fieldList = this.fillterfieldList;
        this.newField.label = this.fieldList.find(x => x.name == fieldName).label;
        this.newField.type = this.fieldList.find(x => x.name == fieldName).type;
        if (this.fieldList.find(x => x.name == fieldName).customField) {
            this.newField.selector = 'custom';
        }
    }

    doFilter(fieldName) {

        if (fieldName.length > 0 && fieldName !== '') {
            this.fieldList = this.fillterfieldList.filter(x => x.name.toLowerCase().includes(fieldName.toLowerCase()));
            //  this.newField.label = this.fillterfieldList.find(x => x.name == fieldName).label;
            //  this.newField.type = this.fillterfieldList.find(x => x.name == fieldName).type;

        } else {
            this.fieldList = this.fillterfieldList;
            this.newField.label = '';
            this.newField.type = '';
        }


    }
    selectorChange(jsonSelector) {
        this.fieldSelectors = Array.from(
            new Set(this.fillterfieldList.flatMap((x) => x.selector || []))
        );
        if (jsonSelector && jsonSelector.trim() !== '') {
            this.fieldSelectors = this.fieldSelectors.filter((field) =>
                field.toLowerCase().includes(jsonSelector.toLowerCase())
            );
        }
        this.fieldSelectors.sort((a, b) => a.localeCompare(b));
    }

    hoverHandler(object) {
        this.ss = 0;
        this.mm = 0;
        this.hh = 0;

        if(object.object_pid > 0){
            this.endTime = new Date();
                if(object.current_crawl_start_time != '0000-00-00 00:00:00'){
                let timeElapsed = Math.floor((new Date().getTime()- new Date(object.current_crawl_start_time).getTime())/ 1000);
                clearInterval(this.timerId);
                let sec = timeElapsed;
                if(sec < 60){
                    this.ss = sec;
                }
                let min = 0;
                if(sec >= 60){
                    this.ss = sec%60;
                    min = sec/60; 
                    this.mm = ~~min;
                }
                if(this.mm >= 60){
                    this.mm = this.mm%60;
                    this.hh = ~~(sec/3600);
                }
                }else{
                    this.ss = 0;
                    this.mm = 0;
                    this.hh = 0;
                }
                // Stop => Running
                    this.timerId = window.setInterval(() => {

                        if(this.ss < 60){
                            this.ss++;
                        }
                        if(this.ss >=60){
                            this.mm++;
                            this.ss = 0;
                        }
                        if (this.mm >= 60) {
                          this.hh++;
                          this.mm = 0;
                        }
                        if (this.mm >= 60) {
                          this.hh++;
                          this.mm = 0;
                        }
                      }, 1000);
            }
      }

      format(num: number) {
        return (num + '').length === 1 ? '0' + num : num + '';
      }

      formatCount(count){
        if(count>=1000000){
            let convert = (count/1000000).toFixed(2);
            if((convert.split('.')[1])=='0'){
                convert = (count/1000000).toFixed(0);
            }
            return `${convert}M`;
        }
        return count;
      }
    filterOutList(fieldName) {
        if(this.fieldList.length){
            if (fieldName.trim().length) {
                this.fieldList = this.fillterfieldList.filter(x => x.name.toLowerCase()==fieldName.toLowerCase());
                if(!this.fieldList.length){
                    this.fieldList = [];
                    this.newField.type = '';
                }
            } else {
                this.fieldList = [];
                this.newField.type = '';
            }
        }
    }

    clearfields() {
        this.newField.label = '';
        this.newField.type = '';
        this.newField.name = '';
        this.newField.selector = '';
        // let fieldsO = this.sfObjects[this.currentIndex].fields;
        let fieldsO = this.fieldsArray;
        fieldsO = fieldsO.concat(this.deletedFields);
        fieldsO = fieldsO.filter(o => o.id);
        this.sfObjects[this.currentIndex].fields = fieldsO;
    }

    addObject() {
        if ((!this.newSfObject.name || !this.newSfObject.name.trim().length) || (!this.newSfObject.label || !this.newSfObject.label.trim().length)) {
            var toastOptions: ToastOptions = {
                title: "Error",
                msg: "Name & Label are required fields",
                showClose: true,
                timeout: 3000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
            return;
        }

        if (this.jsWebType) {
            if (!this.sfObjects.length) {
                if (!this.newSfObject.path) {
                    this.newSfObject.path = '.*';
                }
            }
            if (this.sfObjects.length && !this.newSfObject.path) {
                this.doesPathExists = false;
                var toastOptions: ToastOptions = {
                    title: "Error",
                    msg: "This field is required",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
                return;
            } else {
                this.doesPathExists = true;
            }
        }
        
        if (this.websiteType || this.apiCrawlerType) {
            let name = this.newSfObject.name;
            let pattern = /^[a-zA-Z0-9_]*$/;
            if (!name.match(pattern)) {
                var toastOptions: ToastOptions = {
                    title: "Warning",
                    msg: "Spaces and Special characters are not allowed!",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
                return;
            }
        }

        for (let count = 0; count < this.sfObjects.length; count++) {
            this.found = false;
            this.samePathExists = false;
            if (this.jsWebType
                && this.sfObjects[count].path.toLowerCase().trim() === this.newSfObject.path.toLowerCase().trim()) {
                this.samePathExists = true;
            }
            var toastOptions: ToastOptions = {
                title: "Error",
                msg: "Same name already exists",
                showClose: true,
                timeout: 3000,
                theme: 'default'
            };
            if (this.sfObjects[count].name.toLowerCase().trim() === this.newSfObject.name.toLowerCase().trim()) {
                this.found = true;
                this.isNewObjectNameValid = false;
                this.validObjectName = false;

                if (this.jsWebType) {
                    if (this.samePathExists && this.found) {
                        toastOptions.msg = 'An Object already created with the same Path and Name';
                    }
                    else if (this.found) {
                        toastOptions.msg = 'An Object already created with the same Name';
                    }
                }
                this.toastyService.error(toastOptions);
                return;
            }

            if (this.sfObjects[count].label.toLowerCase().trim() === this.newSfObject.label.toLowerCase().trim()) {
                toastOptions.msg = 'An Object already created with the same label';
                this.toastyService.error(toastOptions);
                return;
            }

            if (this.jsWebType) {
                if (this.samePathExists) {
                    toastOptions.msg = 'An Object already created with the same Path';
                    this.toastyService.error(toastOptions);
                    return;
                }
            }
        }

        let addObj = {
            content_source_id: this.content_source_id,
            name: this.newSfObject.name,
            valid: this.addedObjectName,
            label: this.newSfObject.label.trim(),
            path: this.newSfObject.path,            
            fields: []
        };

        if (this.jsWebType) {
            this.sfObjects.push(addObj);
        } else {
            this.sfObjects.unshift(addObj);
        }

        this.objectsDocCount[this.newSfObject.name] = 0;
        this.newSfObject = {};
        var toastOptions: ToastOptions = {
            title: "Success",
            msg: "Object added successfully",
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        this.toastyService.success(toastOptions);
        this.isNewObjectNameValid = false;
        this.validObjectName = false;
        this.saveObjectsAndFields.emit(this.sfObjects);
    }

    addField() {
        let foundField = false;
        if (!this.newField || !this.newField.name || !this.newField.label || !this.newField.type || this.newField.name.length === 0 || this.newField.label.length === 0 || this.newField.type.length === 0) {
            var toastOptions: ToastOptions = {
                title: "Warning",
                msg: "Please fill out all the fields",
                showClose: true,
                timeout: 3000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
            return;
        } else if (!this.addedObjectName) {
            return;
        }

        let name = this.newField.name;
        this.newField.label = this.newField.label.trim();
       
        let pattern =  this.content_source_type_id === 3 ? /^[a-zA-Z0-9_.]+$/ :/^[a-zA-Z0-9_]*$/ ;

        // let pattern = /^[a-zA-Z0-9_]*$/;
        if(!name.match(pattern) ){
            var toastOptions: ToastOptions = {
                title: "Warning",
                msg: "Spaces and Special characters are not allowed!",
                showClose: true,
                timeout: 3000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
            return;
        }

        for (let count = 0; count < this.fieldsArray.length; count++) {
            foundField = false;
            if (this.newField.name.toLowerCase().trim() === this.fieldsArray[count].name.toLowerCase().trim()) {
                foundField = true;

                var toastOptions: ToastOptions = {
                    title: "Error",
                    msg: "Field with same name already exists",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
                return;
            }
            if (this.newField.label.toLowerCase().trim()  === this.fieldsArray[count].label.toLowerCase().trim()) {
                foundField = true;

                var toastOptions: ToastOptions = {
                    title: "Error",
                    msg: "Field with same label already exists",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
                return;
            }
        }

        if (this.newField.type) {
            if (this.newField.type == 'datetime' || this.newField.type == 'date') {
                this.newField.type = "datetime"
            }
            else if (this.newField.type == 'double' || this.newField.type == 'long' || this.newField.type == 'int' || this.newField.type == 'integer') {
                this.newField.type = "integer"
            }
            else if (this.newField.type == 'multipicklist') {
                this.newField.type = "multipicklist"
            }
            else {
                this.newField.type = "string"
            }
        }
        if (this.newField.name.slice(-10) == ('attachment')) {
            this.newField.selectOption = 'isSearchable';
            this.newField.isSearchable = 1;
            this.newField.isFilterable = 0;
        }
        else if (this.newField.type == 'datetime' || this.newField.type == 'date' || this.newField.name.slice(-7) == "_nested" || (this.newField.name.indexOf('navigation') > -1) || this.newField.type == 'integer' ) {
            this.newField.selectOption = 'isFilterable';
            this.newField.isSearchable = 0;
            this.newField.isFilterable = 1;
        } else {
            this.newField.selectOption = 'isTag';
            this.newField.isFilterable = 1;
            this.newField.isSearchable = 1;
        }
        this.newField.merge_field_id = 0;
        if(this.newField.isMerged || this.newField.name.toLowerCase().includes("attachment_") 
        || this.newField.name.toLowerCase().includes("_nested") 
        || this.newField.name.toLowerCase().includes("_navigation") 
        || this.newField.name.toLowerCase().includes("_flat")
        || this.newField.name.toLowerCase().includes("__body__s")
        || this.newField.name.toLowerCase().includes("accountid") 
        || this.newField.name.toLowerCase().includes("contactid")
        || this.newField.name.toLowerCase().includes("isvisiblein")
        || this.newField.name.toLowerCase().includes("__typecasted__")
        ){
            this.newField.isMerged = 1;
        }
        if (this.content_source_type_id === 3) {
            this.objectFieldResponse.forEach(o => {
                if (this.newField.name === o.name) {
                    if(o.isMerged || 
                    (this.newField.name.toLowerCase().includes("attachment_") 
                    || this.newField.name.toLowerCase().includes("_nested") 
                    || this.newField.name.toLowerCase().includes("_navigation") 
                    || this.newField.name.toLowerCase().includes("_flat")
                    || this.newField.name.toLowerCase().includes("__body__s")
                    || this.newField.name.toLowerCase().includes("accountid") 
                    || this.newField.name.toLowerCase().includes("contactid")
                    || this.newField.name.toLowerCase().includes("isvisiblein")
                    || this.newField.name.toLowerCase().includes("__typecasted__")
                    )){
                        this.newField.isMerged = 1;
                    }
                    this.newField.selector = this.newField.selector === 'custom' ?
                    'custom' : o.originalType;
                    this.newField.single_multiple = o.extraTypeInfo ? o.extraTypeInfo :o.originalType;
                }
            });
        }
        let newArray = {
            name: this.newField.name,
            label: this.newField.label,
            type: this.newField.type,
            selectOption: this.newField.selectOption,
            selector: this.newField.selector,
            single_multiple: this.newField.single_multiple ? this.newField.single_multiple : 'single',
            isSearchable: this.newField.isSearchable,
            isFilterable: this.newField.isFilterable,
            isMerged: this.newField.isMerged || 0,
            merge_field_id: this.newField.merge_field_id,
        }
        if (this.content_source_type_id === 7) {
            this.objectFieldResponse.forEach(o => {
                if (this.newField.name === o.name) {
                    newArray.selector = o.selector ? o.selector : 'string'
                }
            })
        }
        this.fieldsArray.unshift(newArray);
        this.editableFields =  JSON.parse(JSON.stringify(this.fieldsArray))
        this.mergeFilter(newArray, 0);
        this.newField = {};
        this.newField.name = '';
        this.newField.type = '';
        this.newField.selector = '';
        this.newField.single_multiple = 'single';
        this.newField.isMerged = 0;
        this.fieldList = this.fillterfieldList;
        this.disableManageObject = this.fieldsArray.length ? false : true;
    }

    addAllFields() {
        if (this.objectName) {
            this.fieldsArray = [];
            this.isLoadingFields = true;
            this.fieldsArray = this.fieldList;
            this.validationsArray[this.currentIndex] = [];
            this.validationsArray[this.currentIndex] = this.fieldsArray;

            if (this.fieldsArray.length) {
                this.isLoadingFields = false;
            }
            this.fieldsArray.sort(function (a, b) {
                if (a.name < b.name) return -1;
                if (a.name > b.name) return 1;
                return 0;
            })
            for (let counter = 0; counter < this.fieldsArray.length; counter++) {
                if (this.fieldsArray[counter].type) {
                    if (this.fieldsArray[counter].type == 'datetime' || this.fieldsArray[counter].type == 'date') {
                        this.fieldsArray[counter].type = "datetime"
                    }
                    else if (this.fieldsArray[counter].type == 'double' || this.fieldsArray[counter].type == 'long' || this.fieldsArray[counter].type == 'int' || this.fieldsArray[counter].type == 'integer') {
                        this.fieldsArray[counter].type = "integer"
                    } else {
                        this.fieldsArray[counter].type = "string"
                    }
                }
                if (this.fieldsArray[counter].isFilterable == 1 || this.fieldsArray[counter].type == 'date' || this.fieldsArray[counter].type == 'datetime') {
                    this.fieldsArray[counter].isSearchable = 0;
                    this.fieldsArray[counter].isFilterable = 1;
                    this.fieldsArray[counter].selectOption = "isFilterable"

                }
                else {
                    this.fieldsArray[counter].isSearchable = 1;
                    this.fieldsArray[counter].isFilterable = 0;
                    this.fieldsArray[counter].selectOption = "isSearchable"
                }
                this.fieldsArray[counter].merge_field_id = 0;
            }
            this.mergeFilter(this.fieldsArray, undefined);
        } else {
            var toastOptions: ToastOptions = {
                title: "Error",
                msg: "Cannot add fields!!",
                showClose: true,
                timeout: 1000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
        }
        this.disableManageObject = this.fieldsArray.length ? false : true;
    }

    deleteObject(object, index: number) {
        this.closeDialog();
        this.contentSourceService.deleteObjectsAndFields(object.id).then((response) => {
            var toastOptions: ToastOptions = {
                title: "Success",
                msg: "Object deleted successfully!",
                showClose: true,
                timeout: 1000,
                theme: 'default'
            };
            this.indexChanged(3 ,this.contentSourceData.contentSource.label,object.label);
            delete this.objectsDocCount[object.name];
            this.toastyService.success(toastOptions);
            this.annotationDelete(this.contentSourceData.contentSource.id, object.id);
            this.socket.emit(`stopObjectCountCheck_${localStorage.getItem('t_id')}`, { objects: [object], indexName: this.indexName });
        })
        this.sfObjects.splice(index, 1);
        
    }

    processSectionsData(sectionSummary){
        if(sectionSummary && sectionSummary.length){
        const currentSectionIndex = sectionSummary.findIndex(element => element.status === CrawlerConstants.sectioningStatus.inProgress)
        this.logFileSource.currentSection = currentSectionIndex === -1 && sectionSummary[0].status === CrawlerConstants.sectioningStatus.failed ? 1 : currentSectionIndex + 1; 
        this.logFileSource.sectionSummary = sectionSummary
        this.crawlingError = sectionSummary.find(el => el.status === CrawlerConstants.sectioningStatus.failed);
        this.logFileSource.percentageCrawlingDone = sectionSummary.find(el => el.status !== CrawlerConstants.sectioningStatus.completed) ? ((this.logFileSource.currentSection - 1) * 25 ) + 5 : 100;
        } else {
            this.logFileSource.currentSection = 0; 
            this.logFileSource.sectionSummary = this.defaultSectionSummary;
        }
    }

    convertTimezone(match: string): string{
        const momentDate = momentZone(match).tz(this.userTimeZone).format('YYYY-DD-MM HH:mm:ss')
        const dateHtml = `<span class="dateInCrawlingLogs">${momentDate}</span>`

        return dateHtml;
    }

    replaceLogString(logLines) {
        const headingRegex = /^-{5,}(.*?)-{5,}([\s\S]*?)(?=(?:-{5,}|$))/gm;
        const errorRegex = /\[ERROR\](.*?)\n/g;
        const replacedString = logLines.replace(headingRegex, '<div class="sectionHeading"><h1>$1</h1></div>');
        const replacedError = replacedString.replace(errorRegex, '<span class="logsErrorLine"> [ERROR] $1</span>\n');
        const replaceDates = replacedError.replace(/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z)/g, (match, p1) => {
            return this.convertTimezone(p1);
        } )
        this.logFileSource.logLines = replaceDates;
    }

    getCountUpdate(source) {
        let self = this;
        self.socket.emit(`getObjectCount_${localStorage.getItem('t_id')}`, { ...source, indexName: this.indexName });
    }

    getObjectDocumentCounts() {
            this.socket.off(`objectCountUpdate_${localStorage.getItem('t_id')}`);
                let self = this;
                self.objectCountUpdateListener();
                //get count for content sources having pid!= 0
                this.sfObjects.find(function (o) {
                    if (o.object_pid > 0 && self.contentSourceData.contentSource.current_crawl_mode == 'object') {
                        self.getCountUpdate(o);
                    }
                });
    }

    objectCountUpdateListener(){
        let self = this;
        this.socket.on(`objectCountUpdate_${localStorage.getItem('t_id')}`, function (source) {
            self.sfObjects.find(function (o) {
                if (o.id == source.id) {
                    if(o.count != source.count){
                        self.objectsDocCount[o.name] = source.count;
                    }
                    return o;   
                }
            })
        });

        this.socket.on(`objectLogsNotChanged_${localStorage.getItem('t_id')}`, function (source) {
            self.socket.emit(`stopObjectCountCheck_${localStorage.getItem('t_id')}`, { objects: [source], indexName: self.indexName});
            self.socket.emit(`stopWatching_${localStorage.getItem('t_id')}`, [source]);
            if (!source.stuck) {
                self.sfObjects.find(function (o) {
                    if (o.id == source.id && (o.object_pid != 0 && o.object_pid != -1)) {
                        o.object_pid = source.failed ? -1 : 0;
                        if (self.objectSyncList.includes(o.id)) {
                          const indexOfSyncObject = self.objectSyncList.indexOf(o.id);
                          self.objectSyncList.splice(indexOfSyncObject, 1);
                        }
                        var toastOptions: ToastOptions = {
                            title: "Success",
                            msg: `Crawling for ${o.name} completed successfully`,
                            showClose: true,
                            timeout: 1000,
                            theme: 'default'
                        };
                        if (o.object_pid == 0) {
                            o.object_status = null;
                            self.contentSourceData.contentSource.crawl_status = 2;
                            self.toastyService.success(toastOptions);
                        } else if (o.object_pid == -1) {
                            var toastOptions: ToastOptions = {
                                title: "!Try again",
                                msg: `Crawling for ${o.name} failed`,
                                showClose: true,
                                timeout: 1000,
                                theme: 'default'
                            };
                            self.contentSourceData.contentSource.crawl_status = 4;
                            self.toastyService.error(toastOptions);
                        }
                        self.contentSourceData.contentSource.pid = o.object_pid;
                        let newDate  = new Date();
                        o.current_crawl_end_time = newDate.toISOString();
                        if (self.logFileSource) {
                            self.logFileSource.current_crawl_end_time =  o.current_crawl_end_time;
                        }
                    }
                });
            }
        })
        this.socket.on(`objectCrawlingSyncing_${localStorage.getItem('t_id')}`, function (source) {
            if (!source.stuck) {
                self.sfObjects.find(function (o) {
                    if (o.id == source.id && (o.object_pid != 0 && o.object_pid != -1)) {
                        self.objectsDocCount[o.name] = source.count;
                        if (!self.objectSyncList.includes(o.id)) self.objectSyncList.push(o.id);
                    }
                })
            }
        })

    }

    onWheel(event: WheelEvent): void {
        this.disableScrollDown = true;
    }

    scrollToBottom(){
        clearInterval(this.scrollInterval);
        if (this.disableScrollDown) return;
        
        this.scrollInterval = setInterval(() => {
            if(this.myScrollContainer){
                this.disableScrollDown = false;
                this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
                clearInterval(this.scrollInterval);
            }
        });
    }

    escapeHtml(unsafe) {
        return unsafe
             .replace(/&/g, "&amp;")
             .replace(/</g, "&lt;")
             .replace(/>/g, "&gt;")
             .replace(/"/g, "&quot;")
             .replace(/'/g, "&#039;");
      }

    crawlLogFileCheck(crawlFile, crawlStartTime){
        if(!crawlFile){
            return true;
        }
        let fileTime         = crawlFile.split('_');
        let crawlFileDate = fileTime[4]+ "-" + fileTime[5] + "-" + fileTime[6] + "T";
        let crawlFileTime = fileTime[8]+ ":" + fileTime[9] + ":" + fileTime[10] + ".000Z";

        let logFileTime = new Date(crawlFileDate + crawlFileTime).getTime();
        let crawlStartMinutes   = new Date(crawlStartTime).getTime();

        let crawlFileMinutesDiff = Math.abs(crawlStartMinutes - logFileTime)/(1000*60);
        if (!(crawlFileMinutesDiff >=1)) {
            return false;
        }
        return true;
    }

    saveFileAs(text, csData) {
        var blob = new Blob([text], { type: 'text/html' });
        const contentSourceName = this.contentSourceData.contentSource.label ? this.contentSourceData.contentSource.label.replace(/[^a-zA-Z0-9 ]/g, "").replace(/\s+/g, "_") : "";
        const filename = `${csData.name}_${contentSourceName}.log`
        saveAs(blob, filename);
    }

    downloadLogFile() {
            if(this.logFileSource  
                && this.logFileSource.objectAdminLogFile && this.logFileSource.logLines){
            this.contentSourceService.downloadLogFile(this.logFileSource.objectAdminLogFile).then(res => {
                this.saveFileAs(res, this.logFileSource);   
            })
        } else {
            //this.showToasty("not able to fetch", "", "error");
        }    
    }

    getObjectLogFiles(id,crawlStartMinutes,retryCount?){
        retryCount = retryCount || 0;
        const self =  this;
        let source;
        let index = this.sfObjects.findIndex((obj) => obj.id === id);
        self.contentSourceService.getObjectData(id).then((result) =>{
            source = result[0];
            this.sfObjects[index].objectAdminLogFile = source.objectAdminLogFile;
            crawlStartMinutes = source.current_crawl_start_time;
            let crawlTimeCheck = this.crawlLogFileCheck(source.objectAdminLogFile,crawlStartMinutes)
            if (crawlTimeCheck && retryCount < 10) {
                retryCount = retryCount + 1;
                setTimeout(() => {
                    this.getObjectLogFiles(id,crawlStartMinutes)    
                }, 5000);
            }else {
                this.logFileSource = source;
                this.logFileSource.percentageCrawlingDone = 0;
                this.socket.on(`objectLines_${localStorage.getItem('t_id')}`, (data) => {
                    if(data.source.isLogFileMissing){
                        self.logFileSource.isLogFileMissing = true;
                        if(source.objectAdminLogFile && data.source.canDeleteLogFile){
                            this.contentSourceService.updateLogFile({contentSourceId: source.id, objectLogFile: true}).then((res)=>{
                               const result = 'Object Log File Deleted successfully';
                            })
                        }
                        self.logFileSource.waitingScreen = false;
                        self.logFileSource.logFile = null;
                        self.logFileSource.sectionSummary = this.defaultSectionSummary;
                    }
                    else if (self.logFileSource.id == data.source.id) {
                        self.logFileSource.logLines = data.source.logLines;
                        self.processSectionsData(data.source.sectioningData);
                        self.replaceLogString(data.source.logLines);
                        self.logFileSource.waitingScreen = false;
                        self.scrollToBottom();
                    }
                });
        
            this.socket.emit(`objectReadFile_${localStorage.getItem('t_id')}`, { source });
            }  
        })
    }

    getLogFiles(source) {
        this.showLogs = true;
        let self = this;
        this.disableScrollDown = false;
        this.logFileSource = JSON.parse(JSON.stringify(source));
        this.logFileSource.percentageCrawlingDone = 0;
        source.logFile ? this.logFileSource.waitingScreen = true : this.logFileSource.waitingScreen = false;
        let crawlTimeCheck = this.crawlLogFileCheck(source.objectAdminLogFile, source.current_crawl_start_time)
        if (source.object_pid > 0 &&  this.contentSourceData.contentSource.current_crawl_mode === 'object' && crawlTimeCheck)  {
            this.logFileSource.logLines = '';
            this.logFileSource.currentSection = this.defaultSection;
            this.logFileSource.sectionSummary = this.defaultSectionSummary;
            this.logFileSource.waitingScreen = true;
            this.getObjectLogFiles(source.id, source.current_crawl_start_time);   
        }   
        else{
            this.socket.on(`objectLines_${localStorage.getItem('t_id')}`, (data) =>{
                if(data.source.isLogFileMissing){
                    self.logFileSource.isLogFileMissing = true;
                    if(source.objectAdminLogFile && data.source.canDeleteLogFile){
                        this.contentSourceService.updateLogFile({contentSourceId: source.id, objectLogFile: true}).then((res)=>{
                           const result = 'Object Log File Deleted successfully';
                        })
                    }
                    self.logFileSource.waitingScreen = false;
                    self.logFileSource.logFile = null;
                    self.logFileSource.sectionSummary = this.defaultSectionSummary;
                } else if (self.logFileSource.id == data.source.id) {
                    self.logFileSource.logLines = data.source.logLines;
                    self.processSectionsData(data.source.sectioningData);
                    self.replaceLogString(data.source.logLines);
                    self.logFileSource.waitingScreen = false;
                    self.scrollToBottom();
                }
            });
    
            this.socket.emit(`objectReadFile_${localStorage.getItem('t_id')}`, { source });
        }
    }

    getCrawlEndTimeStatus(logFileSource) {
        if (logFileSource.current_crawl_end_time && (this.sfObjects.find(el => logFileSource.id == el.id).object_pid <= 0 || this.contentSourceData.contentSource.current_crawl_mode !== 'object')) {
            return true;
        }

        return false;
    }

    annotationDelete(csId, objectId) {
        var deleteAnnotation = {
            "csId": csId,
            "object_id": objectId
        }
        this.taxonomyService.deleteAnnotation(deleteAnnotation).then(result => {});
    }

    toggleAddFields(index: number, close, objectName) {
        
        if(this.contentSourceData.contentSource.content_source_type_id === 9){
            if(this.isEditing && close) {
                var toastOptions: ToastOptions = {
                title: "Warning",
                msg: "Please fill out all the fields",
                showClose: true,
                timeout: 3000,
                theme: 'default'
                };
                this.toastyService.error(toastOptions);
                return;
            }
        }
        if(this.content_source_type_id == 22)
            this.contentSourceService.getApiJsonFields(this.content_source_id, "response").then((response) => {
                this.json_field_for_api_crawler = response.items;
                this.filteredJsonFieldList  = response.items;
            });
            
        this.objectName = objectName;
        if (close === 'close') {
            if (this.fieldsArray) {
                this.fieldsArray.sort(function (a, b) {
                    if (a.name < b.name) return -1;
                    if (a.name > b.name) return 1;
                    return 0;
                })
                this.sfObjects[this.currentIndex].fields = this.fieldsArray;
            }

            if(this.content_source_type_id == 3){
                // contentSourceId, objectAndFields, deletedFields
                this.disableButton = true;
                this.isHere = !this.isHere;
                this.contentSourceService.updateFields(this.content_source_id, this.sfObjects, this.deletedFields)
                .then((response) => {
                    if(!response.error){
                      this.disableButton = false;
                        var toastOptions: ToastOptions = {
                            title: "Success",
                            msg: "Fields updated successfully!",
                            showClose: true,
                            timeout: 1000,
                            theme: 'default'
                        };
                        this.toastyService.success(toastOptions);
                        
                        for(let i=0; i<response.objectAndFields.length; i++){
                            if(response.objectAndFields[i].name == this.sfObjects[this.currentIndex].name){
                                this.validationsArray[this.currentIndex] = response.objectAndFields[i];
                                this.disableManageObject = response.objectAndFields[i].fields.length ? false : true;
                                this.sfObjects[this.currentIndex] = response.objectAndFields[i];
                                break;
                            }
                        }
                        this.sfObjects = response.objectAndFields;
                        this.saveObjectsAndFields.emit(this.sfObjects);
                        this.isHere = !this.isHere;
                    } else if (response.error) {
                        this.disableButton = false;
                        var toastOptions: ToastOptions = {
                          title: "Error",
                          msg: "Error while updating fields.",
                          showClose: true,
                          timeout: 1000,
                          theme: 'default'
                      };
                      this.toastyService.error(toastOptions);
                    }
                })
            }

          /*  for (let i = 0; i < this.sfObjects.length; i++) {
                this.mergeFalse.map(arr => {
                    this.sfObjects[i].fields.filter(x => {
                        if (x.name === arr.name)
                            return x
                    }).map(y => {
                        y.isMerged = 0;
                    })
                });
            }
            this.mergeFalse.map(arr => {
                this.fieldsArray.map(y => { if (y.name === arr.name) y.isMerged = 0 })
            })*/
            this.newField = {};
            this.newField.name = '';
            this.newField.type = '';
            this.newField.single_multiple = 'single';
            this.newField.selector = '';
            this.newField.isMerged = 0;
            this.fieldList = [];
        } else {
            this.currentIndex = index;
            this.deletedFields = [];
            if (this.sfObjects[index].fields) {
                this.fieldsArray = this.sfObjects[index].fields;
            }

            this.isLoadingFields = true;
            this.contentSourceService.getObjectFields(objectName, this.content_source_id).then((response) => {
                
                if(response.flag == 500 && this.content_source_type_id === 3){
                    var toastOptions: ToastOptions = {
                        title: "Connection Error",
                        msg: "Please re-authenticate the salesforce connection.",
                        showClose: true,
                        timeout: 3000,
                        theme: 'default'
                    };
                    this.toastyService.error(toastOptions);
                }else{
                    this.isLoadingFields = false;
                    this.objectFieldResponse = response.Objects ? response.Objects : [];
                    this.fieldsElements = [];
                    this.fetchFieldsList(this.objectFieldResponse);
                    this.fieldList = this.fieldsElements;
                    if(this.fieldList && this.fieldList.length)
                        this.fieldList.forEach(o => {
                            o.originalType = o.type;
                            if(o.type == "double" || o.type == "int" || o.type == "long"){
                                o.type = "integer"
                            }else if(o.type != "string" && o.type != "datetime"){
                                o.type = "string";
                            }
                        });
                    
                    if(this.content_source_type_id == 3 && ["feeditem","feeditem_1"].includes(objectName)){
                        const fieldsFiltered = this.fieldList.filter(o => ["ParentId","Id"].includes(o.name));
                        fieldsFiltered.forEach(o => {
                            o["isFilterable"] = 1;
                            o["isSearchable"] = 1;
                        });

                        if(!this.sfObjects[index].fields.length)
                            this.fieldsArray = fieldsFiltered;
                        else if(this.sfObjects[index].fields.filter(o => ["ParentId","Id"].includes(o.name)).length == 0)
                            this.fieldsArray.concat(fieldsFiltered);
                    }
                    if (this.content_source_type_id == 3) {
                        this.objectFieldResponse.forEach(o => {
                            this.sfObjects[index].fields.find((element,i) =>{
                                if (element.name === o.name) {
                                    if(o.isMerged || 
                                        (this.sfObjects[index].fields[i].name.toLowerCase().includes("attachment_") 
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("_nested") 
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("_navigation") 
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("_flat")
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("__body__s")
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("accountid") 
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("contactid")
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("isvisiblein")
                                        || this.sfObjects[index].fields[i].name.toLowerCase().includes("__typecasted__")
                                    )){
                                        this.sfObjects[index].fields[i].isMerged = 1;
                                    }
                                    this.sfObjects[index].fields[i].selector === 'custom' ?
                                    'custom' : o.originalType;
                                    this.sfObjects[index].fields[i].single_multiple = o.extraTypeInfo ? o.extraTypeInfo :o.originalType
                                }
                            });
                        });
                    }
                    if(this.content_source_type_id == 7){
                        this.objectFieldResponse.forEach(o => {
                            o.selector = o.selector ? o.selector : 'string'
                        })
                    }
                    this.fillterfieldList = this.fieldList;
                    this.fieldList.sort(function (a, b) {
                        if (a.name < b.name) return -1;
                        if (a.name > b.name) return 1;
                        return 0;
                    })
                }
                
            });

        }
        this.isHere = !this.isHere;
        this.saveObjectsAndFields.emit(this.sfObjects);
        this.fieldList = [];
        this.editableFields =  JSON.parse(JSON.stringify(this.fieldsArray))
        if(this.isHere) {
            this.addClassToBody();
        } else {
            this.removeClassToBody();
        }
    }

    fetchFieldsList(fields) {
        const uniqueNames = new Set();
        fields.forEach((element) => {
            const parts = element.name.split("___");
            const series = parts.pop();
            element.name = series;

            if (!uniqueNames.has(series)) {
                uniqueNames.add(series);
                this.fieldsElements.push(element);
            }
        });
    }

    addClassToBody() {
        var addClass = document.getElementById("desktopview");
        addClass.classList.add("addClass");
    }

    removeClassToBody() {
        var removeClass = document.getElementById("desktopview");
        removeClass.classList.remove("addClass");
    }

    deleteFieldSalesforce(index,field){
        this.deletedFields = this.deletedFields.concat(this.fieldsArray.splice(index, 1));
        this.validationsArray[this.currentIndex] = this.fieldsArray;
        this.disableManageObject = this.fieldsArray.length ? false : true;
        this.removeFieldConfirmation = false;
    }

    removeSingleField(index, field){
        let deleteField : any[] = [];
        deleteField.push(field);
        this.contentSourceService.deleteFields(deleteField ,this.contentSourceData.contentSource.id).then((response) => {
            var toastOptions: ToastOptions = {
                title: "Success",
                msg: "Field deleted successfully!",
                showClose: true,
                timeout: 1000,
                theme: 'default'
            };
            this.toastyService.success(toastOptions);
            this.fieldsArray.splice(index, 1);
            this.validationsArray[this.currentIndex] = this.fieldsArray;
            this.disableManageObject = this.fieldsArray.length ? false : true;
            this.isEditing = false;
            this.removeSingleFieldData = {};
            this.removeFieldConfirmation = false;
            this.editableFields =  JSON.parse(JSON.stringify(this.fieldsArray));
        });
        this.closeDialog();
    }

    deleteField(index, field) {
        if(field){
            this.modalData.action = "removeSingleField";
            this.modalData.noButton = true;
            this.modalData.yesButton = true;
            this.activeTheme != "black" ? this.modalData.image = "Delete.svg" : this.modalData.image = "Delete.svg"; 
            this.modalData.message = [`<strong>Are you sure you want to remove this field?</strong>`];
            this.removeSingleFieldData = {
                index, field
            };
            this.showModal();
        }
    }

    removeAllFields() {
      if(!this.fieldsArray.length){
        return;
      }
      this.modalData.action = "removeFields";
      this.modalData.noButton = true;
      this.modalData.yesButton = true;
      this.activeTheme != "black" ? this.modalData.image = "Delete.svg" : this.modalData.image = "Delete.svg"; 
      this.modalData.message = [`<strong>Are you sure you want to remove all fields?</strong>`];
      this.showModal();
    }

    removeFields() {
        this.fieldsArray.forEach((element, index) => {
            if(element.annotated == 1) {
                this.finalfieldsArray.push(this.fieldsArray[index]);
                this.fieldsArray.splice(index, 1)
            }
        });
      if(this.content_source_type_id == 3){
          if(this.objectName == "feeditem" || this.objectName == "feeditem_1"){
            this.deletedFields = this.fieldsArray.filter(o => !["Id","ParentId"].includes(o.name));
            this.fieldsArray = this.fieldsArray.filter(o => ["Id","ParentId"].includes(o.name));
        }else{
            this.deletedFields = this.fieldsArray;
            this.fieldsArray = this.finalfieldsArray.length !== 0 ? this.finalfieldsArray :[];
        }
        this.sfObjects[this.currentIndex].fields
        this.validationsArray[this.currentIndex] = this.fieldsArray;
        this.disableManageObject = this.fieldsArray.length ? false : true;
      }else{  
        this.contentSourceService.deleteFields(this.fieldsArray).then((response) => {
            var toastOptions: ToastOptions = {
                title: "Success",
                msg: "Fields deleted successfully!",
                showClose: true,
                timeout: 1000,
                theme: 'default'
            };
            this.toastyService.success(toastOptions);
            this.fieldsArray = this.finalfieldsArray.length !== 0 ? this.finalfieldsArray :[];
            this.disableManageObject = this.fieldsArray.length ? false : true;
            this.isEditing = false;
      })
      this.validationsArray[this.currentIndex] = [];
      }
      this.closeDialog();
    }

    keyPressed($event) {
        if ($event.key === 'Enter') {
            this.addObject();
        }
    }

    checkObjectValidity(index: number) {
        this.checkingValidity = true;
        if (index === undefined) {
            if (!this.newSfObject || !this.newSfObject.length) {
                this.addedObjectName = true;
                this.checkingValidity = false;
            } else {
                this.contentSourceService.getObjectFields(this.newSfObject.name, this.content_source_id).then((response) => {
                    if (response.flag === 0) {
                        this.addedObjectName = true;
                    } else {
                        this.addedObjectName = false;
                    }
                    this.checkingValidity = false;
                })
            }
        } else {
            if (!this.sfObjects[index].name || !this.sfObjects[index].name.length) {
                this.sfObjects[index].valid = false;
                this.checkingValidity = false;
            } else {
                this.contentSourceService.getObjectFields(this.newSfObject.name, this.content_source_id).then((response) => {
                    if (response.flag === 0) {
                        this.sfObjects[index].valid = true;
                    } else {
                        this.sfObjects[index].valid = false;
                    }
                    this.checkingValidity = false;
                })
            }
        }
    }

    checkNewObjectValidity() {
        clearTimeout(this.timer)
        this.isNewObjectNameValid = false;
        
        this.timer = setTimeout(() => {
            this.checkingNewObjectValidity = true;
            if (!this.newSfObject.name || !this.newSfObject.name.length) {
                this.checkingNewObjectValidity = false;
            } else {
                this.contentSourceService.getObjectFields(this.newSfObject.name, this.content_source_id).then((response) => {
                    this.checkingNewObjectValidity = false;
                    if (response.flag === 0) {
                        this.isNewObjectNameValid = true;
                    } else {
                        this.isNewObjectNameValid = false;
                        var toastOptions: ToastOptions = {
                            title: "Error",
                            msg: "Invalid object name!",
                            showClose: true,
                            timeout: 1000,
                            theme: 'default'
                        };
                        this.toastyService.error(toastOptions);
                    }
                }, error => {
                    var toastOptions: ToastOptions = {
                        title: "Error",
                        msg: "Invalid object name!",
                        showClose: true,
                        timeout: 1000,
                        theme: 'default'
                    };
                    this.toastyService.error(toastOptions);
                });
            }
        }, 2000);
    }

    checkNewObjectNameValidity() {
        console.log('this.newSfObject.name ',this.newSfObject.name );
        this.validObjectName = true;
        const regexPattern = /^[a-zA-Z0-9_]+$/;
        if ((!this.newSfObject.name || !this.newSfObject.name.trim().length) || !regexPattern.test(this.newSfObject.name)) {
            this.validObjectName = false;
        }
    }

    mergeFilter(field, index) {
        this.mergeArray = [];
        this.validationMessage.objectName = [];
        this.validationMessage.fieldName = [];
        let allFields = Object.assign([], this.sfObjects);
        allFields[this.currentIndex].fields = this.fieldsArray;
        allFields.map(z => {
            if (this.objectName != z.name) {
                z.fields.map(x => {
                    if (x.name === field.name && x.isFilterable ? (x.isFilterable === (field.selectOption === "isFilterable" ? 1:0) || (x.isFilterable === (field.selectOption === "isTag" ? 1:0)) ? 1 : 0) : false) {
                        this.mergeArray.push(x);
                        this.validationMessage.objectName.indexOf(z.name) === -1 ? this.validationMessage.objectName.push(z.name) : '';
                        this.validationMessage.fieldName.indexOf(field.name) === -1 ? this.validationMessage.fieldName.push(field.name) : '';
                    }
                })
            }
        })
        if (this.validationMessage.objectName.length) {
            this.validationMessage.objectName.join(',');
            this.validationMessage.fieldName.join(',');
            this.showPopup = true;
        }
        else {
            this.showPopup = false;
        }
    }

    jsonFieldFilter(jsonSelector) {
        if(jsonSelector && jsonSelector !== ''){
            this.filteredJsonFieldList =  this.json_field_for_api_crawler.filter(x => x.toLowerCase().includes(jsonSelector.toLowerCase()));
        } else {
            this.filteredJsonFieldList = this.json_field_for_api_crawler;
        }
    }
    
    mergedField() {
        for (let i = 0; i < this.sfObjects.length; i++) {
            this.mergeArray.map(arr => {
                this.sfObjects[i].fields.filter(x => {
                    if (x.name === arr.name)
                        return x
                }).map(y => {
                    y.isMerged = 1;
                })
            });
        }
        this.mergeArray.map(arr => {
            this.fieldsArray.map(y => { if (y.name === arr.name) y.isMerged = 1 })
        })
        this.showPopup = false;
    }

    toggleModal() {
        this.showPopup = false;
    }

    reduceObject(final, arr) {
        return final.concat(arr)
    }

    toggleEditMode(index, mode) {
        this.sfObjects[index].label = this.sfObjects[index].label.trim();
        if (mode === 'save') {
            const matchingIndexes = this.sfObjects.filter((field) => field.label.toLowerCase().trim() === this.objectLabel.toLowerCase().trim());
 
            if ( matchingIndexes.length > 1) {
              console.log("this.sfObjects[index].label", this.sfObjects[index].label); 
              this.sfObjects[index] = this.editableObjects[index];
             
                var toastOptions: ToastOptions = {
                    title: "Error",
                    msg: "Same object label already exists",
                    showClose: true,
                    timeout: 5000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
            } 
        }
        this.sfObjects[index].disabled = !this.sfObjects[index].disabled;
        this.editableObjects = JSON.parse(JSON.stringify(this.sfObjects));
    }

    toggleEditLabelMode(index, mode) {
        this.fieldsArray[index].label = this.fieldsArray[index].label.trim();
        if (mode === 'save') {
            const matchingIndexes = this.fieldsArray.filter(field => field.label.toLowerCase().trim() === this.objectLabel.toLowerCase().trim());
            if(matchingIndexes.length > 1 ){
                var toastOptions: ToastOptions = {
                    title: "Error",
                    msg: "Same field label already exists",
                    showClose: true,
                    timeout: 5000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
                this.fieldsArray[index] =  { ... this.editableFields[index]};
            } 
            this.editableFields = JSON.parse(JSON.stringify(this.fieldsArray));
        }
    }

    changeLabel(label) {
        this.objectLabel = label;
    }

    toggleEditField(index) {
          if(this.fieldsArray[index].merge_field_id === 1) return;
          if(this.contentSourceData.contentSource.content_source_type_id === 9){
            if(this.fieldsArray[index].disabled){
              if(this.fieldsArray[index].label.trim() && this.fieldsArray[index].name.trim() && this.fieldsArray[index].selector.trim() && this.fieldsArray[index].type.trim()){
                this.fieldsArray[index].disabled = !this.fieldsArray[index].disabled;
                this.isEditing = false;
              }
              else{
                var toastOptions: ToastOptions = {
                  title: "Warning",
                  msg: "Please fill out all the fields",
                  showClose: true,
                  timeout: 3000,
                  theme: 'default'
              };
              this.toastyService.error(toastOptions);
              }

            } else {
              if(this.isEditing){
                var toastOptions: ToastOptions = {
                  title: "Error",
                  msg: 'Another field is being edited.',
                  showClose: true,
                  timeout: 3000,
                  theme: 'default'
              };
              this.toastyService.error(toastOptions);
              return;
              }
              this.fieldsArray[index].disabled = !this.fieldsArray[index].disabled;
              this.isEditing = true;
            }
          } else {
            this.fieldsArray[index].disabled = !this.fieldsArray[index].disabled;
          }
    }

    toggleFieldConditions(index) {
        this.currentIndex = index;
        this.toggleConditions = !this.toggleConditions;
        if(this.toggleConditions) {
            this.addClassToBody();
        } else {
            this.removeClassToBody();
        }
        if (index != undefined) {
            this.fieldsArray = this.sfObjects[index].fields;
            this.fieldConditions = this.sfObjects[index].field_conditions;
        }
    }
    toggleReplaceConditions() {
        this.toggleReplace = !this.toggleReplace;
        if(this.toggleReplace) {
            this.addClassToBody();
        } else {
            this.removeClassToBody();
        }
    }


    saveConditions(event) {
        var toastOptions: ToastOptions = {
        	title: "Success",
        	msg: "Fields Condition updated successfully!",
        	showClose: true,
        	timeout: 3000,
        	theme: 'default'
        };
      	this.toastyService.success(toastOptions);
        this.sfObjects[this.currentIndex].field_conditions = JSON.stringify(event)
        this.toggleConditions = !this.toggleConditions;
        if(this.toggleConditions) {
            this.addClassToBody();
        } else {
            this.removeClassToBody();
        }
    }

    toggleAddReplace(index: number, objectName) {
        this.currentIndex = index;
        if (this.sfObjects[index].fields) {
            this.fieldsArray = this.sfObjects[index].fields;
            this.filterFieldArray = this.fieldsArray.filter(function (el) {
                if (el.regex != null && el.regex != "") {
                    return el.regex.length != 0 && el.regex_value.length != 0;
                }
                else {

                }

            });

        }
        this.toggleReplace = !this.toggleReplace;
        if(this.toggleReplace) {
            this.addClassToBody();
        } else {
            this.removeClassToBody();
        }
    }
    showModal(){
        let modal = document.getElementById("alertModalA");
        document.getElementById("backdropA").style.display = "block"
        document.getElementById("alertModalA").style.display = "block"
        document.getElementById("alertModalA").className += "show";
    }
    closeDialog(){
        this.reCrawlOption = false;
        document.getElementById("backdropA").style.display = "none"
        document.getElementById("alertModalA").style.display = "none"
        document.getElementById("alertModalA").className += document.getElementById("alertModalA").className.replace("show", "")
        this.modalData.image = "";
        this.modalData.message= [];
        this.modalData.note = [];
        this.modalData.object = {};
        this.modalData.noButton = false;
        this.modalData.okButton = false;
        this.modalData.yesButton = false;

        if(this.modalData.action === "removeFields"){
          this.modalData.action = "";
        }
    }

    setCronId(object, index) {
        this.cronObject = object;
        if(this.sfObjects.find(el => el.object_pid > 0)){  
            this.modalData.image = "newly added Salesforce object.svg";
            this.modalData.message = [`<strong>Object Crawling in Progress.</strong>`, `To start object crawling for ${this.sanitizer.sanitize(SecurityContext.HTML, this.escapeHtml(object.name))}, either wait for the current crawling to finish  or stop it manually.`];
            this.modalData.okButton = true;
            this.showModal();    
            return;
        }  
        if(this.contentSourceData.contentSource.crawl_status == 0){  
            this.modalData.image = "newly added Salesforce object.svg";
            this.modalData.message = [`Manual crawl is required to generate the index. Object crawling can only be done after that.`];
            this.modalData.okButton = true;
            // this.modalData = popUpData;
            this.showModal();    
            return;
        }
        if(this.contentSourceData.contentSource.sync_frequency_name != "Never"){  
            this.modalData.image = "newly added Salesforce object.svg";
            this.modalData.message = [`Please set the Crawl Frequency of the content source to <strong>'Never'</strong> if you want to crawl the Salesforce Object.`];
            // this.ContentSourcesComponent.modalData = popUpData;
            this.modalData.okButton = true;
            // this.modalData = popUpData;
            this.showModal();    
            return;
        } 
        if(this.contentSourceData.contentSource.current_crawl_mode == 'reindexing'
            &&(this.contentSourceData.contentSource.crawl_status == 3 
                || this.contentSourceData.contentSource.crawl_status == 4)){
            this.modalData.image = 'manualCrawlIsRequired.svg';
            this.modalData.message= [`Last crawling was incomplete.`, `Manually crawl the entire content source first and then return to object crawling.`];
            this.modalData.note = [];
            this.modalData.okButton = true;
            this.showModal();
            return;
        }   
        // if(this.contentSourceData.contentSource.crawl_status == 1 && this.contentSourceData.contentSource.current_crawl_mode != "object") {
        if(this.contentSourceData.contentSource.crawl_status == 1) {
            this.modalData.image = "newly added Salesforce object.svg";
            this.modalData.message = [`<strong>Crawling in Progress.</strong>`, `To start object crawling, either wait for the crawling to finish  or stop it manually.`]; 
            // this.modalData.note = [`Search  might be affected, If content source is not crawled completely.`]
            this.modalData.okButton = true;
            this.modalData.action = "crawlObject";
            // this.modalData = this.modalData;
            this.showModal();
            return;
        }
        // if(this.contentSourceData.contentSource.crawl_status == 1 && this.contentSourceData.contentSource.current_crawl_mode == "object") {
        //     this.modalData.image = "assets/img/newly added Salesforce object.svg";
        //     this.modalData.message = [`<strong>Crawling in Progress.</strong>`, `<strong>Click "Yes" to stop.</strong>`]; 
        //     // this.modalData.note = [`Search might be affected, If content source is not crawled completely.`]
        //     this.modalData.yesButton = true;
        //     this.modalData.noButton = true;
        //     this.modalData.action = "crawlObject";
        //     // this.modalData = this.modalData;
        //     this.showModal();
        //     return;
        // }
        if(this.contentSourceData.contentSource.sync_frequency_name == 'Never' && this.contentSourceData.contentSource.crawl_status != 1){
            this.reCrawlOption = true;
            this.modalData.image = "newly added Salesforce object.svg";
            // this.modalData.message = [`This will sync latest data for ${this.cronObject.label}. Would you like to continue? `];
            this.modalData.message = [`Please select the following options to sync your data, would you like to continue `];

            this.modalData.note = [];
            this.modalData.noButton = true;
            this.modalData.action = "crawlObject";
            this.modalData.yesButton = true;
            // this.modalData = this.modalData;
            this.showModal();
            return;
        }
        
    }

    setCronAndCrawlData() {
        this.closeDialog();
        if (!this.content_source_pid || !this.cronObject.object_pid || this.cronObject.object_pid == -1 || this.cronObject.object_pid == 0) {
            this.contentSourceService.startObjectCrawling(this.cronObject.name, this.content_source_id, this.cronObject.id,  this.reCrawlChoice).then((response) => {
                if (response.flag === 0) {
                    var toastOptions: ToastOptions = {
                        title: "Success",
                        msg: `${response.data}`,
                        showClose: true,
                        timeout: 1000,
                        theme: 'default'
                    };
                    this.toastyService.success(toastOptions);
                    this.toggleObjectCrawlStatus({ objectId: this.cronObject.id, objectPid: response.pid, crawlStartTime: response.current_crawl_start_time, crawlStatus: null });
                    this.indexChanged(1 ,this.contentSourceData.contentSource.label,this.cronObject.name)
                    this.contentSourceData.contentSource.current_crawl_mode = response.current_crawl_mode;

                    if (this.content_source_type_id === 3 || this.content_source_type_id === '3' || this.content_source_type_id === 7 || this.content_source_type_id === '7') {
                      this.getObjectDocumentCounts();
                    }
                    // this.reset.emit();
                } else {
                    var toastOptions: ToastOptions = {
                        title: "Error",
                        msg: `${response.data}`,
                        showClose: true,
                        timeout: 3000,
                        theme: 'default'
                    };
                    this.toastyService.error(toastOptions);
                    this.toggleObjectCrawlStatus({ objectId: this.cronObject.id, objectPid: response.pid, crawlStartTime: response.current_crawl_start_time, crawlStatus: null });
                    // this.reset.emit();
                }
            })
        } else {
            var toastOptions: ToastOptions = {
                title: "Warning",
                msg: `Content source crawling in process. Unable to start object crawling`,
                showClose: true,
                timeout: 3000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
        }
    }
    getTheme() {
        this.userManagementService.userSpecificSettings({type : 'get'}).then((res) => {
            this.activeTheme = res.data.lightMode || 'white';
        })
    }
    openDialog(object?: any, action?:string, index?){
        //this.getTheme();
        this.modalData.ObjectIndex = index;
        if(action == "stop"){
            this.activeTheme != "black" ? this.modalData.image = "Crawling in Progress click Yes to stop.svg" : this.modalData.image = "Click Yes to stop black.svg"; 
            this.modalData.message= [`<strong>Object crawling is in progress.</strong>`, `<strong>Click "Yes" to stop.</strong>`];
            this.modalData.note = [];
            this.modalData.noButton = true;
            this.modalData.action = action;
            this.modalData.yesButton = true;
            this.modalData.object = object;
        } else  if(action == "delete"){
            this.modalData.image = 'Delete.svg';
            if ((object.object_pid && object.object_pid > 0) || this.contentSourceData.contentSource.crawl_status === 1) {
                this.modalData.okButton = true;
                this.modalData.delete = false;
                this.activeTheme != "black" ? this.modalData.image = "Cannot Delete while crawl in progress.svg" : this.modalData.image = "Cannot Delete while crawl in progress.svg"; 
                this.modalData.message = [`<strong>Crawling in Progress</strong> <br> Cannot delete object: ${this.sanitizer.sanitize(SecurityContext.HTML, this.escapeHtml(object.name))}`];
            } else {
                this.modalData.message = [`<strong>Are you sure you want to delete ${this.sanitizer.sanitize(SecurityContext.HTML, this.escapeHtml(object.label))} object?</strong>`];
                this.modalData.note =[`Data present in the index will be removed and Search will also get affected.`];
                this.modalData.noButton = true;
                this.modalData.action = action;
                this.modalData.yesButton = true;
                this.modalData.object = object;
            }
        }
        this.showModal();
    }

    stopCrawler(object) {
        this.hasCrawlingStopped = true;
        this.closeDialog();
        let objectValue ={
            objectName : object.name
        }
        let contSource = { content_source_pid: this.content_source_pid, content_source_id: this.content_source_id, objectId: object.id, objectName:object.name }
        this.contentSourceService.stopObjectCrawling(contSource).then(result => {
            if (result.flag === 0) {
                var toastOptions: ToastOptions = {
                    title: "Success",
                    msg: `${result.data}`,
                    showClose: true,
                    timeout: 1000,
                    theme: 'default'
                };
                this.toggleObjectCrawlStatus({ objectId: object.id, objectPid: result.pid, crawlStartTime: null,  crawlStatus: 5 });
                this.indexChanged(2 ,this.contentSourceData.contentSource.label,objectValue.objectName);
                if (!this.objectSyncList.includes(object.id)) this.objectSyncList.push(object.id);
                this.toastyService.success(toastOptions);
                this.getObjectDocumentCounts();
                // this.reset.emit();
            } else {
                var toastOptions: ToastOptions = {
                    title: "Error",
                    msg: `${result.data}`,
                    showClose: true,
                    timeout: 1000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
            }
            this.hasCrawlingStopped = false;
        });
    }

    ngOnDestroy() {
        // this.subscription.unsubscribe();
        this.saveObjectsAndFields.emit(this.sfObjects);
        if (this.socket) {
            this.socket.off(`objectCountUpdate_${localStorage.getItem('t_id')}`);
            this.socket.emit(`stopObjectCountCheck_${localStorage.getItem('t_id')}`, { objects: this.sfObjects, indexName: this.indexName });
            this.socket.close();
        }
    }

    toggleObjectCrawlStatus({ objectId, objectPid, crawlStartTime, crawlStatus }){
        if (objectId && (objectPid || objectPid === 0)) {
            const index = this.sfObjects.findIndex(el => el.id === objectId);
            this.sfObjects[index].object_pid = objectPid;
            if (objectPid > 0) {
                this.contentSourceData.contentSource.crawl_status = crawlStatus || 1;
                this.contentSourceData.contentSource.pid = objectPid;
                this.sfObjects[index].objectAdminLogFile = null;
                this.sfObjects[index].objectLogFile = null;
            } else {
                this.contentSourceData.contentSource.crawl_status = 2;
            }
            if (crawlStartTime) {
                this.sfObjects[index].current_crawl_start_time = crawlStartTime;
            }
            this.saveObjectsAndFields.emit(this.sfObjects);
        }
    }

    stopWatching(source) {
        this.socket.emit(`stopWatching_${localStorage.getItem('t_id')}`, { source });
        this.logFileSource = {};
        this.showLogs = false;
    }

    indexChanged(option, source,objName) {
        if (option || option == 0) {
            let info;
            if (option == 1) {
                info = 'Refresh single object Index ' + source +'- '+objName+' started'
            } else if (option == 2){
                info = 'Refresh single object Index ' + source +'- '+objName+' stopped'
            } else if (option == 3){
                info = 'Deleted object ' + objName +' on '+source
            }
            let options = {
                info: 'Content Source > ' + info,
                object: 'Content Source'
            }
        //    console.log(options);
            this.adminAnalyticsService.trackAdminAnalytics(options);
        }
    }

    checkPathValidity(path: string, index) {
        this.objectPath = path;
        if (!path) {
            this.sfObjects[index].emptyPath = true;
            this.sfObjects[index].samePathExists = false;
        } else {
            this.sfObjects[index].emptyPath = false;
            this.sfObjects[index].samePathExists = false;

            // let samePathExists = false;
            for (let count = 0; count < this.sfObjects.length; count++) {
                if (this.jsWebType && count != index && this.sfObjects[count].path.toLowerCase().trim() === path.toLowerCase().trim()) {
                    // samePathExists = true;
                    this.sfObjects[index].samePathExists = true;
                    return
                }
            }
        }

    }

    savePath() {
        const toastOptions: ToastOptions = {
            title: "Path Edited Successfully",
            msg: "To apply the changes, need to recrawl the object.",
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };

        this.toastyService.success(toastOptions);
    }

    togglePathEditMode(index, mode: string) {
        this.sfObjects[index].editPath = !this.sfObjects[index].editPath;
        if (mode === 'save') {
            this.sfObjects[index].path = this.objectPath || this.sfObjects[index].path;
            this.savePath();
        }
    }
    private searchClientsName: any;
    private removeFieldConfirmation: boolean = false;
    private showMoreSearchClientName: boolean = false;
    showMoreSearchClientNameFunc() {
        this.showMoreSearchClientName = !this.showMoreSearchClientName;
    }

    confirmFieldDeletion(index,field) {
        this.deleteFieldIndex = index;
        this.fieldToDelete = field;
        this.contentSourceService.getAffectedSearchClients({csId: this.content_source_id, fieldId: this.fieldToDelete.id}).then((response) => {
            this.searchClientsName = null;
            if (response){
                if (response.scRows && response.scRows.length) {
                    this.searchClientsName = response.scRows.map((sc) => sc.scName).join(', ');
                    this.showMoreSearchClientName = false;
                }
                if (response.isConfig) {
                    this.tenantISConfig = response.isConfig;
                }
            }
            this.removeFieldConfirmation = true;
        })
    }

}
