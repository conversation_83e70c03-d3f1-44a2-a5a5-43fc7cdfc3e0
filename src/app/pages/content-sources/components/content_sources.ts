import { <PERSON><PERSON><PERSON>, OnInit, OnDestroy , Input, ViewChild, ElementRef, Renderer2} from '@angular/core';
import { trigger, state, style, transition, animate } from "@angular/animations";
import { trigger as triggers, style as styles, transition as transitions, animate as animations, query, stagger } from '@angular/animations';
import { CookieService } from '../../../services/cookie.service';
import { Router, RouterModule, Event, NavigationEnd } from '@angular/router';
import { PaginationService } from 'ng2-pagination';
import { ContentSourceService } from '../../../services/contentSource.service';
import { ContentTypesService } from '../../../services/contentTypes.service';
import { ToastyService, ToastyConfig, ToastOptions } from 'ng2-toasty';
import { AdminAnalyticsService } from '../../../services/adminAnaytics.service';
import { UserManagementService } from '../../../services/userManagement.service';
import { ViewEncapsulation } from '@angular/core';
import { Variables } from '../../../variables/contants';
import { CrawlerConstants } from '../../../variables/crawler-constants';
import { CategoryPipe } from '../../../pipes/category';
import * as momentZone from 'moment-timezone';
import { TimeZonePipe } from '../../../pipes/timezone.pipe';
import { TEMP_MAPPING_EXCLUSIONS, TEMP_MAPPING_CS } from '../../../variables/contants';
import { ThemeService } from 'app/services/theme.service';
import * as socketIo from 'socket.io-client';
import { TimezoneService } from 'app/services/timezone.service';
import { TaxonomyService } from 'app/services/taxonomy.service';

@Component({
    selector: 'content-sources',
    templateUrl: 'content_sources.html',
    providers: [ ContentSourceService, PaginationService, ToastyService, ToastyConfig, ContentTypesService, AdminAnalyticsService, CategoryPipe, TaxonomyService],
    animations: [
        trigger('opened', [
            state('void', style({
                'transform': 'translateY(-100%)'
            })),
            transition('void => *', [animate(200)]),
            transition('* => void', [animate(200)])
        ]),
        trigger('previewTemplate', [
            state('void', style({
                'transform': 'translateY(-100%)'
            })),
            transition('void => *', [animate(200)]),
            transition('* => void', [animate(200)])
        ]),
        triggers('listAnimation', [
            transitions('* <=> *', [
                query(':enter', [
                    styles({ opacity: 0, transform: 'translateX(-15px)' }),
                    stagger('50ms', animations('550ms ease-out', styles({ opacity: 1, transform: 'translateX(0px)' })))
                ],
                    { optional: true }
                ),
                query(':leave', animations('50ms', styles({ opacity: 0 })), {
                    optional: true
                })
            ])
        ])
    ],
    styleUrls: ['contentSources.scss'],
    encapsulation: ViewEncapsulation.None
})
export class ContentSourcesComponent implements OnInit, OnDestroy {
    @ViewChild('logFileDiv') private myScrollContainer: ElementRef;
    @Input()
    arrowPosition:  'before' ;
    public addingContentSource: boolean;
    public userContentSources: any;
    private allSupportedContentSourceTypes: any;
    public allSupportedContentSourceCategories: any;
    private allCategories: any;
    private date: any;
    private selectedCStype: any;
    public currentlyAddingCS: boolean;
    private isLoading: boolean;
    public editMode: boolean;
    public addNewCs: boolean;
    private BASE_HREF: string;
    public uniqueCSName: string;
    public contentSourceData: any;
    private instructions: string;
    private cronId: number;
    private tabName: string;
    private contentSource: any = null;
    private allowClone: boolean;
    private clone: any;
    public addingCs: string;
    private uniqueClone: boolean;
    private isLoadingAdded: boolean;
    showModalLoader:boolean = false;
    private saveUpdated = 0;
    public sourceId = [];
    public selectedContentSource: boolean = false;
    private socket: any;
    private adminSession: any = {};
    public userSuccess: any;
    private showLogs: boolean;
    private logFileSource: any;
    private crawlStatus: any;
    private currentCrawling: any;
    private compleate_crawlbutton: boolean = false;
    private shareModalData: any = {};
    private currentSelectedUserForShare: any = [];
    private backup: any = []
    private addArray: any = [];
    private removeArray: any = [];
    private sorted : boolean= false;
    private showModal :boolean = false;
    public activeTheme : string ;
    private otherWebsiteRunning : boolean = false;
    private standardCS: any = [];
    private savingContentSource: Boolean = false;
    disablePopupButton: Boolean = false;
    enableDeleteButton : boolean = false;
    private scrollInterval: any;
    private deleteScIds: any = [];
    private disableScrollDown: Boolean = false;
    public modalData :any = {
       showModal  : false ,
        image: '',
        message : '',
        message2 : '',
        note: '',
    };
    public lastSyncDateChecks = ['0000-00-00 00:00:00', null];
    public endTime:any;
    public elapsedTime:number;
    public timeElapsed :any;
    public hh = 0;
    public mm = 0;
    public ss = 0;
    public timerId = 0;
    public clientURLPattern: any = "((?:http(s)?):\\/\\/[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]\\b([-a-zA-Z0-9@:%_\\+.,~#?&//=]*))";
    public clientNamePattern : any = "(?! )[A-Za-z0-9 ]*(?<! )";
    public editing  : boolean;
    public contentAnnotation: boolean;
    private deleteAnnotation: { csId: any; };
    private disableAddButton : boolean;
    hasCrawlingStopped: boolean;
    public loadTreeComponentsScript(url: string) {
        const body = <HTMLDivElement>document.body;
        const script = document.createElement('script');
        script.src = url;
        body.appendChild(script);
    }
    public loadTreeComponentscss(url: string) {
        const body = <HTMLLinkElement>document.body;
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        body.appendChild(link);
    }
    private objectsAndFieldsCopy: any = [];
    private spacesORboardsCopy: any;
    private indexLabel: any;
    private getWebConfCopy: any = {};
    changeInRulesTab: any = false;
    frequencyChanged: boolean = false;
    private contentSourceCopy: any = {};
    authorizationChanged: boolean = false;
    addContentSource : boolean;
    resetchangeInRulesTab : boolean ;
    public excludeArray:any =["updated"]
    private logFileData: any;
    public crawlIds:any = [];
    userTimeZone : any = 'UTC';
    oauthPendingMessage = "OAuth set up is pending";
    oauthKnowMoreMessage = "Click on the Icon to know more"

    private defaultSection : any = 0;
    
    private defaultSectionSummary =[
        {
          "name": "Preparation",
          "status": 0
        },
        {
          "name": "Crawling",
          "status": 0
        },
        {
          "name": "Indexing",
          "status": 0
        },
        {
          "name": "Completion",
          "status": 0
        }
      ]
    private defaultSuccessSectionSummary = [
        {
        "name": "Preparation",
        "status": 2
        },
        {
        "name": "Crawling",
        "status": 2
        },
        {
        "name": "Indexing",
        "status": 2
        },
        {
        "name": "Completion",
        "status": 2
        }
    ]
    private crawlingError : any = false;
    /**
     * Constructor
     */
    constructor(private cookieService: CookieService, private router: Router, private RouterModule: RouterModule, private contentSourceService: ContentSourceService, private toastyService: ToastyService, private contentTypesService: ContentTypesService, private adminAnalyticsService: AdminAnalyticsService, private category: CategoryPipe,private userManagementService: UserManagementService, private TimezoneService: TimezoneService, private taxonomyService: TaxonomyService, private theme : ThemeService, private renderer: Renderer2, private el: ElementRef) { 
        TimezoneService.getUserTimeZone().subscribe((data)=>{
			this.userTimeZone = data
	    })
        this.router.events.subscribe((event: Event) => {
            if (event instanceof NavigationEnd) {
              if(event.url === "/dashboard/content-sources") {
                this.addingContentSource = false;
              }
            }            
        });
        this.scrollToBottom = this.scrollToBottom.bind(this);
    }

    /**
     * OnInit
     */
    ngOnInit() { 
        this.theme.activeTheme.subscribe(t=>{
            this.activeTheme = t;
        })   
        this.showModal = false;
        this.router.navigate(['dashboard/content-sources']);
        this.loadTreeComponentsScript('assets/vakata-jstree-a6a0d0d/dist/jstree.min.js');
        this.loadTreeComponentscss('assets/vakata-jstree-a6a0d0d/dist/themes/default/style.min.css');
        this.socket = socketIo(`${window.location.origin}`, {
            path: `${Variables.baseHref}/su-crawler/socket.io`
        });
        this.isLoading = true;
        // this.getTheme();
        this.isLoadingAdded = false;
        this.selectedCStype = 'All';
        this.date = new Date().toJSON().slice(0,10).replace(/-/g,'-');
        this.fetchAllsources();
        this.getUserContentSources();
        this.getAllSupportedContentSourceTypes();
        this.getAllSupportedContentSourceCategories();
        this.setAddedContentSources();
        this.getSessionDetails();
        this.uniqueCSName = '';

        this.contentSourceData = {
            contentSource: {},
            authorization: {},
            objectsAndFields: [],
            spacesORboards: [],
            apiCrawlerFields: {},
            language: []
        }
        this.contentSourceData.contentSource.content_source_type_id = "";
        this.allowClone = false;
        this.clone = {};
        this.contentAnnotation = false;
        this.addingContentSource = false;
        this.currentlyAddingCS = false;
        this.addingCs = '';
        this.uniqueClone = false;
        this.editMode = true;
        this.showLogs = false;
        this.crawlStatus = [];
        this.currentCrawling;
        this.addNewCs = false;
        this.objectsAndFieldsCopy=  [];
        this.getWebConfCopy ={}
    }

    fetchAllsources(){
        let content_source_id;
        this.contentSourceService.fetchCSConfig(content_source_id).then(res => {

            if(res.error || res.code == "ETIMEDOUT"){
                this.showToasty("not able to fetch", "", "error");
            } else{
                let data =res.data;
                for(let i=0; i<data.length; i++){
                    let content = data[i];
                    this.standardCS.push(content.content_source_type_id);
                }
            }
        })
    };

    showToast({title,msg,type}){
        var toastOptions: ToastOptions = {
            title,
            msg,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        if(type == "error")
            this.toastyService.error(toastOptions);
        else if(type == "success")
            this.toastyService.success(toastOptions);
        else if(type == "warning")
            this.toastyService.warning(toastOptions);
    }

    getSessionDetails() {
        this.cookieService.getSearchUnifySession().then(result => {
            this.adminSession = result;
            this.userSuccess = this.adminSession["email"].includes(result.suDomains[0]) || this.adminSession["email"].includes(result.suDomains[1]);
        })
    }

    bringShareAccessSettings(source) {
        this.currentSelectedUserForShare = []
        let data = { id: source.id, task: 'get' };
        this.contentSourceService.shareAccessSettings(data).then(result => {
            if (result.status == 200 || result.status == "200") {
                if (result.data.owner[0] && result.data.owner[0].roleId == 1) { result.data.admin.unshift(result.data.owner[0]) }
                else if (result.data.owner[0] && result.data.owner[0].roleId == 2) { result.data.moderator.unshift(result.data.owner[0]) }
                this.shareModalData = result.data
                this.shareModalData.name = source.name
                this.currentSelectedUserForShare = JSON.parse(result.data.accessDetails.sharedAccess);
                this.backup = JSON.parse(JSON.stringify(this.currentSelectedUserForShare));

            }
        })
    }

    closeModal() {
        this.shareModalData = {};
        this.currentSelectedUserForShare = [];
        this.backup = [];
    }
    updateShareSettings(data) {
        if (this.adminSession.email != data &&  this.shareModalData.accessDetails.ownerEmail!= data) {
            if (this.currentSelectedUserForShare.includes(data)) {
                this.currentSelectedUserForShare.splice(this.currentSelectedUserForShare.indexOf(data), 1)
            }
            else {
                this.currentSelectedUserForShare.push(data)
            }
        }
    }

    saveShareSettingsForCS(id, scName) {
        this.addArray = this.currentSelectedUserForShare.filter(x => this.backup.indexOf(x) === -1);
        this.removeArray = this.backup.filter(x => this.currentSelectedUserForShare.indexOf(x) === -1);

        let data = { id: id, task: 'updateAray', accessArray: this.currentSelectedUserForShare };
        this.contentSourceService.shareAccessSettings(data).then(result => {
            if (result.flag == 401) {
                this.showToasty("Unauthorized!", "", "error")
            } else if (result.status == 200 || result.status == "200") {
                this.showToasty("Access Settings Changed!", "Content Source access updated", "success");
                /** Track Admin  hit for all newly added users*/
                this.addArray.map(e => {
                    let options = {
                        info: `Content Source ${scName} > Share > Access Given to ${e}`,
                        object: `Content Source`
                    }
                    this.adminAnalyticsService.trackAdminAnalytics(options);
                })
                /** Track Admin  hit for all removed users*/
                this.removeArray.map(e => {
                    let options = {
                        info: ` Content Source ${scName} > Share > Access revoked to ${e}`,
                        object: `Content Source`
                    }
                    this.adminAnalyticsService.trackAdminAnalytics(options);
                })
            }
            else {
                this.showToasty("Something went wrong!", "", "error")
            }
        })
    }

    /**
     * Fetches all supported content source types
     */
    getAllSupportedContentSourceTypes() {
        this.contentSourceService.getAllSupportedContentSourceTypes().then(result2 => {
            this.disableAddButton = result2.disableAddButton;
            this.allSupportedContentSourceTypes = result2.data;
            this.allSupportedContentSourceTypes.sort((a, b) => a.name.toLowerCase() > b.name.toLowerCase() ? 1 : -1);
        });
        this.isLoading = false;
    }

    /**
     * Check whether the content souce is added in the instance
     */
    setAddedContentSources() {
        setTimeout(() => {
            if(this.allSupportedContentSourceTypes && this.userContentSources)
                this.allSupportedContentSourceTypes.forEach(element => {
                    this.userContentSources.some(addedCs => addedCs.content_source_type_id == element.id) ? element.installed = true : element.installed = false;
                });
        }, 500);
    }

    /**
     * Fetches all supported content source Categories
     */
    getAllSupportedContentSourceCategories() {
        this.contentSourceService.getAllSupportedContentSourceCategories().then(result => {
            this.allSupportedContentSourceCategories = result.data;
            this.allSupportedContentSourceCategories.sort((a, b) => a.type.toLowerCase() > b.type.toLowerCase() ? 1 : -1);
            this.allCategories = [...this.allSupportedContentSourceCategories];
            this.allSupportedContentSourceCategories.map(v => ({ ...v, showCategory: true }))
            this.allCategories.unshift({ type: 'All', id: 0 }); //Add All as a new category
        });
    }

    indexChanged(option, source?) {
        if (option || option == 0) {
            let info;
            if (option == 0) {
                info = 'Clicked Edit configuration on ' + source.label;
            } else if (option == 1) {
                info = 'Refresh index ' + source.label + ' started';
            } else if (option == 2) {
                info = 'Refresh index ' + source.label + ' stopped';
            }else if (option == 3) {
                info = 'Cloned ' + source.label;
            } else if (option == 4) {
                info = 'Deleted ' + source;
            } else if (option == 5) {
                info = 'Clicked add a content source';
            } else if (option == 6) {
                info = 'Updated ' + source;
            } else if (option == 7) {
                info = 'Edited Authentication on ' + source.label;
            } else if (option == 8) {
                info = 'Edited Frequency on ' + source.label;
            } else if (option == 9) {
                info = 'Edited Rules on ' + source.label;
            } else if (option == 10) {
                info = 'Viewed logs on ' + source.label;
            } else if (option == 11) {
                info = 'Viewed Total documents on ' + source.label + ' index';
            } else if (option == 13) {
                info = 'Edited API response Configuration on ' + source.label;
            } else if (option == 14) {
                info = 'Cannot stop crawling when syncing is in progress'
            }
            let options = {
                info: 'Content Source > ' + info,
                object: 'Content Source'
            }
           // console.log(options);
            
            this.adminAnalyticsService.trackAdminAnalytics(options);
        }
        this.closeDialog();
    }
    browseComponentWithIndex(id, label) {
        this.sourceId = [];
        this.sourceId.push(id, label)
    }



    sortUserContentSources (){
        localStorage.getItem("CSSortOrder") == "true" ? this.sorted =  true: this.sorted = false;
        this.sorted = !this.sorted;
        localStorage.setItem("CSSortOrder", String(this.sorted));  
        this.getUserContentSources();
    }
    backButton() {
        this.getUserContentSources();
    }
    getSortOrder() {
        localStorage.getItem("CSSortOrder") == "true" ? this.sorted =  true: this.sorted = false;
        return localStorage.getItem("CSSortOrder");
    }
    
    clickHandler(source) {
        this.ss = 0;
        this.mm = 0;
        this.hh = 0;
        if([1,5].includes(source.crawl_status)){
            this.endTime = new Date();
                if(source.current_crawl_start_time != '0000-00-00 00:00:00'){
                let timeElapsed = Math.floor((new Date().getTime()- new Date(source.current_crawl_start_time).getTime())/ 1000);
                clearInterval(this.timerId);
                let sec = timeElapsed;
                if(sec < 60){
                    this.ss = sec;
                }
                let min = 0;
                if(sec >= 60){
                    this.ss = sec%60;
                    min = sec/60; 
                    this.mm = ~~min;
                }
                if(this.mm >= 60){
                    this.mm = this.mm%60;
                    this.hh = ~~(sec/3600);
                }
                }else{
                    this.ss = 0;
                    this.mm = 0;
                    this.hh = 0;
                }
                // Stop => Running
                    this.timerId = window.setInterval(() => {

                        if(this.ss < 60){
                            this.ss++;
                        }
                        if(this.ss >=60){
                            this.mm++;
                            this.ss = 0;
                        }
                        if (this.mm >= 60) {
                          this.hh++;
                          this.mm = 0;
                        }
                        if (this.mm >= 60) {
                          this.hh++;
                          this.mm = 0;
                        }
                      }, 1000);
            }else if(![1,5].includes(source.crawl_status)){
                let endtime = source.current_crawl_end_time;
                if(endtime && endtime!='0000-00-00 00:00:00'){
                    this.endTime = endtime.replace('T', ' ').split('.')[0];
                }else{
                    this.endTime = null;
                }
                clearInterval(this.timerId);
                this.ss=0;
                this.mm =0;
                this.hh =0;
                this.elapsedTime = new Date(source.current_crawl_end_time).getTime()- new Date(source.current_crawl_start_time).getTime();
                if(this.elapsedTime/1000<60){
                    let k = this.elapsedTime/1000;
                    this.ss = ~~k; 
                }
                if(this.elapsedTime/1000>=60){
                    let a  = this.elapsedTime/1000;
                    let min = ~~a/60;
                    let seconds = ~~a%60;               
                    this.ss = seconds;
                    this.mm = ~~min;
                }
                if(this.mm >= 60){
                    let hrs = this.mm/60;
                    this.mm = this.mm%60;
                    this.hh = ~~hrs;
                }
            }
      }

      format(num: number) {
        return (num + '').length === 1 ? '0' + num : num + '';
      }

      formatCount(count){
        if(count>=1000000){
            let convert = (count/1000000).toFixed(2);
            if((convert.split('.')[1])=='0'){
                convert = (count/1000000).toFixed(0);
            }
            return `${convert}M`;
        }
        return count;
      }
    /**
     * Fetches user content sources.
     */
    getUserContentSources(firstTimeload?, cronId?, sortDir?) {
        this.cronId = cronId ? cronId : this.cronId;
        this.isLoadingAdded = true;
        sortDir = this.getSortOrder();
        sortDir == 'true'?  sortDir = 1: sortDir = 0;
        
        this.socket.off(`countUpdate_${localStorage.getItem('t_id')}`);
        this.contentSourceService.getUserContentSources(sortDir).then(result1 => {
            if(result1.error){
                this.userContentSources = [];
                this.showToasty(result1.message, "", "error");
            }
            else {
                result1.data.map((n)=>{
                    if(n.crawl_status ==0 || n.crawl_status ==2 ){
                        n.tempCount = 0;
                        n.sectionSummary = n.crawl_status ==0 ? this.defaultSectionSummary : this.defaultSuccessSectionSummary;
                        n.currentSection = this.defaultSection;
                    }
                });
                this.userContentSources = result1.data;
                let self = this;
                self.countUpdateListener();
                //get count for content sources having pid!= 0
                this.userContentSources.find(function (o) {
                    if (o.pid > 0 && (o.crawl_status === 1 || o.crawl_status === 5)) {
                        let crawlTimeCheck = self.crawlLogFileCheck(o.adminLogFile, o.current_crawl_start_time)
                        if (crawlTimeCheck) {
                            self.getContentSource(o.id,o.current_crawl_start_time,'getCountUpdate');
                        }  else {
                            self.getCountUpdate(o);
                        }  
                    }
                });
            }
            this.isLoadingAdded = false;
        
        });
    }

    /**
     * Cancels adding/editing content source.
     */
    back(message) {
        this.toggleAddingContentSource();
        if (message === "toggleEditMode") {
            this.editMode = !this.editMode;
            this.router.navigate(['/dashboard/content-sources']);
        } else {
            this.contentSourceData.contentSource = {};
            this.contentSourceData.authorization = {};
            this.contentSourceData.objectsAndFields = [];
            this.contentSourceData.spacesORboards = [];
            this.contentSourceData.apiCrawlerFields = {};
            this.contentSourceData.language = [];
            delete this.contentSourceData.getWebConf
        }
        // this.addingContentSource = !this.addingContentSource
        this.currentlyAddingCS = false;
        this.contentSourceData.contentSource.content_source_type_id = '';
    }

    /**
     * Toggles addingContentSource variable.
     */
    toggleAddingContentSource() {
        this.addingContentSource = !this.addingContentSource;
        this.contentAnnotation = !this.contentAnnotation;
    }

    /**
     * Toggles Browse component variable.
     */
    selectedBrowseContentSource() {
        this.toggleAddingContentSource();
        this.selectedContentSource = !this.selectedContentSource;
    }

    /**
     * Edit user content source.
     */
    editUserContentSource(id, content_source_id, content_source_name: string, indexing_method, is_active, mapping_id, publicCrawlerId) {
        this.instructions = this.allSupportedContentSourceTypes.find(x => x.id == content_source_id).instructions;
        this.isLoading = true;
        this.authorizationChanged = false;
        this.contentSourceService.editUserContentSource(id, content_source_id, mapping_id).then(result => {
            result = result.data;
            // console.log("edit", result)
            this.contentSourceData = result;
            this.contentSourceData.contentSource.editing = true;

            this.editMode = true;
            this.isLoading = false;
            this.addingContentSource = true;
            this.contentAnnotation = true;
            this.uniqueCSName = result.contentSource.label;
            this.indexChanged(0,result.contentSource);
            if (this.contentSourceData.contentSource.sync_start_date)
                // this.contentSourceData.contentSource.sync_start_date = result.contentSource.sync_start_date.substring(0, result.contentSource.sync_start_date.length - 8).substring(0, 10);
                this.contentSourceData.contentSource.sync_start_date = result.contentSource.sync_start_date;

            this.contentSourceData.contentSource.content_source_type_id = result.contentSource.content_source_type_id;
            this.contentSourceData.objectsAndFields = result.objectsAndFields;
            this.contentSourceCopy = JSON.parse(JSON.stringify(this.contentSourceData.contentSource));
            this.spacesORboardsCopy= JSON.parse(JSON.stringify(this.contentSourceData.spacesORboards));
            this.objectsAndFieldsCopy = JSON.parse(JSON.stringify(this.contentSourceData.objectsAndFields));
            this.getWebConfCopy = JSON.parse(JSON.stringify(this.contentSourceData.getWebConf));
            this.contentSourceData.language = result.language;
            this.resetchangeInRulesTab = true;
            this.changeInRulesTab = false;
            if (result.apiCrawlerFields) {
                this.contentSourceData.apiCrawlerFields = result.apiCrawlerFields;
            } else {
                this.contentSourceData.apiCrawlerFields = {};
            }
            if(this.standardCS.includes(this.contentSourceData.contentSource.content_source_type_id))
                this.router.navigate(['dashboard/content-sources/auth/' + this.contentSourceData.contentSource.content_source_type_id]);
            else
                this.router.navigate(['dashboard/content-sources/' + this.contentSourceData.contentSource.content_source_type_id]);

        });
    }

    onpremAfterImport(contentSourceData) {
        if (contentSourceData.tabName) {
            this.contentSourceData = contentSourceData;
            this.tabName = this.contentSourceData.tabName;
            this.addUserContentSource(this.contentSourceData);
        }
        else {
            this.contentSourceService.editUserContentSource(contentSourceData.contentSource.id, contentSourceData.contentSource.content_source_type_id, "-1").then(result => {
                result = result.data;
                // console.log("edit", result)
                this.contentSourceData = result;

                this.editMode = true;
                this.isLoading = false;
                this.addingContentSource = true;
                this.contentAnnotation = true;
                this.uniqueCSName = result.contentSource.label;

                if (this.contentSourceData.contentSource.sync_start_date)
                    this.contentSourceData.contentSource.sync_start_date = result.contentSource.sync_start_date.substring(0, result.contentSource.sync_start_date.length - 8).substring(0, 10);

                this.contentSourceData.contentSource.content_source_type_id = result.contentSource.content_source_type_id;
                this.contentSourceData.objectsAndFields = result.objectsAndFields;

            });
        }
    }

    /**
     * Delete user content source.
     */
    deleteUserContentSource(contentSource) {
            this.closeDialog();
            if(contentSource.content_source_type_id == 9 && [1,5].includes(contentSource.crawl_status)){
              this.otherWebsiteRunning = false;
            }
            this.contentSourceService.deleteUserContentSource(contentSource.id, contentSource.content_source_type_id, this.deleteScIds).then(result => {
                if (result.flag == 401) {
                    this.showToasty("Unauthorized!", "", "error")
                } else {
                    this.indexChanged(4, contentSource.label);
                    this.showToasty("Content source deleted!", "Content source deleted successfully", "success");
                }
                this.getUserContentSources();
                this.addingContentSource = false;
                this.contentAnnotation = false;
                this.annotationDelete(contentSource.id);
                this.getAllSupportedContentSourceTypes();
            }, error => {
                var toastOptions: ToastOptions = {
                    title: "Content source cannot be deleted!",
                    msg: "Content source cannot be deleted",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
            })
            this.socket.emit(`stopCountCheck_${localStorage.getItem('t_id')}`, [contentSource]);
            //this.crawlStatus.splice(this.crawlStatus.indexOf(contentSource.id),1);
        
    }

    annotationDelete(id) {
        this.deleteAnnotation = {
            "csId": id
        }
        this.taxonomyService.deleteAnnotation(this.deleteAnnotation).then(result => {});
    }
    
    /**
     * Clears indexing method CANCEL BUTTON.
     */
    resetIndexingSelection() {
        this.contentSourceData.contentSource = {};
        this.contentSourceData.authorization = {};
        this.contentSourceData.objectsAndFields = [];
        this.contentSourceData.spacesORboards = [];
        this.contentSourceData.apiCrawlerFields = {};
        this.contentSourceData.contentSource.content_source_type_id = '';
        this.addingContentSource = false;
        this.contentAnnotation = false;
        this.addingCs = '';
        this.contentSourceData.language = [];
    }

    /**
     * Add New content source.
     */
    setIndexingSelection(source: any) {
        this.contentSourceData.contentSource.editing = false;
        this.editMode = false;
        this.currentlyAddingCS = true;
        this.contentSourceData.contentSource.content_source_type_id = source;
        this.addingCs = this.allSupportedContentSourceTypes.find(x => x.id === source).name;
        this.instructions = this.allSupportedContentSourceTypes.find(x => x.id == source).instructions;
        this.getWebConfCopy={}
        this.spacesORboardsCopy = []
        this.objectsAndFieldsCopy = []
        this.contentSourceCopy = {}
        this.resetchangeInRulesTab = false;
    }

    toggleChildLoading(){
        this.savingContentSource = !this.savingContentSource;
    }

    /**
    * Save all content sources.
    */
    addUserContentSource(value,skipBack?) {
        if(!this.resetchangeInRulesTab){
            this.changeInRulesTab = false;
        }
        this.addContentSource = false;
        this.contentSourceData = value;
        if(value.tabName) this.tabName = value.tabName;
        delete this.contentSourceData.contentSource.pid;
        this.contentSourceData.objectsAndFields.map(x => { delete x.object_pid; delete x.object_status });
        this.objectsAndFieldsCopy.map(x => { delete x.object_pid; delete x.object_status; delete x.valid; });
        if (!this.authorizationChanged ){
            for (var field in this.contentSourceCopy){
                if (field.indexOf('sync') == -1 && this.contentSourceData.contentSource[field] && this.contentSourceCopy[field] != this.contentSourceData.contentSource[field]) {
                    this.indexChanged(7,this.contentSourceData.contentSource);
                    this.contentSourceCopy = JSON.parse(JSON.stringify(this.contentSourceData.contentSource));
                    break;
                }
            }
        }
        if (this.frequencyChanged) {
            this.indexChanged(8,this.contentSourceData.contentSource);
            this.frequencyChanged = false;
        }
        if ((this.contentSourceData.tabName == 'Rules' || this.contentSourceData.tabName == 'rules')
            && ((this.changeInRulesTab
                || (this.adminAnalyticsService.compareJsonAdminLogs(this.contentSourceData.spacesORboards || [], this.spacesORboardsCopy || [],[], this.excludeArray, 'spacesAndBoards', true))
                || (this.adminAnalyticsService.compareJsonAdminLogs(this.contentSourceData.getWebConf || {}, this.getWebConfCopy || {}, [],this.excludeArray,'getWebConf'))
                || (this.adminAnalyticsService.compareJsonAdminLogs(this.contentSourceData.objectsAndFields || {}, this.objectsAndFieldsCopy || {},[], this.excludeArray, 'objectsAndFields', true))
            )
            )
        ) {
            this.addContentSource = true;
        }
        this.contentSourceData.tabName = this.tabName;
        if(this.addContentSource){
            this.indexChanged(9, this.contentSourceData.contentSource);
            this.changeInRulesTab = false;
        }
        let csLabel = this.contentSourceData.contentSource.label;
        let pattern = /^[a-zA-Z0-9_ ]+$/;
        if(!csLabel.match(pattern) ){
            var toastOptions: ToastOptions = {
                title: "Warning",
                msg: "Spaces and Special characters are not allowed!",
                showClose: true,
                timeout: 3000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
            return;
        }
        this.contentSourceService.addContentSource(this.contentSourceData).then(result => {
            if (result.status == 403) {
                var toastOptions: ToastOptions = {
                    title: "Error",
                    msg: "Cannot save content source!",
                    showClose: true,
                    timeout: 2000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
            }
            else {
                this.contentSourceData = result;
                if ((this.contentSourceData.contentSource.content_source_type_id == 10 || this.contentSourceData.contentSource.content_source_type_id == 9) && !this.contentSourceData.web_conf)
                    this.contentSourceData.web_conf = {};
                var toastOptions: ToastOptions = {
                    title: "Content source saved",
                    msg: "Content source saved successfully!!",
                    showClose: true,
                    timeout: 1000,
                    theme: 'default'
                };
                if(this.saveUpdated != 0){
                    // this.indexChanged(6,this.contentSourceData.contentSource.label);
                }
                else{
                    if(this.contentSourceData.contentSource.content_source_type_id != 1 && this.contentSourceData.contentSource.content_source_type_id != 2 &&this.contentSourceData.contentSource.content_source_type_id != 3 &&this.contentSourceData.contentSource.content_source_type_id != 4 &&this.contentSourceData.contentSource.content_source_type_id != 9 && this.contentSourceData.contentSource.content_source_type_id != 11 && this.contentSourceData.contentSource.content_source_type_id != 16 && this.contentSourceData.contentSource.content_source_type_id != 19){
                        // this.indexChanged(5,this.contentSourceData.contentSource.label);
                    }
                }
                this.saveUpdated = 1;
                this.toastyService.success(toastOptions);
                if (!skipBack && this.tabName === 'Rules') {
                    this.savingContentSource  = false;
                    this.back('toggleEditMode');
                    this.getUserContentSources();
                    this.getAllSupportedContentSourceTypes();
                }
            }
        }, error => {
            var toastOptions: ToastOptions = {
                title: "Error",
                msg: "Cannot save content source!",
                showClose: true,
                timeout: 1000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
        });

    }

    setCronId(source) {
        this.cronId = source.id;
       this.openDialog(source, "crawl");
    }


    getTheme() {
        this.userManagementService.userSpecificSettings({type : 'get'}).then((res) => {
            this.activeTheme = res.data.lightMode || 'white';
        })
    }
    
    checkIfTempIndex (crawlMode, csTypeId) {
        return ((
            crawlMode == "update" && TEMP_MAPPING_CS.includes(csTypeId)
        ) || crawlMode == "reindexing") && !TEMP_MAPPING_EXCLUSIONS.includes(csTypeId);
    }

    openDialog(source?: any, action?:string){
        this.modalData.type = '';
        // this.getTheme();
        this.showModalLoader = false;
        if(action == 'oauthError'){
            this.modalData.action = action;
            this.modalData.source = source;
            this.modalData.okButton = true;
            this.activeTheme != "black" ? this.modalData.image = "ConnectionError.svg" : this.modalData.image = "ConnectionError.svg" ;
            this.modalData.message = [`<strong>Connection Error!</strong>`, `SearchUnify was not able to establish a connection with the specified Client URL. Please check the configurations and try connecting again`];
            this.modalData.note =[];
        }
        else if(action == 'stop'){
            this.modalData.action = action;
            this.modalData.source = source;
            this.modalData.noButton = true;
            this.modalData.yesButton =true;
            this.activeTheme != "black" ? this.modalData.image = "Crawling in Progress click Yes to stop.svg" : this.modalData.image = "Click Yes to stop black.svg"; 
            this.modalData.message = [`<strong>Crawling is in Progress.</strong>`, `<strong>Click "Yes" to stop.</strong>`];
            this.modalData.note = [
                `1. Last sync time will get Updated.`, 
                `2. Data might not be completely crawled.`
            ];

            if(this.checkIfTempIndex(source.current_crawl_mode, source.content_source_type_id)){
                this.modalData.note.push(
                    `3. You may see incomplete data for some time.`
                )
            }

        }else if(action == 'crawl'){
            this.modalData.action = action;
            this.modalData.source = source;
            Object.keys(this.userContentSources).forEach(key => {
                if(this.userContentSources[key]['content_source_type_id'] === 9 &&
                        this.userContentSources[key]['crawl_status'] === 1 ){
                        this.otherWebsiteRunning =  true;
                        }})

            const csWithSpaces = [2, 4, 5, 6, 8, 12, 13, 14, 15, 17, 18, 21, 23, 24, 26, 33, 39, 42, 46, 55, 61, 66, 67, 71];

            const csWithSpacesAndisSelected = [1];

            if(csWithSpaces.includes(source.content_source_type_id) && source.space_count == 0){
                this.modalData.image = "crawler_places_error.svg"
                this.modalData.okButton = true;
                this.modalData.noButton = false;
                this.modalData.yesButton = false;
                this.modalData.message = [`Configuration Incomplete! Make atleast one selection under Rules Tab to start crawling.`];
                this.modalData.note = [];
            } else if (csWithSpacesAndisSelected.includes(source.content_source_type_id) && source.space_count > 0 && source.isSelected_count == 0){
                this.modalData.image = "crawler_places_error.svg"
                this.modalData.okButton = true;
                this.modalData.noButton = false;
                this.modalData.yesButton = false;
                this.modalData.message = [`Configuration Incomplete! Make atleast one selection under Rules Tab to start crawling`];
                this.modalData.note = [];
            } else if (source.object_count == 0){
                this.modalData.image = "crawler_places_error.svg"
                this.modalData.okButton = true;
                this.modalData.noButton = false;
                this.modalData.yesButton = false;
                this.modalData.message = [`Configuration Incomplete! Make atleast one selection under Rules Tab to start crawling`];
                this.modalData.note = [];
            } else if(source.content_source_type_id === 9 && source.crawl_status != 1 &&  this.otherWebsiteRunning ){
                this.modalData.image = "website_parallel_crawl_error.svg"
                this.modalData.okButton = true;
                this.modalData.noButton = false;
                this.modalData.yesButton = false;
                this.modalData.message = [`Other website is being crawled. Either wait for it to finish or stop it manually.`];
                this.modalData.note = [];
            } else if(source.object_field_count === 0){
                this.modalData.image = "crawler_places_error.svg"
                this.modalData.okButton = true;
                this.modalData.noButton = false;
                this.modalData.yesButton = false;
                this.modalData.message = [`Configuration Incomplete! Select at least one field in the configured objects in the Rules Tab.`];
                this.modalData.note = [];
            } else if(source.sync_frequency_name != "Never") {
                this.modalData.image ="To update the current index.svg" ;
                this.modalData.okButton = true;
                this.modalData.noButton = false;
                this.modalData.yesButton = false;
                this.modalData.message = [`To update the current index, please set the current frequency to <strong>'Never'</strong>.`];
                this.modalData.note = [];
            }
            else if (source.crawl_status == 3 || source.crawl_status == 4 && source.current_crawl_mode == 'reindexing'){
                this.modalData.image = "to update current index with updated one latest.svg"; 
                this.modalData.noButton = true;
                this.modalData.yesButton =true;
                this.modalData.message = [`<strong>Click "Yes" to replace the current index with an updated one.</strong>`];
                this.modalData.note =[`Previous crawl is failed or terminated unexpectedly.`];
            }
           else if(source.crawl_status == 0) {
                this.modalData.image = "content Source set for indexing.svg"; 
                this.modalData.noButton = true;
                this.modalData.yesButton =true;
                this.modalData.message = [`<strong>Content Source set for indexing.</strong>`, `<strong>Click "Yes" to start.</strong>`];
                this.modalData.note =[];
            } 
            else{
                this.modalData.noButton = true;
                this.modalData.yesButton =true;
                this.activeTheme != 'black' ? this.modalData.image = `Click Yes to replace the current index.svg`  : this.modalData.image = "Click Yes to replace the current index black.svg";
                this.modalData.message = [`<strong>Click "Yes" to replace the current index with an updated one.</strong>`] 
                this.modalData.note = [`<strong>Search experience might be affected during the update.</strong>`]
            }
              
        } else if( action =='delete') {
            // deleteUserContentSource
            if([1,5].includes(source.crawl_status)){
            this.modalData.action = action;
            this.modalData.source = source;
            this.modalData.okButton = true;
            this.modalData.delete = false;
            this.activeTheme != "black" ? this.modalData.image = "Cannot Delete while crawl in progress.svg" : this.modalData.image = "Cannot Delete while crawl in progress.svg"; 
            this.modalData.message = [`<strong>Crawling in Progress</strong> <br> Cannot delete Content Source ${source.label}.`];
        }
            else{
            const id = source.id;
            this.modalData.action = action;
            this.modalData.type = 'delete_no_crawl';
            this.modalData.source = source;
            this.modalData.keep = true;
            this.modalData.delete = true;
            this.activeTheme != "black" ? this.modalData.image = "Delete.svg" : this.modalData.image = "Delete.svg";
            this.modalData.message = [];
            this.modalData.message2 = [];
            this.enableDeleteButton = false;
            this.modalData.message = [`<span class="openDialogTheme">Are you sure you want to delete ${source.label} content source?</span>`];
            this.showModalLoader = true;
            this.contentSourceService.getSearchClients(id).then(result => {
                this.showModalLoader = false;
                this.enableDeleteButton = true;
                let str = '';
                if (result.data && result.data.length) {
                    let searchClientNames = result.data.map((x)=> x.scName);
                    this.deleteScIds = result.data.map((x)=> x.scUid);
                    if(searchClientNames.length === 1) {
                        str = '"' + searchClientNames.join('" "') + '"';
                        this.modalData.message2 = [`<span>All the configurations related to this content source present in  <span class ="description-modal-bold"> ${str} </span> search client will be permanently deleted.</span>`];
                    } else if (searchClientNames.length > 1) {
                        str = `" ${searchClientNames.slice(0, -1).join('", "')}` + '" & ' + `"${searchClientNames.slice(-1)}"`; 
                        this.modalData.message2 = [`<span>All the configurations related to this content source present in  <span class ="description-modal-bold"> ${str} </span> search clients will be permanently deleted.</span>`];
                    }
                    //  this.modalData.message2 = [`<span>All the configurations related to this content source present in  <span class ="description-modal-bold"> ${str} </span> search clients will be permanently deleted.</span>`];
                } else {
                    // this.modalData.message2 = [''];
                    this.isLoading = false;
                }
         })
}
        } else if(action == 'startCrawlError'){
            this.modalData.action = action;
            this.modalData.source = source;
            this.modalData.okButton = true;
            this.activeTheme != "black" ? this.modalData.image = source.white_image : this.modalData.image = source.black_image; 
            this.modalData.message = [source.message];
        }
        // this.modalData.noButton = true; 
        document.getElementById("backdrop").style.display = "block"
        document.getElementById("alertModal").style.display = "block"
        document.getElementById("alertModal").className += "show"
        // console.log(document.getElementById("alertModal").className);
    }
    closeDialog(){
        this.showModal = false;
        document.getElementById("backdrop").style.display = "none"
        document.getElementById("alertModal").style.display = "none"
        // console.log(document.getElementById("alertModal").className );
        document.getElementById("alertModal").className = document.getElementById("alertModal").className.replace("fadeshow", "fade")
        // console.log(document.getElementById("alertModal").className);
        this.modalData.image = "";
        this.modalData.message= [];
        this.modalData.message2= [];
        this.modalData.note = [];
        this.modalData.noButton = false;
        this.modalData.okButton = false;
        this.modalData.yesButton = false;
        this.modalData.forcefulSuccess = false;
        this.modalData.keep = false;
        this.showModalLoader = false;
    }

    /**
    * Refresh Index
    */
    setCronAndCrawlData(source) {
        this.disablePopupButton = true;
        this.contentSourceService.setCronAndCrawlData(this.cronId).then(result => {
            if (result.flag === 401) {
                this.showToasty("Unauthorized!", "", "error")
            } else if (result.flag === 0) {
                this.indexChanged(1, source);
                this.showToasty("Success", `${result.data}`, "success");
            } else {
                let errorMessage = "Error!";
                if(result.flag && typeof result.data == 'string')
                    errorMessage = result.data;
                source.message = errorMessage;
                source         = {...source, ...CrawlerConstants.crawlDataApiResponseFlags[result.flag]};
                
                this.openDialog(source, "startCrawlError");
            }
            source.crawl_status = 1;
            this.getLogFiles(source);
            this.currentCrawling = this.cronId;
            this.getUserContentSources();
            this.showModal = false;
            this.disablePopupButton = false;
        });
    }

    /**
    * Stop Indexing
    */
    stopCrawler(modelData) {
        this.hasCrawlingStopped = true;
        let contentSource = {...modelData.source, forcefulSuccess: modelData.forcefulSuccess};
        delete contentSource.crawl_mode;
        delete contentSource.cs_type_name;
        delete contentSource.display;
        delete contentSource.editable;
        delete contentSource.shareAccess;
        delete contentSource.csOwnerName;
        delete contentSource.last_updated_by;
        this.otherWebsiteRunning = false;
        const index  = this.userContentSources.findIndex(obj => obj.cs_id === contentSource.cs_id)
        this.contentSourceService.stopCrawler(contentSource).then(result => {
            if (result.flag === 401) {
                this.showToasty("Unauthorized!", "", "error")
            } else if (result.flag === 0) {
                this.showToasty("Success", `${result.data}`, "success");
                this.indexChanged(2, contentSource);
            } 
            else if(result.flag === 204){
                this.showToasty("Error", `${result.data}`, "error");
                this.indexChanged(2, contentSource);
            }
            else {
                this.showToasty("Error", `${result.data}`, "error");
                
            }
            this.socket.emit(`stopCountCheck_${localStorage.getItem('t_id')}`, [contentSource]);
            //this.crawlStatus.splice(this.crawlStatus.indexOf(contentSource.id),1);
            contentSource.current_crawl_end_time = new Date().toISOString();
            this.hasCrawlingStopped = false;
            this.getUserContentSources();
        });

    }

    saveChildUserContentSource(contentSourceData) {
        this.contentSourceData = contentSourceData;
        this.tabName = this.contentSourceData.tabName;
        this.addUserContentSource(this.contentSourceData);
    }

    checkClone() {
        if (this.userContentSources.find(x => x.label.toLowerCase() === this.clone.name.toLowerCase())) {
            this.uniqueClone = true;
            var toastOptions: ToastOptions = {
                title: "Cannot save content source!",
                msg: "Same name already exists",
                showClose: true,
                timeout: 3000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
        }
        else {
            this.uniqueClone = false;
        }
    }

    cloneContentSource() {
        this.contentSourceService.cloneContentSource(this.clone).then(result => {
            if (result) {
                var toastOptions: ToastOptions = {
                    title: "Cloned successfully",
                    msg: "Content source cloned successfully",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.success(toastOptions);
                this.getUserContentSources();
            }
            else {
                var toastOptions: ToastOptions = {
                    title: "Cloning failed",
                    msg: "Content source cloning failed",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
            }
            this.clone = {};
        });
    }

    toggleClone(source) {
        this.clone.id = source.id;
        this.clone.name = '';
        this.clone.content_source_type_id = source.content_source_type_id;
        this.allowClone = !this.allowClone;
    }

    cancelEvent() {
        this.contentSourceData.contentSource = {};
        this.contentSourceData.authorization = {};
        this.contentSourceData.objectsAndFields = [];
        this.contentSourceData.spacesORboards = [];
        this.contentSourceData.apiCrawlerFields = {};
        this.contentSourceData.contentSource.content_source_type_id = '';
        this.addingContentSource = false;
        this.contentAnnotation = false;
        this.addingCs = '';
        this.getUserContentSources();
    }

    getCountUpdate(source) {
        let self = this;
        self.socket.emit(`getCount_${localStorage.getItem('t_id')}`, source);
    }

    countUpdateListener(){
        let self = this;
        this.socket.on(`countUpdate_${localStorage.getItem('t_id')}`, function (source) {
            self.userContentSources.find(function (o) {
                if (o.id == source.id) {
                    if(o.crawl_status === 1 || o.crawl_status === 5){
                        o.crawl_status = source.crawl_status;
                        o.count        = source.count;
                        o.tempCount    = source.tempCount;
                    }
                    return o;   
                }
            })
        });

        this.socket.on(`logsNotChanged_${localStorage.getItem('t_id')}`, function (source) {
            self.socket.emit(`stopWatching_${localStorage.getItem('t_id')}`, { source });
            self.socket.emit(`stopCountCheck_${localStorage.getItem('t_id')}`, [source]);
            //self.crawlStatus.splice(self.crawlStatus.indexOf(source.id),1);
            if (!source.stuck) {
                self.userContentSources.find(function (o) {
                    if (o.id == source.id && (o.pid != 0 && o.pid != -1)) {
                        o.pid = source.failed ? -1 : 0;
                        o.crawl_status = source.failed ? 3 : 2;
                        o.crawl_status = source.crawl_status;
                        o.last_sync_date = source.last_sync_date;
                        o.current_crawl_end_time = source.current_crawl_end_time;
                        o.count = source.count;
                        if(![3,4].includes(source.crawl_status)){
                            o.tempCount = 0;
                        }
                        self.otherWebsiteRunning = false;
                        var toastOptions: ToastOptions = {
                            title: "Success",
                            msg: `Crawling for ${o.label} completed successfully`,
                            showClose: true,
                            timeout: 1000,
                            theme: 'default'
                        };
                        if (o.pid == 0 && source.crawl_status != 5) {
                            self.toastyService.success(toastOptions);
                            if(source.content_source_type_id == 3)
                                self.getUserContentSources();
                        } else if (o.pid == -1) {
                            var toastOptions: ToastOptions = {
                                title: "!Try again",
                                msg: `Crawling for ${o.label} failed`,
                                showClose: true,
                                timeout: 1000,
                                theme: 'default'
                            };
                            self.toastyService.error(toastOptions);
                        }

                    }
                });
            }
        })
        this.socket.on(`syncingInProgress_${localStorage.getItem('t_id')}`, function (source) {
            if (!source.stuck) {
                self.userContentSources.find(function (cs) {
                    if (cs.id == source.id && (cs.pid != 0 && cs.pid != -1)) {
                        cs.crawl_status = source.crawl_status;
                        cs.count = source.count;
                    }
                })
            }
        })
    }

    editLastSyncDate(editLastSync, source) {
        if(editLastSync.form.valid){
     const csId = source.id == -1 ? null : source.id;
     const {value} = editLastSync;
     const lastSyncDate = value.date == -1 ? null: value.date;
        this.contentSourceService.updateLastSyncDate(csId, lastSyncDate).then(res => {
            if(!res.success) {
                var toastOptions: ToastOptions = {
                    title: "Updatation Failed",
                    msg: "Last Sync date updation Failed",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
            } else {
                source.last_sync_date=new Date(lastSyncDate).toISOString();
                var toastOptions: ToastOptions = {
                    title: "Updated successfully",
                    msg: "Last Sync date updated successfully",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.success(toastOptions);
            }
        });
        editLastSync.form.reset();
      }
    };


    crawlLogFileCheck(crawlFile, crawlStartTime){
        if(!crawlFile){
            return true;
        }
        let fileTime         = crawlFile.split('_');
        let i=0;
        if(!isNaN(fileTime[i+4]) && fileTime[i+4].length != 4){
            i=-1;
        }
        let crawlFileDate = fileTime[i+4]+ "-" + fileTime[i+5] + "-" + fileTime[i+6] + "T";
        let crawlFileTime = fileTime[i+8]+ ":" + fileTime[i+9] + ":" + fileTime[i+10] + ".000Z";
        let logFileTime = new Date(crawlFileDate + crawlFileTime).getTime();
        let crawlStartMinutes   = new Date(crawlStartTime).getTime();

        let crawlFileMinutesDiff = Math.abs(crawlStartMinutes - logFileTime)/(1000*60);
        if (!(crawlFileMinutesDiff >= 1.5)) {
            return false;
        }
        return true;
    }

    scrollToBottom(){
        clearInterval(this.scrollInterval);
        if (this.disableScrollDown) return;
        
        this.scrollInterval = setInterval(() => {
            if(this.myScrollContainer){
                this.disableScrollDown = false;
                this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
                clearInterval(this.scrollInterval);
            }
        });
    }

    processSectionsData(sectionSummary){
        if(sectionSummary && sectionSummary.length){
        const currentSectionIndex = sectionSummary.findIndex(element => element.status === CrawlerConstants.sectioningStatus.inProgress)
        this.logFileSource.currentSection = currentSectionIndex === -1 ? sectionSummary[0].status === CrawlerConstants.sectioningStatus.failed ? 1 : sectionSummary[3].status > 1 ? 5 : 0  : currentSectionIndex + 1; 
        this.logFileSource.sectionSummary = sectionSummary
        this.crawlingError = sectionSummary.find(el => el.status === CrawlerConstants.sectioningStatus.failed);
        this.logFileSource.percentageCrawlingDone = sectionSummary.find(el => el.status !== CrawlerConstants.sectioningStatus.completed) ? ((this.logFileSource.currentSection - 1) * 25 ) + 5 : 100;
        
        } else {
            this.logFileSource.currentSection = 0; 
            this.logFileSource.sectionSummary = this.defaultSectionSummary;
        }
    }

    convertTimezone(match: string): string{
        const momentDate = momentZone(match).tz(this.userTimeZone).format('YYYY-DD-MM HH:mm:ss')
        const dateHtml = `<span class="dateInCrawlingLogs">${momentDate}</span>`

        return dateHtml;
    }

    replaceLogString(logLines) {
        const headingRegex = /^-{5,}(.*?)-{5,}([\s\S]*?)(?=(?:-{5,}|$))/gm;
        const errorRegex = /\[ERROR\](.*?)\n/g;
        const replacedString = logLines.replace(headingRegex, '<div class="sectionHeading"><h1>$1</h1></div>');
        const replacedError = replacedString.replace(errorRegex, '<span class="logsErrorLine"> [ERROR] $1</span>\n');
        const replaceDates = replacedError.replace(/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z)/g, (match, p1) => {
            return this.convertTimezone(p1);
        } )
        this.logFileSource.logLines = replaceDates;
      }

    getContentSource(id,crawlStartMinutes,event, retryCount?){
        retryCount = retryCount || 0;
        const self =  this;
        let source;
        let index = this.userContentSources.findIndex((obj) => obj.id === id);
        this.contentSourceService.getContentSourceInRulesTab(id).then((csData) =>{
            this.userContentSources[index].adminLogFile = csData.data.contentSource.adminLogFile;
            source = csData.data.contentSource;
            crawlStartMinutes = source.current_crawl_start_time;
            let crawlTimeCheck = this.crawlLogFileCheck(source.adminLogFile,crawlStartMinutes)
            if (crawlTimeCheck && retryCount < 11) {
                retryCount = retryCount + 1;
                setTimeout(() => {
                    this.getContentSource(id,crawlStartMinutes,event, retryCount)    
                }, 5000);
            }else if (event === 'getLogLines') {
                this.logFileSource = source;
                this.socket.on(`lines_${localStorage.getItem('t_id')}`, (data) =>{
                    if(data.source.isLogFileMissing){
                        this.logFileSource.isLogFileMissing = true;
                        if(source.adminLogFile && data.source.canDeleteLogFile){
                            this.contentSourceService.updateLogFile({contentSourceId: source.id, objectLogFile: false}).then((res)=>{
                                const result = 'Log File Deleted successfully';
                            })
                        }
                        this.logFileSource.waitingScreen = false;
                        this.logFileSource.logFile = null;
                    }
                    else {
                        if (self.logFileSource.id == data.source.id) {
                            self.logFileSource.waitingScreen = false;
                            self.processSectionsData(data.source.sectioningData);
                            self.replaceLogString(data.source.logLines);
                            self.scrollToBottom();
                        }
                    }
                });
        
                this.socket.emit(`readFile_${localStorage.getItem('t_id')}`, { source });
            } else if (event === 'getCountUpdate') {
                this.getCountUpdate(source);
            }  
        })
    }

    onWheel(event: WheelEvent): void {
        this.disableScrollDown = true;
    }

    logFileNotFound(){
        return (!this.logFileSource || !this.logFileSource.logLines) && !this.logFileSource.waitingScreen && (!this.logFileSource.logFile || this.logFileSource.isLogFileMissing);
    }

    getLogFiles(source) {
        let self = this;
        this.disableScrollDown = false;
        this.logFileSource = source;
        this.logFileSource.percentageCrawlingDone = 0;
        source.logFile ? this.logFileSource.waitingScreen = true : this.logFileSource.waitingScreen = false;
        let crawlTimeCheck = this.crawlLogFileCheck(source.adminLogFile, source.current_crawl_start_time)
        if (crawlTimeCheck && [1,5].includes(source.crawl_status) && source.current_crawl_object_name == '-')  {
            this.logFileSource.logLines = '';
            this.logFileSource.currentSection = this.defaultSection;
            this.logFileSource.sectionSummary = this.defaultSectionSummary;
            this.logFileSource.waitingScreen = true;
            this.getContentSource(source.id,source.current_crawl_start_time, 'getLogLines');   
        } else {
            this.socket.on(`lines_${localStorage.getItem('t_id')}`, (data) => {
                if(data.source.isLogFileMissing){
                    self.logFileSource.isLogFileMissing = true;
                    if(source.adminLogFile && data.source.canDeleteLogFile){
                        this.contentSourceService.updateLogFile({contentSourceId: source.id, objectLogFile: false}).then((res)=>{
                           const result = 'Log File Deleted successfully';
                        })
                    }
                    self.logFileSource.waitingScreen = false;
                    self.logFileSource.logFile = null;
                }
                else{
                    if (self.logFileSource.id == data.source.id) {
                        self.processSectionsData(data.source.sectioningData);
                        self.replaceLogString(data.source.logLines)
                        self.scrollToBottom();
                        self.logFileSource.waitingScreen = false;
                    }
                }
            });
    
            this.socket.emit(`readFile_${localStorage.getItem('t_id')}`, { source });
        }
    }

    stopWatching(source) {
        this.socket.emit(`stopWatching_${localStorage.getItem('t_id')}`, { source });
        this.logFileSource = {};
        this.showLogs = false;
    }

    saveFileAs(text, csData) {
        var blob = new Blob([text], { type: 'text/html' });
        const labelForFileName = csData.label.replace(/[^a-zA-Z0-9 ]/g, "").replace(/\s+/g, "_");
        const filename = `${labelForFileName}_Crawler.log`
        saveAs(blob, filename);
    }
    downloadLogFile() {
        if(this.logFileSource  
        && this.logFileSource.adminLogFile && this.logFileSource.logLines){
            this.contentSourceService.downloadLogFile(this.logFileSource.adminLogFile).then(res => {
                this.saveFileAs(res, this.logFileSource);   
            })
        } else {
            this.showToasty("Unable to download files", "There is no data yet to be downloaded", "error");
        }
        
    }
    showToasty(title, msg, type) {
        var toastOptions: ToastOptions = {
            title,
            msg,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        if (type == 'success') {
            this.toastyService.success(toastOptions);
        }
        else if (type == 'error') {
            this.toastyService.error(toastOptions);
        } else if (type == 'warning') {
            this.toastyService.warning(toastOptions);
        }
    }
    ngOnDestroy() {
        this.socket.off(`countUpdate_${localStorage.getItem('t_id')}`);
        this.socket.close();
    }
}
interface MyWindow extends Window {
    myFunction(): void;
}

declare var window: MyWindow;
