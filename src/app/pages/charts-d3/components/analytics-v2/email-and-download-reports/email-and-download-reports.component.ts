import { <PERSON>MM<PERSON>, ENTER, SPACE } from '@angular/cdk/keycodes';
import { Component, OnInit, Input, ElementRef, ViewChild, HostListener } from '@angular/core';
import { FormControl, Validators, FormGroup } from '@angular/forms';
import { ToastyService, ToastyConfig, ToastOptions } from 'ng2-toasty';
import { MatAutocompleteSelectedEvent, MatAutocomplete } from '@angular/material/autocomplete';
import { MatChipInputEvent } from '@angular/material/chips';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import moment from 'moment/moment';
import { AnalyticService } from '../../../../../services/analytics';
import { SearchTuningService } from '../../../../../services/searchTuning.service';
import { ContentSourceService } from '../../../../../services/contentSource.service';
import { Router } from '@angular/router';
import { AnalyticV2Service } from 'app/services/analytics-v2.service';
import { AhAnalyticsService } from 'app/services/ah-analytics.service';
import { CookieService } from 'app/services/cookie.service';

import * as CryptoJS from 'crypto-js';

@Component({
  selector: 'app-email-and-download-reports',
  templateUrl: './email-and-download-reports.component.html',
  styleUrls: ['./email-and-download-reports.component.css'],
  providers: [SearchTuningService, AnalyticService, AhAnalyticsService, ContentSourceService]
})

export class EmailAndDownloadReportsComponent implements OnInit {

  @Input() searchClients: any;
  @Input() selectedContentSourceIndex: any;
  @Input() unassistedSSVQuarterStartDate: any;
  @Input() unassistedSSVQuarterEndDate: any;
  @Input() assistedCVQuarterStartDate: any;
  @Input() assistedCVQuarterEndDate: any;
  @Input() assistedSSVQuarterStartDate: any;
  @Input() costSavingQuarterStartDate: any;
  @Input() costSavingQuarterEndDate: any;
  @Input() assistedSSVQuarterEndDate: any;
  @Input() ahAdoptionQuarterStartDate: any;
  @Input() ahAdoptionQuarterEndDate: any;
  @Input() ahTtrQuarterStartDate: any;
  @Input() ahTtrQuarterEndDate: any;
  @Input() caseEscalationQuarterStartDate: any;
  @Input() caseEscalationQuarterEndDate: any;
  @Input() quarterFilter: any;
  @Input() directlyViewSetting: string;
  @Input() range: any;
  @Input() internalUserInside: any;
  @Input() internalUser: any;
  @Input() advertisementFiltersOnInitialLoad : any;
  @Input() advertisementSearchedQuery: any;
  @Input() tab: any;
  @Input() subTab: any;
  @Input() reportName: any;
  @Input() filterSelected: any;
  @Input() starsdownloadReport: any;
  @Input() selectedConversionType: any;
  @Input() Session: any;
  @Input() topConvertedKeywords: any;
  @Input() hideDownload: boolean;
  @Input() cookie: any;
  @Input() caseReportFilter: any;
  @Input() clickResultReport: any;
  @Input() sessionClickPositionReport: any;
  @Input() sessionClickText: any;
  @Input() searchType: string;
  @Input() searchTypeDeflection: string;
  @Input() searchFilterMenuValue: any; 
  @Input() clickFilterMenuValue: any; 
  @Input() supportFilterMenuValue: any; 
  @Input() caseFilterMenuValue: any; 
  @Input() articleFilter: any;
  @Input() GlobalSearchfilter : any;
  @Input() GlobalConversion  : any;
  @Input() SupportSearchFilter  :any;
  @Input() SupportConversonfilter :any;
  @Input() sortType: any;
  @Input() sortByField: any;
  @Input() searchingType: any;
  @Input() SearchInputText: any; 
  @Input() getDocumentsWithLargeContentIndexid: any;
  @Input() selectedActivityFilter: any;
  public ToggleChangeDateFormat: any;
  public ToggleCumulativeDownload: any;
  @Input() dropDownValue: any;
  @Input() objToSend: any;
  @Input() browserObjectToSend: any;
  @Input() searchResults: any;
  @Input() browsedResults: any;
  @Input() contentFacetsFilterObject: any;
  @Input() chipFilterArray: any;
  @Input() exactSearch: any;

  @Input() filterParameterSearchReport: any;
  @Input() filterParameterSearchReportLabel:any;
  @Input() searchReportFilters:any;
  @Input() highConversionSessionDetailReport: any;
  @Input() highConversionClickUrl: any;
  @Input() highConversionData: any;
  @Input() rangeDays: any;
  @Input() textFeedBackToggle:any;
  @Input() modeselectInsideResults: any;
  @Input() caseNumberText: any;
  @Input() linkedByText: any;
  @Input() isEcosystemSelected: boolean;
  @Input() ecoSystem:any;
  @Input() activitySortType:any;
  @Input() AllUserEmails: any;
  @Input() selectedActivityFilterInside: any;
  @Input() searchQuery1: any;
  @Input() reactionFilterType: any;
  @Input() userMetricVariable: string;
  @Input() userMetricsFilters: Map<string, { userMetricValues: string[], updateReport: any }>;
  @Input() userMetricValues: string[];
  @Input() searchFilterUserMetricValues: string[];
  @Input() isClusteringEnabled: boolean = true;
  @Input() costPerCase: any;
  @Input() featureTypes: any;

  public actionNames: any = [];
  public selectedValue: any;
  public reportsDownloadValue: any;
  public emailForReports: any;
  private userEmail: any;
  private chipFilterConvertedArray: any;
  public includeAdvanceFilterDetails: boolean = false;
  private disableSend = true;
  private sendFile = true;
  private selectedReport = true ;
  visible = true;
  selectable = true;
  removable = true;
  addOnBlur: boolean = true;
  maxLimit: number = 4;
  public emailResultType: any;

  selectedTimePeriod: string;
  longDurationReportNames: string[];
  emailDateRangeOptions: Array<{ value: string; label: string }>;
  emailDateRange: any;
  today: any;
  utcToday: any;

  separatorKeysCodes: number[] = [ENTER, COMMA, SPACE];
  emailFormAn: FormGroup
  emailCtrl = new FormControl('',[
    Validators.email,
    Validators.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")
  ]);

  filteredEmails: Observable<string[]>;
  userAddedData: string[] = [];
  hideExtraemail: boolean = false
  tempfuit: any;
  FormInvalid: boolean = false;
  TempReport: any;
  // dateDefaultValue: string = 'datetime';
  // sessionDefaultValue: string = 'perSession';
  dateDefaultValue: string ;
  sessionDefaultValue: string;

  @ViewChild('emailIdInput') emailIdInput: ElementRef<HTMLInputElement>;
  @ViewChild('auto') matAutocomplete: MatAutocomplete;
  @HostListener('document:keydown.tab', ['$event'])

  onKeydownHandler(event: KeyboardEvent) {
    if(event.key == 'Tab' ){
      event.preventDefault();
    }
}

  constructor(private router: Router, private cookieService: CookieService, private analyticsV2Service: AnalyticV2Service, private analyticService: AnalyticService, private toastyService: ToastyService, private searchTuningService: SearchTuningService, private ahAnalyticsService: AhAnalyticsService, private contentSourceService: ContentSourceService,) {
    this.today = new Date();
    this.utcToday = new Date(this.today.getUTCFullYear(), this.today.getUTCMonth(), this.today.getUTCDate(), this.today.getUTCHours(), this.today.getUTCMinutes(), this.today.getUTCSeconds(), this.today.getUTCMilliseconds());
    this.selectedTimePeriod = 'selectedDateRange';
    this.longDurationReportNames = ['TileData', 'Search Summary', 'Average Click Position Report', 'CaseDeflectionTrendChart', 'Content Gap Analysis Tiles data'];
    this.emailDateRangeOptions = [
      { value: 'selectedDateRange', label: 'Selected Date Range' },
      { value: 'last6Months', label: 'Last 6 months' },
      { value: `year_${this.today.getUTCFullYear() - 1}`, label: `Year ${this.today.getUTCFullYear() - 1}` },
      { value: `year_${this.today.getUTCFullYear() - 2}`, label: `Year ${this.today.getUTCFullYear() - 2}` }
    ];
    this.emailDateRange = {
      from: '',
      to: '',
    };

    this.filteredEmails = this.emailCtrl.valueChanges.pipe(
      startWith(null),
      map((fruit: string | null) => fruit ? this._filter(fruit) : this.AllUserEmails.slice()));
  }

  ngOnInit() {
    if (this.reportName) {
      this.TempReport = this.reportName.replace(/ +/g, "");
    }
    this.getSessionDetails();
    this.saveSessionTrackingDownloadSettings(false);
    if(this.reportName === 'Top Searches With No Result' || this.reportName === 'Searches With No Result') {
      this.getActionNameWithIcon();
    }
  }

  toggleOverflowVisible(attr){
    attr && document.body.setAttribute('style', `overflow: ${attr}`);
    !attr && document.body.removeAttribute('style');
  }

  onEmailTimePeriodChange() {
    if (this.selectedTimePeriod === 'last6Months') {
      this.emailDateRange.from = moment(this.utcToday).subtract(6, 'month').startOf('month').format("YYYY-MM-DD");
      this.emailDateRange.to = moment(this.utcToday).subtract(1, 'month').endOf('month').format("YYYY-MM-DD");
    } else if (this.selectedTimePeriod.startsWith('year_')) {
      const year = parseInt(this.selectedTimePeriod.split('_')[1]);

      this.emailDateRange.from = `${year}-01-01`;
      this.emailDateRange.to = `${year}-12-31`;
    } else if(this.selectedTimePeriod === 'selectedDateRange') {
      this.emailDateRange.to = this.range.to;
      this.emailDateRange.from = this.range.from;
    }
  }

  sendEmailToggle(){
    if(this.emailCtrl && this.emailCtrl.value && this.emailCtrl.value.length > 0){
      this.disableSend = false;
    }
    if((this.emailCtrl && this.emailCtrl.value && this.emailCtrl.value.length == 0 && (this.userAddedData && this.userAddedData.length == 0))){
      this.disableSend = true;
    }
  }

  onRadioButtonChange(event){
   this.disableSend = !this.userAddedData.length ? true : false
   this.selectedReport = false;
    this.emailResultType = event.value;
  }

  sendLimitReachedToasty () {
    const toastOptions: ToastOptions = {
      title: "Download Limit Reached!",
      msg: "You have reached the download limit. Try again later.",
      showClose: true,
      timeout: 3000,
      theme: 'default'
    };
    this.toastyService.error(toastOptions);
  }

  reportsFilter(csv, sendToemail, filterSelected?, starsdownloadReport?) {
    if(sendToemail !== 'Send Via Email' || this.selectedTimePeriod === 'selectedDateRange'){
      this.emailDateRange.to = this.range && this.range.to
      this.emailDateRange.from = this.range && this.range.from
    }
    this.cookieService.getSearchUnifySession().then(result => {
      if(result){
    if (!this.sendFile) { return; }
    if (this.tab == "OverView") { /* ###########-------- Check For OverView --------########### */
      if (this.reportName == "TileData") {
        if (sendToemail === 'Send Via Email') {
          this.getTilesData(csv, sendToemail);
        } else {
          this.getTilesData(csv, sendToemail);
        }
      }
      else if (this.reportName == "Search Summary") {
        if (sendToemail === 'Send Via Email') {
          this.drawSearchHistogram(csv, sendToemail);
        } else {
          this.drawSearchHistogram(csv, sendToemail)
        }
      }
      else if (this.reportName == "All Searches") {
        if (sendToemail === 'Send Via Email') {
          this.getTopSearches(csv, sendToemail);
        } else {
          this.getTopSearches(csv, sendToemail)
        }
      } else if (this.reportName == "Top Successful Searches") {
        if (sendToemail === 'Send Via Email') {
          this.getTopSuccessfulSearches(csv, sendToemail);
        } else {
          this.getTopSuccessfulSearches(csv, sendToemail)
        }
      } else if (this.reportName == "Top Searches With No Clicks") {
        if (sendToemail === 'Send Via Email') {
          this.getTopSearchesWithNoClick(csv, sendToemail);
        } else {
          this.getTopSearchesWithNoClick(csv, sendToemail)
        }
      } else if (this.reportName == "Top Searches With No Result") {
        if (sendToemail === 'Send Via Email') {
          this.getTopSearchesWithNoResult(csv, sendToemail);
        } else {
          this.getTopSearchesWithNoResult(csv, sendToemail)
        }
      } else if (this.reportName == "Search index by content source") {
        if (sendToemail === 'Send Via Email') {
          this.getTypesStatistics(csv, sendToemail, this.reportName);
        } else {
          this.getTypesStatistics(csv, sendToemail, this.reportName)
        }
      }
      else if (this.reportName == "Newly added content sources") {
        if (sendToemail === 'Send Via Email') {
          this.getFrequencyBySource(csv, sendToemail, this.reportName);
        } else {
          this.getFrequencyBySource(csv, sendToemail, this.reportName)
        }
      } else if (this.reportName == "Newly added content sources") {
        if (sendToemail === 'Send Via Email') {
          this.getFrequencyBySource(csv, sendToemail, this.reportName);
        } else {
          this.getFrequencyBySource(csv, sendToemail, this.reportName)
        }
      } else if (this.reportName == "Top Rated Featured Results") {
        if (sendToemail === 'Send Via Email') {
          this.getTopUsefulFeaturedSnippet(csv, sendToemail);
        } else {
          this.getTopUsefulFeaturedSnippet(csv, sendToemail)
        }
      } else if (this.reportName == "Top Knowledge Graph Titles") {
        if (sendToemail === 'Send Via Email') {
          this.getTopKnowledgeGraphTitles(csv, sendToemail);
        } else {
          this.getTopKnowledgeGraphTitles(csv, sendToemail)
        }
      } else if (this.reportName == "Content Experience Feedback") {
        if (sendToemail === 'Send Via Email') {
          this.getPageRatingFeedback(csv, sendToemail,filterSelected,starsdownloadReport);
        } else {
          this.getPageRatingFeedback(csv, sendToemail,filterSelected,starsdownloadReport)
        }
      }else if (this.reportName == "Cases Created") {
        if (sendToemail === 'Send Via Email') {
          this.getCreatedCaseReport(csv, sendToemail);
        } else {
          this.getCreatedCaseReport(csv, sendToemail)
        }
      }else if(this.reportName == 'sessionActivityDetail'){
        if (sendToemail === 'Send Via Email') {
          this.getCreatedCaseReportDetails(csv, sendToemail);
        } else {
          this.getCreatedCaseReportDetails(csv, sendToemail)
        }
      }else if (this.reportName == "Click Position Report") {
        if (sendToemail === 'Send Via Email') {
          this.getClickPositionReport(csv, sendToemail);
        } else {
          this.getClickPositionReport(csv, sendToemail)
        }
      }else if (this.reportName == "Session Click Position") {
        if (sendToemail === 'Send Via Email') {
          this.getSessionActivityClickPositionReport(csv, sendToemail);
        } else {
          this.getSessionActivityClickPositionReport(csv, sendToemail)
        }
      }else if (this.reportName == "Average Click Position Report") {
        if (sendToemail === 'Send Via Email') {
          this.getAverageClickPositionReport(csv, sendToemail);
        } else {
          this.getAverageClickPositionReport(csv, sendToemail)
        }
      }else if (this.reportName == 'searchReportNested'){
        if (sendToemail === 'Send Via Email') {
          this.searchesNestedReport(csv, sendToemail);
        } else {
          this.searchesNestedReport(csv, sendToemail)
        }
      }
      else if (this.reportName == "Search Experience Feedback") {
        if (sendToemail === 'Send Via Email') {
          this.getSearchFeedback(csv, sendToemail);
        } else {
          this.getSearchFeedback(csv, sendToemail)
        }
      }
      else if (
        this.reportName == "All searches-session details" ||
        this.reportName == "Successful searches-session details" ||
        this.reportName == "Searches with no clicks-session details" ||
        this.reportName == "Searches with no result-session details"
        ){
        if (sendToemail === 'Send Via Email') {
          this.sessionFilterNestedReport(csv, sendToemail);
        } else {
          this.sessionFilterNestedReport(csv, sendToemail)
        }
      }  else if (this.reportName === "Search Group Queries") {
        if (sendToemail === 'Send Via Email') {
          this.searchGroupQueriesNestedReport(csv, sendToemail);
        } else {
          this.searchGroupQueriesNestedReport(csv, sendToemail)
        }
      }
      else if(
        this.reportName == "Advertisement Performance Report"
      ){
        if (sendToemail === 'Send Via Email') {
          this.advertisementCall(csv, sendToemail);
        } else {
          this.advertisementCall(csv, sendToemail)
        }
      }

      else if(
        this.reportName == "SearchUnifyGPT Feedback"
      ){
        if (sendToemail === 'Send Via Email') {
          this.feedbackDownloadCall(csv, sendToemail);
        } else {
          this.feedbackDownloadCall(csv, sendToemail)
        }
      }

    } else if (this.tab == "Conversions") { /* ####### -------* Conversion Tab Here *------- ##### */
      if (this.reportName == "SessionTrackingDetails") {
        if (sendToemail === 'Send Via Email') {
          this.getSessionTrackingDetails(csv, sendToemail);
        }
      } else if (this.reportName == "SessionTrackingActivityDetails") {
        if (sendToemail === 'Send Via Email') {
          this.getSelectedSessionsActivityDetailsDownload(csv, sendToemail);
        }
      } else if (this.reportName == "TopSearchesWithClicks") {
        if (sendToemail === 'Send Via Email') {
          this.getTopSearchSessions(csv, sendToemail);
        } else {
          this.getTopSearchSessions(csv, sendToemail);
        }
      } else if (this.reportName == "SearchFilterBasedClicks") {
        if (sendToemail === 'Send Via Email') {
          this.getFilterBasedSearchChart(csv, sendToemail);
        } else {
          this.getFilterBasedSearchChart(csv, sendToemail);
        }
      } else if (this.reportName == "TopClickedSearchResults") {
        if (sendToemail === 'Send Via Email') {
          this.getTopClickedResults(csv, sendToemail);
        } else {
          this.getTopClickedResults(csv, sendToemail);
        }
      } else if (this.reportName == "TopClickedSearches") {
        if (sendToemail === 'Send Via Email') {
          this.getConversions(csv, sendToemail);
        } else {
          this.getConversions(csv, sendToemail);
        }
      }else if(this.reportName == "Discussionreadytobecomehelparticle"){
        if (sendToemail === 'Send Via Email') {
          this.getReadyDiscussions(csv, sendToemail);
        } else {
          this.getReadyDiscussions(csv, sendToemail);
        }
      } else if (this.reportName == "AttachedtoCase") {
        if (sendToemail === 'Send Via Email') {
          this.getAttachedToCaseReport(csv, sendToemail);
        } else {
          this.getAttachedToCaseReport(csv, sendToemail);
        }
      } else if (this.reportName == "ArticlesfailedtoDeflectcases") {
        if (sendToemail === 'Send Via Email') {
          this.getCaseFormPageReport(csv, sendToemail);
        } else {
          this.getCaseFormPageReport(csv, sendToemail);
        }
      } else if (this.reportName == "ShareResultsAnalytics") {
        if (sendToemail === 'Send Via Email') {
          this.getShareResultsAnalyticsChart(csv, sendToemail);
        } else {
          this.getShareResultsAnalyticsChart(csv, sendToemail)
        }
      } else if (this.reportName == "DeflectiononCaseSubmissionPage") {
        if (sendToemail === 'Send Via Email') {
          this.getCaseNotCreatedFormPageReport(csv, sendToemail);
        } else {
          this.getCaseNotCreatedFormPageReport(csv, sendToemail);
        }
      }else if (this.reportName == "CaseDeflectionTrendChart") {
          this.caseDeflectionTrendsDownload(csv, sendToemail);
      }else if (this.reportName == "RelevanceIndexQuarterlyReport") {
          if(sendToemail === 'Send Via Email'){
          this.relevanceIndexTrendsDownload(csv, sendToemail);
        } else {
          this.relevanceIndexTrendsDownload(csv, sendToemail);
      }
      }


    } else if (this.tab == "ContentGapAnalysis") { /* ###### ----* CONTENT GAP ANALYSIS *----- ##### */
      if (this.reportName == "Unsuccessful Searches") {
        if (sendToemail === 'Send Via Email') {
          this.drawUnsuccessfulSearchHistogram(csv, sendToemail);
        } else {
          this.drawUnsuccessfulSearchHistogram(csv, sendToemail);
        }
      } else if (this.reportName == "Searches With No Clicks") {
        if (sendToemail === 'Send Via Email') {
          this.getTopSearchesWithNoClickGap(csv, sendToemail);
        } else {
          this.getTopSearchesWithNoClickGap(csv, sendToemail);
        }
      } else if (this.reportName == "Searches With No Result") {
        if (sendToemail === 'Send Via Email') {
          this.getTopSearchesWithNoResultGap(csv, sendToemail);
        } else {
          this.getTopSearchesWithNoResultGap(csv, sendToemail);
        }
      } else if (this.reportName == "Sessions with unsuccessful searches") {
        if (sendToemail === 'Send Via Email') {
          this.drawUnsuccessfulSearchSessionHistogram(csv, sendToemail);
        } else {
          this.drawUnsuccessfulSearchSessionHistogram(csv, sendToemail);
        }
      } else if (this.reportName == "KCS Articles Usage Analytics") {
        if (sendToemail === 'Send Via Email') {
          this.getKCSDetails(csv, sendToemail,"KCS Articles Usage Analytics");
        } else {
          this.getKCSDetails(csv, sendToemail,"KCS Articles Usage Analytics");
        }
      } else if (this.reportName == "High Conversion Results Not on Page One") {
        if (sendToemail === 'Send Via Email') {
          this.getTopBackwardDocuments(csv, sendToemail);
        } else {
          this.getTopBackwardDocuments(csv, sendToemail);
        }
      } else if (this.reportName == "High Conversion Session Results Not on Page One") {
        if (sendToemail === 'Send Via Email') {
          this.highConversionSessionReport(csv, sendToemail);
        } else {
          this.highConversionSessionReport(csv, sendToemail);
        }
      }else if (this.reportName == "KCS Articles") {
        if (sendToemail === 'Send Via Email') {
          this.getKcsArticles(csv, sendToemail);
        } else {
          this.getKcsArticles(csv, sendToemail);
        }
      }else if(this.reportName === "Documents by content length"){
        if (sendToemail === 'Send Via Email') {
          this.getDocumentsWithLargeContent(csv, sendToemail);
        } else {
          this.getDocumentsWithLargeContent(csv, sendToemail);
        }
      }else if(this.reportName === "Content Gap Analysis Tiles data"){
        this.getContentGapAnalysisTilesData(csv, sendToemail);
      } else if(this.reportName === "Article Usage by Agents report"){
        if (sendToemail === 'Send Via Email') {
          this.getArticlesUsageByAgent(csv, sendToemail);
        } else {
          this.getArticlesUsageByAgent(csv, sendToemail);
        }
      }

        } else if (this.tab == "Browse") {
          if (sendToemail === 'Send Via Email') {

            this.disableSend = true;
            if(( this.searchResults && this.searchResults.length <= 0 && this.subTab == 'searchClientContent') 
              || (this.browsedResults && this.browsedResults.length <= 0 && this.subTab == 'BrowsedContent')){
              var toastOptions: ToastOptions = {
                title: 'No search results found, to be send via email.',
                msg: 'No search results found, to be send via email.',
                showClose: true,
                timeout: 3000,
                theme: 'default'
              };
              this.disableSend = true;
              this.toastyService.error(toastOptions);
            } else if (!this.userAddedData.length) {
              var toastOptions: ToastOptions = {
                title: 'Please enter valid email address',
                msg: 'Please enter valid email address.',
                showClose: true,
                timeout: 3000,
                theme: 'default'
              };
              this.disableSend = true;
              this.toastyService.error(toastOptions);
            } else if (!this.emailResultType) {
              var toastOptions: ToastOptions = {
                title: 'Please select at least one option.',
                msg: 'Please select at least one option.',
                showClose: true,
                timeout: 3000,
                theme: 'default'
              };
              this.disableSend = true;
              this.toastyService.error(toastOptions);
            } else {
              this.disableSend = false;
              if (this.subTab == 'BrowsedContent') {
                this.emailBrowsedResults(csv, sendToemail);
              }
              else {
                this.emailSearchResults(csv, sendToemail);
                var toastOptions: ToastOptions = {
                  title: 'Sent email successfully',
                  msg: 'Sent email successfully',
                  showClose: true,
                  timeout: 3000,
                  theme: 'default'
                };
                this.toastyService.success(toastOptions);
              }
            }
          }
        } else if(this.tab == "ah-analytics" ){
          if (this.reportName == "Agent Helper Adoption") {
              this.ahAdoptionMetrics(csv, sendToemail,this.quarterFilter);
          }else if (this.reportName == "Average Ttr Metrics") {
              this.averageTtrMetrics(csv, sendToemail,this.quarterFilter);
          }
          else if (this.reportName == "Case Escalation Metrics") {
            this.caseEscalationMetrics(csv, sendToemail,this.quarterFilter);
        }
          this.getAgentHelperAnalytics(csv,sendToemail);
        } else if (this.tab == "leadershipDashboard") { /* ###### ----* Leadership Dashboard *----- ##### */
          if (this.reportName == "Unassisted Self Solve Volume") {
            if (sendToemail === 'Send Via Email') {
              this.unassistedSelfSolveVolume(csv, sendToemail);
            } else {
              this.unassistedSelfSolveVolume(csv, sendToemail);
            }
          } else if (this.reportName == "Assisted Case Volume") {
            if (sendToemail === 'Send Via Email') {
              this.assistedCaseVolume(csv, sendToemail);
            } else {
              this.assistedCaseVolume(csv, sendToemail);
            }
          } else if (this.reportName == "Assisted Self Solve Volume") {
            this.assistedSelfSolveVolume(csv, sendToemail);
          } else if (this.reportName == "Cost Savings due to Explicit Deflection") {
            this.deflectionCostSavings(csv, sendToemail);
          }
    
            }
      } else {
        console.log(this.reportName)
        this.router.navigate(['/']);
      }
      this.ClearForm();
  });
  }



  onInputChangeSessionToggle(event, value) {
    if (value == 'dateFormat') {
      if (event.value == 'date') {
        this.ToggleChangeDateFormat = true;
      } else {
        this.ToggleChangeDateFormat = false;
      }

      this.dateDefaultValue = event.value;

    } else if (value == 'activityCount') {
      if (event.value == 'perActivity') {
        this.ToggleCumulativeDownload = true;
      } else {
        this.ToggleCumulativeDownload = false;
      }

      this.sessionDefaultValue = event.value;
    } else {
      if (event.checked && value == 'advanceSearch') {
        this.includeAdvanceFilterDetails = true;
      } else {
        this.includeAdvanceFilterDetails = false;
      }
    }

    this.saveSessionTrackingDownloadSettings(true)
  }

  getTilesData(csv: any, sendToemail) {
    // if (csv) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Tile Data");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTileDataDownload(this.searchClients, this.emailDateRange, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.userAddedData = []
      this.disableSend = true;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
    // } else {
    //     var cntx = this;
    //     this.analyticsV2Service.getTileData(this.searchClients, this.range, this.internalUser).then(result => {
    //         if (result.status && result.status == true) {
    //             cntx.tileData = result.data;
    //         }
    //     });
    // }
  }


  drawSearchHistogram(csv: number, sendToemail) {

    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Summary");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getSearchSummaryChartDownload(this.searchClients, this.emailDateRange, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.selectedConversionType, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }


  getTopSearches(csv: number, sendToemail) {
    // if (csv) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = ''
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (All Searches)");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopSearchesDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.searchReportFilters,this.isClusteringEnabled, userMetricValues).then(result => {
      // this.isReportUp = true;
      this.range.csv = 0;
      if(result.data && result.data.length > 0){

        var toastOptions: ToastOptions = {
          title: "Report added to que",
          msg: result.data[0].message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }else{
      
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
    //}
  }


  getTopSuccessfulSearches(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopSuccessfulSearchesDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.searchReportFilters, this.isClusteringEnabled, userMetricValues).then(result => {
      if(result && result.data.length > 0){

        var toastOptions: ToastOptions = {
          title: "Report added to que",
          msg: result.data[0].message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }else{
      
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
      // this.isReportUp = true;
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }


  getTopSearchesWithNoClick(csv: number, sendToemail) {
    // if (csv) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }

    this.analyticsV2Service.getTopSearchesWithNoClickDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.searchReportFilters, this.isClusteringEnabled, userMetricValues).then(result => {
      if(result.data.length > 0){

        var toastOptions: ToastOptions = {
          title: "Report added to que",
          msg: result.data[0].message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }else{
      
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
      // this.isReportUp = true;
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }


  getTopSearchesWithNoResult(csv: number, sendToemail) {
    let actionFilters = [];
    let reportId = 1; // Report Id for overview tab searches with no results 
    if(localStorage.actionFilterApplied != null) {
      let actionSavedFilterObj = JSON.parse(localStorage.actionFilterApplied);
      if(actionSavedFilterObj.hasOwnProperty(this.searchClients.uid)) {
          actionSavedFilterObj[this.searchClients.uid].forEach(filteredAction => {
            actionFilters.push(filteredAction);   
        });
      } else {
        this.actionNames.forEach(action => {
          actionFilters.push(action);      
        });
      } 
    } else {
      this.actionNames.forEach(action => {
        actionFilters.push(action);      
      });
    }
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopSearchesWithNoResultDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.searchReportFilters, actionFilters, reportId, this.isClusteringEnabled, userMetricValues).then(result => {
      if(result.data.length > 0){

        var toastOptions: ToastOptions = {
          title: "Report added to que",
          msg: result.data[0].message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }else{
      
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
      // this.isReportUp = true;
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }


  getTypesStatistics(csv: number, sendToemail, label: string) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 0;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
      csv = 0
    } else {
      this.reportsDownloadValue = 1;
      this.emailForReports = ''
    }
    this.analyticsV2Service.getTypesStatistics(csv, label, this.reportsDownloadValue, this.emailForReports).then(result => {
      if(result.message === "Api limit exceeded !!"){
        this.sendLimitReachedToasty();
      } else {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.success(toastOptions);
      }
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
    
  }


  getFrequencyBySource(csv: number, sendToemail, label: string) {
    // if (csv) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 0;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 1;
      this.emailForReports = ''
    }
    this.analyticsV2Service.getContentCount(csv, label, this.reportsDownloadValue, this.emailForReports).then(result => {
      if(result.message === "Api limit exceeded !!"){
        this.sendLimitReachedToasty();
      } else {
          var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
          };
          this.toastyService.success(toastOptions);
      }
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
    // }
  }


  getTopUsefulFeaturedSnippet(csv: number, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Top Rated Featured Results");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopUsefulFeaturedSnippetDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }



  getTopKnowledgeGraphTitles(csv: number, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }

    this.analyticsV2Service.getTopKnowledgeGraphTitlesDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getSearchFeedback(csv: number, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Experience Feedback");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getSearchFeedbackDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.textFeedBackToggle, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getShareResultsAnalyticsChart(csv: number, sendToemail) {
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Share Results Analytics");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
          [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.shareResultsAnalyticsCSV(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.modeselectInsideResults, this.caseNumberText, this.linkedByText, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }


  getPageRatingFeedback(csv: number, sendToemail,filterSelected?,starsdownloadReport?) {
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Content Experience Feedback");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getPageRatingFeedbackDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports,filterSelected,starsdownloadReport, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }
/**
 * logic to handle either clicked on download button or send via report
 * @param csv  
 * @param sendToemail  sendToemail got value only when user sends report via email
 */

  advertisementCall(csv: number, sendToemail){
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.getAdvertisementReportList(csv)
  }

  feedbackDownloadCall(csv: number, sendToemail){
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.getFeedbackDownReportList(csv, sendToemail)
  }

/**
 * Function executes to get the report downloaded or send to email
 * @param csv 
 */

  getAdvertisementReportList(csv){
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Advertisement Performance Report");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getAdvertisementReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports,this.advertisementFiltersOnInitialLoad,this.advertisementSearchedQuery, userMetricValues).then((result)=>{
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }

  getFeedbackDownReportList(csv: number, sendToemail){
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.responseFeedbackDownload(this.searchClients.name, this.searchClients.uid, this.range.from, this.range.to, this.reactionFilterType, this.searchQuery1, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then((result)=>{
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }
  
  getCreatedCaseReport(csv: number, sendToemail) {
    // console.log("coming in the function", sendToemail);
    let obj = {
      caseUid:  this.caseReportFilter[0].value,
      caseSubject: this.caseReportFilter[1].value,
      cookie: this.caseReportFilter[2].value,
      emailId: this.caseReportFilter[3].value
    };
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Cases Created");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getCaseCreatedReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, obj, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getCreatedCaseReportDetails(csv: number, sendToemail) {
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Cases Created");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getSessionAndActivityReportDownload(this.searchClients, this.range, this.internalUser,this.cookie,csv, this.reportsDownloadValue, this.emailForReports, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getClickPositionReport(csv: number, sendToemail) {
    let obj = {
      searchQuery:  this.clickResultReport[0].value
    };
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Click Position Report");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getSearchClickPositionReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, obj, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getSessionActivityClickPositionReport(csv: number, sendToemail) {
    let obj = {
      searchText: this.sessionClickText,
      searchQuery:  this.sessionClickPositionReport[0].value,
      timeZoneOffset: new Date().getTimezoneOffset()
    };
    this.range.csv = 4;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Click Position Report");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getSessionActivityClickPositionReportDownload(this.range, this.searchClients.uid, this.internalUser,  this.range.csv, this.reportsDownloadValue, this.emailForReports, obj, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }
  
  getAverageClickPositionReport(csv: number, sendToemail) {
    // console.log("coming in the function", sendToemail);
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Average Click Position");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getAverageClickPositionReportDownload(this.emailDateRange, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }

  searchGroupQueriesNestedReport(csv: number, sendToEmail) {
    if (sendToEmail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }

    // Fetching report specific user metric values from the map
    let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined;
    if (this.searchReportFilters.isClicked === 'all') {
      userMetricObject = this.userMetricsFilters.get("Search Classifications (All Searches)");
    } else if (this.searchReportFilters.isClicked === true) {
      userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
    } else if (this.searchReportFilters.isClicked === false) {
      userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
    } else if (this.searchReportFilters.isClicked === 'noResult') {
      userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
    }
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.searchGroupQueriesNestedReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.searchReportFilters, this.reportName, this.isClusteringEnabled, userMetricValues).then(result => {
      this.range.csv = 0;
      if(result.data.length > 0) {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
    })

  }

  searchesNestedReport(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }

    let csvFileName = this.filterParameterSearchReportLabel;
    // Fetching report specific user metric values from the map
    let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined;
    if (this.filterParameterSearchReport.isClicked === 'all') {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (All Searches)");
        csvFileName = this.isClusteringEnabled ? 'All_Search_Groups-Facet_Details' : this.filterParameterSearchReportLabel;
    } else if (this.filterParameterSearchReport.isClicked === true) {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
        csvFileName = this.isClusteringEnabled ? 'Successful_Search_Groups-Facet_Details' : this.filterParameterSearchReportLabel;
    } else if (this.filterParameterSearchReport.isClicked === false) {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
        csvFileName = this.isClusteringEnabled ? 'Search_Groups_with_no_Click-Facet_Details' : this.filterParameterSearchReportLabel;
    } else if (this.filterParameterSearchReport.isClicked === 'noResult') {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        csvFileName = this.isClusteringEnabled ? 'Search_Groups_with_no_Result-Facet_Details' : this.filterParameterSearchReportLabel;
    }
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.searchNestedReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.filterParameterSearchReport, csvFileName, this.isClusteringEnabled, userMetricValues).then(result => {
      this.range.csv = 0;
      if(result.data.length > 0){

        var toastOptions: ToastOptions = {
          title: "Report added to que",
          msg: result.data[0].message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }else{
      
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  sessionFilterNestedReport(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }

    let csvFileName = this.reportName;
    // Fetching report specific user metric values from the map
    let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined;
    if (this.searchReportFilters.filterByReport === 'allSearches') {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (All Searches)");
        csvFileName = this.isClusteringEnabled ? 'All Search Groups-session details' : this.reportName;
    } else if (this.searchReportFilters.filterByReport === 'successfullSearches') {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
        csvFileName = this.isClusteringEnabled ? 'Successful Search Groups-session details' : this.reportName;
    } else if (this.searchReportFilters.filterByReport === 'noClicks') {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
        csvFileName = this.isClusteringEnabled ? 'Search Groups with no Click-session details' : this.reportName;
    } else if (this.searchReportFilters.filterByReport === 'noResult') {
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        csvFileName = this.isClusteringEnabled ? 'Search Groups with no Result-session details' : this.reportName;
    }
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.sessionFilterNestedReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.searchReportFilters, csvFileName, this.isClusteringEnabled, userMetricValues).then(result => {
      this.range.csv = 0;
      if(result.data.length > 0) {

     
      
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  /* #####----- ConversionTab Started ----- ##### */

  getSessionTrackingDetails(csv: any, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    }
    let timeZoneOffset = new Date().getTimezoneOffset();
    var reportsDownloadValue = 1;
    this.chipFilterConvertedArray=Array.from(this.chipFilterArray);
    this.analyticsV2Service.getSessionsActivityDetailsDownload(
        this.searchClients, 
        this.range, 
        this.internalUser, 
        this.searchFilterMenuValue, 
        this.clickFilterMenuValue, 
        this.supportFilterMenuValue, 
        this.caseFilterMenuValue, 
        this.articleFilter,
        this.GlobalSearchfilter,
        this.GlobalConversion ,
        this.SupportSearchFilter  ,
        this.SupportConversonfilter ,
        this.sortType,
        this.sortByField,
        this.searchingType,
        this.SearchInputText, 
        this.selectedActivityFilter,
        csv, 
        this.ToggleChangeDateFormat,
        this.ToggleCumulativeDownload,
        this.includeAdvanceFilterDetails,
        this.reportsDownloadValue, 
        this.emailForReports,
        timeZoneOffset,
        this.contentFacetsFilterObject,
        this.chipFilterConvertedArray,
        this.exactSearch,
        this.userMetricVariable,
        this.userMetricValues).then(result => {

          if(result.data.length > 0){

            var toastOptions: ToastOptions = {
              title: "Report added to que",
              msg: result.data[0].message,
              showClose: true,
              timeout: 3000,
              theme: 'default'
            };
          }else{
          
            var toastOptions: ToastOptions = {
              title: "Report created!",
              msg: result.message,
              showClose: true,
              timeout: 3000,
              theme: 'default'
            };
          }
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }

  getSelectedSessionsActivityDetailsDownload(csv, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    }

    var reportsDownloadValue = 1;
    csv = 4;
    this.analyticsV2Service.getSelectedSessionsActivityDetailsDownload(this.searchClients, this.range, this.internalUser,this.internalUserInside, csv, this.Session.cookie, reportsDownloadValue, this.emailForReports, this.activitySortType, this.selectedActivityFilterInside, this.userMetricVariable, this.userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getTopSearchSessions(csv: number, sendToemail) {
    // if (csv) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (Conversions)");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopSearchSessionsDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.isClusteringEnabled, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
    // } else {
    //     this.searchSessions = [];
    //     this.analyticsV2Service.getTopSearchSessions(this.searchClients, this.range, this.internalUser).then(result => {
    //         this.searchSessions.isEmpty = false;
    //         if (result.status == true && result.data.length !== 0) {
    //             this.searchSessions = result.data;
    //             this.searchSessions.isEmpty = false;
    //         } else {
    //             this.searchSessions.isEmpty = true;
    //             this.searchSessions.push('empty');
    //         }
    //     });
    // }
  }

  getFilterBasedSearchChart(csv, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
    }
    let userMetricValues: any;
    if (this.searchFilterUserMetricValues && this.searchFilterUserMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: this.searchFilterUserMetricValues
      };
    }
    if (csv) {
      this.analyticsV2Service.searchConversionWithFiltersCSV(this.searchClients,this.range, csv, this.internalUser, this.reportsDownloadValue, this.emailForReports, userMetricValues)
        .then(result => {
          var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
          };
          this.toastyService.success(toastOptions);
        }).catch(err => {
          if (err.message === "Api limit exceeded !!") {
            this.sendLimitReachedToasty();
          }
        });
    }
  }

  getTopClickedResults(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Top Clicked Search Results");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopClickedResultsDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }


  getConversions(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    let query = this.topConvertedKeywords ? this.topConvertedKeywords.search_keyword : "";
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Top Clicked Searches");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getConversionsDownload(this.range, this.searchClients.uid, this.internalUser, query, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      //this.isReportUp = true;
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }


  getReadyDiscussions(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Discussion ready to become help article");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getReadyToBecomeHelpArticleDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      this.range.csv = 0;
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }



  getAttachedToCaseReport(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Attached to Case");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getAttachedToCaseReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.emailForReports, this.reportsDownloadValue, userMetricValues).then(result => {
      if (result.message == 'No data') {
        var toastOptions: ToastOptions = {
          title: "Report creation failed!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.success(toastOptions);
      } else {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.success(toastOptions);
      }
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getCaseFormPageReport(csv: number, sendToemail) {
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Articles failed to Deflect cases");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getCaseFormPageReportDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.emailForReports,this.reportsDownloadValue,this.searchType, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues)
      .then(result => {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.success(toastOptions);
        this.range.csv = 0;
      }).catch(err => {
        if (err.message === "Api limit exceeded !!") {
          this.sendLimitReachedToasty();
        }
      });

  }

  getCaseNotCreatedFormPageReport(csv: number, sendToemail) {
    this.range.csv = 1;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Articles that Deflected cases");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getCaseDeflectedFormPageReportDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.emailForReports, this.reportsDownloadValue ,this.searchTypeDeflection, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues)
      .then(result => {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.success(toastOptions);
        this.range.csv = 0;
      }).catch(err => {
        if (err.message === "Api limit exceeded !!") {
          this.sendLimitReachedToasty();
        }
      });

  }


  /* ######------- Content Gap Analysis ------###### */


  getTopSearchesWithNoClickGap(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    let tempObj = {searchQuery: '',
      currentPage: 1,
      sortingField: 'Searches',
      sortType: 'Desc'}
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Searches With No Clicks");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopSearchesWithNoClickDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, tempObj, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  getTopBackwardDocuments(csv: number, sendToemail) {
    this.range.csv = 1;
    let obj = {
      searchUrl: this.highConversionData[0].value,
      searchQuery: this.highConversionData[1].value,
    };
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("High Conversion Results Not on Page One");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopBackwardDocumentsDownload(this.range, this.searchClients, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, obj, userMetricValues).then(result => {
      this.range.csv = 0;
      if (result.data.length > 0) {
        var toastOptions: ToastOptions = {
          title: "Report added to que",
          msg: result.data[0].message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      } else {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }

  highConversionSessionReport(csv: number, sendToemail) {
    let obj = {
      searchText: this.highConversionClickUrl,
      cookie: this.highConversionSessionDetailReport[0].value,
      emailId: this.highConversionSessionDetailReport[1].value,
      searchQuery: this.highConversionSessionDetailReport[2].value,
      timeZoneOffset: new Date().getTimezoneOffset()
    };
    this.range.csv = 4;
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("High Conversion Results Not on Page One");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.highConversionSessionReportDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, obj, userMetricValues).then(result => {
      this.range.csv = 0;
      if (result.data.length > 0) {
        var toastOptions: ToastOptions = {
          title: "Report added to que",
          msg: result.data[0].message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      else {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
      }
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }

  getKcsArticles(csv: number, sendToemail){
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.getKcsArticlesDownload(this.range, this.searchClients, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
}


getDocumentsWithLargeContent(csv: number, sendToemail) {
  // console.log('here: ' + this);
  if (sendToemail === 'Send Via Email') {
    //this.reportsDownloadValue = 1;
    this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    csv = 0
  } else {
    
   // this.reportsDownloadValue = 0;
    this.emailForReports = '';
  }

  let ObjTosend = { csv: csv, indexId: this.getDocumentsWithLargeContentIndexid, uid: this.searchClients,email: this.emailForReports}

    this.analyticService.getDocumentsWithLargeContent(ObjTosend).then(result => {
        var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
      };
      this.toastyService.success(toastOptions);
  });
  
}
  getTopSearchesWithNoResultGap(csv: number, sendToemail) {
    let reportId = 3; // report Id for content-gap tab searches with no results
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Searches With No Result");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getTopSearchesWithNoResultDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports,{offset:1, sortType:'desc'},this.actionNames,reportId, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }

  getContentGapAnalysisTilesData(csv: number, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Tile Data Content");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getContentGapTileDataDownload(this.searchClients, this.emailDateRange, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.rangeDays, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.userAddedData = []
      this.disableSend = true;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }



  getKCSDetails(csv:number,sendToemail,label){
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.kcsDownload(this.range, this.searchClients.uid, label, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  caseDeflectionTrendsDownload(csv:number,sendToemail){
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Case Deflection Trend Chart");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.caseDeflectionTrendsDownload( this.searchClients.uid,this.emailDateRange, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports,this.dropDownValue, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });

  }

  relevanceIndexTrendsDownload(csv:number,sendToemail){
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.relevanceIndexTrendsDownload( this.searchClients.uid, csv, this.reportsDownloadValue, this.emailForReports,this.dropDownValue, this.internalUser).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    });

  }

/* ######------- Content Gap Analysis ------###### */

drawUnsuccessfulSearchHistogram(csv: number,sendToemail) {

  if (sendToemail === 'Send Via Email') {
    this.reportsDownloadValue = 1;
    this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
  } else {
    this.reportsDownloadValue = 0;
    this.emailForReports = '';
  }
  // Fetching report specific user metric values from the map
  const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Unsuccessful Searches");
  let userMetricValues: any;
  if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
    userMetricValues = {
      [this.userMetricVariable]: userMetricObject.userMetricValues
    };
  }
  this.analyticsV2Service.getUnsuccessfulSearchSummaryChartDownlaod(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
          title: "Report created!",
          msg: result.message,
          showClose: true,
          timeout: 3000,
          theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
  }).catch(err => {
    if (err.message === "Api limit exceeded !!") {
      this.sendLimitReachedToasty();
    }
  });

}




drawUnsuccessfulSearchSessionHistogram(csv: number,sendToemail) {
      if (sendToemail === 'Send Via Email') {
          this.reportsDownloadValue = 1;
          this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
      } else {
          this.reportsDownloadValue = 0;
          this.emailForReports = '';
      }
      // Fetching report specific user metric values from the map
      const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Sessions with unsuccessful searches");
      let userMetricValues: any;
      if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
        userMetricValues = {
          [this.userMetricVariable]: userMetricObject.userMetricValues
        };
      }
      this.analyticsV2Service.getUnsuccessfulSearchSessionSummaryChartDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues).then(result => {
          var toastOptions: ToastOptions = {
              title: "Report created!",
              msg: result.message,
              showClose: true,
              timeout: 3000,
              theme: 'default'
          };
          this.toastyService.success(toastOptions);
          this.range.csv = 0;
      }).catch(err => {
        if (err.message === "Api limit exceeded !!") {
          this.sendLimitReachedToasty();
        }
      });

}



  add(event: MatChipInputEvent): void {
    const input = event.input? event.input : event;
    const value = event.value;
    this.sendFile = true;
    // Add our fruit
    if ((value || '').trim()) {
      if (this.userAddedData.length < this.maxLimit) {
        if (this.userAddedData.includes(value)) {
          var toastOptions: ToastOptions = {
            title: "Already Exist",
            msg: 'User Already Exist  ' + value,
            showClose: true,
            timeout: 3000,
            theme: 'default'
          };
          this.toastyService.error(toastOptions);
          this.sendFile = false;
          // this.FormInvalid = true;
        } else if ( value.length > 320){
          var toastOptions: ToastOptions = {
            title: "Invalid Email",
            msg: 'Email is too long ',
            showClose: true,
            timeout: 3000,
            theme: 'default'
          };
          this.toastyService.error(toastOptions);
        } else {
          if (this.validateEmail(value)) {
            this.userAddedData.push(value.trim());
            this.disableSend = false;
            this.FormInvalid = false;
          } else {
            var toastOptions: ToastOptions = {
              title: "Invalid Email",
              msg: 'Invalid Email id ' + value,
              showClose: true,
              timeout: 3000,
              theme: 'default'
            };
            this.toastyService.error(toastOptions);
            this.sendFile = false;
            if(this.userAddedData.length > 0){
              this.disableSend = false;
              this.FormInvalid = false;
            }else{
              this.FormInvalid = true;

            }
          }
          this.hello();
        }
      } else {
        var toastOptions: ToastOptions = {
          title: "Email User limit exceeded!",
          msg: 'Max allowed emails : 4',
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
        this.sendFile = false;
      }
    }

    // Reset the input value
    if (input) {
      input.value = '';
    }

    this.emailCtrl.setValue(null);
  }

  remove(fruit: string): void {
    const index = this.userAddedData.indexOf(fruit);

    if (index >= 0) {
      this.userAddedData.splice(index, 1);
    }
    if(this.emailCtrl && this.emailCtrl.value && this.emailCtrl.value.length == 0){
      this.disableSend = true;
    }
    if(this.userAddedData && this.userAddedData.length == 0){
      this.disableSend = true;
    }
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    if (this.userAddedData.length < this.maxLimit) {
      if (this.userAddedData.includes(event.option.viewValue)) {
        var toastOptions: ToastOptions = {
          title: "Already Exist",
          msg: 'Selected email already added',
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
      }else{
        this.userAddedData.push(event.option.viewValue);
        if(this.emailIdInput != undefined){
          this.emailIdInput.nativeElement.value = '';
        } 
        this.emailCtrl.setValue(null);
        this.disableSend = false;
        this.FormInvalid = false;
        this.hello();
      }
    } else {
      var toastOptions: ToastOptions = {
        title: "Max Limit exceded",
        msg: 'User Limit Exceeded',
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.error(toastOptions);
    }

  }


  hello() {
    let tempuserAddedData = this.userAddedData;
    if (this.userAddedData.length >= this.maxLimit) {
      this.disableSend = false;
      // k.length = 3
      //this.tempfuit = k[0]+','+k[1]+','+k[2]+','+k[3]+','+k[4].toString()
      this.tempfuit = []
      for (let p = 0; p < tempuserAddedData.length; p++) {
        if (p <= 2) {
          this.tempfuit.push(tempuserAddedData[p])
        } else {
          break;
        }

      }
      this.tempfuit = this.tempfuit.toString();
      this.hideExtraemail = true
    }
  }

  recoverstate() {
    if (this.userAddedData.length > 3) {
      this.hideExtraemail = false
    }
  }

  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();

    return this.AllUserEmails.filter(({ email }) => email.toLowerCase().indexOf(filterValue) === 0);
  }

  getSessionDetails() {
    this.cookieService.getSearchUnifySession().then(result => {
      this.userEmail = result.email;
    })
  }

  saveSessionTrackingDownloadSettings(value){
    //this function save the details in local storage
      // dateDefaultValue: string = 'datetime';
  // sessionDefaultValue: string = 'perSession';

    let trackingObjects= {
      sessionTrackingDateDownloadSettings: this.dateDefaultValue ? this.dateDefaultValue : 'datetime',
      sessionTrackingActvityDownloadSettings: this.sessionDefaultValue ? this.sessionDefaultValue : 'perSession',
      includeAdvanceFilterDetails: this.includeAdvanceFilterDetails ? this.includeAdvanceFilterDetails : false
    }

    let trackingSettings = localStorage.getItem(`stDsettings`);

    if(!trackingSettings || value == true)
    {
      localStorage.setItem(`stDsettings`, JSON.stringify(trackingObjects))
    }else{
      let parsedSettings = JSON.parse(trackingSettings)

      this.dateDefaultValue =  parsedSettings.sessionTrackingDateDownloadSettings
      this.sessionDefaultValue = parsedSettings.sessionTrackingActvityDownloadSettings
      
      if (this.dateDefaultValue == 'date') {
        this.ToggleChangeDateFormat = true;
      } else {
        this.ToggleChangeDateFormat = false;
      }


      if (this.sessionDefaultValue == 'perActivity') {
        this.ToggleCumulativeDownload = true;
      } else {
        this.ToggleCumulativeDownload = false;
      }

      this.includeAdvanceFilterDetails = parsedSettings.includeAdvanceFilterDetails ? parsedSettings.includeAdvanceFilterDetails : false; 
    }

  }

  getActionNameWithIcon() {
    this.analyticsV2Service.getActionNameWithIcon().then(result => {
      if(result.data) {
        this.actionNames = Object.keys(result.data); // storing action status filter scenario
      }
    });
  }

  private validateEmail(email) {
    var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  }

  ClearForm() {
    this.userAddedData = [];
    this.disableSend = true;
    this.emailResultType = '';
    this.selectedReport = true;
  }

  emailSearchResults(csv: number, sendToemail) {
    this.getSessionDetails();
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    if (this.emailResultType === "all") {
      this.objToSend.pageNo = 1;
      this.objToSend.resultsPerPage = 100;
      this.objToSend.from = 0;
    }
    let sendEmailOnly = true;
    this.searchTuningService.exportSearch(
      this.objToSend.searchString,
      this.objToSend.from,
      this.objToSend.pageNo,
      this.objToSend.searchClientUid,
      this.objToSend.resultsPerPage,
      this.objToSend.agg,
      this.emailForReports,
      this.objToSend.startDate,
      this.objToSend.endDate,
      this.objToSend.sortby,
      this.objToSend.orderBy,
      this.objToSend.fromTestTuning,
      this.objToSend.contentSourceName,
      this.objToSend.byPassEnable,
      sendEmailOnly
  )
  
    this.ClearForm();
  }

  emailBrowsedResults(csv: number, sendToemail) {
let browserObject = JSON.parse(JSON.stringify(this.browserObjectToSend));


    this.getSessionDetails();
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      let emailId = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
      browserObject.emailId = emailId; 
      browserObject.sendEmail = true;
    }

    if (this.emailResultType === "all") {
      browserObject.pageSize = 100;
      browserObject.pageNumber = 1;
    } else {
      browserObject.pageSize =  this.browserObjectToSend.pageSize;
      browserObject.pageNumber = this.browserObjectToSend.pageNumber;
    }
   
    this.contentSourceService.getIndexedDocuments(browserObject).then(result => {
      
      if (result.statusCode == 200  && result.message === "Success"){
        var toastOptions: ToastOptions = {
          title: 'Sent email successfully',
          msg: 'Sent email successfully',
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.success(toastOptions);
      } else {

        var toastOptions: ToastOptions = {
          title: 'Error while sending Email' ,
          msg: result.error || '',
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
      }
      
    });
  
    this.ClearForm();
  }


  

  getArticlesUsageByAgent(csv: number, sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    // Fetching report specific user metric values from the map
    const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Articles Usage By Agents");
    let userMetricValues: any;
    if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
      userMetricValues = {
        [this.userMetricVariable]: userMetricObject.userMetricValues
      };
    }
    this.analyticsV2Service.getArticlesUsageByAgentDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, userMetricValues).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  }


  getAgentHelperAnalytics(csv: number, sendToemail){
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    const body = {
      uid:this.searchClients,
      range:this.range,
      csv:csv,
    }
    this.ahAnalyticsService.downloadAgentHelperAnalytics(body,this.reportsDownloadValue, this.emailForReports, this.featureTypes).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
      this.range.csv = 0;
    });  
  }

  unassistedSelfSolveVolume(csv: number,sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.unassistedSelfSolveVolumeDownload(this.searchClients, csv, this.reportsDownloadValue, this.emailForReports, this.directlyViewSetting, this.unassistedSSVQuarterStartDate, this.unassistedSSVQuarterEndDate, this.internalUser).then(result => {
        var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  
  }

  assistedSelfSolveVolume(csv: number,sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.assistedSelfSolveVolumeDownload(this.searchClients, csv, this.reportsDownloadValue, this.emailForReports, this.assistedSSVQuarterStartDate, this.assistedSSVQuarterEndDate, this.internalUser).then(result => {
        var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  
  }

  deflectionCostSavings(csv: number, sendToemail) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.deflectionCostSavingsDownload(this.searchClients, csv, this.reportsDownloadValue, this.emailForReports, this.costSavingQuarterStartDate, this.costSavingQuarterEndDate, this.internalUser, this.costPerCase).then(result => {
      var toastOptions: ToastOptions = {
        title: "Report created!",
        msg: result.message,
        showClose: true,
        timeout: 3000,
        theme: 'default'
      };
      this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  
  }

  assistedCaseVolume(csv: number,sendToemail) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.assistedCaseVolumeDownload(this.searchClients, csv, this.reportsDownloadValue, this.emailForReports, this.selectedContentSourceIndex, this.assistedCVQuarterStartDate, this.assistedCVQuarterEndDate).then(result => {
        var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        this.toastyService.success(toastOptions);
    }).catch(err => {
      if (err.message === "Api limit exceeded !!") {
        this.sendLimitReachedToasty();
      }
    });
  
  }
  
  ahAdoptionMetrics(csv: number,sendToemail,quarterFilter) {
    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.ahAdoptionDownload(this.searchClients, csv, this.reportsDownloadValue, this.emailForReports, this.ahAdoptionQuarterStartDate, this.ahAdoptionQuarterEndDate,this.quarterFilter).then(result => {
        var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        this.toastyService.success(toastOptions);
    });
  
  }

  averageTtrMetrics(csv: number,sendToemail,quarterFilter) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.averageTtrDownload(this.searchClients, csv, this.reportsDownloadValue, this.emailForReports, this.ahTtrQuarterStartDate, this.ahTtrQuarterEndDate,this.quarterFilter).then(result => {
        var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        this.toastyService.success(toastOptions);
    });
  
  }

  caseEscalationMetrics(csv: number,sendToemail,quarterFilter) {

    if (sendToemail === 'Send Via Email') {
      this.reportsDownloadValue = 1;
      this.emailForReports = this.userAddedData ? this.userAddedData.toString() : this.userEmail;
    } else {
      this.reportsDownloadValue = 0;
      this.emailForReports = '';
    }
    this.analyticsV2Service.caseEscalationDownload(this.searchClients, csv, this.reportsDownloadValue, this.emailForReports, this.selectedContentSourceIndex, this.caseEscalationQuarterStartDate, this.caseEscalationQuarterEndDate,this.quarterFilter).then(result => {
        var toastOptions: ToastOptions = {
            title: "Report created!",
            msg: result.message,
            showClose: true,
            timeout: 3000,
            theme: 'default'
        };
        this.toastyService.success(toastOptions);
    });
  
  }

}
