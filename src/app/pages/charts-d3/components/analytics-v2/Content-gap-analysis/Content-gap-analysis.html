<ng2-toasty></ng2-toasty>
<mat-progress-bar class="allign-top" mode="indeterminate" *ngIf="allGraphLoaded == false"></mat-progress-bar>
<div class="ad-heading-fixed">
  <div class="topHeading" [ngClass]="{'ad-heading-fixed-top' : isTopbarShow}">
    <div class="header-1 display-flex">
      <div class="header-2 margin-TB-auto-LR-0">
        <div class="heading-source">
          <span class="Search-Analytics">Search Analytics</span>
          <span class="definition">View search data filtered by users and search clients.</span>
        </div>
      </div>
      <div class="analytics-topbar">
        <div class="form-group-head">
          <div class="form-group analytics-header-block">
            <div class="analytics-header-row ad_analytics-list-item">
              <mat-form-field
                class="analytics-header-field"
                (click)="openPanel()">
                <mat-select #select panelClass="matRole test-sc-analytics" [ngModel]="platform" (selectionChange)="changeClient(select.value)"
                  placeholder="Search Client / Ecosystem" [compareWith]="compareByUid">
                  <mat-accordion class="scroll">
                    <mat-expansion-panel #first class="analytics-dropdown mat-elevation-z0 scPanel" [ngClass]="isEcosystemSelected === false && 'scPanelExpand'" (opened)="sourcePlatformEco.length > 0 && changeEcoPanel()" (closed)="changeScPanel()">
                      <mat-expansion-panel-header style="font-size:10px; color:#919191; font-weight:semibold;">
                        Search Clients
                      </mat-expansion-panel-header>
                      <span style="overflow: auto !important; max-height: 100px !important;">
                        <ng-container *ngFor="let client of sourcePlatformSc; ; let index = index">
                          <ng-container *ngIf="client.ab_test_parent == null">
                            <mat-option [value]="client" matTooltip="{{client.name}}"
                              matTooltipPosition="below" aria-label="Button that displays a tooltip in various positions">
                              {{client.name.length> 20 ? client.name.substring(0,20) + '...' : client.name }}
                              <button *ngIf="haveDropdown[index]" type="button" class="border-0 bg-transparent ml-2 toggle-sc" (click)="$event.stopPropagation(); toggleExpand(index)">
                                <app-svg [ngClass]="expandedIndex === index ? 'toggle-child' : '' " name='analyticsIcon'></app-svg>
                              </button>
                            </mat-option>
                            <div class="child-options" [ngClass]="expandedIndex === index ? 'd-block' : 'd-none' ">
                              <!-- A/B testing clonned SC -->
                              <ng-container *ngFor="let childClient of childSC">
                                <mat-option [value]="childClient" matTooltip="{{childClient.searchTerm}}" matTooltipPosition="below" class="su__text-ellipses"
                                    *ngIf="childClient.ab_test_parent == client.uid">
                                    {{ childClient.searchTerm }}
                                </mat-option>
                              </ng-container>
                            </div>
                          </ng-container>
                        </ng-container>
                      </span>
                    </mat-expansion-panel>
                    <mat-divider style="margin: 0 5.5px 0 18.5px !important;"></mat-divider>
                    <mat-expansion-panel #second class="analytics-dropdown mat-elevation-z0 ecoPanel" [ngClass]="isEcosystemSelected === true && 'ecoPanelExpand'" *ngIf = "sourcePlatformEco.length>0" (opened)="changeScPanel()" (closed)="changeEcoPanel()">
                      <mat-expansion-panel-header style="font-size:10px; color:#919191; font-weight:semibold;">
                        Ecosystems
                      </mat-expansion-panel-header>
                      <span style="overflow: auto !important; max-height: 100px !important;">
                        <mat-option *ngFor="let eco of sourcePlatformEco" [value]="eco" matTooltip="{{eco.name}}">
                          {{eco.name.length> 20 ? eco.name.substring(0,20) + '...' : eco.name }}
                        </mat-option>
                      </span>
                    </mat-expansion-panel>
                    <mat-divider style="margin: 0 5.5px 0 18.5px !important;"></mat-divider>
                  </mat-accordion>
                </mat-select>
              </mat-form-field>
                <div class="su__sc-pin-analytics">
                  <svg (click)="preSelectedSCPin(false);" *ngIf = "!pinStatus" matTooltip="Pin default search client" matTooltipPosition="below" aria-label="Button that displays a tooltip in various positions" xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" fill="#8D8D8D" width="18px" height="18px">
                      <g><rect fill="none" height="24" width="24"/></g>
                      <g><path d="M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z" fill-rule="evenodd"/></g>
                  </svg>
                  <svg (click)="preSelectedSCPin(true);" *ngIf= "pinStatus" matTooltip="Unpin default search client" matTooltipPosition="below" aria-label="Button that displays a tooltip in various positions" xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" fill="#56C5FF" width="18px" height="18px">
                      <g><rect fill="none" height="24" width="24"/></g>
                      <g><path d="M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z" fill-rule="evenodd"/></g>
                  </svg>
              </div>
            </div>
  
            <div class="analytics-header-row">
              <mat-form-field
              class="analytics-header-field">
                <mat-select [(ngModel)]="internalUser" [disabled]="!(emailTrackingAddonInstalled && externalEnabled)"
                  (selectionChange)="changeUser()" placeholder="Select User">
                  <mat-option value="all">All</mat-option>
                  <mat-option value="internal">Internal User</mat-option>
                  <mat-option value="external">External User</mat-option>
                  <mat-option value="externalOnly">
                    External Only
                    <i
                      class="fas fa-info-circle tool-tip-info"
                      mat-raised-button
                      matTooltipClass="externalOnlyTooltip"
                      matTooltip="It will exclude any sessions or data that have internal user activities"
                      aria-label="Button that displays a tooltip when focused or hovered over"></i>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>


            <div class="analytics-header-row">
              <mat-form-field class="analytics-header-field">
                <input type="text" matInput name="daterangeInput" id="select-range" placeholder="Date range"
                  class="pull-right form-control date-input" daterangepicker [options]='daterangepickerOptions'
                  (selected)="selectedDate($event, daterange)" style="float:left;">
                  <span class="analytics-header-calendar">
                    <svg height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0V0z" fill="none"/><path class="ad-dark-fill" d="M19 4h-1V3c0-.55-.45-1-1-1s-1 .45-1 1v1H8V3c0-.55-.45-1-1-1s-1 .45-1 1v1H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 15c0 .55-.45 1-1 1H6c-.55 0-1-.45-1-1V9h14v10zM7 11h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z" fill="#6b6b6b"/></svg>
                  </span>
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="sectionDiv colubridae19-sectionDiv" [ngClass]="{'ad-heading-space' : isTopbarShow}">
  <div class="analyticsV2-content-gap-analysis" id="searchAnalytics">
    <nav mat-tab-nav-bar>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/overview">
        Overview
      </a>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/conversions">
        Conversions
      </a>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/content-gap-analysis">
        Content Gap Analysis
      </a>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/leadership">
        Leadership Dashboard
      </a>
      <!-- <a *ngIf="communityHelperAddonStatus" mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/community-helper-analytics">
        Community Helper Analytics
      </a> -->
    </nav>
  </div>
  <div class="sectionMainDiv darkmode" *ngIf="selectedIndex===2">
    <div class="Expand">
      <app-email-and-download-reports
        [searchClients]="searchClients"
        [range]="range"
        [internalUser]="internalUser"
        [tab]="ContentGapAnalysis.tabName"
        [reportName]="ContentGapAnalysis.Reports[8]"
        [hideDownload]="true"
        [rangeDays]="range_days"
        [AllUserEmails] = "AllUserEmails"
        [userMetricVariable]="userMetricVariable"
        [userMetricsFilters]="userMetricsFilters"
      >
      </app-email-and-download-reports>
      <app-user-metric-filter
        *ngIf="userMetricEnabled"
        [userMetricLabel]="userMetricLabel"
        [userMetricVariable]="userMetricVariable"
        [userId]="userUniqueId"
        [email]="userEmail"
        [uid]="searchClients.uid"
        [internalUser]="internalUser"
        [from]="range.from"
        [to]="range.to"
        [reportName]="'Tile Data Content'"
        [userMetricURL]="['/content/tileDataContent']"
        [body]="{
          from: range.from,
          to: range.to,
          internalUser: internalUser,
          uid: searchClients.uid
        }"
        (userMetricEvent)="sendUserMetricValues($event)"
      ></app-user-metric-filter>
      <svg class="su-sub" width="24" height="24" viewBox="0 0 24 24" *ngIf="!showHide" (click)="showHide = true"
        data-toggle="collapse" data-target="#cards8" style="float: right;cursor: pointer;">
        <defs>
          <clipPath id="clip-Substract">
            <rect width="24" height="24" />
          </clipPath>
        </defs>
        <g id="Substract" clip-path="url(#clip-Substract)">
          <g id="Group_1754" data-name="Group 1754" transform="translate(12051 8643)">
            <g id="outline-add_box-24px_3_" data-name="outline-add_box-24px (3)" transform="translate(-12051 -8643)">
              <path id="Path_1329" data-name="Path 1329" d="M0,0H24V24H0Z" fill="none" />
              <path id="Path_1330" data-name="Path 1330" d="M13,13h4V11H7v2h6Z" fill="#58c0fe" />
            </g>
          </g>
        </g>
      </svg>
      <svg class="su-add" width="24" height="24" viewBox="0 0 24 24" *ngIf="showHide" (click)="showHide = false"
        data-toggle="collapse" data-target="#cards8" style="float: right;cursor: pointer;">
        <defs>
          <clipPath id="clip-Add">
            <rect width="24" height="24" />
          </clipPath>
        </defs>
        <g id="Add" clip-path="url(#clip-Add)">
          <g id="Group_1753" data-name="Group 1753" transform="translate(11958 8644)">
            <g id="outline-add_box-24px_3_" data-name="outline-add_box-24px (3)" transform="translate(-11958 -8644)">
              <path id="Path_1331" data-name="Path 1331" d="M0,0H24V24H0Z" fill="none" />
              <path id="Path_1332" data-name="Path 1332" d="M11,17h2V13h4V11H13V7H11v4H7v2h4Z" fill="#59bffe" />
            </g>
          </g>
        </g>
      </svg>
    </div>
    <div id="cards8" class="collapse show">
      <div class="cards-8">
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-1 right one">
          <div class="analytics-card analytics-first">
            <div (mouseenter)="isEcosystemSelected && tileDataContent.uniqueFailedSearches != 0 ? showUniqueFailedSearchesStats() : null" (mouseleave)="isEcosystemSelected && tileDataContent.uniqueFailedSearches != 0 ? showUniqueFailedSearchesStats() : null">
              <div class="analytics-section-heading-height-content">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Unique Unsuccessful Searches
                  </div>
                  <div class="float-right">
                    <i class="analytics-card-image-1"></i>
                  </div>
                </div>
              </div>
              <div class="">
                <div class="analytics-card-count" *ngIf="!isEcosystemSelected || tileDataContent.uniqueFailedSearches == 0" matTooltip="{{ tileDataContent.uniqueFailedSearches || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataContent.uniqueFailedSearches || 0 | thousandStuff :1}}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataContent.uniqueFailedSearches != 0" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataContent.uniqueFailedSearches || 0 | thousandStuff :1}}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showUniqueFailedSearchesData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total unique unsuccessful searches
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataContent.uniqueFailedSearches}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileDataContent" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.uniqueFailedSearches || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.uniqueFailedSearches" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.uniqueFailedSearches || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-1"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 two">
          <div class="analytics-card analytics-second">
            <div (mouseenter)="isEcosystemSelected && tileDataContent.noClickSearches != 0 ? showNoClickSearchesStats() : null" (mouseleave)="isEcosystemSelected && tileDataContent.noClickSearches != 0 ? showNoClickSearchesStats() : null">
              <div class="analytics-section-heading-height-content">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Searches With No Clicks
                  </div>
                  <div class="float-right">
                    <i class="analytics-card-image-2"></i>
                  </div>
                </div>
              </div>
              <div class="">
                <div class="analytics-card-count extra-info-count-padding" *ngIf="!isEcosystemSelected || tileDataContent.noClickSearches == 0" matTooltip="{{ tileDataContent.noClickSearches || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-upper-tooltip">
                  {{tileDataContent.noClickSearches || 0 | thousandStuff :1}}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataContent.noClickSearches != 0" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataContent.noClickSearches || 0 | thousandStuff :1}}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showNoClickSearchesData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total searches with no clicks
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataContent.noClickSearches}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileDataContent" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.noClickSearches || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.noClickSearches" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.noClickSearches || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="float-left colubridae19Colors tile-extra-info" matTooltip="{{ tileDataContent.noClickSessions || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-cg-tooltip">
              Sessions With No Clicks {{tileDataContent.noClickSessions || 0 | thousandStuff :1}}
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content extra-info-footer-padding">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-2"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 one">
          <div class="analytics-card analytics-third">
            <div (mouseenter)="isEcosystemSelected && tileDataContent.searchesWithoutResults != 0 ? showSearchesWithoutResultsStats() : null" (mouseleave)="isEcosystemSelected && tileDataContent.searchesWithoutResults != 0 ? showSearchesWithoutResultsStats() : null">
              <div class="analytics-section-heading-height-content">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Searches With No Result
                  </div>
                  <div class="float-right">
                    <i class="analytics-card-image-3"></i>
                  </div>
                </div>
              </div>
              <div class="">
                <div class="analytics-card-count analytics-count-second extra-info-count-padding" *ngIf="!isEcosystemSelected || tileDataContent.searchesWithoutResults == 0" matTooltip="{{ tileDataContent.searchesWithoutResults || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-upper-tooltip">
                  {{tileDataContent.searchesWithoutResults || 0 | thousandStuff :1}}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataContent.searchesWithoutResults != 0" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataContent.searchesWithoutResults || 0 | thousandStuff :1}}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showSearchesWithoutResultsData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total searches with no result
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataContent.searchesWithoutResults}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileDataContent" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.searchesWithoutResults || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.searchesWithoutResults" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.searchesWithoutResults || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="float-left colubridae19Colors tile-extra-info" matTooltip="{{ tileDataContent.noResultSessions || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-cg-tooltip">
              Sessions With No Result {{tileDataContent.noResultSessions || 0 | thousandStuff :1}}
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content extra-info-footer-padding">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-3"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column left-1 left two">
          <div class="analytics-card analytics-fourth">
            <div class="analytics-section-heading-height-content">
              <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                <div class="float-left colubridae19Colors">
                  Session With
                  <a href="https://docs.searchunify.com/Content/Search-Analytics/Search-Analytics.htm"
                    target="_blank">
                    <i class="fas fa-info-circle tool-tip-info" mat-raised-button
                      matTooltip="Includes both no click and no result search sessions"
                      aria-label="Button that displays a tooltip when focused or hovered over"></i></a>
                  Unsuccessful Searches
                </div>
                <div class="float-right">
                  <i class="analytics-card-image-4"></i>
                </div>
              </div>
            </div>
            <div class="">
              <div class="analytics-card-count" matTooltip="{{ tileDataContent.usersWithFailedSearches || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                {{tileDataContent.usersWithFailedSearches || 0 | thousandStuff :1}}
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-4"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="cards-8">
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-1 right one">
          <div class="analytics-card analytics-first">
            <div (mouseenter)="isEcosystemSelected && tileDataContent.unique_failed_searches_range_days != 0 ? showAvgUniqueFailedSearchesStats() : null" (mouseleave)="isEcosystemSelected && tileDataContent.unique_failed_searches_range_days != 0 ? showAvgUniqueFailedSearchesStats() : null">
              <div class="analytics-section-heading-height-content">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Daily Avg Unsuccessful Searches
                  </div>
                  <div class="float-right">
                    <i class="analytics-card-image-5"></i>
                  </div>
                </div>
              </div>
              <div class="">
                <div class="analytics-card-count" *ngIf="!isEcosystemSelected || tileDataContent.unique_failed_searches_range_days == 0" matTooltip="{{ tileDataContent.unique_failed_searches_range_days || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataContent.unique_failed_searches_range_days || 0 | thousandStuff :1}}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataContent.unique_failed_searches_range_days != 0" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataContent.unique_failed_searches_range_days || 0 | thousandStuff :1}}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showAvgUniqueFailedSearchesData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Daily avg unsuccessful searches
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataContent.unique_failed_searches_range_days}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileDataContent" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.dailyAvgUniqueFailedSearches || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.dailyAvgUniqueFailedSearches" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.dailyAvgUniqueFailedSearches || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-5"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 two">
          <div class="analytics-card analytics-second">
            <div (mouseenter)="isEcosystemSelected && tileDataContent.no_click_searches_range_days != 0 ? showAvgNoClickSearchesStats() : null" (mouseleave)="isEcosystemSelected && tileDataContent.no_click_searches_range_days != 0 ? showAvgNoClickSearchesStats() : null">
              <div class="analytics-section-heading-height-content">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Daily Avg No Click Searches
                  </div>
                  <div class="float-right">
                    <i class="analytics-card-image-6"></i>
                  </div>
                </div>
              </div>
              <div class="">
                <div class="analytics-card-count" *ngIf="!isEcosystemSelected || tileDataContent.no_click_searches_range_days == 0" matTooltip="{{ tileDataContent.no_click_searches_range_days || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataContent.no_click_searches_range_days || 0 | thousandStuff :1}}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataContent.no_click_searches_range_days != 0" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataContent.no_click_searches_range_days || 0 | thousandStuff :1}}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showAvgNoClickSearchesData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Daily avg no click searches
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataContent.no_click_searches_range_days}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileDataContent" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.dailyAvgNoClickSearches || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.dailyAvgNoClickSearches" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.dailyAvgNoClickSearches || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-6"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 one">
          <div class="analytics-card analytics-third">
            <div (mouseenter)="isEcosystemSelected && tileDataContent.without_result_range_days != 0 ? showAvgSearchesWithoutResultsStats() : null" (mouseleave)="isEcosystemSelected && tileDataContent.without_result_range_days != 0 ? showAvgSearchesWithoutResultsStats() : null">
              <div class="analytics-section-heading-height-content">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Daily Avg Searches With No Result
                  </div>
                  <div class="float-right">
                    <i class="analytics-card-image-7"></i>
                  </div>
                </div>
              </div>
              <div class="">
                <div class="analytics-card-count analytics-count-second" *ngIf="!isEcosystemSelected || tileDataContent.without_result_range_days == 0" matTooltip="{{ tileDataContent.without_result_range_days || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataContent.without_result_range_days || 0 | thousandStuff :1}}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataContent.without_result_range_days != 0" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataContent.without_result_range_days || 0 | thousandStuff :1}}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showAvgSearchesWithoutResultsData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Daily avg searches with no result
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataContent.without_result_range_days}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileDataContent" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.dailyAvgSearchesWithoutResults || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.dailyAvgSearchesWithoutResults" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.dailyAvgSearchesWithoutResults || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-7"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column left-1 left two">
          <div class="analytics-card analytics-fourth">
            <div class="analytics-section-heading-height-content">
              <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                <div class="float-left colubridae19Colors">
                  Daily Avg Sessions With Unsuccessful Searches
                </div>
                <div class="float-right">
                  <i class="analytics-card-image-8"></i>
                </div>
              </div>
            </div>
            <div class="">
              <div class="analytics-card-count" matTooltip="{{ tileDataContent.users_with_failed_searches_range_days || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                {{tileDataContent.users_with_failed_searches_range_days || 0 | thousandStuff :1}}
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-content">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-8"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div class="" *ngIf="reportSettings.length>0 && reportSettings[33] && reportSettings[33].is_enabled">
      <div class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;" id="unSuccessfulSearchChart">
        <div class="card card-block" style="padding:0px;margin-top: 20px;" >
          <div class="analytics-section-heading">
            <!-- add -->
            <!-- Unsuccessful Searches -->
            {{reportSettings[33].label}}
            <!-- <a data-toggle="modal" (click)="addClassToBody();" href="#alertModalForUnsuccessfullSearch"
              data-backdrop="static" data-keyboard="false">
              <svg class="downloadImg" width="40" height="40" viewBox="-5 -5 40 40" *ngIf="unsuccessfulSearchHistogram">
                <path data-name="Path 1128"
                  d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
              </svg>
            </a> -->
            <app-email-and-download-reports
              *ngIf="unsuccessfulSearchHistogram"
              [searchClients]="searchClients"
              [range]="range"
              [internalUser]="internalUser"
              [tab]="ContentGapAnalysis.tabName"
              [reportName]="reportSettings[33].label"
              [hideDownload]="true"
              [AllUserEmails] = "AllUserEmails"
              [userMetricVariable]="userMetricVariable"
              [userMetricsFilters]="userMetricsFilters"
            >
            </app-email-and-download-reports>
            <app-user-metric-filter
              *ngIf="userMetricEnabled"
              [userMetricLabel]="userMetricLabel"
              [userMetricVariable]="userMetricVariable"
              [userId]="userUniqueId"
              [email]="userEmail"
              [uid]="searchClients.uid"
              [internalUser]="internalUser"
              [from]="range.from"
              [to]="range.to"
              [reportName]="'Unsuccessful Searches'"
              [userMetricURL]="['/content/unSuccessfulSummaryChart']"
              [body]="{
                from: range.from,
                to: range.to,
                internalUser: internalUser,
                uid: searchClients.uid
              }"
              (userMetricEvent)="sendUserMetricValues($event)"
            ></app-user-metric-filter>
          </div>
          <!-- ======================== Modal Starts =================== -->
          <!-- <div id="alertModalForUnsuccessfullSearch" class="modal" style="padding-right: 0px;">
            <div class="modal-container-new">
              <form>
                <div class="modal-content-new" style="border-radius:8px;">
                  <div class="modal-header-new">
                    <h6 class="modal-title-new" style="font-size: 14px;margin-bottom: 15px;">Please select one of the
                      following the options.
                    </h6>
                    <mat-form-field class="field mat-form-field-custom">
                      <mat-select name="modalSelect" placeholder="Select Download Option" [(ngModel)]="selectedValue"
                        required>
                        <mat-option *ngFor="let option of reportsDownloadOption" [value]="option">
                          {{option}}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                    <mat-form-field *ngIf="selectedValue==='Send Via Email'" class="example-full-width">
                      <input matInput name="modalInputValue" [(ngModel)]="emailForReports" placeholder="Email Id"
                        style="line-height:1.19;" class="mat-input-content" required>
                    </mat-form-field>
               
                  </div>
                  <div class="modal-footer-new">
                    <button class="buttonPrimary buttonSmall margin-right-5px" data-dismiss="modal"
                      (click)="removeClassToBody();">
                      Cancel</button>
                    <button class="buttonPrimary buttonSmall" data-dismiss="modal"
                      (click)="drawUnsuccessfulSearchHistogram(1);removeClassToBody();">
                      Ok
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div> -->
          <!-- ======================== Modal Ends ===================  -->
          <div class="app-line-conversion-chart">
             <!-- Loader -->
             <div *ngIf="!unsuccessfulSearchHistogram" style="text-align: center;">
              <div class="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
              </div>
            </div>
            <!-- Loader Ends -->
            <app-line-conversion-chart *ngIf="unsuccessfulSearchHistogram" [zoom1]="false" [chartType]="true"
              [colors]="['#538cc1', '#dd4546', '#4bc0c0', '#8fed7c', '#0f0', '#00f', '#08EE86', '#4bc1c0', 'Green', 'Blue', '#6b486b', '#d0743c',  '#8a89a6', '#7b6888', '#a05d56', '#ff8c00']"
              [inputConversionData]="unsuccessfulSearchHistogram" [analyticsV2]="false"></app-line-conversion-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div class="sectionMainDiv-inner-box" id="searchesWithNoClicksForSuccessiveSearches">
      <div class="analytics-section-heading"
        *ngIf="reportSettings.length>0 && reportSettings[36] && reportSettings[36].is_enabled">
        <!-- Searches With No Clicks -->
        Search Classifications
      </div>
      <div class="searches-with-no-result"
        *ngIf="reportSettings.length>0 && reportSettings[36] && reportSettings[36].is_enabled">
        <div class="col-xl-12 responsive" style="padding-right: 0px;padding-left: 0px;">
          <div class="card card-block card-1" style="padding:0;margin-top: 0;border: solid 2px #e9ebf0;">
            <mat-grid-list cols="3" rowHeight="507px" gutterSiz="15px">
              <mat-grid-tile colspan="2">
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div class="analytics-section-header">
                    <i class="fa fa-line-chart" aria-hidden="true"></i>
                    <!-- Searches With No Clicks -->
                    {{reportSettings[36].label}}
                    <!-- <a data-toggle="modal" (click)="addClassToBody();" href="#alertModalForSearchesWithNoClicks"
                      data-backdrop="static" data-keyboard="false">
                      <svg class="downloadImg" width="40" height="40" viewBox="-5 -5 40 40"
                        *ngIf="withNoClickSearchesGap && !withNoClickSearchesGap.isEmpty">
                        <path data-name="Path 1128"
                          d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
                      </svg>
                    </a> -->
                    <app-email-and-download-reports 
                      *ngIf="withNoClickSearchesGap && !withNoClickSearchesGap.isEmpty"
                      [searchClients]="searchClients"
                      [range]="range"
                      [internalUser]="internalUser"
                      [tab]="ContentGapAnalysis.tabName"
                      [reportName]="ContentGapAnalysis.Reports[1]"
                      [hideDownload]="true"
                      [searchReportFilters]='withNoClickSearchesGapObj'
                      [AllUserEmails] = "AllUserEmails"
                      [userMetricVariable]="userMetricVariable"
                      [userMetricsFilters]="userMetricsFilters"
                    >
                    </app-email-and-download-reports>
                    <app-user-metric-filter
                      *ngIf="userMetricEnabled"
                      [userMetricLabel]="userMetricLabel"
                      [userMetricVariable]="userMetricVariable"
                      [userId]="userUniqueId"
                      [email]="userEmail"
                      [uid]="searchClients.uid"
                      [internalUser]="internalUser"
                      [from]="range.from"
                      [to]="range.to"
                      [reportName]="'Searches With No Clicks'"
                      [userMetricURL]="['/overview/searchsWithNoClicks']"
                      [body]="{
                        from: range.from,
                        to: range.to,
                        internalUser: internalUser,
                        uid: searchClients.uid,
                        offset: 1,
                        limit: 50,
                        sortingField: 'Searches',
                        sortType: 'desc'
                      }"
                      (userMetricEvent)="sendUserMetricValues($event)"
                    ></app-user-metric-filter>
                  </div>
                  <!-- ======================== Modal Starts =================== -->
                  <!-- <div id="alertModalForSearchesWithNoClicks" class="modal" style="padding-right: 0px;">
                    <div class="modal-container-new">
                      <form>
                        <div class="modal-content-new" style="border-radius:8px;">
                          <div class="modal-header-new">
                            <h6 class="modal-title-new" style="font-size: 14px;margin-bottom: 15px;">Please select one
                              of the following the options.
                            </h6>
                            <mat-form-field class="field mat-form-field-custom">
                              <mat-select name="modalSelect" placeholder="Select Download Option"
                                [(ngModel)]="selectedValue" required>
                                <mat-option *ngFor="let option of reportsDownloadOption" [value]="option">
                                  {{option}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <mat-form-field *ngIf="selectedValue==='Send Via Email'" class="example-full-width">
                              <input matInput name="modalInputValue" [(ngModel)]="emailForReports"
                                placeholder="Email Id" style="line-height:1.19;" class="mat-input-content" required>
                            </mat-form-field>
           
                          </div>
                          <div class="modal-footer-new">
                            <button class="buttonPrimary buttonSmall margin-right-5px" data-dismiss="modal"
                              (click)="removeClassToBody();">
                              Cancel</button>
                            <button class="buttonPrimary buttonSmall" data-dismiss="modal"
                              (click)="getTopSearchesWithNoClickGap(1,'Content gap analysis');removeClassToBody();">
                              Ok
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div> -->
                  <!-- ======================== Modal Ends ===================  -->
                  <div
                    style="background-color: #fbfbfb; height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden"
                    class="perfect">
                    <style>
                      .side-select-list {
                        cursor: pointer;
                      }

                      .side-selected-list {
                        cursor: pointer;
                        font-weight: bold;
                        background-color: white;
                        border-left: 3px solid #56c7ff;
                      }

                      .side-select-list:hover {
                        background-color: white;
                      }

                      .side-select-list.active {
                        background-color: white;
                        background-image: none;
                        border-left: 3px solid #56c7ff;
                      }

                      td>.clickable {
                        text-decoration: underline;
                        color: #0275d8;
                      }
                    </style>
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Top Searches</th>
                          <th>#Sessions</th>
                          <th>#Users</th>
                          <th>Count</th>
                        </tr>
                      </thead>
                      <ng-container *ngIf="!withNoClickSearchesGap.isEmpty">
                        <tr *ngFor="let d of withNoClickSearchesGap; let i=index"
                          (click)="(withNoClickSearchesGap.active!=i && withNoClickSearchesGap.isUp)?getNoClickNextSuccessfulSearches(i):0;"
                          (keyup.enter)="(withNoClickSearchesGap.active!=i && withNoClickSearchesGap.isUp)?getNoClickNextSuccessfulSearches(i):0;"
                          [ngClass]="i===withNoClickSearchesGap.active?'side-selected-list':'side-select-list'">
                          <td style="word-break: break-word;">{{d[0]}}</td>
                          <td>{{d[2]}}</td>
                          <td>{{d[3]}}</td>
                          <td>{{d[1]}}</td>
                        </tr>
                      </ng-container>
                      <tr *ngIf="withNoClickSearchesGap.isEmpty">
                        <td colspan="4" class="no-docs">
                          No Searches to show.
                          <img class="doc-img">
                        </td>
                      </tr>
                      <tr *ngIf="withNoClickSearchesGap.length === 0">
                        <td colspan="4">
                          <div style="text-align: center;">
                            <div class="spinner">
                              <div class="bounce1"></div>
                              <div class="bounce2"></div>
                              <div class="bounce3"></div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </mat-grid-tile>
              <mat-grid-tile style="box-shadow: rgba(0, 0, 0, 0.25) -5px 0px 10px 0px;">
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div>
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <div class="analytics-section-header">
                        Successive searches with clicks
                      </div>
                      <div style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                        <table class="table table-su" style="margin: 0px;">
                          <ng-container *ngIf="!withNoClickNextSearch.isEmpty">
                            <tr *ngFor="let d of withNoClickNextSearch" style="word-break: break-word;">
                              <td *ngIf="d.cnt != 0">{{d.text_entered}}</td>
                              <td *ngIf="d.cnt != 0" style="word-break: initial">{{d.cnt}}</td>
                            </tr>
                          </ng-container>
                          <tr *ngIf="withNoClickNextSearch.isEmpty">
                            <td colspan="2" class="no-docs">
                              No Searches to show.
                              <img class="doc-img">
                            </td>
                          </tr>
                          <tr *ngIf="withNoClickNextSearch.length === 0">
                            <td colspan="2">
                              <div style="text-align: center;">
                                <div class="spinner">
                                  <div class="bounce1"></div>
                                  <div class="bounce2"></div>
                                  <div class="bounce3"></div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </mat-grid-tile>
            </mat-grid-list>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div id="searchesWithoutResultForSuccessiveSearch" class="sectionMainDiv-inner-box">
      <div class="analytics-section-heading"
        *ngIf="reportSettings.length>0 && reportSettings[37] && reportSettings[37].is_enabled">
        <!-- Searches With No Result -->
        Search Classifications
      </div>
      <div class="searches-with-no-result"
        *ngIf="reportSettings.length>0 && reportSettings[37] && reportSettings[37].is_enabled">
        <div class="col-xl-12 responsive" style="padding-right: 0px;padding-left: 0px;">
          <div class="card card-block card-1" style="padding:0;margin-top: 0;border: solid 2px #e9ebf0;">
            <mat-grid-list cols="3" rowHeight="507px" gutterSiz="15px">
              <mat-grid-tile colspan="2">
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div class="analytics-section-header">
                    <i class="fa fa-line-chart" aria-hidden="true"></i>
                    <!-- Searches With No Result -->
                    {{reportSettings[37].label}}
                    <!-- <a data-toggle="modal" (click)="addClassToBody();" href="#alertModalForSearchesWithNoResult"
                      data-backdrop="static" data-keyboard="false">
                      <svg class="downloadImg" width="40" height="40" viewBox="-5 -5 40 40"
                        *ngIf="withNoResultSearchesGap && !withNoResultSearchesGap.isEmpty">
                        <path data-name="Path 1128"
                          d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
                      </svg>
                    </a> -->
                    <app-email-and-download-reports
                      *ngIf="withNoResultSearchesGap && !withNoResultSearchesGap.isEmpty"
                      [searchClients]="searchClients"
                      [range]="range"
                      [internalUser]="internalUser"
                      [tab]="ContentGapAnalysis.tabName"
                      [reportName]="ContentGapAnalysis.Reports[2]"
                      [hideDownload]="true"
                      [searchReportFilters]='withNoResultSearchesObj'
                      [AllUserEmails] = "AllUserEmails"
                      [userMetricVariable]="userMetricVariable"
                      [userMetricsFilters]="userMetricsFilters"
                    >
                    </app-email-and-download-reports>
                    <app-user-metric-filter
                      *ngIf="userMetricEnabled"
                      [userMetricLabel]="userMetricLabel"
                      [userMetricVariable]="userMetricVariable"
                      [userId]="userUniqueId"
                      [email]="userEmail"
                      [uid]="searchClients.uid"
                      [internalUser]="internalUser"
                      [from]="range.from"
                      [to]="range.to"
                      [reportName]="'Searches With No Result'"
                      [userMetricURL]="['/overview/searchesWithNoResult']"
                      [body]="{
                        from: range.from,
                        to: range.to,
                        internalUser: internalUser,
                        uid: searchClients.uid,
                        offset: 1,
                        limit: 50,
                        sortingField: 'Searches',
                        sortType: 'desc'
                      }"
                      (userMetricEvent)="sendUserMetricValues($event)"
                    ></app-user-metric-filter>
                  </div>

                  <!-- ======================== Modal Starts =================== -->
                  <!-- <div id="alertModalForSearchesWithNoResult" class="modal" style="padding-right: 0px;">
                    <div class="modal-container-new">
                      <form>
                        <div class="modal-content-new" style="border-radius:8px;">
                          <div class="modal-header-new">
                            <h6 class="modal-title-new" style="font-size: 14px;margin-bottom: 15px;">Please select one
                              of the following the options.
                            </h6>
                            <mat-form-field class="field mat-form-field-custom">
                              <mat-select name="modalSelect" placeholder="Select Download Option"
                                [(ngModel)]="selectedValue" required>
                                <mat-option *ngFor="let option of reportsDownloadOption" [value]="option">
                                  {{option}}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <mat-form-field *ngIf="selectedValue==='Send Via Email'" class="example-full-width">
                              <input matInput name="modalInputValue" [(ngModel)]="emailForReports"
                                placeholder="Email Id" style="line-height:1.19;" class="mat-input-content" required>
                            </mat-form-field>
                         
                          </div>
                          <div class="modal-footer-new">
                            <button class="buttonPrimary buttonSmall margin-right-5px" data-dismiss="modal"
                              (click)="removeClassToBody();">
                              Cancel</button>
                            <button class="buttonPrimary buttonSmall" data-dismiss="modal"
                              (click)="getTopSearchesWithNoResultGap(1,'Content gap analysis');removeClassToBody();">
                              Ok
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div> -->
                  <!-- ======================== Modal Ends ===================  -->
                  <div
                    style="background-color: #fbfbfb; height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden"
                    class="perfect">
                    <style>
                      .side-select-list:hover {
                        background-color: white;
                      }

                      .side-select-list.active {
                        background-color: white;
                        background-image: none;
                        border-left: 3px solid #56c7ff;
                      }

                      .side-selected-list {
                        cursor: pointer;
                        font-weight: bold;
                        background-color: white;
                        border-left: 3px solid #56c7ff;
                      }

                      td>.clickable {
                        text-decoration: underline;
                        color: #0275d8;
                      }
                    </style>
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Top Searches</th>
                          <th>#Sessions</th>
                          <th>#Users</th>
                          <th>Count</th>
                        </tr>
                      </thead>
                      <ng-container *ngIf="!withNoResultSearchesGap.isEmpty">
                        <tr *ngFor="let d of withNoResultSearchesGap; let i=index"
                          (click)="(withNoResultSearchesGap.active!=i && withNoResultSearchesGap.isUp)?getNoResultNextSuccessfulSearches(i):0;"
                          (keyup.enter)="(withNoResultSearchesGap.active!=i && withNoResultSearchesGap.isUp)?getNoResultNextSuccessfulSearches(i):0;"
                          [ngClass]="i===withNoResultSearchesGap.active?'side-selected-list':'side-select-list'">
                          <td style="word-break: break-word;">{{d[0]}}</td>
                          <td>{{d[2]}}</td>
                          <td>{{d[3]}}</td>
                          <td>{{d[1]}}</td>
                        </tr>
                      </ng-container>
                      <tr *ngIf="withNoResultSearchesGap.isEmpty">
                        <td colspan="4" class="no-docs">
                          No Searches to show.
                          <img class="doc-img">
                        </td>
                      </tr>
                      <tr *ngIf="withNoResultSearchesGap.length === 0">
                        <td colspan="4">
                          <div style="text-align: center;">
                            <div class="spinner">
                              <div class="bounce1"></div>
                              <div class="bounce2"></div>
                              <div class="bounce3"></div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </mat-grid-tile>
              <mat-grid-tile style="box-shadow: rgba(0, 0, 0, 0.25) -5px 0px 10px 0px;">
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div>
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <div class="analytics-section-header">
                        Successive searches with clicks
                      </div>
                      <div style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                        <table class="table table-su" style="margin: 0px;">
                          <ng-container *ngIf="!withNoResultNextSearch.isEmpty">
                            <tr *ngFor="let d of withNoResultNextSearch" style="word-break: break-word;">
                              <td *ngIf="d.cnt != 0">{{d.text_entered}}</td>
                              <td *ngIf="d.cnt != 0" style="word-break: initial">{{d.cnt}}</td>
                            </tr>
                          </ng-container>
                          <tr *ngIf="withNoResultNextSearch.isEmpty">
                            <td colspan="2" class="no-docs">
                              No Searches to show.
                              <img class="doc-img">
                            </td>
                          </tr>
                          <tr *ngIf="withNoResultNextSearch.length === 0">
                            <td colspan="2">
                              <div style="text-align: center;">
                                <div class="spinner">
                                  <div class="bounce1"></div>
                                  <div class="bounce2"></div>
                                  <div class="bounce3"></div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </mat-grid-tile>
            </mat-grid-list>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div class="" *ngIf="reportSettings.length>0 && reportSettings[34] && reportSettings[34].is_enabled">
      <div class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;"
        id="unSuccessfulSearchSession">
        <div class="card card-block" style="padding:0;margin-top: 20px;">
          <div class="analytics-section-heading">
            <!-- add -->
            <!-- Users with Unsuccessful Searches -->
            {{reportSettings[34].label}}
            <!-- <a data-toggle="modal" (click)="addClassToBody();" href="#alertModalForUnsuccessfullSearchSession"
              data-backdrop="static" data-keyboard="false">
              <svg class="downloadImg" width="40" height="40" viewBox="-5 -5 40 40">
                <path data-name="Path 1128"
                  d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
              </svg>
            </a> -->
            <app-email-and-download-reports
              [searchClients]="searchClients"
              [range]="range"
              [internalUser]="internalUser"
              [tab]="ContentGapAnalysis.tabName"
              [reportName]="ContentGapAnalysis.Reports[3]"
              [hideDownload]="true"
              [isEcosystemSelected]="isEcosystemSelected"
              [ecoSystem] = "ecoSystem"
              [AllUserEmails] = "AllUserEmails"
              [userMetricVariable]="userMetricVariable"
              [userMetricsFilters]="userMetricsFilters"
            >
            </app-email-and-download-reports>
            <app-user-metric-filter
              *ngIf="userMetricEnabled"
              [userMetricLabel]="userMetricLabel"
              [userMetricVariable]="userMetricVariable"
              [userId]="userUniqueId"
              [email]="userEmail"
              [uid]="searchClients.uid"
              [internalUser]="internalUser"
              [from]="range.from"
              [to]="range.to"
              [reportName]="'Sessions with unsuccessful searches'"
              [userMetricURL]="['/content/unSuccessfulSearchSessionChart']"
              [body]="{
                from: range.from,
                to: range.to,
                internalUser: internalUser,
                uid: searchClients.uid
              }"
              (userMetricEvent)="sendUserMetricValues($event)"
            ></app-user-metric-filter>
          </div>
          <!-- ======================== Modal Starts =================== -->
          <!-- <div id="alertModalForUnsuccessfullSearchSession" class="modal" style="padding-right: 0px;">
            <div class="modal-container-new">
              <form>
                <div class="modal-content-new" style="border-radius:8px;">
                  <div class="modal-header-new">
                    <h6 class="modal-title-new" style="font-size: 14px;margin-bottom: 15px;">Please select one of the
                      following the options.
                    </h6>
                    <mat-form-field class="field mat-form-field-custom">
                      <mat-select name="modalSelect" placeholder="Select Download Option" [(ngModel)]="selectedValue"
                        required>
                        <mat-option *ngFor="let option of reportsDownloadOption" [value]="option">
                          {{option}}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                    <mat-form-field *ngIf="selectedValue==='Send Via Email'" class="example-full-width">
                      <input matInput name="modalInputValue" [(ngModel)]="emailForReports" placeholder="Email Id"
                        style="line-height:1.19;" class="mat-input-content" required>
                    </mat-form-field>
                    
                  </div>
                  <div class="modal-footer-new">
                    <button class="buttonPrimary buttonSmall margin-right-5px" data-dismiss="modal"
                      (click)="removeClassToBody();">
                      Cancel</button>
                    <button class="buttonPrimary buttonSmall" data-dismiss="modal"
                      (click)="drawUnsuccessfulSearchSessionHistogram(1);removeClassToBody();">
                      Ok
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div> -->
          <!-- ======================== Modal Ends ===================  -->
          <div class="general-bar-chart" style="height: 350px; margin: 0px; padding: 20px;">
            <!-- Loader -->
            <div *ngIf="!unsuccessfulSearchSessionHistogram" style="text-align: center;">
              <div class="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
              </div>
            </div>
            <!-- Loader Ends -->
            <general-bar-chart [range]="range" [uid]="searchClients.uid" [reportSettings]="reportSettings[34]"
              [frequencyData]="unsuccessfulSearchSessionHistogram" [reload]="reloadUSSH"></general-bar-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex!=1">
    <div class="sectionMainDiv-inner-box" id="highConversionResultNotFirstPage">
      <div class="analytics-section-heading"
        *ngIf="reportSettings.length>0 && reportSettings[46] && reportSettings[46].is_enabled">
        <!-- High Conversio Data -->
        <span matTooltip = 'Page one refers to the Top 10 search results'
        matTooltipPosition = "right" matTooltipClass="custom-tooltip-highConversionResultNotFirstPage">
          {{reportSettings[46].label}}
        </span>
          <app-email-and-download-reports
            [searchClients]="searchClients"
            [range]="range"
            [internalUser]="internalUser"
            [tab]="ContentGapAnalysis.tabName"
            [reportName]="ContentGapAnalysis.Reports[4]"
            [highConversionData]="highConversionReport"
            [hideDownload]="true"
            [AllUserEmails] = "AllUserEmails"
            [userMetricVariable]="userMetricVariable"
            [userMetricsFilters]="userMetricsFilters"
          >
          </app-email-and-download-reports>
          <app-user-metric-filter
            *ngIf="userMetricEnabled"
            [userMetricLabel]="userMetricLabel"
            [userMetricVariable]="userMetricVariable"
            [userId]="userUniqueId"
            [email]="userEmail"
            [uid]="searchClients.uid"
            [internalUser]="internalUser"
            [from]="range.from"
            [to]="range.to"
            [reportName]="'High Conversion Results Not on Page One'"
            [userMetricURL]="['/content/highConversion']"
            [body]="{
              from: range.from,
              to: range.to,
              internalUser: internalUser,
              uid: searchClients.uid
            }"
            (userMetricEvent)="sendUserMetricValues($event)"
          ></app-user-metric-filter>
      </div>
      <div class="high-conversion-result"
        *ngIf="reportSettings.length>0 && reportSettings[46] && reportSettings[46].is_enabled">
        <div class="col-xl-12 responsive" style="padding-right: 0px;padding-left: 0px;">
          <div class="card card-block card-1" style="padding:0;margin-top: 0;border: solid 2px #e9ebf0;">
            <mat-grid-list cols="4" rowHeight="460px" gutterSiz="15px">
              <mat-grid-tile colspan="2">
                <div style="height: 460px; overflow:hidden; width: 100%;"> 
                  <!-- <div class="analytics-section-header">
                    <i class="fa fa-line-chart" aria-hidden="true"></i>
                    {{reportSettings[46].label}}
                  </div> -->
                   <!-- <div> -->
                    <div class="card card-block" style="padding:0; margin: 0px;">
                  <div
                    style="height: 460px; margin: 0 auto; overflow-y:auto; overflow-x: hidden"
                    class="perfect">
                    <style>
                      td>.clickable {
                        text-decoration: underline;
                        color: #0275d8;
                      }
                    </style>
                    <table class="table table-su card-1" style="width: 100%;">
                      <thead class="t-head">
                        <tr class="highConversionHeader">
                          <th>{{highConversionReport[0].label}} 
                            <span
                          (click)="filterHighConversion(highConversionReport[0].label,!highConversionReport[0].filled)" class="highConversion-icons-th">&nbsp;</span>
                            <div class = "left"> 
                              <div id="highConversion">
                                <input type="search" [(ngModel)]="highConversionReport[0].value" [placeholder]="highConversionReport[0].placeholder" class="filled" style="padding-right: 18px !important;padding-left: 10px;left: 15px;"
                                  [ngClass]="(highConversionReport[0].filled == true)? 'show' : 'hide'"
                                  (keyup.enter)="searchHighConversionReport(highConversionReport[0].label,$event.target.value, false); clearHighConversionClickFilters(highConversionReport[1].label)">
                                <button class="tableBoxclose" *ngIf="highConversionReport[0].filled" style="right: 3px;"
                                  (click)="highConversionReport[0].filled=false; clearHighConversionFilters(highConversionReport[0].label)">X</button>
                              </div>
                            </div>
                           </th>
                            <th *ngFor="let col of highConversionColumnsArray" style="font-size:14px;">
                              <div class = "highConversionSort">  
                                <span class = "highConversionText">
                                  {{highConversionMapColumns[col]}}
                                </span>
                                <span (click)="changeSortOrder(col); clearHighConversionClickFilters(highConversionReport[1].label)" style="cursor: pointer;">
                                  <svg *ngIf="sortingField !== col" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                    viewBox="0 0 16 16" style="margin-left: 8px;">
                                    <defs>
                                      <style>
                                        .a {
                                          fill: none;
                                        }
              
                                        .b {
                                          fill: #707070;
                                        }
                                      </style>
                                    </defs>
                                    <path class="a" d="M0,0H16V16H0Z" />
                                    <path class="b"
                                      d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                      transform="translate(-1.903 -1.069)" />
                                  </svg>
                                </span>
                                <span *ngIf="sortingField === col" (click)="changeSortOrder(col); clearHighConversionClickFilters(highConversionReport[1].label)" style="cursor: pointer;">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" style="margin-left: 8px;">
                                    <defs>
                                      <style>
                                        .aa {
                                          fill: none;
                                        }
              
                                        .bb {
                                          fill: #53C6FF;
                                        }
                                      </style>
                                    </defs>
                                    <path class="aa" d="M0,0H16V16H0Z" />
                                    <path class="bb"
                                      d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                      transform="translate(-1.903 -1.069)" />
                                  </svg>
                                </span> 
                              </div>  
                            </th>
                        </tr>
                      </thead>
                      <tbody *ngIf="!highConversionData?.isEmpty;else noData;">
                        <ng-container *ngFor="let click of highConversionData | paginate: {itemsPerPage: 20, currentPage: highConversionData?.currentPage, id: 'highConversion',totalItems: highConversionData?.count};let j=index">
                          <tr [ngClass]="(highConversionData.active == j)?'highConverion-selected-list':'highConverion-select-list'">
                          <td style="width:80%;">
                            <a class="su-docs-li" target="_blank" href="{{click.url}}" matTooltip="{{click.title}}"
                              matTooltipPosition="below" matTooltipClass="custom-tooltip-casereport"> <span
                                class="an_text_truncate"> {{click.title ? (click.title ) : (click.url )}} </span></a>
                          </td>
                          <td  [attr.class]="(click.hideflag) ? 'active ' : ''">
                            <u>
                              <a href="JavaScript:void(0);" (click)="getHighConversionSessionsResult(0, 1, click)">
                                <span style="cursor: pointer;">
                                  {{click.session_count}}
                                </span>
                              </a>
                            </u>
                          </td>
                          <td>
                            <u>
                              <a href="javascript:void(0)" 
                              (click)="(highConversionData.active!=j)?getHighConversionClicksResult(j,1):0;"
                              class="pointer">{{click.click_count}}</a>
                            </u>
                          </td>
                        </tr>
                        <div class="overlay" [attr.class]="(click.hideflag) ? 'overlaySession ':''" *ngIf="click.hideflag " [hidden]="!click.hideflag">
                          <div class="filter-container shadowDiv animate-div table-responsive" [@opened]=""
                            style="padding: 0px;overflow: hidden; height: 84%">
                            <div style="width: 100%;display: flex;padding: 5px;border-bottom: 1px solid #DDDFE1;">
                              <div style="display: flex;margin: 0;flex: 1;width: 80%;">
                                <div>
                                  <span matTooltip="{{ click.title }}" matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport"
                                    style="font-size: 13px;font-weight: 900;color: #707070;display: inline-block;padding: 15px 1px 4px 10px; letter-spacing: 0px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 800px;">
                                    Document Title : {{click.title}}
                                  </span>
                                  <div style="font-size: 13px; font-weight: 900; color: #A3A3A3; display: block; letter-spacing: 0px; width: 65%; padding: 2px 1px 15px 10px;">
                                    Number of clicks ({{highConversionSession.count}})
                                  </div>
                                  </div>
                              </div>
                              <img (click)="click.hideflag = false; clearFilterValues(); removeClassToBody();" src="assets/img/crossClick.svg"
                                style="cursor: pointer;float: right;height: 14px; margin: 20px; margin-right: 10px; order: 3;">
                              <app-email-and-download-reports class="highConversionSessionEmail" *ngIf="highConversionSession && !highConversionSession?.isEmpty"
                                [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="ContentGapAnalysis.tabName"
                                [reportName]="ContentGapAnalysis.Reports[9]" [highConversionClickUrl] = "click.url" 
                                [highConversionSessionDetailReport]="highConversionSessionData" [hideDownload]="true" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                              </app-email-and-download-reports>
                            </div>
                            <div class="Session-Tracking-Details-popup">
                              <table class="table table-su card-1 highConversionSession" *ngIf="highConversionSession">
                                <thead class="t-head">
                                  <tr class="search-click-header">
                                    <th class="highConversionSession singleLineheading" *ngFor="let filter of highConversionSessionData | slice:0:2">{{filter.label}} <span
                                      (click)="filterhighConversionSessionResult(filter.label,!filter.filled)" class="search-icons-th">&nbsp;</span>
                                    <div class="left">
                                      <div id="sessionClick">
                                        <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder" class="filled"
                                          [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                          (keyup.enter)="conversionSessionSort = true;highConversionSessions(click, filter.label,$event.target.value)">
                                        <button class="tableBoxclose" *ngIf="filter.filled"
                                          (click)="conversionSessionSort = true;filter.filled=false; highConversionSessions(click, filter.label,$event.target.value)">X</button>
                                      </div>
                                    </div>
                                   </th>
                                   <th class="sessionClickPosition singleLineheading">
                                    {{conversionSessionClickMap[conversionSessionClickArray[0]]}}
                                    <span (click)="conversionSessionSort = true;changehighConversionSessionOrder(click, conversionSessionClickArray[0])" style="cursor: pointer;">
                                      <svg *ngIf="conversionSessionSortField !== conversionSessionClickArray[0]" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .a {
                                              fill: none;
                                            }
                  
                                            .b {
                                              fill: #707070;
                                            }
                                          </style>
                                        </defs>
                                        <path class="a" d="M0,0H16V16H0Z" />
                                        <path class="b"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                    <span *ngIf="conversionSessionSortField === conversionSessionClickArray[0]" (click)="conversionSessionSort = true;changehighConversionSessionOrder(click, conversionSessionClickArray[0])" style="cursor: pointer;">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .aa {
                                              fill: none;
                                            }
                  
                                            .bb {
                                              fill: #53C6FF;
                                            }
                                          </style>
                                        </defs>
                                        <path class="aa" d="M0,0H16V16H0Z" />
                                        <path class="bb"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                  </th>
                                  <th class="highConversionSession singleLineheading">{{highConversionSessionData[2].label}} <span
                                    (click)="filterhighConversionSessionResult(highConversionSessionData[2].label,!highConversionSessionData[2].filled)" class="search-icons-th">&nbsp;</span>
                                  <div class="left">
                                    <div id="sessionClick">
                                      <input type="search" [(ngModel)]="highConversionSessionData[2].value" [placeholder]="highConversionSessionData[2].placeholder" class="filled"
                                        [ngClass]="(highConversionSessionData[2].filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="conversionSessionSort = true;highConversionSessions(click, highConversionSessionData[2].label,$event.target.value)">
                                      <button class="tableBoxclose" *ngIf="highConversionSessionData[2].filled"
                                        (click)="conversionSessionSort = true;highConversionSessionData[2].filled=false; highConversionSessions(click, highConversionSessionData[2].label,$event.target.value)">X</button>
                                    </div>
                                  </div>
                                 </th>
                                  <th class="sessionClickPosition singleLineheading">Facet Selected</th>
                                  <th class="sessionClickPosition singleLineheading">
                                    {{conversionSessionClickMap[conversionSessionClickArray[1]]}}
                                    <span (click)="conversionSessionSort = true;changehighConversionSessionOrder(click, conversionSessionClickArray[1])" style="cursor: pointer;">
                                      <svg *ngIf="conversionSessionSortField !== conversionSessionClickArray[1]" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .a {
                                              fill: none;
                                            }
                  
                                            .b {
                                              fill: #707070;
                                            }
                                          </style>
                                        </defs>
                                        <path class="a" d="M0,0H16V16H0Z" />
                                        <path class="b"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                    <span *ngIf="conversionSessionSortField === conversionSessionClickArray[1]" (click)="conversionSessionSort = true;changehighConversionSessionOrder(click, conversionSessionClickArray[1])" style="cursor: pointer;">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .aa {
                                              fill: none;
                                            }
                  
                                            .bb {
                                              fill: #53C6FF;
                                            }
                                          </style>
                                        </defs>
                                        <path class="aa" d="M0,0H16V16H0Z" />
                                        <path class="bb"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                  </th>
                                  </tr>
                                </thead>
                                <ng-container *ngFor="let activity of highConversionSession | paginate: {itemsPerPage: 20, currentPage: highConversionSession?.currentPage, id: 'highConversionSessions',totalItems: highConversionSession?.count};let activityIndex = index">
                                  <tr *ngIf="highConversionSession">
                                    <td style="text-align: left; padding-left: 20px;">{{activity.cookie}}</td>
                                    <td style="text-align: left; padding-left: 20px;">{{activity.email}}</td>
                                    <td style="text-align: left; padding-left: 20px;">{{activity.ts | timeZone:userTimeZone:"contentSource"}}</td>
                                    <td style="text-align: left; padding-left: 20px;">{{activity.text_entered}}</td>
                                    <td style="text-align: left; padding-left: 20px;">
                                        <a href="JavaScript:void(0);">
                                          <span (click)="toggleClickHighSessionsAdvancedDetails(activityIndex)"
                                            *ngIf="((activity.filters && activity.filters.length > 0)||(activity.exactphrase && activity.exactphrase.length > 0) ||(activity.withoneormore && activity.withoneormore.length > 0) ||(activity.withoutwords && activity.withoutwords.length > 0))"
                                            style="float: left; cursor: pointer;">
                                            More Detail
                                          </span>
                                        </a>
                                      <svg class="svgRight" (click)="toggleClickHighSessionsAdvancedDetails(activityIndex)"
                                      *ngIf="((activity.filters && activity.filters.length > 0)||(activity.exactphrase && activity.exactphrase.length > 0) ||(activity.withoneormore && activity.withoneormore.length > 0) ||(activity.withoutwords && activity.withoutwords.length > 0))"
                                        width="24" height="24" viewBox="0 0 24 24">
                                        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" fill="grey" />
                                        <path d="M0 0h24v24H0z" fill="none" /></svg>
                                      <svg class="svgMore" (click)="toggleClickHighSessionsAdvancedDetails(activityIndex)"
                                        *ngIf="((activity.filters && activity.filters.length > 0)||(activity.exactphrase && activity.exactphrase.length > 0) ||(activity.withoneormore && activity.withoneormore.length > 0) ||(activity.withoutwords && activity.withoutwords.length > 0))"
                                        width="24" height="24" viewBox="0 0 24 24">
                                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z" fill="grey" />
                                        <path d="M0 0h24v24H0z" fill="none" /></svg>
                                    </td>
                                    <td style="text-align: left; padding-left: 20px;">{{activity.rank}}</td>  
                                  </tr>
                                  <tr *ngIf="((activity.filters && activity.filters.length > 0)||(activity.exactphrase && activity.exactphrase.length > 0) ||(activity.withoneormore && activity.withoneormore.length > 0) ||(activity.withoutwords && activity.withoutwords.length > 0))"
                                    [attr.class]="activity.active?'activity-detail-search activity-detail-active':'activity-detail-search'">
                                    <td class="popupDetails" colspan="100">
                                      <table class="exactPhaseDetails sessionFacetBorder"  style="width:100%">
                                        <thead>
                                          <tr class="popupDetails">
                                            <th class ="sessionFacetBorder">Facets Type</th>
                                            <th class ="sessionFacetBorder">Facets Value</th>
                                            <th class ="sessionFacetBorder">Exact Phrase</th>
                                            <th class ="sessionFacetBorder">With One Or More
                                            </th>
                                            <th class ="sessionFacetBorder">Without The Words
                                            </th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          <ng-container *ngFor="let filters of activity.filters;let k=index">
                                            <tr class="popupDetails">
                                                <td class ="sessionFacetBorder">{{filters.name}}</td>
                                                <td class ="sessionFacetBorder" *ngIf="filters.selectedValues && filters.selectedValues.length > 0">
                                                  <ng-container *ngIf="filters.selectedValues[0].min_value; else noMinValue">
                                                    <div>
                                                      Min: {{ formatValue(filters.selectedValues[0].min_value) }}
                                                    </div>
                                                    <div>
                                                      Max: {{ formatValue(filters.selectedValues[0].max_value) }}
                                                    </div>
                                                    </ng-container>
                                                  
                                                  <ng-template #noMinValue>
                                                    {{ filters.selectedValues[0] }}
                                                  </ng-template>
                                                  
                                                  </td>
                                                      <td class ="sessionFacetBorder">
                                                      <span> {{activity.exactphrase}}</span>
                                                    </td>
                                                    <td class ="sessionFacetBorder">
                                                      {{activity.withoneormore}}
                                                    </td>
                                                    <td class ="sessionFacetBorder">
                                                      {{activity.withoutwords}}
                                                    </td>
                                            </tr>
                                          </ng-container>
                                          <ng-container *ngIf="(activity.exactphrase || activity.withoneormore || activity.withoutwords) && activity.filters.length === 0">
                                            <tr class="popupDetails">
                                                <td></td>
                                                <td></td>
                                                <td>{{ activity.exactphrase }}</td>
                                                <td>{{ activity.withoneormore }}</td>
                                                <td>{{ activity.withoutwords }}</td>
                                            </tr>
                                          </ng-container>  
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </ng-container>
                                <tr *ngIf="highConversionSession?.length != 0" class="hover">
                                  <td colspan="100" style="text-align: right;">
                                    <pagination-controls id="highConversionSessions" (pageChange)="conversionSessionSort = true; getHighConversionSessionsResult(0,$event, click)">
                                    </pagination-controls>
                                  </td>
                                </tr>
                              </table>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                      <tr *ngIf="highConversionData.count >= 20" class="hover">
                        <td colspan="100" style="text-align: right;">
                          <pagination-controls id="highConversion" (pageChange)="getHighConverisonData(0,$event)">
                          </pagination-controls>
                        </td>
                      </tr>
                    </tbody>
                      <tr *ngIf="highConversionData?.isEmpty">
                        <td colspan="2" class="no-docs">No Documents to show.
                          <img class="doc-img">
                        </td>
                      </tr>
                      <tr *ngIf="highConversionData?.length === 0">
                        <td colspan="4">
                          <div style="text-align: center;">
                            <div class="spinner">
                              <div class="bounce1"></div>
                              <div class="bounce2"></div>
                              <div class="bounce3"></div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                  </div>
                </div>
              </mat-grid-tile>
              <mat-grid-tile  colspan="2" style="box-shadow: rgba(0, 0, 0, 0.25) -5px 0px 10px 0px;">
                <div style="height: 460px; overflow:auto; width: 100%;">
                  <div>
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <div style="height: 460px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                        <table class="table table-su" style="margin: 0px;">
                          <thead class="t-head highConversionHeader">
                            <th>#</th>
                            <th>{{highConversionReport[1].label}} 
                            <span
                            (click)="filterHighConversion(highConversionReport[1].label,!highConversionReport[1].filled)" class="highConversion-icons-th">&nbsp;</span>
                              <div class = "left"> 
                                <div id="highConversion">
                                  <input type="search" [(ngModel)]="highConversionReport[1].value" [placeholder]="highConversionReport[1].placeholder" class="filled" style="padding-right: 18px !important;padding-left: 10px;left: 15px;"
                                    [ngClass]="(highConversionReport[1].filled == true)? 'show' : 'hide'"
                                    (keyup.enter)="searchHighConversionReport(highConversionReport[1].label,$event.target.value, true)">
                                  <button class="tableBoxclose" *ngIf="highConversionReport[1].filled" style="right: 3px;"
                                    (click)="highConversionReport[1].filled=false; clearHighConversionClickFilters(highConversionReport[1].label)">X</button>
                                </div>
                              </div>
                             </th>
                             <th *ngFor="let coln of highConversionClickColumnsArray" style="font-size:14px;" class="singleLineheading">
                              <div class = "highConversionSort">  
                                <span class = "highConversionText">
                                  {{highConversionClickColumns[coln]}}
                                </span>
                                <span (click)="changeClickSortOrder(coln)" style="cursor: pointer;">
                                  <svg *ngIf="clickSortField !== coln" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                    viewBox="0 0 16 16" style="margin-left: 8px;">
                                    <defs>
                                      <style>
                                        .a {
                                          fill: none;
                                        }
              
                                        .b {
                                          fill: #707070;
                                        }
                                      </style>
                                    </defs>
                                    <path class="a" d="M0,0H16V16H0Z" />
                                    <path class="b"
                                      d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                      transform="translate(-1.903 -1.069)" />
                                  </svg>
                                </span>
                                <span *ngIf="clickSortField === coln" (click)="changeClickSortOrder(coln)" style="cursor: pointer;">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" style="margin-left: 8px;">
                                    <defs>
                                      <style>
                                        .aa {
                                          fill: none;
                                        }
              
                                        .bb {
                                          fill: #53C6FF;
                                        }
                                      </style>
                                    </defs>
                                    <path class="aa" d="M0,0H16V16H0Z" />
                                    <path class="bb"
                                      d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                      transform="translate(-1.903 -1.069)" />
                                  </svg>
                                </span> 
                              </div>  
                            </th>                        
                           </thead>
                          <tbody *ngIf="!highConversionClicks?.isEmpty">
                            <ng-container *ngFor="let click of highConversionClicks | paginate: {itemsPerPage: 20, currentPage: highConversionClicks?.currentPage, id: 'highConversionClick',totalItems: highConversionClicks?.count};let i=index">
                              <tr style="word-break: break-word;">
                                <td style="vertical-align: middle;">{{(i+1)+(highConversionClicks.currentPage-1)*20}}</td>
                                <td *ngIf="click.search_count != 0">{{click.text_entered}}</td>
                                <td *ngIf="click.search_count != 0" class="highConversionClickCount" style="word-break: initial">{{click.search_count}}</td>
                              </tr>
                            </ng-container>
                            <tr *ngIf="highConversionClicks.count >= 20" class="hover">
                              <td colspan="100" style="text-align: right;">
                                <pagination-controls id="highConversionClick" (pageChange)="getHighConversionClicksResult(activeClick,$event)">
                                </pagination-controls>
                              </td>
                            </tr>
                            <tr *ngIf="highConversionClicks?.isEmpty">
                              <td colspan="2" class="no-docs">
                                No Searches to show.
                                <img class="doc-img">
                              </td>
                            </tr>
                            <tr *ngIf="highConversionClicks?.length === 0">
                              <td colspan="2">
                                <div style="text-align: center;">
                                  <div class="spinner">
                                    <div class="bounce1"></div>
                                    <div class="bounce2"></div>
                                    <div class="bounce3"></div>
                                  </div>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </mat-grid-tile>
            </mat-grid-list>
          </div>
        </div>
      </div>
    </div>
  </div>
<!-- DO NOT DELETE THIS COMMENT -->
  <!-- <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div *ngIf="reportSettings[47] && reportSettings[47] && reportSettings[47].is_enabled">
      <div class="col-xl-12 col-lg-12 col-sm-12 col-xs-12"
        style="padding-right: 0px; padding-left: 0px;margin-top: 20px;">
        <div class="card card-block" style="padding:0;" id="avgTimeDocument">
          <div class="analytics-section-heading">
            {{reportSettings[47].label}}
            <svg class="downloadImg" width="40" height="40" viewBox="-5 -5 40 40"
              *ngIf="pageTime && (!pageTime.isEmpty)" (click)="getPageTime(1)">
              <path data-name="Path 1128" d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
            </svg>
          </div>
          <div id="avgTimeDocument">
            <page-time [range]="range" [uid]="searchClients.uid" [reportSettings]="reportSettings[47]"
              [internalUser]="internalUser"></page-time>
          </div>
        </div>
      </div>
    </div>
  </div> -->

  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div class="sectionMainDiv-inner-box">
      <div class="analytics-section-heading" *ngIf="reportSettings.length>0 && reportSettings[61] && reportSettings[61].is_enabled">
        {{reportSettings[61].label}}
        <app-email-and-download-reports *ngIf="articlesUsageByAgents && !articlesUsageByAgents.isEmpty" [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="ContentGapAnalysis.tabName" [reportName]="ContentGapAnalysis.Reports[10]" [hideDownload]="true" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
        </app-email-and-download-reports>
        <app-user-metric-filter
          *ngIf="userMetricEnabled"
          [userMetricLabel]="userMetricLabel"
          [userMetricVariable]="userMetricVariable"
          [userId]="userUniqueId"
          [email]="userEmail"
          [uid]="searchClients.uid"
          [internalUser]="internalUser"
          [from]="range.from"
          [to]="range.to"
          [reportName]="'Articles Usage By Agents'"
          [userMetricURL]="['/content/articleUsageByAgents']"
          [body]="{
            from: range.from,
            to: range.to,
            internalUser: internalUser,
            uid: searchClients.uid
          }"
          (userMetricEvent)="sendUserMetricValues($event)"
        ></app-user-metric-filter>
      </div>
      <div class="articles-usage-by-agent"
        *ngIf="reportSettings.length>0 && reportSettings[61] && reportSettings[61].is_enabled">
        <div class="col-xl-12 responsive" style="padding-right: 0px;padding-left: 0px;">
          <div class="card card-block card-1" style="padding:0;margin-top: 0;border: solid 2px #e9ebf0;">
            <mat-grid-list cols="10" rowHeight="507px" gutterSiz="15px">
              <mat-grid-tile colspan="4">
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div class="analytics-section-header">
                    <i class="fa fa-line-chart" aria-hidden="true"></i>
                    Agent Details
                  </div>
                  <div
                    style="background-color: #fbfbfb; height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden"
                    class="perfect">
                    <style>
                      .side-select-list:hover {
                        background-color: white;
                      }
  
                      .side-select-list.active {
                        background-color: white;
                        background-image: none;
                        border-left: 3px solid #56c7ff;
                      }
  
                      .side-selected-list {
                        cursor: pointer;
                        font-weight: bold;
                        background-color: white;
                        border-left: 3px solid #56c7ff;
                      }
  
                      .truncate {
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                      }
  
                      .clickable {
                        text-decoration: underline;
                        color: #0275d8;
                      }
                          
                    </style>
                    <table class="table" style="table-layout: fixed;">
                      <thead>
                        <tr>
                          <th colspan="2" style="width: 50%;">Agent
                            <span (click)="filtersSearchArticlesUsageByAgents(articlesUsageByAgentsFilter[0].label,!articlesUsageByAgentsFilter[0].filled)" class="search-icons-th">&nbsp;</span>
                            <!-- SearchBox -->
                            <div style="top: 65px;left: -3px;width: 40%;height: 22px;" class="left" *ngIf="articlesUsageByAgentsFilter[0].filled === true">
                            <div id="search">
                            <input type="search"
                              [(ngModel)]="articlesUsageByAgentsFilter[0].value"
                              [placeholder]="articlesUsageByAgentsFilter[0].placeholder" 
                              class="filled"
                              [ngClass]= "(articlesUsageByAgentsFilter[0].filled == true)? 'show' : 'hide'"
                              (keyup.enter)="searchQueryArticlesUsageByAgents(articlesUsageByAgentsFilter[0].label,$event.target.value)">
                               <button class="tableBoxclose" 
                               style="padding-top: 0px !important;"
                               *ngIf="articlesUsageByAgentsFilter[0].filled"
                              (click)="articlesUsageByAgentsFilter[0].filled=false; searchQueryArticlesUsageByAgents(articlesUsageByAgentsFilter[0].label,$event.target.value)">X</button>
                            </div>
                           </div>
                           </th>
                          <th colspan="2" style="width: 50%;">
                            <span>
                            #Attachments
                            <span *ngIf=!agentsListSorted (click)="sortArticlesUsageByAgents()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 20 20"><defs>
                              <style>.attachmentsSorted{fill:none;}.attachmentsUnSorted{fill:#707070;}</style>
                              </defs><path class="attachmentsSorted" d="M0,0H16V16H0Z"/><path class="attachmentsUnSorted" d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z" transform="translate(-1.903 -1.069)"/></svg>
                            </span>
                            <span *ngIf=agentsListSorted (click)="sortArticlesUsageByAgents()">
                              <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 20 20"><defs><style>.attachmentsSorted{fill:none;}.attachmentsUnSorted{fill:#53C6FF;}</style></defs><path class="attachmentsSorted" d="M0,0H16V16H0Z"/><path class="attachmentsUnSorted" d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z" transform="translate(-1.903 -1.069)"/></svg>
                            </span>
                            </span>
                          </th>  
                        </tr>
                      </thead>
                      <ng-container *ngIf="!articlesUsageByAgents.isEmpty">
                        <tr *ngFor="let d of articlesUsageByAgents; let i=index"
                        (click)="(articlesUsageByAgents.active!=i && articlesUsageByAgents.isUp)?getArticleUsageNextSuccessfullSearches(i):0;"
                        (keyup.enter)="(articlesUsageByAgents.active!=i && articlesUsageByAgents.isUp)?getArticleUsageNextSuccessfullSearches(i):0;"
                          [ngClass]="i===articlesUsageByAgents.active?'side-selected-list':'side-select-list'">
                          <td colspan="2" class="truncate" style="word-break: break-word;" matTooltip="{{d.agent_email}}"
                          matTooltipPosition="below" matTooltipClass="an-tooltip-agentReport">{{d.agent_email}}</td>
                          <td colspan="2" style="color:#7290F8; text-decoration: underline;">{{d.totat_attachments}}</td>
                        </tr>
                      </ng-container>
                      <tr *ngIf="articlesUsageByAgents.isEmpty">
                        <td colspan="6" class="no-docs">
                          No Agents To Show.
                          <img class="doc-img">
                        </td>
                      </tr>
                      <tr *ngIf="articlesUsageByAgents.length === 0">
                        <td colspan="6">
                          <div style="text-align: center;">
                            <div class="spinner">
                              <div class="bounce1"></div>
                              <div class="bounce2"></div>
                              <div class="bounce3"></div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </mat-grid-tile>
              <mat-grid-tile style="box-shadow: rgba(0, 0, 0, 0.25) -5px 0px 10px 0px;" colspan="6">
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div>
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <div class="analytics-section-header">
                        Attached Articles
                      </div>
                      <div style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                        <table class="table table-su" style="table-layout: fixed;">
                            <thead *ngIf="!articlesUsageByAgents.isEmpty && (!withArticleUsageNextSearch.isEmpty || articlesUsageByAgentsFilter[1].filled === true || articlesUsageByAgentsFilter[2].filled === true)">
                              <tr>
                                <th colspan="2">Article Title
                                  <span (click)="filtersSearchArticlesUsageByAgents(articlesUsageByAgentsFilter[1].label,!articlesUsageByAgentsFilter[1].filled)" class="search-icons-th">&nbsp;</span>
                                  <!-- SearchBox -->
                                  <div style="left: -3px;top: 16px;width: 31%;height: 22px;" 
                                       class="left" 
                                       *ngIf="articlesUsageByAgentsFilter[1].filled === true">
                                  <div id="search">
                                  <input type="search"
                                    [(ngModel)]="articlesUsageByAgentsFilter[1].value"
                                    [placeholder]="articlesUsageByAgentsFilter[1].placeholder" 
                                    class="filled"
                                    [ngClass]= "(articlesUsageByAgentsFilter[1].filled == true)? 'show' : 'hide'"
                                    (keyup.enter)="searchQueryArticlesUsageByAgents(articlesUsageByAgentsFilter[1].label,$event.target.value)">
                                  <button class="tableBoxclose"
                                  style="padding-top: 0px !important;" 
                                  *ngIf="articlesUsageByAgentsFilter[1].filled"
                                  (click)="articlesUsageByAgentsFilter[1].filled=false;articlesUsageByAgentsFilter[1].value=''; searchQueryArticlesUsageByAgents(articlesUsageByAgentsFilter[1].label,'')">X</button>
                                  </div>
                                  </div>  
                                </th>
                                <th colspan="2">Case Subject
                                  <span (click)="filtersSearchArticlesUsageByAgents(articlesUsageByAgentsFilter[2].label,!articlesUsageByAgentsFilter[2].filled)" class="search-icons-th">&nbsp;</span>
                                  <!-- SearchBox -->
                                  <div style="top: 16px; left: 34%; width: 31%;height: 22px;" 
                                       class="left" 
                                       *ngIf="articlesUsageByAgentsFilter[2].filled === true">
                                  <div id="search">
                                  <input type="search"
                                    [(ngModel)]="articlesUsageByAgentsFilter[2].value"
                                    [placeholder]="articlesUsageByAgentsFilter[2].placeholder" 
                                    class="filled"
                                    [ngClass]= "(articlesUsageByAgentsFilter[2].filled == true)? 'show' : 'hide'"
                                    (keyup.enter)="searchQueryArticlesUsageByAgents(articlesUsageByAgentsFilter[2].label,$event.target.value)">
                                  <button class="tableBoxclose"
                                  style="padding-top: 0px !important;" 
                                  *ngIf="articlesUsageByAgentsFilter[2].filled"
                                  (click)="articlesUsageByAgentsFilter[2].filled=false;articlesUsageByAgentsFilter[2].value='';  searchQueryArticlesUsageByAgents(articlesUsageByAgentsFilter[2].label,'')">X</button>
                                  </div>
                                  </div>  
                                </th>  
                                <th colspan="2">
                                  <span>
                                  Attach Date
                                  <span 
                                  *ngIf=!attachedArticlesListSorted (click)="sortAttachedArticlesByDate(articlesUsageByAgents.active || 0)">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 20 20"><defs>
                                  <style>.attachedDateSorted{fill:none;}.attachedDateUnSorted{fill:#707070;}</style>
                                  </defs><path class="attachedDateSorted" d="M0,0H16V16H0Z"/><path class="attachedDateUnSorted" d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z" transform="translate(-1.903 -1.069)"/></svg>
                                  </span>
                                  <span *ngIf=attachedArticlesListSorted (click)="sortAttachedArticlesByDate(articlesUsageByAgents.active || 0)">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 20 20"><defs><style>.attachedDateSorted{fill:none;}.attachedDateUnSorted{fill:#53C6FF;}</style></defs><path class="attachedDateSorted" d="M0,0H16V16H0Z"/><path class="attachedDateUnSorted" d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z" transform="translate(-1.903 -1.069)"/></svg>
                                  </span>
                                  </span>
                                </th>
                               
                              </tr>
                            </thead>
                            <ng-container *ngIf="!withArticleUsageNextSearch.isEmpty">
                            <tr *ngFor="let d of withArticleUsageNextSearch" style="word-break: break-word;">
                              <td class="truncate" colspan="2"
                              matTooltip="{{d.article_title}}" matTooltipPosition="below" matTooltipClass="an-tooltip-sub-agentReport"><a href="{{d.article_url}}" target="_blank">{{d.article_title}}</a></td>
                              <td class="truncate" colspan="2">{{d.case_subject}}</td>
                              <td class="truncate" colspan="2">{{d.attached_date | timeZone:userTimeZone:"contentSource"}}</td>
                            </tr>
                          </ng-container>
                          <tr *ngIf="withArticleUsageNextSearch.isEmpty" >
                            <td colspan="6" class="no-docs">
                              No Articles to show.
                              <img class="doc-img">
                            </td>
                          </tr>
                          <tr *ngIf="withArticleUsageNextSearch.length === 0">
                            <td colspan="6">
                              <div style="text-align: center;">
                                <div class="spinner">
                                  <div class="bounce1"></div>
                                  <div class="bounce2"></div>
                                  <div class="bounce3"></div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </mat-grid-tile>
            </mat-grid-list>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div *ngIf="reportSettings[48] && reportSettings[48].is_enabled">
      <div class="col-xl-12 col-lg-12 col-sm-12 col-xs-12"
        style="padding-right: 0px; padding-left: 0px;margin-top: 20px;">
        <div class="card card-block" style="padding:0;" id="kcsReport">
          <div class="analytics-section-heading">
            {{reportSettings[48].label}}
            <!-- <a data-toggle="modal" (click)="addClassToBody();" href="#alertModalForKcsReport"
              data-backdrop="static" data-keyboard="false">
              <svg class="downloadImg" width="40" height="40" viewBox="-5 -5 40 40">
                <path data-name="Path 1128"
                  d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
              </svg>
            </a> -->
            <app-email-and-download-reports *ngIf="kcsSupportArticlesList.record.length > 0" [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="ContentGapAnalysis.tabName" [reportName]="ContentGapAnalysis.Reports[5]" [hideDownload]="true" [AllUserEmails] = "AllUserEmails">
            </app-email-and-download-reports>
          </div>
          <!-- ======================== Modal Starts =================== -->
          <!-- <div id="alertModalForKcsReport" class="modal" style="padding-right: 0px;">
            <div class="modal-container-new">
              <form>
                <div class="modal-content-new" style="border-radius:8px;">
                  <div class="modal-header-new">
                    <h6 class="modal-title-new" style="font-size: 14px;margin-bottom: 15px;">Please select one of the
                      following the options.
                    </h6>
                    <mat-form-field class="field mat-form-field-custom">
                      <mat-select name="modalSelect" placeholder="Select Download Option" [(ngModel)]="selectedValue"
                        required>
                        <mat-option *ngFor="let option of reportsDownloadOption" [value]="option">
                          {{option}}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                    <mat-form-field *ngIf="selectedValue==='Send Via Email'" class="example-full-width">
                      <input matInput name="modalInputValue" [(ngModel)]="emailForReports" placeholder="Email Id"
                        style="line-height:1.19;" class="mat-input-content" required>
                    </mat-form-field>
                
                  </div>
                  <div class="modal-footer-new">
                    <button class="buttonPrimary buttonSmall margin-right-5px" data-dismiss="modal"
                      (click)="removeClassToBody();">
                      Cancel</button>
                    <button class="buttonPrimary buttonSmall" data-dismiss="modal"
                      (click)="getKcsArticles(1);removeClassToBody();">
                      Ok
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div> -->
          <!-- ======================== Modal Ends ===================  -->
          <mat-tab-group class="kcs-mat-tab" [(selectedIndex)]="selectedIndex3Tab" style="padding:10px;">
            <mat-tab label="Report">
              <div *ngIf="kcsSupportArticlesList.record" style="border: 2px solid #efefef;" id="queries-report"
                class="table-responsive perfect kcsSupportArticle">
                <table class="table table-su" style="border: none;box-shadow: none;">
                  <thead class="t-head">
                    <tr>
                      <th>Case Subject</th>
                      <th>Count</th>
                    </tr>
                  </thead>
                  <ng-container *ngIf="!kcsSupportArticlesList.isEmpty">
                    <tr *ngFor="let q of kcsSupportArticlesList.record;let i = index;" style="word-break: break-word;">
                      <td style="width: 80%;">
                        <a [href]="q.case_url" target="_blank">
                          {{q.case_subject}}
                        </a>
                      </td>
                      <td style="cursor: pointer;">
                        <a href="javascript:void(0)" class="pointer"
                          (click)="getUpdatedArticlesOnCases(q.case_subject,q.case_url,i);">{{q.counts}}</a>
                      </td>
                    </tr>
                  </ng-container>
                  <tr *ngIf="kcsSupportArticlesList.isEmpty">
                    <td colspan="2" class="no-docs">
                      No documents to show.
                      <img class="doc-img">
                    </td>
                  </tr>
                  <tr *ngIf="kcsSupportArticlesList.loading">
                    <td colspan="2">
                      <div style="text-align: center;">
                        <div class="spinner">
                          <div class="bounce1"></div>
                          <div class="bounce2"></div>
                          <div class="bounce3"></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                </table>
              </div>
            </mat-tab>
            <div class="card card-block" id="reportId == 16 || reportId == 45">
              <mat-tab>
                <div class="kcsReportBorder" style="width: 100%;">
                  <div class="lightThemeTitleBackground" style="position: relative;">
                    <span style="padding: 23px; display: inline-block;">{{kcsSupportTitle}}</span>
                    <button class="buttonSecondary backToReport" (click)="toggleViewState();"
                      style="width: 100px;position: absolute;right: 10px;top: 10px;">
                      <i class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                  </div>
                  <div style="height:325px;margin: 0 auto;overflow-y: auto;overflow-x: hidden;"
                    class="kcsReportBackground-theme">
                    <table class="table table-su">
                      <thead class="t-head">
                        <tr>
                          <th style="font-weight: 500;width:25%">Article Title</th>
                          <th style="font-weight: 500;width:25%">Author</th>
                          <th style="font-weight: 500;width:25%">Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let kcs of kcsSupportArticleDetailList.record;let i=index">
                          <td style="width:25%">{{kcs.article_title}}</td>
                          <td style="width:25%">{{kcs.article_author}}</td>
                          <td style="width:25%">{{kcs.created_date}}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </mat-tab>
            </div>
          </mat-tab-group>
        </div>
      </div>
    </div>
  </div>
  
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div *ngIf="reportSettings[56] && reportSettings[56].is_enabled">
      <div class="col-xl-12 col-lg-12 col-sm-12 col-xs-12"
        style="padding-right: 0px; padding-left: 0px;margin-top: 20px;">
        <div class="card card-block" style="padding:0;" id="kcsReport">
          <div class="analytics-section-heading">
            <span>{{reportSettings[56].label}} </span>
            <app-email-and-download-reports  *ngIf="kcs.record.totalKcs" [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="ContentGapAnalysis.tabName" [reportName]="ContentGapAnalysis.Reports[6]" [hideDownload]="true" [AllUserEmails] = "AllUserEmails">
            </app-email-and-download-reports>
          </div>
          <mat-tab-group class="kcs-mat-tab" [(selectedIndex)]="selectedIndex3Tab" style="padding:10px;">
            <mat-tab label="Report">
              <div *ngIf="kcs.record" style="border: 2px solid #efefef;" id="queries-report"
                class="table-responsive perfect kcsSupportArticle">
                <table class="table table-su" style="border: none;box-shadow: none;">
                  <thead class="t-head kcsDetails">
                    <tr>
                      <th style="width: 50px;">#</th>
                      <th style="width:110px;">Article <span>({{ kcs.record.totalKcs || 0}})</span></th>
                      <th >Article Title</th>
                      <th style="width:200px">
                        <span class="left"  *ngIf="!kcs.search"  >
                          Author Email id
                          <svg class="searchIcon"  *ngIf="!kcs.search"  (click)="toggleSearchBox()" width="13.49"
                            height="13.49" viewBox="0 0 13.49 13.49">
                            <path id="Path_2764" data-name="Path 2764"
                              d="M12.641,11.484h-.609l-.216-.208a5.021,5.021,0,1,0-.54.54l.208.216v.609l3.856,3.849,1.149-1.149Zm-4.628,0a3.471,3.471,0,1,1,3.471-3.471A3.466,3.466,0,0,1,8.013,11.484Z"
                              transform="translate(-3 -3)" fill="#707070" />
                          </svg>
                        </span>
                        <div style="position:relative;">
                          <input class="kcsSearchBar" [ngClass]="{'showSearch':kcs.search , 'hideSearch':!kcs.search,'closeSearxchBox':!kcs.searchBoxClose ,'showSearxchBox':kcs.searchBoxClose  }"  [(ngModel)]="kcsEmailSearch" 
                            [placeholder]="kcs.search?'Email id':''"
                            (keyup.enter)="getKCSDetails(kcsEmailSearch);">
                          <button [ngClass]="{'showCross':kcs.search, 'hideSearch':!kcs.search}" class="kcstableBoxclose" (click)="getKCSDetails(null);">X</button>
                        </div>
                      </th>
                      <th>Created date</th>
                      <th>Total shares</th>
                    </tr>
                  </thead>
                  <ng-container *ngIf="!kcs.isEmpty">
                    <tr *ngFor="let q of kcs.record.articles;let i = index;" style="word-break: break-word;">
                      <td>{{i+1}}</td>
                      <td style="width:110px;" >
                        {{q.articleid}}
                      </td>
                      <td style="cursor: pointer;">
                        <a [href]="q.articleurl" target="_blank" class="pointer">{{q.title}}</a>
                      </td>
                      <td>{{q.author}}</td>
                      <td>{{q.ts | timeZone : userTimeZone:"contentSource"}}</td>
                      <td><span class='tableLinks'
                          (click)="sharedKCSArticles(0, q.doc_id,q.totalshares);">{{q.totalshares}}</span></td>
                    </tr>
                  </ng-container>
                </table>
                  <div *ngIf="kcs.isEmpty">
                    <div colspan="2" class="no-docs">
                      No documents to show.
                      <img class="doc-img">
                    </div>
                  </div>
                  <div *ngIf="kcs.loading">
                    <div colspan="2">
                      <div style="text-align: center;">
                        <div class="spinner">
                          <div class="bounce1"></div>
                          <div class="bounce2"></div>
                          <div class="bounce3"></div>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              <mat-grid-tile *ngIf="sharedKCSArticlesDetails" class="mat-grid-tile" colspan="2"
                style="box-shadow: rgba(0, 0, 0, 0.25) -5px 0px 10px 0px; left: calc((33.3333% - 0.666667px + 1px) * 1); width: calc((33.3333% - 0.666667px) * 2 + 1px); top: 0px; height: calc(386px);"
                ng-reflect-colspan="2" rowspan="1">
                <figure class="mat-figure">
                  <div style="height: 386px; overflow:auto; width: 100%;">
                    <div style = "height:100%">
                      <div class="card card-block" style="padding:0; margin: 0px;height:100%">
                        <div>
                          <table style="
                          margin: 8px 14px;display: inline-block;
                      ">
                            <thead>
                              <tr>
                                <td>Total Shares : {{sharedTotal}} </td>
                              </tr>
                            </thead>
                          </table>
                          <span style='float:right;margin-top: 4px;margin-right: 10px;font-size: 26px;cursor:pointer'
                            (click)="sharedKCSArticlesDetails = false">&times;</span>
                        </div>
                        <table style="margin-bottom: 0;">
                              <thead class="reportHeader sharedArticlesHeader" style="display: table-row;">
                                <th style="width:295px;">Case Title</th>
                                <th>Shared via Email</th>
                                <th>Shared via CaseComment</th>
                                <th>Copied To Clipboard</th>
                                <th>Attached To Case</th>
                              </thead>
                          <tbody class="perfect" style="height: 290px; margin: 0 auto; overflow-y:auto; overflow-x: hidden">
                                <ng-container *ngIf="!sharedArticles.isEmpty">
                                  <tr class="sharedArticlesTable"
                                    *ngFor="let q of sharedArticles.record;let j = index;"
                                    style="word-break: break-word;">
                                    <td title='q.casesubject' style="width:295px;">{{q.casesubject}}</td>
                                    <td>{{q.linksharingviaemail || 0}}</td>
                                    <td>{{q.linksharingviacasecomment || 0}}</td>
                                    <td>{{q.copytoclipboard || 0}}</td>
                                    <td>{{q.attachtocasecomment || 0}}</td>
                                  </tr>
                                </ng-container>
                                <tr *ngIf="sharedArticles.isEmpty">
                                  <td class="no-docs" colspan="2">No Documents to show. <img class="doc-img"></td>
                                </tr>
                              </tbody>
                            </table>
                        </div>
                    </div>
                  </div>
                </figure>
              </mat-grid-tile>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div *ngIf="reportSettings.length>0  && reportSettings[55] && reportSettings[55].is_enabled && searchClients.uid">
      <div class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0;margin-top: 20px;">
        <div class="card card-block" style="padding:0;" id="documentsBylength">
          <analytics-results id="documentsByContentLength" [range]="range" [internalUser]="internalUser"
            [uid]="searchClients.uid" [reportSettings]="reportSettings[55]"></analytics-results>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex===2">
    <div class="">
      <div *ngIf="Analyticstab3 == false && allGraphLoaded == true"
        class="col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 Enable-analytics" style="margin-top:20px;">
        <div *ngIf="!this.isEcosystemSelected" (click)="enableAnalyticsSettings()" class="enable-analytics-settings">
          <img *ngIf="!this.isEcosystemSelected" src="assets/img/Enable-analytics-under-search-client.svg" class="Enable-analytics-img">
          <h6 class="Enable-analytics-under-search-client" *ngIf="!this.isEcosystemSelected">Enable analytics under search client</h6>
        </div>
        <div *ngIf="this.isEcosystemSelected === true" (click)="enableAnalyticsSettingsEco()" class="enable-analytics-settings">
          <img *ngIf="this.isEcosystemSelected === true" src="assets/img/Group 5508.svg" class="Enable-analytics-img" style="margin-top: 13px;">
        </div>
      </div>
    </div>
  </div>
</div>