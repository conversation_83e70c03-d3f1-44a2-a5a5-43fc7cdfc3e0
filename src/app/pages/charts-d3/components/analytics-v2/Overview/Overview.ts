import { <PERSON>mponent, OnInit, ViewChild, ElementRef, HostListener } from '@angular/core';
import { CookieService } from '../../../../../services/cookie.service';
import { Router, ActivatedRoute } from '@angular/router';
import { AnalyticService } from '../../../../../services/analytics';
import { AnalyticV2Service } from '../../../../../services/analytics-v2.service';
import { ToastyService, ToastyConfig, ToastOptions } from 'ng2-toasty';
import { SidebarService } from '../../../../../services/sideBar.service';
import { AdminAnalyticsService } from '../../../../../services/adminAnaytics.service';
import { EcosystemService } from '../../../../../services/ecoSystem.service';
import { DaterangePickerComponent } from 'ng2-daterangepicker';
import { Subscription, Subject } from 'rxjs';
import moment from 'moment/moment';
import * as momentZone from 'moment-timezone';
import { ThousandSuffixesPipe } from '../../../../../pipes/ThousandMillion';
import { FormBuilder, FormControl, Validators, FormGroup } from '@angular/forms';
import { MatOption } from '@angular/material/core';
import { addonsService } from '../../../../../services/addons.service';
import { trigger, state, style, animate, transition, query } from '@angular/animations';
import { UserManagementService } from '../../../../../services/userManagement.service';
import { PageRatingService } from '../../../../../services/pageRating.service'
import { Variables } from '../../../../../variables/contants';
import { TimezoneService } from 'app/services/timezone.service';
import { TimeZonePipe } from '../../../../../pipes/timezone.pipe';
import { GenerateSearchClientComponent } from 'app/pages/generate-search-client/components/generate_search_client';
import { ContentSourceService } from 'app/services/contentSource.service';
import { SearchClientService } from 'app/services/searchClient.service';
import { SearchTuningService } from 'app/services/searchTuning.service';
import { CategoryPipe } from 'app/pipes/category';
import {MatExpansionPanel} from '@angular/material/expansion';

import { MatDialog, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSelect} from '@angular/material/select';
import { MatMenuTrigger } from '@angular/material/menu';
import { takeUntil } from 'rxjs/operators';
@Component({
    selector: 'Overview',
    templateUrl: 'Overview.html',
    styleUrls: ['../analytics-v2.scss'],
    providers: [ AnalyticService, AnalyticV2Service, ToastyService, ToastyConfig, UserManagementService, AdminAnalyticsService, PageRatingService, EcosystemService, GenerateSearchClientComponent, ContentSourceService, SearchClientService, SearchTuningService, CategoryPipe],
    animations: [
        trigger('opened', [
            state('void', style({
                'transform': 'translateY(-100%)'
            })),
            transition('no => yes', [animate(200), style({ transform: 'translate(100%)' })]),
            transition('yes => no', [animate(200), style({ transform: 'translate(-100%)' })]),
            transition('void => *', [animate(200)]),
            transition('* => void', [animate(200)])
        ]),
        trigger('previewTemplate', [
            state('void', style({
                'transform': 'translateY(-100%)'
            })),
            transition('void => *', [animate(200)]),
            transition('* => void', [animate(200)])
        ])
    ]
})
export class OverviewComponent implements OnInit {
    @ViewChild('synSelect') synSelect : MatSelect;
    @ViewChild('clickHoverMenuTrigger') clickHoverMenuTrigger: MatMenuTrigger;
    @ViewChild(MatMenuTrigger) menuTrigger: MatMenuTrigger; 
    private advertisementList: any;
    private advertismentSearchedStrings: any =[];
    private GPTfeedbackData: any = [];
    private settings;
    private lastSelectedParentFilter;
    private sampleArray=[1,2,3,4,5];
    private starsdownloadReport:any
    private filterSelected:any;
    private starFiltersCheck:any;
    private myVal: string;
     private  noFilterSelected=false
    panelOpenState = false;
    private isTopbarShow = false;
    private selectedIndex: any;
    private allGraphLoaded = false;
    private tileData: any = {
        visitors: 0,
        searchUsers: 0,
        searches: 0,
        withResult: 0,
        clicks: 0,
        caseCount: 0,
        withoutResult: 0,
        uniqueSearches: 0
    };
    private tileDataMetrics1Timeout = false; // For checking whether the request (/tileDataMetrics1) has failed.
    private tileDataMetrics1Loading = true; // For checking whether the request (/tileDataMetrics1) is in progress.
    private tileDataMetrics1: any = {
        visitors: 0,
        searchUsers: 0
    };
    private tileDataMetrics2Timeout = false; // For checking whether the request (/tileDataMetrics2) has failed.
    private tileDataMetrics2Loading = true; // For checking whether the request (/tileDataMetrics2) is in progress.
    private tileDataMetrics2: any = {
        searches: 0,
        withResult: 0,
        clicks: 0,
        caseCount: 0,
        withoutResult: 0,
        uniqueSearches: 0
    };
    private splitTileData: any = [];
    private Analyticstab1 = false;
    private Analyticstab2 = false;
    private Analyticstab3 = false;
    private reportSettings: any = [];
    private starFilters: any=[];
    selected = 'allSearches';
    private searchHistogram: any;
    private dynamicDiv: string;
    private activeReport: any = 0;
    private isReportUp: boolean = true;
    private topSearches: any = {};
    public allSearchesCount: any;
    private convertedSearches: any = {};
    private successfulSearches: any = {};
    private withNoClickSearches: any = [];
    private withNoResultSearches: any = [];
    myCheckbox = true;
    isClusteringEnabled: boolean = false;
    private topRatedFeaturedResult: any;
    private pageRatingTitles: any = [];
    private advertisementSortingButton = false;
    private createdCasesData: any;
    private sessionActivityData: any;
    private searchclickPosition: any;
    private sessionclickPosition: any;
    dateRange: any;
    private range_days = 1;
    private daterangepickerOptions: any = {};
    private searchClients: any;
    private platformLabels: any = [];
    private data: any;
    private getResponse: any = [];
    private pieChart: any = [];
    private reloadPie: any = 1;
    private indexSize: any = [];
    private withNoClickSearchesGap: any = [];
    private withNoClickNextSearch: any = [];
    private searchSessions: any = [];
    private searches: any;
    destroy$: Subject<boolean> = new Subject<boolean>();
    subscription: Subscription;
    private externalEnabled = false;
    private userMetricEnabled: boolean;
    private userMetricLabel: string;
    private userMetricVariable: string;
    private userId: number;
    private email: string;
    private emailTrackingAddonInstalled = 0;
    private newScrollDiv: any;
    private styleDiv: any;
    private div: any;
    private internalUser: any;
    private userUniqueId: any;
    private userEmail: any;
    public selectedIndex1Tab: any;
    public selectedIndex3Tab: any;
    public reportsDownloadOption: any = ["Download Reports.", "Send Via Email"];
    public selectedValue: any;
    public emailForReports: any;
    public conversionValue: any;
    public queryFilters: any;
    public searchGroupFilters: any;
    public selectedIndexTabForNoResultSearch: any;
    public selectedIndexTabForAllSearches: any;
    public sessionFilters: any;
    private advertisementFiltersOnInitialLoad;
    public withResult: boolean;
    public readyDiscussion: any;
    public pageTime: any;
    public reportsDownloadValue: any;
    public caseFromReportsDownloadValue: number;
    public selectedConversionType: any;
    public hideflag: any;
    public sessionResult: any;
    public sessionResultTemp: any;
    public sessionCookies: any;
    public addonsStatus: any;
    public sessionTotal: number;
    private communityHelperAddonStatus: boolean = false;
    private allAddons: any;
    private clickPositionHistogram: any;
    private averageTotalSearches: any;
    private averageTotalClicks: any;
    private averageTotalAvg: any;
    private preSelectSC: any = '';
    private AllSearchesColumns: any;
    private allSucessfulSearchesColumns: any;
    private topSearchesWithNoClicksColumns: any;
    private topSearchesWithNoResultsColumns: any;
    private sessionFillterColumns: any = ['Last Search']
    private searchSubReport: any = ['Searches'];
    public ecoSystemList: any[];
    public ecoSystem: any;
    public ecoName: any;
    public isEcosystemSelected: boolean;
    public platform: any;
    public closeButtonToggle: boolean = false;
    
    private showClicksData = false;
    private showCasesData = false;
    private showSearchesData = false;
    private showWithResultData = false;
    private showWithoutResultData = false;
    private showUniqueSearchesData = false;
    showGPTFeedbackFilters = false;

    private searchFilterAllSearchesReport:any = [ { label: 'Query', value: '', filled: false, placeholder: 'Search Query', id:'AllSearches'}]
    private allSucessfulSearches:any = [ { label: 'Query', value: '', filled: false, placeholder: 'Search Query', id:'allSucessfulSearches'}]
    private topSearchesWithNoClicksSearch:any = [ { label: 'Query', value: '', filled: false, placeholder: 'Search Query', id:'topSearchesWithNoClicksSearch'}]
    private topSearchesWithNoresult:any = [ { label: 'Query', value: '', filled: false, placeholder: 'Search Query', id:'topSearchesWithNoClicksSearch'}]
    private sessionFillter:any = [ { label: 'Session Id', value: '', filled: false, placeholder: 'Search Query', id:'sessionFilterSubReportSearch'}, { label: 'Email', value: '', filled: false, placeholder: 'Search email', id:'sessionFilterSubReportSearch'}]
    private searchQueryInClusterFilter:any = [ { label: 'Search Query', value: '', filled: false, placeholder: 'Search Query', id:'searchQueryInClusterFilterSubReportSearch'}]
    private advertisementFilter : any = [{ label: 'Search Text', value: '', filled: false, placeholder: 'Search Query', id:'searchedAdvertisementString'}];
    private searchUnifyGPTFeedback : any = [{ label: 'Search Query', value: '', filled: false, placeholder: 'Search Query', id:'searchUnifyGPTFeedback'}];
    private advertisementSearchedQuery : any;


//
    private sourcePlatformSc: any [] = [];
    private sourcePlatformEco: any = [];
    public pinStatus: boolean;
//
    private childSC: any = [];
    private haveDropdown: any = [];
    
    public formatValue(value: any): string {
        // Ensure the value is a valid number (integer timestamp)
        const timestamp = Number(value);
        if (!isNaN(timestamp) && Number.isInteger(timestamp) && timestamp > 0) {
            const date = new Date(timestamp);
            // Check if the date is valid and within a reasonable range
            if (!isNaN(date.getTime()) && date.getFullYear() > 1970) {
                return this.formatDate(date);
            }
        }
        // Return the value as is if it's not a valid timestamp
        return value;
    }

    public formatDate(date: Date): string {
        // Format date to YYYY-MM-DD HH:mm:ss
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    parseJson(val: string): any {
        try {
            return JSON.parse(val);
        } catch (e) {
            return null;
        }
    }


    showCommunity: boolean;
    private FeedbackquerySelected;
    private Feedbackevent;
    private 
    allBots: any[];
    private topKnowledgeGraphTitles: any;
    OverviewTab: any = {
        "tabName": "OverView",
        "Reports": [
            "TileData",
            "Search Summary",
            {
                "Search_Report": [
                    "All Searches",
                    "Top Successful Searches",
                    "Top Searches With No Clicks",
                    "Top Searches With No Result",
                    "searchReportNested"
                ], "SearchReportSessions": [
                    "All searches-session details",
                    "Successful searches-session details",
                    "Searches with no clicks-session details",
                    "Searches with no result-session details"
                ]
            },
            "Search index by content source",
            "Newly added content sources",
            "Top Rated Featured Results",
            "Top Knowledge Graph Titles",
            "Content Experience Feedback",
            "sessionActivityDetail",
            "Cases Created",
            "Click Position Report",
            "Session Click Position",
            "Average Click Position Report",
            "Search Experience Feedback",
            "Advertisement Performance Report",
            "SearchUnifyGPT Feedback"
        ]
    };
    private caseDeflectionFormulaAndSettings: any;
    public selectedActivityFilterInside = {};
    public firstTime = 0;
    cookie: any;
    allSelected;
    @ViewChild('allSelectedInside') private allSelectedInside: MatOption;
    @ViewChild('search') searchInput: ElementRef;
    @ViewChild('sessionIdClass') sessionId: ElementRef;
    @ViewChild('emailIdClass') emailId: ElementRef;
    @ViewChild('first') panel1: MatExpansionPanel;
    @ViewChild('second') panel2: MatExpansionPanel;
    modeselectInside: string[];
    modeselect: string[];
    public selectedActivityFilterType = {
        "all": "All",
        "pageView": "Viewed Page",
        "search": "Text Searched",
        "conversion": "Clicked Search Result",
        "caseCreated": "Created a Case",
        "supportVisit": "Visited Support",
        "caseDeflection": "Case Deflection"
    }
    public selectedActivityFilter = {
        "all": 1,
        "pageView": 1,
        "search": 1,
        "conversion": 1,
        "caseCreated": 1,
        "supportVisit": 1,
        "caseDeflection": 1
    }
    activityTypes : any[] = [];
    activityTypesInside : any[] = [];
    queryForm: FormGroup;
    selectActionForm: FormGroup;
    submitted;
    boxClose;
    loader = false;
    caseDetails = false;
    caseCreatedDiv = true;
    ascending = true;
    public caseReportFilter = [
        { label: 'SearchUnify Case Id', value: '', filled: false, placeholder: 'SearchUnify Case Id' },
        { label: 'Case title', value: '', filled: false, placeholder: 'Search Title' },
        { label: 'Session Id', value: '', filled: false, placeholder: 'Session Id' },
        { label: 'Email Id', value: '', filled: false, placeholder: 'Email Id' }
    ]
    searchClickPositionReport = true;
    public clickResultReport = [
        { label: 'Search query', value: '', filled: false, placeholder: 'Search query' }
    ]
    public columnsMap = {
        'session': '#Sessions',
        'search': '#Searches',
        'click': '#Clicks',
        'most_click_position': 'Most Clicked Position',
        'average_position': 'Average Position'
    }
    public columnsArray = ['session', 'search', 'click', 'most_click_position', 'average_position'];
    public columnsArraySearchReport = ['Searches'];
    public searchFilterSearchReport = [
        { label: 'Facet Type', value: '', filled: false, placeholder: 'Search by facet type', id: 'sr_search_type' },
        { label: 'Value', value: '', filled: false, placeholder: 'Search by facet value', id: 'sr_search_value' }
    ]
    public sortingField = 'click';
    public sortingFieldCopy = 'click';
    public sortType = 'desc';
    public sortTypeAllSearches = 'desc';
    public sortingFieldAllSearches = 'Searches'
    public sortingFieldSuccessFullSearches = 'Searches'
    public sortingSearchSubReport = 'Searches';
    public sortingFieldTopSearchesWithNoClicksSearchReport = 'Searches';
    public sortingFieldTopSearchesWithNoResult = 'Searches'
    public sortingFieldNoResultSessionReport = 'Last Search'


    public getFiltersForQueriesCopy: any = { search_term: '', searchCount: 0, sortType: 'desc', isClicked: 'all', limit: 50, offset: 1 };

    public sessionClickResultData = [
        { label: 'Session Id', value: '', filled: false, placeholder: 'Session' }
    ]
    public sessionClickMap = {
        'activity_time': 'Activity Time',
        'click_position': 'Click Position'
    }
    public sessionClickArray = ['activity_time', 'click_position'];
    public sessionSortField = 'activity_time';
    public sessionSortFieldCopy = 'activity_time';
    public sessionSortType = 'desc';
    public sessionSort = false;
    public AllSearchesObj = {
        searchQuery: '',
        currentPage: 1,
        sortingField: 'Searches',
        sortType: 'desc',
        limit: 50
    };
    public withNoResultFillters = {
        searchQuery: '',
        currentPage: 1,
        sortingField: 'Searches',
        sortType: 'desc',
        limit: 50,
        total: 0
    };
    public allSuccessfullSearchesObj = {
        searchQuery: '',
        currentPage: 1,
        sortingField: 'Searches',
        sortType: 'desc',
        total: 0,
        limit: 50
    };

    public topSearchesWithNoClicksObj = {
        searchQuery: '',
        currentPage: 1,
        sortingField: 'Searches',
        sortType: 'desc',
        total: 0,
        limit: 50
    };

    public searchFeedbkData: any;
    public textFeedBackToggle: boolean = false;
    public actionListNames: any = [];
    public actionStatusFilter: any = [];
    public actionListObj = {};
    public actionSavedFilterObj = {};
    public emailTrackingEnabled: boolean = false;
    public AllUserEmails: any = [];
    @ViewChild(DaterangePickerComponent)
    // emailFormControl = new FormControl('', [
    //     Validators.required,
    //     Validators.email,
    //     Validators.pattern('^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-.]+)+$')
    // ]);
    // getErrorMessage() {
    //     return this.emailFormControl.hasError('required') ? 'You must enter a value' :
    //         (this.emailFormControl.hasError('email')||this.emailFormControl.hasError('pattern')) ? 'Not a valid email' : '';
    // }
    // @ViewChild(DaterangePickerComponent)
    private picker: DaterangePickerComponent;
    private range: any = {
        "from": '2017-01-01',
        "to": (new Date()).toISOString().split("T")[0]
    };

    userTimeZone: any = 'UTC';
    today = new Date()
    utcToday: any;

    private userMetricsFilters: Map<string, { userMetricValues: string[], updateReport: any }> = new Map([
        ["Tile Data", {
            userMetricValues: [],
            updateReport: () => this.getTilesData(0)
        }],
        ["Search Summary", {
            userMetricValues: [],
            updateReport: () => this.drawSearchHistogram(0)
        }],
        ["Search Classifications (All Searches)", {
            userMetricValues: [],
            updateReport: () => this.getTopSearches()
        }],
        ["Search Classifications (Successful Searches)", {
            userMetricValues: [],
            updateReport: () => this.getTopSuccessfulSearches()
        }],
        ["Search Classifications (Searches With No Clicks)", {
            userMetricValues: [],
            updateReport: () => this.getTopSearchesWithNoClick(0)
        }],
        ["Search Classifications (Searches With No Result)", {
            userMetricValues: [],
            updateReport: () => this.getTopSearchesWithNoResult(0)
        }],
        ["Average Click Position", {
            userMetricValues: [],
            updateReport: () => this.drawclickPositionHistogram(0)
        }],
        ["Click Position Report", {
            userMetricValues: [],
            updateReport: () => this.searchClickPosition({
                searchQuery: this.clickResultReport[0].value,
                currentPage: 1,
                sortingField: this.sortingField,
                sortType: this.sortType
            })
        }],
        ["Cases Created", {
            userMetricValues: [],
            updateReport: () => this.caseReport({
                caseUid: this.caseReportFilter[0].value,
                caseSubject: this.caseReportFilter[1].value,
                cookie: this.caseReportFilter[2].value,
                emailId: this.caseReportFilter[3].value,
                currentPage: 1
            })
        }],
        ["Top Rated Featured Results", {
            userMetricValues: [],
            updateReport: () => this.getTopUsefulFeaturedSnippet(0)
        }],
        ["Content Experience Feedback", {
            userMetricValues: [],
            updateReport: () => this.getPageRatingFeedback(this.FeedbackquerySelected, this.Feedbackevent)
        }]
        // ["Search Experience Feedback", {
        //     userMetricValues: [],
        //     updateReport: () => this.getSearchFeedback(1)
        // }],
        // ["Advertisement Performance Report", {
        //     userMetricValues: [],
        //     updateReport: () => this.getAdvertisements()
        // }]
    ]);

    constructor(private cookieService: CookieService, private analyticsV2Service: AnalyticV2Service,private addonsService: addonsService, private analyticService: AnalyticService, private route: ActivatedRoute, private router: Router, private toastyService: ToastyService, private sidebarService: SidebarService, private fb: FormBuilder, private adminAnalyticsService: AdminAnalyticsService, private userManagementService: UserManagementService, private TimezoneService: TimezoneService, private PageRatingService: PageRatingService, private EcosystemService:EcosystemService, private GenerateSearchClientComponent: GenerateSearchClientComponent) {
        TimezoneService.getUserTimeZone().subscribe((data)=>{
            this.today = new Date()
            this.utcToday = new Date(this.today.getUTCFullYear(), this.today.getUTCMonth(), this.today.getUTCDate(), this.today.getUTCHours(), this.today.getUTCMinutes(), this.today.getUTCSeconds(), this.today.getUTCMilliseconds());
            this.userTimeZone = data
            this.utcToday = momentZone.utc(this.today).tz(this.userTimeZone)
            this.daterangepickerOptions = {
                locale: { format: "YYYY-MM-DD" },
                alwaysShowCalendars: false,
                autoApply: true,
                startDate: moment(localStorage.startDate),
                endDate: moment(localStorage.endDate),
                minDate: moment(this.utcToday).subtract(12, 'month').startOf('month'),
                maxDate: moment(this.utcToday),
                ranges: {
                    'Today': [moment(this.utcToday), moment(this.utcToday)],
                    'Yesterday': [moment(this.utcToday).subtract(1, 'days'), moment(this.utcToday).subtract(1, 'days')],
                    'Last 7 Days': [moment(this.utcToday).subtract(6, 'days'), moment(this.utcToday)],
                    'Last 30 Days': [moment(this.utcToday).subtract(29, 'days'), moment(this.utcToday)],
                    'This Month': [moment(this.utcToday).startOf('month'), moment(this.utcToday).endOf('month')],
                    'Last Month': [moment(this.utcToday).subtract(1, 'month').startOf('month'), moment(this.utcToday).subtract(1, 'month').endOf('month')]
                },
                get from() { return this.startDate.format("YYYY-MM-DD"); },
                get to() { return this.endDate.format("YYYY-MM-DD"); },
                set from(value) { this.startDate = moment(value); },
                set to(value) { this.endDate = moment(value); }
            };
            this.range = this.daterangepickerOptions;
	    
        });
        this.selectedIndex = 0;
        this.searchClients = [];
        this.userMetricEnabled = false;
        this.userMetricLabel = '';
        this.userMetricVariable = '';

        /* For Interactive Search */
        this.subscription = this.sidebarService.getMessage().subscribe(message => {
            if (message.div) {
                if (message.dateRange) {
                    this.dateRangeForInteractiveSearch(message);
                }
                else {
                    this.getRouterNavigationForInteractiveSearch(message.div);
                }
            }
        });

        addonsService.getcommunityHelperAddonStatus().subscribe((status) => {
            this.communityHelperAddonStatus = status;
        });

    }


    ngOnInit() {
        this.setAllSearchesColumns();
        this.updateSearchFilterLabel();
        this.setAllSuccessfulSearchesColumns();
        this.setTopSearchesWithNoClicksColumns();
        this.setTopSearchesWithNoResultsColumns();
        this.advertismentSearchedStrings = [];
        this.searchclickPosition = [];
        this.createdCasesData = [];
        this.readRatingFromLocalStorage();
        this.selectActionForm = this.fb.group({ 
            actionType: new FormControl('')
        });

        window.scrollTo({top: 0, behavior: 'smooth'});
        try {
            this.platform = {};
            this.allAddons = JSON.parse(localStorage.allAddons);
            this.communityHelperAddonStatus = this.allAddons[13] ? true : false;
        } catch (error) {
            console.log(error);
        }
        this.showCommunity = true
        this.addonsStatus = [];
        this.allBots = [];
        this.topKnowledgeGraphTitles = [];
        this.queryFilters = {
            state: "hidden",
            selectedSearchTerm: "",
            filters: []
        };
        this.searchGroupFilters = {
          state: "hidden",
          selectedSearchTerm: "",
          searchCount: 0,
          filters: [],
          searchquery: this.searchQueryInClusterFilter[0].value,
          isClicked: 'all',
          limit: 50,
          offset: 1,
          total: 0
        }
        this.sessionFilters = {
            state: "hidden",
            selectedSearchTerm: "",
            filters: [],
            sessionid: this.sessionFillter[0].value,
            email: this.sessionFillter[1].value,
            sortType: "desc",
            sortingField: 'Last Search',
            filterByReport: 'allSearches',
            limit: 50,
            offset: 1,
            total: 0
        };
        this.advertisementFiltersOnInitialLoad = {
            sortType: "desc",
            pageNo : 1,
        },
        this.selectedActivityFilter = {
            "all": 1,
            "pageView": 1,
            "search": 1,
            "conversion": 1,
            "caseCreated": 1,
            "supportVisit": 1,
            "caseDeflection": 1
        }
        if (localStorage.internalUser) {
            this.internalUser = localStorage.internalUser;
        } else {
            this.internalUser = "all";
        }
        if (localStorage.clientName !== "All") {
            this.searchClients.name = localStorage.clientName;
        }
        if (!localStorage.startDate) {
            localStorage.startDate = moment(this.utcToday).subtract(6, 'days').format("YYYY-MM-DD");
        }
        if (!localStorage.endDate) {
            localStorage.endDate = moment(this.utcToday).add(1, 'day').format("YYYY-MM-DD");
        }
        if (!localStorage.clientName) localStorage.clientName = "All";
        if (!localStorage.internalUser) localStorage.internalUser = "all";

        this.range_days = Math.ceil((this.range.endDate - this.range.startDate) / 1000 / 60 / 60 / 24);
        if (this.range_days == 0) {
            this.range_days = 1;
        }
        this.getActionNameSavedFilters();
        let paramters = { type: 'get' }
        this.userManagementService.userSpecificSettings(paramters).then((res) => {
            try {
                this.preSelectSC = JSON.parse(res.data.preselectedPinSCAnalytics);
                this.userId = res.data.userId;
                this.email = res.data.email;
            } catch (error) {
                this.preSelectSC = '';
            }
            this.getAllPlatforms();
        });
        this.searchClients.name = 'All';
        this.getSessionDetails();
        if (this.route.snapshot.params['div']) {
            this.getRouterNavigationForInteractiveSearch(this.route.snapshot.params['div']);
        }
        this.activityTypes = Object.keys(this.selectedActivityFilter);
        this.activityTypes.splice(0, 1);
        this.modeselect = Object.keys(this.selectedActivityFilter);
        this.scrollDownEvents();
        this.selectedIndexTabForAllSearches = 0
        this.ecoSystemList = [];
        this.ecoSystem = {};
        this.ecoName = '';
        this.isEcosystemSelected= false;
        this.pinStatus = false;
        if (this.FeedbackquerySelected) {
            this.getPageRatingFeedback(
                this.FeedbackquerySelected,
                this.Feedbackevent
            );
        } else {
            this.getPageRatingFeedback("all", "most_recent");
        }
        this.getAdvertisements();
        this.getUsers();    
        
        this.reactionFilterType = localStorage.getItem('reactionFilterType') ?  localStorage.getItem('reactionFilterType') : "all";
    }

    //destroying subscriber
    ngOnDestroy() {
        this.subscription.unsubscribe();
        this.destroy$.next(true);
        this.destroy$.unsubscribe();
        this.analyticsV2Service.getAnalyticsStatus(true);
    }

    getUsers() {
        this.analyticsV2Service.getAllUsers().then((response) => {
            this.AllUserEmails = response.filter(({ activation_date }) => activation_date != null);
        })
    }

    assignAllToPlatform(){
        this.searchClients.name = "All"
        this.searchClients.uid = "";
        this.platform = this.sourcePlatformSc.find(x => x.uid === "");
        this.internalUser = "all";
        this.searchClients.ecoId = null;
        this.ecoSystem = {};
        this.isEcosystemSelected = false;
    }

    lastplatform(){
        try {
            if(sessionStorage.getItem('lastUsedPlatform')){
                this.lastUsedPlatformSessionStorage()
    
            } else if (this.preSelectSC && this.preSelectSC.preselectedPinSCAnalytics !== null && this.preSelectSC.preselectedPinSCAnalytics !== '') {
                this.getPinnedPlatform();
    
            } else if (localStorage.lastUsedPlatform) {
                this.lastUsedPlatformLocalStorage();
                
            } else {
                this.assignAllToPlatform()
            }
        } catch (error) {
            this.assignAllToPlatform();
        }

        sessionStorage.setItem("lastUsedPlatform", JSON.stringify(this.platform));

        if (typeof (Storage) !== "undefined") {
            localStorage.lastUsedPlatform = JSON.stringify(this.platform);
        }
        
        this.update();
    }

    lastUsedPlatformSessionStorage(){
        try {
            let lastUsedPlatform = JSON.parse(sessionStorage.getItem('lastUsedPlatform'));
            this.assignPlatform(lastUsedPlatform);
        } catch (err) {
            this.assignAllToPlatform();
        }
    }

    lastUsedPlatformLocalStorage(){
        try {
            var lastUsedPlatform = JSON.parse(localStorage.lastUsedPlatform);
            this.assignPlatform(lastUsedPlatform);
        } catch (err) {
            this.assignAllToPlatform();
        }
    }

    assignPlatform(lastUsedPlatform){
        try {
            if (lastUsedPlatform.type === "sc"){
                if (lastUsedPlatform.uid === "" || lastUsedPlatform.uid === null) {
                    this.assignAllToPlatform()
                } else {
                    this.platform = this.sourcePlatformSc.find(x => x.uid === lastUsedPlatform.uid);
                    if (this.platform) {
                        let client = this.searchClients.find(x => x.uid === lastUsedPlatform.uid);
                        this.searchClients.name = client.name
                        this.searchClients.uid = client.uid;
                        this.externalEnabled = client.external_user_enabled;
                        this.userMetricEnabled = client.user_attribute_tracking_enabled;
                        this.userMetricLabel = client.user_attribute_label;
                        this.userMetricVariable = client.user_attribute_variable;
                        if (!this.externalEnabled)
                            this.internalUser = "all";
                    } else {
                        this.assignAllToPlatform();
                    }
                    
                }
                this.isEcosystemSelected = false;
                this.searchClients.ecoId = null;
                this.ecoSystem = {};
            } else {
                this.platform = this.sourcePlatformEco.find(x => x.uid === lastUsedPlatform.uid);
                if (this.platform) {
                    this.ecoSystem = this.ecoSystemList.find(x => x.uid === lastUsedPlatform.uid);
                    this.searchClients.ecoId = this.ecoSystem.uid;
                    this.externalEnabled = this.ecoSystem.external_user_enabled;
                    this.userMetricEnabled = false;
                    if (!this.externalEnabled)
                        this.internalUser = "all";
    
                    this.isEcosystemSelected = true;
                    this.searchClients.uid = null;
                    this.searchClients.name = "";
                } else {
                    this.assignAllToPlatform();
                }
            }
        } catch (err) {
            this.assignAllToPlatform();
        }

        this.checkPinStatus();
    }

    getPinnedPlatform(){
        try {
            if(this.preSelectSC.scTypePin === "eco"){
                this.platform = this.sourcePlatformEco.find(
                    (x) => x.uid === this.preSelectSC.preselectedPinSCAnalytics
                );
                this.ecoSystem = this.ecoSystemList.find(
                    (x) => x.uid === this.preSelectSC.preselectedPinSCAnalytics
                );
                if(this.platform && this.ecoSystem){
                    this.searchClients.uid = null;
                    this.searchClients.ecoId = this.ecoSystem.uid;
                    this.externalEnabled = this.ecoSystem.external_user_enabled;
                    this.userMetricEnabled = false;
                    if (!this.externalEnabled) this.internalUser = "all";
    
                    this.searchClients.uid = null;
                    this.searchClients.name = "";
                    this.isEcosystemSelected = true;
                }
                else{
                    this.lastUsedPlatformLocalStorage();
                }
            } else if(this.preSelectSC.scTypePin === "uid"){
    
                if (this.preSelectSC.preselectedPinSCAnalytics === "All") {
                    this.assignAllToPlatform();
                } else {
                    this.platform = this.sourcePlatformSc.find(
                        (x) => x.uid === this.preSelectSC.preselectedPinSCAnalytics
                    );
                    let client = null;
                    if (this.searchClients.name === "All") { //dount
                        this.searchClients.uid = "";
                    } else {
                        client = this.searchClients.find(
                            (x) => x.uid === this.preSelectSC.preselectedPinSCAnalytics
                        );
                    }
                    if(this.platform && client && client.uid && this.searchClients.name  !== 'All'){
                        this.searchClients.uid = client.uid;
                        this.searchClients.name = client.name;
                        localStorage.clientName = this.searchClients.name;
                        this.externalEnabled = client.external_user_enabled;
                        this.userMetricEnabled = client.user_attribute_tracking_enabled;
                        this.userMetricLabel = client.user_attribute_label;
                        this.userMetricVariable = client.user_attribute_variable;
                        if (!this.externalEnabled) this.internalUser = "all";
    
                        this.searchClients.ecoId = null;
                        this.isEcosystemSelected = false;
                        this.ecoSystem = {};
                    }
                    else{
                        this.lastUsedPlatformLocalStorage()
                    }  
                }
            }
        } catch (error) {
            this.assignAllToPlatform();
        }

        this.checkPinStatus();
    }

    checkPinStatus(){
        try {
            if ((this.preSelectSC.preselectedPinSCAnalytics === this.platform.uid && this.preSelectSC.preselectedPinSCAnalytics !== 'All') || (this.preSelectSC.preselectedPinSCAnalytics === 'All' && this.platform.name ==='All')) {
                this.pinStatus = true;
            } else {
                this.pinStatus = false;
            }
        } catch (error) {
            this.pinStatus = false;
        }
    }

    private setAllSearchesColumns(): void {
      if (this.isClusteringEnabled) {
          this.AllSearchesColumns = ['Queries', 'Users', 'Sessions', 'Searches'];
          this.sortingFieldAllSearches = 'Queries'; 
          this.AllSearchesObj.sortingField = 'Queries';
      } else {
          this.AllSearchesColumns = ['Users', 'Sessions', 'Searches'];
          this.sortingFieldAllSearches = 'Searches';
          this.AllSearchesObj.sortingField = 'Searches';
      }
  }
  
    private setAllSuccessfulSearchesColumns(): void {
        if (this.isClusteringEnabled) {
            this.allSucessfulSearchesColumns = ['Queries', 'Users', 'Sessions', 'Searches']; 
            this.sortingFieldSuccessFullSearches = 'Queries';
            this.allSuccessfullSearchesObj.sortingField = 'Queries';
        } else {
            this.allSucessfulSearchesColumns = ['Users', 'Sessions', 'Searches'];
            this.sortingFieldSuccessFullSearches = 'Searches';
            this.allSuccessfullSearchesObj.sortingField = 'Searches';     
        }
    }
    
    private setTopSearchesWithNoClicksColumns(): void {
        if (this.isClusteringEnabled) {
            this.topSearchesWithNoClicksColumns = ['Queries', 'Users', 'Sessions', 'Searches'];
            this.sortingFieldTopSearchesWithNoClicksSearchReport = 'Queries';
            this.topSearchesWithNoClicksObj.sortingField = 'Queries';
        } else {
            this.topSearchesWithNoClicksColumns = ['Users', 'Sessions', 'Searches'];
            this.sortingFieldTopSearchesWithNoClicksSearchReport = 'Searches';
            this.topSearchesWithNoClicksObj.sortingField = 'Searches';
        }
    }
    
    private setTopSearchesWithNoResultsColumns(): void {
        if (this.isClusteringEnabled) {
            this.topSearchesWithNoResultsColumns = ['Queries', 'Users', 'Sessions', 'Searches'];
            this.sortingFieldTopSearchesWithNoResult = 'Queries';
            this.withNoResultFillters.sortingField = 'Queries';
        } else {
            this.topSearchesWithNoResultsColumns = ['Users', 'Sessions', 'Searches'];
            this.sortingFieldTopSearchesWithNoResult = 'Searches';
            this.withNoResultFillters.sortingField = 'Searches';
        }
    }
    
    private updateSearchFilterLabel(): void {
        const updatedLabel = this.isClusteringEnabled ? 'Query Group' : 'Query';
        const updatedPlaceholder = this.isClusteringEnabled ? 'Search Query Group' : 'Search Query';
    
        this.allSucessfulSearches[0].label = updatedLabel;
        this.allSucessfulSearches[0].placeholder = updatedPlaceholder;
        this.searchFilterAllSearchesReport[0].label = updatedLabel;
        this.searchFilterAllSearchesReport[0].placeholder = updatedPlaceholder;
        this.topSearchesWithNoresult[0].label = updatedLabel;
        this.topSearchesWithNoresult[0].placeholder = updatedPlaceholder;
        this.topSearchesWithNoClicksSearch[0].label = updatedLabel;
        this.topSearchesWithNoClicksSearch[0].placeholder = updatedPlaceholder;
    }
    
    onToggleChange(): void {
        this.isClusteringEnabled = !this.isClusteringEnabled;
        this.searchSubReport = this.searchSubReport[0] === 'Searches'
            ? ['No. of Searches']
            : ['Searches'];
        this.closeButtonToggle = true;
        this.searchFilterAllSearchesReport[0].value = '';
        this.searchFilterAllSearchesReport[0].filled = false;
        this.allSucessfulSearches[0].value = '';
        this.allSucessfulSearches[0].filled = false;
        this.topSearchesWithNoClicksSearch[0].value = '';
        this.topSearchesWithNoClicksSearch[0].filled = false;
        this.topSearchesWithNoresult[0].value = '';
        this.topSearchesWithNoresult[0].filled = false;
        this.sessionFillter[0].value = '';
        this.sessionFillter[0].filled = false;
        this.sessionFillter[1].value = '';
        this.sessionFillter[1].filled = false;
        this.searchQueryInClusterFilter[0].value = '';
        this.searchQueryInClusterFilter[0].filled = false;
        this.setAllSearchesColumns();
        this.updateSearchFilterLabel();
        this.setAllSuccessfulSearchesColumns();
        this.setTopSearchesWithNoClicksColumns();
        this.setTopSearchesWithNoResultsColumns();
        if (this.activeReport === 0) {
          this.AllSearchesObj.searchQuery = '';
          this.getTopSearches();
        } else if (this.activeReport === 2) {
          this.allSuccessfullSearchesObj.searchQuery = '';
          this.getTopSuccessfulSearches();
        } else if (this.activeReport === 3) {
            this.topSearchesWithNoClicksObj.searchQuery = '';
          this.getTopSearchesWithNoClick(0);
        } else if (this.activeReport === 4) {
          this.withNoResultFillters.searchQuery = '';
          this.getTopSearchesWithNoResult(0);
        }
    }

    closeButtonToggleChange(): void {
        this.closeButtonToggle = false;
    }

    // To toggle the showClicksData variable to show the split wise tile data on hover
    showClicksStats() {
        this.showClicksData = !this.showClicksData;
    }

    showCasesStats() {
        this.showCasesData = !this.showCasesData;
    }

    showSearchesStats() {
        this.showSearchesData = !this.showSearchesData;
    }

    showWithResultStats() {
        this.showWithResultData = !this.showWithResultData;
    }

    showWithoutResultStats() {
        this.showWithoutResultData = !this.showWithoutResultData;
    }

    showUniqueSearchesStats() {
        this.showUniqueSearchesData = !this.showUniqueSearchesData;
    }

    getActionNameSavedFilters() {
        if (localStorage.actionFilterApplied != null) {
            this.actionSavedFilterObj = JSON.parse(localStorage.actionFilterApplied);
        }
        this.analyticsV2Service.getActionNameWithIcon().then(result => {
            if (result.data) {
                this.actionListObj = result.data;
                this.actionListNames = Object.keys(this.actionListObj);
            }
        });
    }

    getEmailTrackingFlag() {
        this.emailTrackingEnabled = false;
        var client = this.searchClients.find(o => o.name === localStorage.clientName);
        if (client) {
            this.analyticsV2Service.getSearchClientDeflectionFormula(client.id).then(result => {
                if (result && result.data && result.data.length) {
                    this.emailTrackingEnabled = result.data[0].email_tracking_enabled;
                }
            });
            this.getTilesData(0);
        }
        else if(this.isEcosystemSelected && this.ecoSystem && this.ecoSystem.email_tracking_enabled){
            this.emailTrackingEnabled = true;
            this.getTilesData(0);
        } else {
            this.getTilesData(0);
        }
    }

    sessionIdDiv() {
        this.loader = true;
        this.caseCreatedDiv = false;
        this.caseDetails = true;
    }

    filtersSearch(label, value) {
        if (value) {

            for (let p = 0; p < this.caseReportFilter.length; p++) {
                if (this.caseReportFilter[p].label == label) {
                    this.caseReportFilter[p].filled = value
                }
            }
        }

    }


    filtersSearchSearchReport(label, value) {
        if (value) {


            for (let p = 0; p < this.searchFilterSearchReport.length; p++) {
                if (this.searchFilterSearchReport[p].label == label) {
                    this.searchFilterSearchReport[p].filled = value
                }
            }
        }

    }

    // This function is invoked when we have clicked on session Id in Cases Created Report and changed the date range or client
    backToCase() {
        this.loader = false;
        this.caseCreatedDiv = true;
        this.caseDetails = false;
        for(let p =0;p <this.caseReportFilter.length;p++){
            this.caseReportFilter[p].value = ''
            this.caseReportFilter[p].filled = false
        }
    }

    // This function is invoked when Back button is clicked in Cases Created Report
    backToCaseReports() {
        this.backToCase();
        this.caseReport({ currentPage: 1 });
    }

    savedActionFilter() {   // before switching searches with no results report
        this.actionStatusFilter = [];
        if (localStorage.actionFilterApplied == null) { // action filters reset
            this.actionListNames.forEach((allActions) => {
                this.actionStatusFilter.push(allActions);
            });
            this.selectActionForm.controls.actionType.patchValue([...this.actionListNames.map(action => action), 'all']);
        } else {  // action filters do not reset
            let actionFiltersTemp = JSON.parse(localStorage.actionFilterApplied);
            if (this.actionSavedFilterObj.hasOwnProperty(this.searchClients.uid)) {
                if (actionFiltersTemp[this.searchClients.uid].length == 0) {
                    this.selectActionForm.controls.actionType.patchValue([]);
                } else if (actionFiltersTemp[this.searchClients.uid].length == this.actionListNames.length) {
                    actionFiltersTemp[this.searchClients.uid].forEach((allActions) => {
                        this.actionStatusFilter.push(allActions);
                    });
                    this.selectActionForm.controls.actionType.patchValue([...this.actionListNames.map(action => action), 'all']);
                } else {
                    actionFiltersTemp[this.searchClients.uid].forEach(filteredAction => {
                        this.actionStatusFilter.push(filteredAction);
                    });
                    this.selectActionForm.controls.actionType.patchValue([...this.actionStatusFilter.map(action => action)])
                }
            } else {
                this.actionListNames.forEach((allActions) => {
                    this.actionStatusFilter.push(allActions);
                });
                this.selectActionForm.controls.actionType.patchValue([...this.actionListNames.map(action => action), 'all']);
            }
        }
    }

    selectActionFilter(selectedActionFilter) {
        if (this.actionStatusFilter.includes(selectedActionFilter)) {
            this.actionStatusFilter.splice(this.actionStatusFilter.indexOf(selectedActionFilter), 1);
        }
        else {
            this.actionStatusFilter.push(selectedActionFilter);
        }
        this.actionSavedFilterObj[this.searchClients.uid] = this.actionStatusFilter;
        localStorage.actionFilterApplied = JSON.stringify(this.actionSavedFilterObj);
        this.getTopSearchesWithNoResult(0);

        if (this.allSelectedInside.selected) {
            this.allSelectedInside.deselect();
            return false;
        }

        if (this.selectActionForm.controls.actionType.value.length == this.actionListNames.length)
            this.allSelectedInside.select();
    }

    selectAllActionFilters() {
        this.actionStatusFilter = [];
        if (this.allSelectedInside.selected) { // condition true if all is checked
            this.actionListNames.forEach((action) => {
                this.actionStatusFilter.push(action);
            });
            this.selectActionForm.controls.actionType.patchValue([...this.actionListNames.map(action => action), 'all']);
        } else {
            this.selectActionForm.controls.actionType.patchValue([]);  // if all is unchecked, then patchValue updates to empty array so that all action values are unchecked
        }
        this.actionSavedFilterObj[this.searchClients.uid] = this.actionStatusFilter;
        localStorage.actionFilterApplied = JSON.stringify(this.actionSavedFilterObj);
        this.getTopSearchesWithNoResult(0);
    }

    toggleActivityInside(activity) {
        // this.selectedActivityFilterInside[activity] = !this.selectedActivityFilterInside[activity];
        if (activity == 'all') {
            if (this.selectedActivityFilterInside['all'] == 0) {

                Object.keys(this.selectedActivityFilterInside).map(k => {
                    if (this.modeselect.includes(k)) {
                        this.selectedActivityFilterInside[k] = 1;
                    }

                })
                this.selectedActivityFilterInside['all'] = 1
                this.modeselectInside = Object.keys(this.selectedActivityFilterInside);
            } else {
                Object.keys(this.selectedActivityFilterInside).map(k => {
                    if (this.modeselect.includes(k)) {
                        this.selectedActivityFilterInside[k] = 0;
                    }
                })
                this.selectedActivityFilterInside['all'] = 0
                this.modeselectInside = [];
            }
        } else {
            this.selectedActivityFilterInside['all'] = 0;
            if (this.selectedActivityFilterInside[activity] && this.allSelectedInside.selected) {

                this.allSelectedInside.deselect();
                this.selectedActivityFilterInside['all'] = 0;

            } else if (!this.selectedActivityFilterInside[activity] && this.modeselectInside.length == this.modeselect.length) {
                this.selectedActivityFilterInside['all'] = 1;
                this.allSelectedInside.select();
            }
            this.selectedActivityFilterInside[activity] = !this.selectedActivityFilterInside[activity];
        }
    }

    searchquery() {
        this.submitted = true;
        const queryvalue = this.queryForm.value.query;
    }
    onKey() { }
    blurFunc() { this.boxClose = false; }
    closeSearchBar() {
        this.boxClose = false;
    }

    // For interactive search

    //For interactive search
    dateRangeForInteractiveSearch(message) {
        if (message.dateRange == "Yesterday") {
            var obj = { "interactive_search": true, "startDate": moment().subtract(1, 'days').format("YYYY-MM-DD"), "endDate": moment().subtract(1, 'days').format("YYYY-MM-DD") }
        }
        if (message.dateRange == "Today") {
            var obj = { "interactive_search": true, "startDate": moment().format("YYYY-MM-DD"), "endDate": moment().format("YYYY-MM-DD") }
        }
        if (message.dateRange == "Last 7 Days") {
            var obj = { "interactive_search": true, "startDate": moment().subtract(6, 'days').format("YYYY-MM-DD"), "endDate": moment().format("YYYY-MM-DD") }
        }
        if (message.dateRange == "Last 30 Days") {
            var obj = { "interactive_search": true, "startDate": moment().subtract(29, 'days').format("YYYY-MM-DD"), "endDate": moment().format("YYYY-MM-DD") }
        }
        this.selectedDate(obj, {})
        this.getRouterNavigationForInteractiveSearch(message.div);
    }

    getSessionDetails() {
        this.cookieService.getSearchUnifySession().then(result => {
            this.userUniqueId = result.userId;
            this.userEmail = result.email;
        })
    }

    removeDivHighlight(newScrollDiv) {
        if (newScrollDiv) {
            var arr = ["topRatedFeaturedResults", "searchesWithNoResult", "successfulSearchesWithClicks", "allSearches", "searchesWithNoClicks", "newlyAddedContentSource", "indexBySource", "searchHitCount", "knowledgeGraphTitles"];
            for (var i = 0; i < arr.length; i++) {
                this.styleDiv = document.getElementById(arr[i]);
                if (this.styleDiv) {
                    this.styleDiv.setAttribute('style', '#ffffff -12px 1px 10px 2px;');
                }
            }
        }
    }

    //For interactive search
    getRouterNavigationForInteractiveSearch(newScrollDiv) {
        if (newScrollDiv.includes('/dashboard/analytics-v2/overview/') || newScrollDiv.indexOf('/') == -1) {


            this.newScrollDiv = newScrollDiv.split('/dashboard/analytics-v2/overview/').pop()
            if (this.newScrollDiv) {
                this.dynamicDiv = '';
                // var arr = ["topRatedFeaturedResults", "searchesWithNoResult","successfulSearchesWithClicks","allSearches","searchesWithNoClicks","newlyAddedContentSource", "indexBySource", "searchHitCount", "knowledgeGraphTitles"];
                // for (var i = 0; i < arr.length; i++) {
                //     this.styleDiv = document.getElementById(arr[i]);
                //     if (this.styleDiv) {
                //        this.styleDiv.setAttribute('style', 'box-shadow: #64abfb 0px 0px 10px 1px;');
                //     }
                // }

                if (this.newScrollDiv === 'searchesWithNoResult') { this.selectedIndex = 0; this.dynamicDiv = 'searchesWithNoResult' }
                if (this.newScrollDiv === 'successfulSearchesWithClicks') { this.selectedIndex = 0; this.dynamicDiv = 'successfulSearchesWithClicks' }
                if (this.newScrollDiv === 'allSearches') { this.selectedIndex = 0; this.dynamicDiv = 'allSearches' }
                if (this.newScrollDiv === 'searchesWithNoClicks') { this.selectedIndex = 0; this.dynamicDiv = 'searchesWithNoClicks' }
                if (this.newScrollDiv === 'newlyAddedContentSource') { this.selectedIndex = 0; }
                if (this.newScrollDiv === 'indexBySource') { this.selectedIndex = 0; }
                if (this.newScrollDiv === 'searchHitCount') { this.selectedIndex = 0; }
                if (this.newScrollDiv === 'topRatedFeaturedResults') { this.selectedIndex = 0; }
                if (this.newScrollDiv === 'knowledgeGraphTitles') { this.selectedIndex = 0; }

                let holdInterval = setInterval(() => {
                    this.div = document.getElementById(this.newScrollDiv);
                    if (this.div) {
                        if (this.div.id === "allSearches") this.switchSearchReport(0);
                        if (this.div.id === "successfulSearchesWithClicks") this.switchSearchReport(2);
                        if (this.div.id === "searchesWithNoClicks") this.switchSearchReport(3);
                        if (this.div.id === "searchesWithNoResult") this.switchSearchReport(4);
                        this.div.setAttribute('style', 'box-shadow: #64abfb 0px 0px 10px 1px;');
                        var headerOffset = 80;
                        var elementPosition = this.div.getBoundingClientRect().top;
                        var offsetPosition = elementPosition - headerOffset;
                        window.scrollTo({ top: offsetPosition, behavior: "smooth" });
                        clearInterval(holdInterval);
                    } else {
                        clearInterval(holdInterval);
                    }
                }, 2000);

            }

        } else {
            this.router.navigate([newScrollDiv])
        }
    }

    openPanel() {
        if (this.sourcePlatformEco.length > 0 ) {
            if (this.isEcosystemSelected) {
                this.panel2.open();
                this.panel1.close();
            } else {
                this.panel1.open();
                this.panel2.close();
            }
        } else {
            this.panel1.open();
        }
    }

    changeEcoPanel() {
        document.querySelector(".ecoPanel").classList.remove("ecoPanelExpand");
    }

    changeScPanel() {
        document.querySelector(".scPanel").classList.remove("scPanelExpand");
    }

    changeClient(selectedplatform) {
        this.searchQuery = '';
        this.searchUnifyGPTFeedback[0].filled = false;
        if(selectedplatform.ab_test_parent != null) {
            selectedplatform = {
                name: selectedplatform.searchTerm,
                type: "sc",
                uid : selectedplatform.uid
            }
        }
        this.platform = selectedplatform;
        this.advertismentSearchedStrings.isEmpty = false;
        this.internalUser = localStorage.internalUser;
        if(selectedplatform.name == 'All'){
            this.internalUser = "all";
        }
        if (selectedplatform.type==='eco') {
            this.ecoName = selectedplatform.name;
            this.isEcosystemSelected= true;
            this.range.csv = 0;
            if (typeof (Storage) !== "undefined") {
                localStorage.clientName = null;
                localStorage.lastUsedPlatform = JSON.stringify(selectedplatform);
            }
            this.ecoSystem = this.ecoSystemList.find(x => x.uid === selectedplatform.uid);
            
            this.searchClients.ecoId = this.ecoSystem.uid;
            this.searchClients.uid = null;
            this.searchClients.name = "";
            this.externalEnabled = this.ecoSystem.external_user_enabled;
            this.userMetricEnabled = false;
            if (!this.externalEnabled)
                this.internalUser = "all";
            sessionStorage.setItem("lastUsedPlatform", JSON.stringify(selectedplatform));
        } else {
            if (typeof(selectedplatform)==='string') {
                selectedplatform = JSON.parse(selectedplatform);
            }
            var clientName = selectedplatform.name;            
            this.allGraphLoaded = false;
            this.isEcosystemSelected= false;
            if (clientName != null) {
                this.range.csv = 0;
                if (typeof (Storage) !== "undefined") {
                    localStorage.clientName = clientName;
                    localStorage.lastUsedPlatform = JSON.stringify(selectedplatform);
                }
                if (clientName === "All") {
                    this.searchClients.uid = "";
                    this.searchClients.name = "All";
                    this.externalEnabled = false;
                    this.userMetricEnabled = false;
                } else {
                    let client = this.searchClients.find(x => x.uid === selectedplatform.uid);
                    this.searchClients.uid = client.uid;
                    this.searchClients.name = client.name
                    this.externalEnabled = client.external_user_enabled;
                    this.userMetricEnabled = client.user_attribute_tracking_enabled;
                    this.userMetricLabel = client.user_attribute_label;
                    this.userMetricVariable = client.user_attribute_variable;
                    if (!this.externalEnabled)
                        this.internalUser = "all";
                }
                this.searchClients.ecoId = null;
            }
            sessionStorage.setItem("lastUsedPlatform", JSON.stringify(selectedplatform));      
            this.ecoSystem = {};
        }

        this.clickResultReport = [
            { label: 'Search query', value: '', filled: false, placeholder: 'Search query' }
        ]
        this.resetUserMetricValues();
        // Emptying the priority queue
        this.analyticsV2Service.emptyQueue();
        this.backToCase();
        this.update();
        if (this.newScrollDiv) {
            this.removeDivHighlight(this.newScrollDiv);
        }
        window.scrollTo({top: 0, behavior: 'smooth'});
        
        this.checkPinStatus();        
    }

    selectedDate(value: any, datepicker?: any) {
        this.advertismentSearchedStrings.isEmpty = false;
        this.range_days = Math.ceil((value.end - value.start) / 1000 / 60 / 60 / 24);
        if (this.range_days > Variables.dateRangeLimitDays) {
            var toastOptions: ToastOptions = {
                title: "Date Range Exceeded!",
                msg: `You cannot select more than ${Variables.dateRangeLimitDays} days.`,
                showClose: true,
                timeout: 5000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
            this.picker.datePicker.setStartDate(moment(localStorage.startDate).format('YYYY-MM-DD'));
            this.picker.datePicker.setEndDate(moment(localStorage.endDate).format('YYYY-MM-DD'));
        } else {
            this.allGraphLoaded = false;
            if (value.interactive_search) {
                this.range.from = value.startDate;
                this.range.to = value.endDate;
            }
            else {
                this.range.from = value.start.format("YYYY-MM-DD");
                this.range.to = value.end.format("YYYY-MM-DD");
            }
            if (typeof (Storage) !== "undefined") {
                localStorage.startDate = this.range.from;
                localStorage.endDate = this.range.to;
            }
            this.resetUserMetricValues();
            // Emptying the priority queue
            this.analyticsV2Service.emptyQueue();
            this.backToCase();
            this.update();
            if (this.newScrollDiv) {
                this.removeDivHighlight(this.newScrollDiv);
            }
        }
    }
    preSelectedSCPin(pin) { 
        /** pin = true //means already pinned , and need to unpin
         *  pin = false //means alredy unpinned , and need to pin either uid, ecoid, all sc
         */
        
        if (pin) {
            this.preSelectSC = {
                type: "set",
                scTypePin: "uid",
                analytics: true,
                preselectedPinSCAnalytics: null,
            };
            this.pinStatus = false;
        } else {
            if (this.isEcosystemSelected === true) {
                this.preSelectSC = {
                    type: "set",
                    scTypePin: "eco",
                    analytics: true,
                    preselectedPinSCAnalytics: this.ecoSystem.uid
                };
            } else {
                if (this.searchClients.uid === "" && this.searchClients.ecoId === null) {
                    this.preSelectSC = {
                        type: "set",
                        scTypePin: "uid",
                        analytics: true,
                        preselectedPinSCAnalytics: "All"
                    };
                } else {
                    this.preSelectSC = {
                        type: "set",
                        scTypePin: "uid",
                        analytics: true,
                        preselectedPinSCAnalytics: this.searchClients.uid
                    };
                }
            }
            this.pinStatus = true;
        }
        this.userManagementService.userSpecificSettings(this.preSelectSC).then(result => {
            try {
                this.preSelectSC = JSON.parse(result.data.preselectedPinSCAnalytics);
            } catch (error) {
                this.preSelectSC = '';
            }
        })
    }



    getAllPlatforms() {
        const abTestParents = new Set();
        this.childSC = [];
        this.haveDropdown = [];
        let clonnedAbTestSc = true;
        this.analyticsV2Service.getAllSearchPlatforms(clonnedAbTestSc).then(result => {
            this.searchClients = result;
            this.searchClients.map((item) => {
                if (item.ab_test_parent) {
                    const formattedABtestName = /^(.*)_([A-Za-z]+)(Search)_/;
                    const match = item.name.match(formattedABtestName);
                    if (match) {
                        item.searchTerm = `${match[1]} (${match[2]} ${match[3]})`;
 
                    } else {
                        item.searchTerm = null;
                    }
                    this.childSC.push(item);
                    abTestParents.add(item.ab_test_parent);
                }
            });
            this.haveDropdown = [false, ...this.searchClients.map(item => item.ab_test_parent === null && abTestParents.has(item.uid))];
            this.sourcePlatformSc.push({
                uid: "",
                name: "All",
                type: "sc"
            });
            this.searchClients.map(x=>{
                this.sourcePlatformSc.push({
                    uid: x.uid,
                    name: x.name,
                    type: "sc",
                    ab_test_parent: x.ab_test_parent
                });
            });
            result.forEach(row => {
                this.platformLabels[row.id] = row.name;
            });
            this.EcosystemService.getEcosystemsForAnalytics().then((resultEco) => {
                this.ecoSystemList = resultEco;
                this.ecoSystemList.map(x=>{
                    this.sourcePlatformEco.push({
                        uid: x.uid,
                        name: x.name,
                        type: "eco"
                    });
                });
                this.lastplatform();
            })
        });        
    }

    changeUser() {
        this.allGraphLoaded = false;
        if (this.internalUser !== undefined) {
            if (typeof (Storage) !== "undefined") {
                localStorage.internalUser = this.internalUser;
            }
            else {
                localStorage.internalUser = "all";
            }
        }
        this.resetUserMetricValues();
        this.update();
        // window.scrollTo({top: 0, behavior: 'smooth'});
    }

    update() {
        this.range.csv = 0;
        const cntx = this;
        this.Analyticstab1 = false;
        this.Analyticstab2 = false;
        this.Analyticstab3 = false;
        this.getActiveReportsInfo(() => {
            if ((cntx.reportSettings[24] && cntx.reportSettings[24].is_enabled) || (cntx.reportSettings[25] && cntx.reportSettings[25].is_enabled) || (this.reportSettings[26] && this.reportSettings[26].is_enabled) || (this.reportSettings[27] && this.reportSettings[27].is_enabled) || (this.reportSettings[41] && this.reportSettings[41].is_enabled) || (this.reportSettings[42] && this.reportSettings[42].is_enabled) || (this.reportSettings[43] && this.reportSettings[43].is_enabled) || (this.reportSettings[57] && this.reportSettings[57].is_enabled) || (cntx.reportSettings[59] && cntx.reportSettings[59].is_enabled) || (this.reportSettings[58] && this.reportSettings[58].is_enabled) || (this.reportSettings[64] && this.reportSettings[64].is_enabled) || (this.reportSettings[65] && this.reportSettings[65].is_enabled)) {
                this.Analyticstab1 = true;
            }
            if ((cntx.reportSettings[28] && cntx.reportSettings[28].is_enabled) || (cntx.reportSettings[29] && cntx.reportSettings[29].is_enabled) || (this.reportSettings[30] && this.reportSettings[30].is_enabled) || (this.reportSettings[31] && this.reportSettings[31].is_enabled)) {
                this.Analyticstab2 = true;
            }
            if ((cntx.reportSettings[33] && cntx.reportSettings[33].is_enabled) || (cntx.reportSettings[34] && cntx.reportSettings[34].is_enabled) || (this.reportSettings[35] && this.reportSettings[35].is_enabled) || (this.reportSettings[36] && this.reportSettings[36].is_enabled) || (this.reportSettings[37] && this.reportSettings[37].is_enabled) || (this.reportSettings[48] && this.reportSettings[48].is_enabled)) {
                this.Analyticstab3 = true;
            }
            this.getEmailTrackingFlag();
            if (cntx.reportSettings[24] && cntx.reportSettings[24].is_enabled) { //Search Summary
                cntx.drawSearchHistogram(0);
            }
            if (cntx.reportSettings[25] && cntx.reportSettings[25].is_enabled) { // Search Classifications
                this.switchSearchReport(0);
            }
            if (this.reportSettings[26] && this.reportSettings[26].is_enabled) { //Search index by content source
                this.getTypesStatistics(0, 'Content Sources');
            }
            if (cntx.reportSettings[59] && cntx.reportSettings[59].is_enabled) { //Average Click Position
                cntx.drawclickPositionHistogram(0);
            }
            if (this.reportSettings[58] && this.reportSettings[58].is_enabled) {
                this.getsearchClickPositionReport(0, 1);
            }
            if (this.reportSettings[57] && this.reportSettings[57].is_enabled) {
                this.getCaseCreatedReport(0, 1);
            }
            if (this.reportSettings[41] && this.reportSettings[41].is_enabled) {
                this.getTopUsefulFeaturedSnippet(0);
            }
            if (this.reportSettings[42] && this.reportSettings[42].is_enabled) {
                this.getTopKnowledgeGraphTitles(0);
            }
            if (this.reportSettings[43] && this.reportSettings[43].is_enabled) {
                this.getPageRatingFeedback(this.FeedbackquerySelected,this.starFilters,0);
            }
            if (cntx.reportSettings[62] && cntx.reportSettings[62].is_enabled) { //Average Click Position
                this.getSearchFeedback(1);
            }
            if (cntx.reportSettings[64] && cntx.reportSettings[64].is_enabled) { //Average Click Position
                this.advertisementSortingButton = false;
                this.advertisementFiltersOnInitialLoad.pageNo = 1,
                this.advertisementFiltersOnInitialLoad.sortType = 'desc';
                this.getAdvertisements();
            }
            this.analyticsV2Service.startParallelProcessing();
            cntx.allGraphLoaded = true;
        });
        this.getResponseFeedback();
        this.getAllLikesDislikes();
    }

    getActiveReportsInfo(cb) {
        // fetch disable analytics reports and store ids into array
        if (this.isEcosystemSelected) {
            this.EcosystemService.getActiveReportsInfo(this.ecoSystem.uid).then((result) => {
                this.reportSettings = [];
                this.emailTrackingAddonInstalled = this.ecoSystem.email_tracking_enabled;
                result.reports.map(row => {
                    this.reportSettings[row.analytics_report_id] = row;
                });
                if (this.externalEnabled && this.emailTrackingAddonInstalled == 0) {
                    this.internalUser = "all";
                    localStorage.internalUser = "all";
                }
                cb();
            });
        } else {
            this.analyticsV2Service.getActiveReportsInfo(this.searchClients.uid, 2).then(result => {                
                this.reportSettings = [];
                if (this.searchClients.uid === "") {
                    this.emailTrackingAddonInstalled = result.emailTracking;
                    result.reports.map(row => {
                        this.reportSettings[row.id] = row;
                    });
                } else {
                    this.emailTrackingAddonInstalled = result.emailTracking;
                    result.reports.map(row => {
                        this.reportSettings[row.analytics_report_id] = row;
                    });
                    if (this.externalEnabled && result.emailTracking == 0) {
                        this.internalUser = "all";
                        localStorage.internalUser = "all";
                    }
                }
                cb();
            });
        }
    }

    /**
     * Sends the user metric values.
     * @param eventData
     */
    sendUserMetricValues(eventData: { reportName: string, lastSelectionValues: string[], allSelected: boolean }) {
        const { reportName, lastSelectionValues, allSelected } = eventData;

        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get(reportName);
        if (userMetricObject) {
            if (allSelected) {
                userMetricObject.userMetricValues = ['su_um__all__selected'];
            } else {
                userMetricObject.userMetricValues = lastSelectionValues;
            }
            userMetricObject.updateReport();
        }
    }

    /**
     * Resets the user metric filter values.
     */
    resetUserMetricValues() {
        this.userMetricsFilters.forEach(metric => {
            metric.userMetricValues = [];
        });
    }

    /**
     * To reload /tileDataMetrics1
     */
    reloadTileDataMetrics1() {
        let body = {
            from: this.range.from,
            to: this.range.to,
            internalUser: this.internalUser
        }
        if (this.searchClients.ecoId) {
            body["ecoId"] = this.searchClients.ecoId;
        }
        else if (this.searchClients.uid !== '') {
            body["uid"] = this.searchClients.uid
        }
        this.getTileMetrics1Data(body);
    }

    /**
     * To reload /tileDataMetrics2
     */
    reloadTileDataMetrics2() {
        let body = {
            from: this.range.from,
            to: this.range.to,
            internalUser: this.internalUser
        }
        if (this.searchClients.ecoId) {
            body["ecoId"] = this.searchClients.ecoId;
        }
        else if (this.searchClients.uid !== '') {
            body["uid"] = this.searchClients.uid
        }
        this.getTileMetrics2Data(body);
    }

    getTileMetrics1Data(body: any) {
        this.tileDataMetrics1Loading = true;
        this.tileDataMetrics1Timeout = false;
        var cntx = this;

        this.analyticsV2Service.requestAnalyticsRoute('/overview/tileDataMetrics1', 'post', body, '')
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.tileDataMetrics1Loading = false;
                if (result.status && result.status == true) {
                    cntx.tileDataMetrics1 = result.data;
                } else if (result.error) {
                    this.tileDataMetrics1Timeout = true;
                }
            });
    }

    getTileMetrics2Data(body: any) {
        this.tileDataMetrics2Loading = true;
        this.tileDataMetrics2Timeout = false;
        var cntx = this;

        this.analyticsV2Service.requestAnalyticsRoute('/overview/tileDataMetrics2', 'post', body, '')
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.tileDataMetrics2Loading = false;
                if (result.status && result.status == true) {
                    cntx.tileDataMetrics2 = result.data;
                } else if (result.error) {
                    this.tileDataMetrics2Timeout = true;
                }
            });
    }

    getTilesData(csv: any) {
        if (csv) {
            if (this.selectedValue === 'Send Via Email') {
                this.reportsDownloadValue = 1;
                this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
            } else {
                this.reportsDownloadValue = 0;
                this.emailForReports = '';
            }
            this.analyticsV2Service.getTileDataDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
                var toastOptions: ToastOptions = {
                    title: "Report created!",
                    msg: result.message,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.success(toastOptions);
            });
        } else {
            var cntx = this;
            let body = {
                from: this.range.from,
                to: this.range.to,
                internalUser: this.internalUser
            }
            if (this.searchClients.ecoId) {
                body["ecoId"] = this.searchClients.ecoId;
            } else if (this.searchClients.uid !== '') {
                body["uid"] = this.searchClients.uid
            }
            // Fetching report specific user metric values from the map
            const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Tile Data");
            if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
                body["userMetricsFilters"] = {
                    [this.userMetricVariable]: userMetricObject.userMetricValues
                }
            }
            this.getTileMetrics2Data(body);
            this.getTileMetrics1Data(body);

            if(this.isEcosystemSelected){
                let body = {
                    from: this.range.from,
                    to: this.range.to,
                    internalUser: this.internalUser
                }
                if (this.ecoSystem.uid !== '') {
                    body["ecoId"] = this.ecoSystem.uid
                }
                this.analyticsV2Service.requestAnalyticsRoute('/overview/splitTileData', 'post', body, '')
                    .pipe(takeUntil(this.destroy$))
                    .subscribe(result => {
                        if (result.status && result.status == true) {
                            cntx.splitTileData = result.data;
                        }
                    });
            }
        }
    }

    enableAnalyticsSettings() {
        for (var client = 0; client < this.searchClients.length; client++) {
            if (this.searchClients.uid == this.searchClients[client].uid) {
                this.router.navigate(['/dashboard/generate-search-client']).then(result => {
                    this.sidebarService.sendEnableAnalyticsSetting(this.searchClients[client].id);
                })
                break;
            }
        }
    }

    enableAnalyticsSettingsEco() {
        this.router.navigate(['/dashboard/generate-search-client']).then(result => {
            this.GenerateSearchClientComponent.ecoSystemScreen();
        }) 
    }

    changeConversionType(type) {
        this.selectedConversionType = type.value;
        this.drawSearchHistogram(0);
    }

    drawSearchHistogram(csv: number) {
        if (csv) {
            this.range.csv = 1;
            if (this.selectedValue === 'Send Via Email') {
                this.reportsDownloadValue = 1;
                this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
            } else {
                this.reportsDownloadValue = 0;
                this.emailForReports = '';
            }
            // this.ecoId = this.ecoSystem && this.ecoSystem.uid || null  
            this.analyticsV2Service.getSearchSummaryChartDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.selectedConversionType, this.isEcosystemSelected, this.ecoSystem.uid).then(result => {
                this.range.csv = 0;
                var toastOptions: ToastOptions = {
                    title: "Report created!",
                    msg: result.message,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.success(toastOptions);
            });
        } else {
            this.range.csv = 0;
            this.conversionValue = 0;
            this.searchHistogram = undefined;
            let body = {
                from: this.range.from,
                to: this.range.to,
                internalUser: this.internalUser,
                conversionType: this.selectedConversionType
            }
            if (this.isEcosystemSelected) {
                body["ecoId"] = this.ecoSystem.uid;
                body["uid"] = null;
            } else {
                body["ecoId"] = null;
                if (this.searchClients.uid !== '') {
                    body["uid"] = this.searchClients.uid
                }
            }
            // Fetching report specific user metric values from the map
            const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Summary");
            if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
                body["userMetricsFilters"] = {
                    [this.userMetricVariable]: userMetricObject.userMetricValues
                }
            }
            this.analyticsV2Service.requestAnalyticsRoute('/overview/summaryChart', 'post', body, '')
                .pipe(takeUntil(this.destroy$))
                .subscribe(result => {
                    if(result.data) {
                        this.conversionValue = result.data.conversionPercentage ? result.data.conversionPercentage : 0;
                        result = result.data;
                        let resultChart: any = {};
                        resultChart = {};
                        resultChart.title = "Searches";
                        resultChart.xAxis = {};
                        resultChart.xAxis.title = "Date";
                        resultChart.xAxis.format = "Date";
                        resultChart.xAxis.values = [];
                        resultChart.yAxis = {};
                        resultChart.yAxis.title = "Count";
                        resultChart.series = [];
                        if (result.search_with_click_vs_date) {
                            resultChart.xAxis.values = result.search_with_click_vs_date.map(con => {
                                if (result.format == 'day') {
                                    let dateParts = con.created.split("-");
                                    let dateObject = dateParts[2] + '/' + dateParts[1];
                                    con.created = dateObject;
                                }
                                return con.created;
                            });
                        } else {
                            resultChart.xAxis.values = result.unique_search_with_click_vs_date.map(con => {
                                if (result.format == 'day') {
                                    let dateParts = con.created.split("-");
                                    let dateObject = dateParts[2] + '/' + dateParts[1];
                                    con.created = dateObject;
                                }
                                return con.created;
                            });
                        }
                        for (let i = 0; i < Object.keys(result).length; i++) {
                            if (Object.keys(result)[i] !== 'format' && Object.keys(result)[i] !== 'conversionPercentage') {
                                resultChart.series[i] = {};
                                resultChart.series[i].name = Object.keys(result)[i];
                                resultChart.series[i].data = [];
                                resultChart.series[i].type = 'lines';
                                result[Object.keys(result)[i]].map(b => {
                                    resultChart.series[i].data.push(b.count);
                                });
                            } else {
                                resultChart.format = result[Object.keys(result)[i]];
                            }
                        }
                        this.searchHistogram = resultChart;
                    }
                });
        }
    }

    switchSearchReport(reportId: number) {
        this.selectedIndex1Tab = 0;
        this.selectedIndex3Tab = 0;
        this.selectedIndexTabForAllSearches = 0;
        this.selectedIndexTabForNoResultSearch = 0;
        //reset search query 
        this.getFiltersForQueriesCopy.search_term = '';
        this.topSearchesWithNoClicksObj.searchQuery = '';
        this.allSuccessfullSearchesObj.searchQuery = '';
        this.AllSearchesObj.searchQuery = '';
        this.withNoResultFillters.searchQuery = '';
        this.sessionFilters.sessionid = '';
        this.sessionFilters.email = '';
        this.sessionFillter[0].value = '';
        this.sessionFillter[1].value = '';

        this.searchGroupFilters.searchquery = '';
        this.searchQueryInClusterFilter[0].value = '';


        // Reset Index to One
        this.getFiltersForQueriesCopy.offset = 1;
        this.topSearchesWithNoClicksObj.currentPage = 1;
        this.allSuccessfullSearchesObj.currentPage = 1;
        this.AllSearchesObj.currentPage = 1;
        this.withNoResultFillters.currentPage = 1;
        this.sessionFilters.offset = 1;
        this.searchGroupFilters.offset = 1;

        //reset to desc on report switch 
        this.getFiltersForQueriesCopy.sortType = 'desc';
        this.sessionFilters.sortType = 'desc';

        this.topSearchesWithNoClicksObj.sortType = 'desc';
        this.allSuccessfullSearchesObj.sortType = 'desc';
        this.AllSearchesObj.sortType = 'desc';
        this.withNoResultFillters.sortType = 'desc';

        this.sortingFieldAllSearches = this.isClusteringEnabled ? 'Queries' : 'Searches';
        this.sortingFieldSuccessFullSearches = this.isClusteringEnabled ? 'Queries' : 'Searches';
        this.sortingFieldTopSearchesWithNoClicksSearchReport = this.isClusteringEnabled ? 'Queries' : 'Searches';
        this.sortingFieldTopSearchesWithNoResult = this.isClusteringEnabled ? 'Queries' : 'Searches';

        this.topSearchesWithNoClicksObj.sortingField = this.isClusteringEnabled ? 'Queries' : 'Searches';
        this.allSuccessfullSearchesObj.sortingField = this.isClusteringEnabled ? 'Queries' : 'Searches';
        this.AllSearchesObj.sortingField = this.isClusteringEnabled ? 'Queries' : 'Searches';
        this.withNoResultFillters.sortingField = this.isClusteringEnabled ? 'Queries' : 'Searches';

        let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (All Searches)");
        if (userMetricObject) {
            userMetricObject.userMetricValues = [];
        }
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
        if (userMetricObject) {
            userMetricObject.userMetricValues = [];
        }
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
        if (userMetricObject) {
            userMetricObject.userMetricValues = [];
        }
        userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        if (userMetricObject) {
            userMetricObject.userMetricValues = [];
        }

        // reset search box
        this.resetSearchBox();

        this.isReportUp = false;
        this.activeReport = reportId;
        if (reportId == 0) {
            this.getTopSearches();
        } else if (reportId == 1) {
            this.getConvertedSearches(0);
        } else if (reportId == 2) {
            this.getTopSuccessfulSearches();
        } else if (reportId == 3) {
            this.getTopSearchesWithNoClick(0);
        } else if (reportId == 4) {
            this.savedActionFilter();
            this.getTopSearchesWithNoResult(0);
        }
    }

    resetSearchBox() {
        this.searchFilterAllSearchesReport.map((item) => {
            item.filled = false;
            item.value = ''
        });

        this.allSucessfulSearches.map((item) => {
            item.filled = false;
            item.value = ''
        });

        this.topSearchesWithNoClicksSearch.map((item) => {
            item.filled = false;
            item.value = ''
        });

        this.topSearchesWithNoresult.map((item) => {
            item.filled = false;
            item.value = ''
        });

        this.sessionFillter.map((item) => {
            item.filled = false;
            item.value = ''
        });

        this.searchQueryInClusterFilter.map((item) => {
          item.filled = false;
          item.value = ''
        });
    }

    getTopSearches() {
            this.topSearches = [];
            let body = {
                from: this.range.from,
                to: this.range.to,
                internalUser: this.internalUser,
                searchQuery: this.AllSearchesObj.searchQuery,
                offset: this.AllSearchesObj.currentPage,
                limit: this.AllSearchesObj.limit,
                sortingField: this.AllSearchesObj.sortingField,
                sortType: this.AllSearchesObj.sortType,
                searchGrouping: this.isClusteringEnabled
            }
            if (this.isEcosystemSelected) {
                body["ecoId"] = this.ecoSystem.uid;
                body["uid"] = null;
            } else {
                body["ecoId"] = null;
                if (this.searchClients.uid !== '') {
                    body["uid"] = this.searchClients.uid
                }
            }
            // Fetching report specific user metric values from the map
            const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (All Searches)");
            if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
                body["userMetricsFilters"] = {
                    [this.userMetricVariable]: userMetricObject.userMetricValues
                }
            }
            this.analyticsV2Service.requestAnalyticsRoute('/overview/topSearches', 'post', body, '')
                .pipe(takeUntil(this.destroy$))
                .subscribe(result => {
                    if(result.data) {
                        this.allSearchesCount = result.data.total[0].count;
                        this.isReportUp = true;
                        this.topSearches.isEmpty = false;
                        if (result.status == true && result.data.searches.length !== 0) {
                            result.data.searches.forEach((b, i) => {
                              if (this.isClusteringEnabled) {
                                this.topSearches.push([b.cluster_name, b.users_count, b.session_count, b.count, b.queries_count]);
                              } else {
                                this.topSearches.push([b.search_keyword, b.users_count, b.session_count, b.count]);
                              }
                            });
                        } 
                        else {
                            this.topSearches.isEmpty = true;
                            this.topSearches.push('empty');
                        }
                    }
                    else {
                        this.topSearches.isEmpty = true;
                        this.topSearches.push('empty');
                    }

            });
            this.selectedIndexTabForAllSearches = 0;
    }

    getCaseCreatedReport(csv?: number, currentPage?: any) {
        if (csv) {
            this.caseReport({ csv: csv });
        } else {
            let obj = {
                caseUid: this.caseReportFilter[0].value,
                caseSubject: this.caseReportFilter[1].value,
                cookie: this.caseReportFilter[2].value,
                emailId: this.caseReportFilter[3].value,
                currentPage: currentPage
            };
            this.caseReport(obj);
        }
    }

    getConvertedSearches(csv: number) {
        if (csv) {
            this.range.csv = 1;
            this.analyticsV2Service.getConvertedSearches(this.searchClients.uid, this.range, this.internalUser);
            this.isReportUp = true;
            this.range.csv = 0;
        } else {
            this.convertedSearches = [];
            this.analyticsV2Service.getConvertedSearches(this.searchClients.uid, this.range, this.internalUser).then(result => {
                this.isReportUp = true;
                this.convertedSearches.isEmpty = false;
                if (result.status == true && result.data.length !== 0) {
                    result.data.forEach((b, i) => {
                        this.convertedSearches.push([b.search_keyword, b.count]);
                    });
                } else {
                    this.convertedSearches.isEmpty = true;
                    this.convertedSearches.push('empty');
                }
            });
        }
    }

    getTopSuccessfulSearches() {
            this.successfulSearches = [];
            // Fetching report specific user metric values from the map
            const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
            let userMetricValues: any;
            if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
                userMetricValues = {
                    [this.userMetricVariable]: userMetricObject.userMetricValues
                }
            }

            this.analyticsV2Service.getTopSuccessfulSearches(this.searchClients, this.range, this.internalUser,this.allSuccessfullSearchesObj, this.isClusteringEnabled, userMetricValues)
                .pipe(takeUntil(this.destroy$))
                .subscribe(result => {
                    if(result.data) {
                        this.allSuccessfullSearchesObj.total = result.data.total[0].count
                        this.isReportUp = true;
                        this.successfulSearches.isEmpty = false;
                        if (result.status == true && result.data.sucessfulSearches.length !== 0) {
                            result.data.sucessfulSearches.forEach((b, i) => {
                              if (this.isClusteringEnabled) {
                                this.successfulSearches.push([b.cluster_name, b.users, b.sessions, b.count, b.queries_count]);
                              } else {
                                this.successfulSearches.push([b.text_entered, b.users, b.sessions, b.count]);
                              }
                            });
                        } else {
                            this.successfulSearches.isEmpty = true;
                            this.successfulSearches.push('empty');
                        }
                    
                    }
                });

        this.selectedIndex1Tab = 0;
    }

    getTopSearchesWithNoClick(csv: number) {
            this.withNoClickSearches = [];
            // Fetching report specific user metric values from the map
            const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
            let userMetricValues: any;
            if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
                userMetricValues = {
                    [this.userMetricVariable]: userMetricObject.userMetricValues
                };
            }

            this.analyticsV2Service.getTopSearchesWithNoClick(this.searchClients, this.range, this.internalUser, this.topSearchesWithNoClicksObj, this.isClusteringEnabled, userMetricValues)
                .pipe(takeUntil(this.destroy$))
                .subscribe(result => {
                    this.topSearchesWithNoClicksObj.total = result.data.total[0].count
                    this.isReportUp = true;
                    this.withNoClickSearches.isEmpty = false;
                    if (result.status == true && result.data.searchesWithNoClick.length !== 0) {
                        result.data.searchesWithNoClick.forEach((b, i) => {
                          if (this.isClusteringEnabled) {
                            this.withNoClickSearches.push([b.cluster_name, b.users, b.sessions, b.count, b.queries_count]);
                          } else {
                            this.withNoClickSearches.push([b.search_keyword, b.users, b.sessions, b.count]);
                          }
                        });
                    } else {
                        this.withNoClickSearches.isEmpty = true;
                        this.withNoClickSearches.push('empty');
                    }
                });
            this.selectedIndex3Tab = 0;
    }

    getTopSearchesWithNoResult(csv: number) {
        this.withNoResultSearches = [];
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        let userMetricValues: any;
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            userMetricValues = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.getTopSearchesWithNoResult(this.searchClients, this.range, this.internalUser, this.withNoResultFillters, this.actionStatusFilter, this.isClusteringEnabled, userMetricValues)
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.isReportUp = true;
                this.withNoResultSearches.isEmpty = false;
                this.withNoResultFillters.total = result.data.total[0].count;
                if (result.status == true && result.data.searchesWithNoResult.length !== 0) {
                    result.data.searchesWithNoResult.forEach((b, i) => {
                      if (this.isClusteringEnabled) {
                        this.withNoResultSearches.push([b.cluster_name, b.users, b.sessions, b.count, b.action, b.queries_count]);
                      } else {
                        this.withNoResultSearches.push([b.search_keyword, b.users, b.sessions, b.count, b.action]);
                      }
                    });
                } else {
                    this.withNoResultSearches.isEmpty = true;
                    this.withNoResultSearches.push('empty');
                }
            });
        // }
        this.selectedIndexTabForNoResultSearch = 0;
    }

    changeActionStatus(selectedAction, search_keyword) {
        this.analyticsV2Service.getActionStatus(this.searchClients.uid, this.searchClients.ecoId, this.activeReport, selectedAction, search_keyword, this.isClusteringEnabled).then(result => {
            if( result.data.status == true ) {
                let toastOptions: ToastOptions = {
                    title: "Action Saved!",
                    msg: "Action Status Saved Successfully",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                let options = {
                    info: `Search Analytics > Searches with no results > ${search_keyword} > Action value changed to ${selectedAction} on ${this.searchClients.name}`,
                    object: `Search Analytics`
                }
                this.getTopSearchesWithNoResult(0);
                this.toastyService.success(toastOptions);
                this.adminAnalyticsService.trackAdminAnalytics(options);
            } else {
                let toastOptions: ToastOptions = {
                    title: "Error Occured!",
                    msg: "Something Went Wrong",
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.error(toastOptions);
            }
        });
    }

    toggleViewState() {
        this.getFiltersForQueriesCopy.offset = 1;
        this.searchGroupFilters.offset = 1;

        if (this.selectedIndex1Tab === 1 || this.selectedIndex1Tab === 2 || this.selectedIndex1Tab === 3) {
            this.selectedIndex1Tab = 0;
        } else {
            this.selectedIndex1Tab = 1;
        }

        if (this.selectedIndex3Tab === 1 || this.selectedIndex3Tab === 2|| this.selectedIndex3Tab === 3) {
            this.selectedIndex3Tab = 0;
        } else {
            this.selectedIndex3Tab = 1;
        }

        if (this.selectedIndexTabForAllSearches === 1 || this.selectedIndexTabForAllSearches == 2 || this.selectedIndexTabForAllSearches == 3) {
            this.selectedIndexTabForAllSearches = 0
        } else {
            this.selectedIndexTabForAllSearches = 1
        }

        // reset to desc order on back
        this.getFiltersForQueriesCopy.sortType = 'desc';

        this.sessionFilters.sortType = "desc";
        this.sessionFilters.sessionid = '';

        this.searchGroupFilters.searchquery = '';
        this.searchQueryInClusterFilter[0].value = '',

        //reset sear box
        this.sessionFilters.email = '';
        this.sessionFillter[0].value = '',
            this.sessionFillter[1].value = '',
            this.resetSearchBox();

        if (this.selectedIndexTabForNoResultSearch === 1) {
            this.selectedIndexTabForNoResultSearch = 0;
        } else if (this.selectedIndexTabForNoResultSearch === 2) {
            this.selectedIndexTabForNoResultSearch = 0;
        } else if (this.selectedIndexTabForNoResultSearch === 3) {
            this.selectedIndexTabForNoResultSearch = 0;
        } else {
            this.selectedIndexTabForNoResultSearch = 1;
        }
    }

    toggleSessionsAdvancedDetails(sessionResult, index) {
        sessionResult[index].active = sessionResult[index].active ? false : true;
    }

    sendLimitReachedToasty () {
        const toastOptions: ToastOptions = {
          title: "Download Limit Reached!",
          msg: "You have reached the download limit. Try again later.",
          showClose: true,
          timeout: 3000,
          theme: 'default'
        };
        this.toastyService.error(toastOptions);
    }
    
    getTypesStatistics(csv: number, label: string) {
        // if (csv) {
        //     if (this.selectedValue === 'Send Via Email') {
        //         this.reportsDownloadValue = 0;
        //         this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
        //     } else {
        //         this.reportsDownloadValue = 1;
        //     }
        //     this.analyticsV2Service.getTypesStatistics(csv, label, this.reportsDownloadValue, this.emailForReports).then(result => {
        //         var toastOptions: ToastOptions = {
        //             title: "Report created!",
        //             msg: result.message,
        //             showClose: true,
        //             timeout: 3000,
        //             theme: 'default'
        //         };
        //         this.toastyService.success(toastOptions);
        //     });
        // } else {
        this.analyticsV2Service.getTypesStatistics(csv, label, '', '').then(result => {
            if(result.status === 429){
                this.sendLimitReachedToasty();
            }
            else if (result) {
                this.pieChart = null;
                let keys = Object.keys(result);
                this.pieChart = [];
                for (let i = 0; i < keys.length; i++) {
                    this.pieChart[i] = {};
                    this.pieChart[i].count = result[keys[i]].count;
                    this.pieChart[i].types = [];
                    for (let j = 0; j < Object.keys(result[keys[i]].types).length; j++) {
                        this.pieChart[i].types[j] = {};
                        this.pieChart[i].types[j].count = result[keys[i]].types[Object.keys(result[keys[i]].types)[j]].count;
                        this.pieChart[i].types[j].name = Object.keys(result[keys[i]].types)[j];
                    }
                    this.pieChart[i].size = result[keys[i]].size;
                    this.pieChart[i].name = keys[i];
                }
                if (result) this.makeLegend(result);
                this.reloadPie += 1;
            }
        }).catch(err => {
            if (err.message === "Api limit exceeded !!") {
              this.sendLimitReachedToasty();
            }
        });
        // }
    }

    makeLegend(result) {
        for (let res in result) {
            this.indexSize.push({ name: result[res].name, size: result[res].size });
        }
    }

    // getFrequencyBySource(csv: number, label: string) {
    //     if (csv) {
    //         if (this.selectedValue === 'Send Via Email') {
    //             this.reportsDownloadValue = 0;
    //             this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
    //         } else {
    //             this.reportsDownloadValue = 1;
    //         }
    //         this.analyticsV2Service.getContentCount(csv, label, this.reportsDownloadValue, this.emailForReports).then(result => {
    //             var toastOptions: ToastOptions = {
    //                 title: "Report created!",
    //                 msg: result.message,
    //                 showClose: true,
    //                 timeout: 3000,
    //                 theme: 'default'
    //             };
    //             this.toastyService.success(toastOptions);
    //         });
    //     }
    // }

    getTopUsefulFeaturedSnippet(csv: number) {
        // if (csv) {
        //     if (this.selectedValue === 'Send Via Email') {
        //         this.reportsDownloadValue = 1;
        //         this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
        //     } else {
        //         this.reportsDownloadValue = 0;
        //         this.emailForReports = '';
        //     }
        //     this.analyticsV2Service.getTopUsefulFeaturedSnippetDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
        //         var toastOptions: ToastOptions = {
        //             title: "Report created!",
        //             msg: result.message,
        //             showClose: true,
        //             timeout: 3000,
        //             theme: 'default'
        //         };
        //         this.toastyService.success(toastOptions);
        //         this.range.csv = 0;
        //     });
        // } else {
        this.topRatedFeaturedResult = [];
        let body = {
            from: this.range.from,
            to: this.range.to,
            internalUser: this.internalUser
        };
        if (this.searchClients.uid !== '') {
            body["uid"] = this.searchClients.uid;
        }
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Top Rated Featured Results");
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            body["userMetricsFilters"] = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.requestAnalyticsRoute('/overview/featuredSnippet', 'post', body, '')
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.topRatedFeaturedResult.isEmpty = false;
                if (result.status == true && result.data.length !== 0) {
                    this.topRatedFeaturedResult = result.data;
                    this.topRatedFeaturedResult.isEmpty = false;
                } else {
                    this.topRatedFeaturedResult.isEmpty = true;
                    this.topRatedFeaturedResult.push('empty');
                }
            })
        //}
    }

    getTopKnowledgeGraphTitles(csv: number) {
        // if (csv) {
        //     this.reportsDownloadValue = 0;
        //     this.emailForReports = '';
        //     this.analyticsV2Service.getTopKnowledgeGraphTitlesDownload(this.searchClients.uid, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
        //         var toastOptions: ToastOptions = {
        //             title: "Report created!",
        //             msg: result.message,
        //             showClose: true,
        //             timeout: 3000,
        //             theme: 'default'
        //         };
        //         this.toastyService.success(toastOptions);
        //         this.range.csv = 0;
        //     });
        // } else {
        this.topKnowledgeGraphTitles = [];
        let body = {
            from: this.range.from,
            to: this.range.to,
            internalUser: this.internalUser
        };
        if (this.searchClients.uid !== '') {
            body["uid"] =  this.searchClients.uid;
        }
        this.analyticsV2Service.requestAnalyticsRoute('/overview/knowledgeTitle', 'post', body, '')
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.topKnowledgeGraphTitles.isEmpty = false;
                if (result.status == true && result.data.length !== 0) {
                    this.topKnowledgeGraphTitles = result.data;
                    this.topKnowledgeGraphTitles.isEmpty = false;
                } else {
                    this.topKnowledgeGraphTitles.isEmpty = true;
                    this.topKnowledgeGraphTitles.push('empty');
                }
            });
        //}
    }

    readRatingFromLocalStorage(){
        let content_feedback_report = localStorage.getItem('content_feedback_report') ?  JSON.parse(localStorage.getItem('content_feedback_report')) : {};
        this.FeedbackquerySelected = content_feedback_report.querySelected || 'all';
        this.Feedbackevent= content_feedback_report.starFilters || 'most_recent';
        this.lastSelectedParentFilter = this.FeedbackquerySelected;
        this.checkFilter();
    }
    writeRatingtoLocalStorage(){
        let content_feedback_report = JSON.stringify({querySelected : this.FeedbackquerySelected,'starFilters' : this.Feedbackevent })
        localStorage.setItem('content_feedback_report',content_feedback_report);
        this.checkFilter();
    }
    checkFilter(){
        if((Array.isArray(this.Feedbackevent) && this.Feedbackevent.length === 0)){
            this.noFilterSelected=true;
        }
        else{
            this.noFilterSelected = false;
        }
    }

    applyFeedbackFilter(){
        this.getPageRatingFeedback(this.FeedbackquerySelected, this.Feedbackevent);
        this.writeRatingtoLocalStorage();
    }
    /**
     * This function executes when user switches pagination
     * @param pageNo  when user switches pagination
     */
    setPaginationForAdvertisement(pageNo){
        this.advertisementFiltersOnInitialLoad.pageNo =pageNo;
        this.getAdvertisements();
    }
    /**
     * This function is used to call advertisments list 
     */

    getAdvertisements(){
        this.advertismentSearchedStrings.showLoader = true;
        this.advertismentSearchedStrings.advertisements = [];
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Advertisement Performance Report");
        let userMetricValues: any;
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            userMetricValues = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.getAdvertisementList(this.range, this.searchClients.uid, this.internalUser, userMetricValues, this.advertisementSearchedQuery,this.advertisementFiltersOnInitialLoad.sortType,this.advertisementFiltersOnInitialLoad.pageNo).then((result)=>{
            this.advertismentSearchedStrings = result.data;
            if(this.advertismentSearchedStrings && this.advertismentSearchedStrings.advertisements && this.advertismentSearchedStrings.advertisements.length > 0){
                this.advertismentSearchedStrings.isEmpty = false;
                this.advertismentSearchedStrings.showLoader = false;
            } else{
                this.advertismentSearchedStrings.isEmpty = true;
                this.advertismentSearchedStrings.showLoader = false;
            }
        }).catch((err: any) => {
          this.advertismentSearchedStrings.isEmpty = true;
          this.advertismentSearchedStrings.showLoader = false;
        });
    }

    getPageRatingFeedback(querySelected?, event?, csv?: number) {
        let subFilter = Array.isArray(event) ? event.map((item)=>{
            return {'min':item-0.25,'max':item+0.75}
         }) : event;
        this.starFilters=event;
        this.starsdownloadReport=subFilter
        // if (csv) {
        //     this.range.csv = 1;
        //     if (this.selectedValue === 'Send Via Email') {
        //         this.reportsDownloadValue = 1;
        //         this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
        //     } else {
        //         this.reportsDownloadValue = 0;
        //         this.emailForReports = '';
        //     }
        //     this.analyticsV2Service.getPageRatingFeedbackDownload(this.range, this.searchClients.uid, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
        //         this.range.csv = 0;
        //         var toastOptions: ToastOptions = {
        //             title: "Report created!",
        //             msg: result.message,
        //             showClose: true,
        //             timeout: 3000,
        //             theme: 'default'
        //         };
        //         this.toastyService.success(toastOptions);
        //     });
        // } else {
        let body = {
            from: this.range.from,
            to: this.range.to,
            internalUser: this.internalUser
        };
        if (this.searchClients.uid !== '') {
            body["uid"] = this.searchClients.uid;
        }
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Content Experience Feedback");
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            body["userMetricsFilters"] = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        if(subFilter=='most_recent'||subFilter=='least_recent'||subFilter=='count'||subFilter=='dislikes'||subFilter=='likes'){
            body["sortby"]=subFilter?subFilter:[]
        } else{
            body["starFilters"]=subFilter?subFilter:[]
        }
        if(querySelected=='all'||querySelected=='votes'||querySelected=='stars'){
            body["filterType"]=querySelected?querySelected:'all'
        }
        this.analyticsV2Service.requestAnalyticsRoute('/overview/PageRating', 'post', body, '')
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                // this.pageRatingTitles = [];
                this.pageRatingTitles.isEmpty = false;
                if (result.status == true && result.data.length !== 0) {
                    this.pageRatingTitles = result.data;
                    this.pageRatingTitles.isEmpty = false;
                    this.pageRatingTitles = this.pageRatingTitles.map((i) => {
                        if (i.feedback_type === 0) {
                            return i
                        }
                        else {
                            return { ...i,rounded_stars:new Array((Math.round(i.avg_rating * 2) / 2).toFixed(1)),  nonHighlightedStars: new Array((Math.round((5-i.avg_rating) * 2) / 2).toFixed(1)) }
                        }
                    })
                    this.pageRatingTitles = this.pageRatingTitles.map((item) => {
                        if (item.feedback_type === 0) {
                            return item;
                        } else {
                            let value = item.avg_rating;
                            let rounded_stars_array = [];
                            let fullStar = (
                                Math.round(item.avg_rating * 2) / 2
                            ).toFixed(1);
                            let halfStar = (Math.round(item.avg_rating * 2) / 2).toFixed(1)
                            let greyStar 
                            if(Number(halfStar)%1==0.5){
                                greyStar =  5-Number(Number((Math.round(item.avg_rating* 2) / 2).toFixed(1))+0.5)
                            }else{
                                greyStar =  5-Number((Math.round(item.avg_rating* 2) / 2).toFixed(1))
                            }
                            for (let i = 0; i < Number(fullStar[0]); i++) {
                                rounded_stars_array.push(1);
                            }
                            if (Number(halfStar)%1==0.5) {
                                rounded_stars_array.push(0.5);
                            }
                            for (let i = 0; i < greyStar; i++) {
                                rounded_stars_array.push(0);
                            }
                            return {
                                ...item,
                                new_rounded_stars: rounded_stars_array,
                            };
                        }                  
                    });
                } else {
                    this.pageRatingTitles.isEmpty = true;
                    this.pageRatingTitles.push('empty');
                }
            });
        // }
    }

    getSessionValuesDetails(cookie: string, selectedSearchTerm: any) {
        // Fetching report specific user metric values from the map
        let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined;
        if (this.sessionFilters.filterByReport === 'allSearches') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (All Searches)");
        } else if (this.sessionFilters.filterByReport === 'successfullSearches') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
        } else if (this.sessionFilters.filterByReport === 'noClicks') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
        } else if (this.sessionFilters.filterByReport === 'noResult') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        }
        let userMetricValues: any;
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            userMetricValues = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.getSelectedSessionValuesDetails(this.searchClients, this.range, this.internalUser, cookie, this.isClusteringEnabled, userMetricValues)
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.myCheckbox = false;
                this.hideflag = true;
                this.addClassToBody();
                var searchResult = [];
                this.sessionResult = result.data;
                this.sessionResultTemp = result.data;
                for (var i = 0; i < result.data.length; i++) {
                    if (result.data[i].text_entered == selectedSearchTerm && result.data[i].type == "search") {
                        searchResult.push(result.data[i]);
                    }
                    if (result.data[i].type == "search") {
                        result.data[i].activityType = "Text Searched";
                    }

                    if (result.data[i].type == "search" && result.data[i].is_support_search) {
                        result.data[i].activityType = "Text Searched (Support Page)";
                    }


                    if (result.data[i].type == "Page View") {
                        result.data[i].activityType = "Viewed Page";
                    }

                    if (result.data[i].type == "Page View" && result.data[i].is_support_page_view == true) {
                        result.data[i].activityType = "Visited Support";
                    }

                    if (result.data[i].type == "Conversion") {
                        result.data[i].activityType = "Clicked Search Result";
                    }

                    if (result.data[i].type == "Conversion" && result.data[i].is_support_conversion) {
                        result.data[i].activityType = "Clicked Search Result (Support Page)";
                    }

                    if (result.data[i].type == "Case logged") {
                        result.data[i].activityType = "Created a Case";
                    }
                }
                this.sessionResultTemp = result.data;
                this.sessionResult = searchResult;
                this.sessionCookies = cookie;
            });
    }

    getSessionAndActivityReport(cookie: string, csv: number) {
        this.cookie = cookie;
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Cases Created");
        let userMetricValues: any;
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            userMetricValues = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.caseDeflectionFormulaAndSettings(this.searchClients, this.range, this.internalUser, userMetricValues)
            .pipe(takeUntil(this.destroy$))
            .subscribe((response) => {
                this.caseDeflectionFormulaAndSettings = response.data;
            });
        // if (csv) {
        //     this.range.csv = 1;
        //     if (this.selectedValue === 'Send Via Email') {
        //         this.reportsDownloadValue = 1;
        //         this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
        //     } else {
        //         this.reportsDownloadValue = 0;
        //         this.emailForReports = '';
        //     }
        //     this.analyticsV2Service.getSessionAndActivityReportDownload(this.range, this.searchClients.uid, this.internalUser, cookie, csv, this.reportsDownloadValue, this.emailForReports).then(result => {
        //         this.range.csv = 0;
        //         var toastOptions: ToastOptions = {
        //             title: "Report created!",
        //             msg: result.message,
        //             showClose: true,
        //             timeout: 3000,
        //             theme: 'default'
        //         };
        //         this.toastyService.success(toastOptions);
        //     });
        // } 
        // else {
            this.analyticsV2Service.getSessionAndActivityReport(this.searchClients.uid, this.range, this.internalUser, cookie, this.isEcosystemSelected, this.ecoSystem.uid, userMetricValues)
                .pipe(takeUntil(this.destroy$))
                .subscribe(result => {
                    this.selectedActivityFilterInside = Object.assign({}, this.selectedActivityFilter);
                    if (this.modeselect[0] == "all") {
                        this.modeselect.splice(0, 1);
                    }
                    this.selectedActivityFilterInside['all'] = true
                    this.activityTypesInside = this.modeselect;
                    this.modeselectInside = Object.keys(this.selectedActivityFilter);
                    this.sessionActivityData = [];
                    this.sessionActivityData.isEmpty = false;
                    if (result.status == true && result.data) {
                        this.sessionActivityData = result.data;
                        this.sessionActivityData.isEmpty = false;
                        // let session = this.sessionActivityData.activityData;
                        for (let i = 0; i < this.sessionActivityData.activityData.length; i++) {
                            if (this.sessionActivityData.activityData[i].type == "search") {
                                var text_searched = 'Text Searched';
                                if (this.sessionActivityData.activityData[i].window_url != undefined) {
                                    if (this.sessionActivityData.activityData[i].is_support_search) {
                                        text_searched += ' (Support Page)';
                                    } else {
                                        text_searched += ' ';
                                    }
                                } else {
                                    text_searched += ' ';
                                }
                                this.sessionActivityData.activityData[i].typeLabel = text_searched
                            }

                            if (this.sessionActivityData.activityData[i].type == "Page View") {
                                var activity = {
                                    type: 'pageView',
                                    typeLabel: 'Viewed Page'
                                };
                                if (this.sessionActivityData.activityData[i].window_url && this.sessionActivityData.activityData[i].window_url.trim() != '') {
                                    if (this.sessionActivityData.activityData[i].is_support_page_view) {
                                        activity.type = 'supportVisit';
                                        activity.typeLabel = 'Visited Support';
                                    }
                                }
                                this.sessionActivityData.activityData[i].type = activity.type;
                                this.sessionActivityData.activityData[i].typeLabel = activity.typeLabel;
                            }

                            if (this.sessionActivityData.activityData[i].type == "Conversion") {
                                var clickedSearchResult = 'Clicked Search Result';
                                if (this.sessionActivityData.activityData[i].window_url != undefined) {
                                    if (this.sessionActivityData.activityData[i].is_support_conversion) {
                                        clickedSearchResult += ' (Support Page)';
                                    } else {
                                        clickedSearchResult += ' ';
                                    }
                                } else {
                                    clickedSearchResult += ' ';
                                }
                                var conversion = {
                                    type: 'conversion',
                                    typeLabel: clickedSearchResult
                                };
                                this.sessionActivityData.activityData[i].type = conversion.type;
                                this.sessionActivityData.activityData[i].typeLabel = conversion.typeLabel;
                            }

                            if (this.sessionActivityData.activityData[i].type == "Case logged") {
                                var caseLogged = {
                                    type: 'caseCreated',
                                    typeLabel: 'Created a Case',
                                    caseSubject: (this.sessionActivityData.activityData[i].subject && this.sessionActivityData.activityData[i].subject.trim() != '') ? this.sessionActivityData.activityData[i].subject : this.sessionActivityData.activityData[i].window_url
                                };
                                this.sessionActivityData.activityData[i].type = caseLogged.type;
                                this.sessionActivityData.activityData[i].typeLabel = caseLogged.typeLabel;
                                this.sessionActivityData.activityData[i].caseSubject = caseLogged.caseSubject;
                            }
                        }
                    } else {
                        this.sessionActivityData.isEmpty = true;
                    }
                });
        //}
    }

    getSessionValues(session: any, cookiesValue: any, checkbox: boolean, selectedSearchTerm: any) {
        this.hideflag = true;
        this.addClassToBody();
        var searchResult = [];
        for (var j = 0; j < session.length; j++) {
            if (session[j].cookie == cookiesValue) {
                if (checkbox) {
                    this.sessionResult = this.sessionResultTemp;
                    this.sessionCookies = session[j].cookie;
                    this.myCheckbox = checkbox;
                }
                else {
                    for (var i = 0; i < this.sessionResultTemp.length; i++) {
                        if (this.sessionResultTemp[i].text_entered == selectedSearchTerm && this.sessionResultTemp[i].type == "search") {
                            searchResult.push(this.sessionResultTemp[i]);
                        }
                    }
                    this.sessionResult = searchResult;
                    this.sessionCookies = session[j].cookie;
                    this.myCheckbox = checkbox;
                }
            }
        }
    }

    getFiltersForQueries(search_term: string, filter, searchCount, reportNum) {
        this.getFiltersForQueriesCopy.search_term = search_term;
        this.getFiltersForQueriesCopy.searchCount = searchCount;

        if (filter == 1) {
          this.withResult = true;
        } else {
          this.withResult = false;
        }

        if (reportNum == 0) {
            this.getFiltersForQueriesCopy.isClicked = 'all';
            this.selectedIndexTabForAllSearches = this.isClusteringEnabled ? 3 : 2;
        } else if (reportNum == 2) {
            this.getFiltersForQueriesCopy.isClicked = true;
            this.selectedIndex1Tab = this.isClusteringEnabled ? 3 : 2;
        }
        else if (reportNum == 3) {
            this.getFiltersForQueriesCopy.isClicked = false;
            this.selectedIndex3Tab = this.isClusteringEnabled ? 3 : 2;
        } else {
            this.getFiltersForQueriesCopy.isClicked = 'noResult';
            this.selectedIndexTabForNoResultSearch = this.isClusteringEnabled ? 3 : 2;
        }

        if (typeof search_term != 'undefined') {
            this.selectedIndex = 0;
            this.queryFilters.selectedSearchTerm = search_term;
            this.queryFilters.searchCount = searchCount;
            this.internalUser = this.internalUser ? this.internalUser : "all";
            if (this.isEcosystemSelected) {
                this.range.uid = this.ecoSystem.uid;
            } else {
                this.range.uid = this.searchClients.uid || "";
            }
            this.PostRequestForData(search_term, searchCount, this.getFiltersForQueriesCopy);
        }
    }
    getSearchGroups(search_term: string, filter, searchCount, reportNum) {
        if (filter == 1) {
          this.withResult = true;
        } else {
          this.withResult = false;
        }

        if (reportNum == 0) {
            this.searchGroupFilters.isClicked = 'all';
            this.selectedIndexTabForAllSearches = 1;
        } else if (reportNum == 2) {
            this.searchGroupFilters.isClicked = true;
            this.selectedIndex1Tab = 1;
        }
        else if (reportNum == 3) {
            this.searchGroupFilters.isClicked = false;
            this.selectedIndex3Tab = 1;
        } else {
            this.searchGroupFilters.isClicked = 'noResult';
            this.selectedIndexTabForNoResultSearch = 1;
        }

        if (typeof search_term != 'undefined') {
            this.selectedIndex = 0;
            this.searchGroupFilters.selectedSearchTerm = search_term;
            this.searchGroupFilters.searchCount = searchCount;
            this.internalUser = this.internalUser ? this.internalUser : "all";
            if (this.isEcosystemSelected) {
                this.range.uid = this.ecoSystem.uid;
            } else {
                this.range.uid = this.searchClients.uid || "";
            }
            this.PostRequestForGroupData(this.searchGroupFilters);
        }
    }

    PostRequestForData(search_term, searchCount, fillters) {
        this.queryFilters['filters'] = []
        this.queryFilters["state"] = "loading";
        if (this.isEcosystemSelected) {
            this.range.isEcosystemSelected = this.isEcosystemSelected;
        } else {
            this.range.isEcosystemSelected = this.isEcosystemSelected;
        }
        // Fetching report specific user metric values from the map
        let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined;
        if (fillters.isClicked === 'all') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (All Searches)");
        } else if (fillters.isClicked === true) {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
        } else if (fillters.isClicked === false) {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
        } else if (fillters.isClicked === 'noResult') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        }
        let userMetricValues: any;
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            userMetricValues = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticService.getFiltersForQueries(this.range.uid , this.range, this.internalUser, search_term, fillters, this.isClusteringEnabled, userMetricValues)
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                if(result.status == true) {
                    let reStructure = result.data.filters;
                    let reResult = this.transform(reStructure);

                    this.queryFilters = {
                        "state": "shown",
                        "selectedSearchTerm": search_term,
                        "searchCount": searchCount,
                        "filters": reResult,
                        "total": result.data.count
                    }
                } else {
                    this.queryFilters["state"] = "loading";
                }
            });
    }

    PostRequestForGroupData(fillters) {
        this.searchGroupFilters['filters'] = []
        this.searchGroupFilters['state'] = "loading";
        if (this.isEcosystemSelected) {
            this.range.isEcosystemSelected = this.isEcosystemSelected;
        } else {
            this.range.isEcosystemSelected = this.isEcosystemSelected;
        }
        // Fetching report specific user metric values from the map
        let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined;
        if (fillters.isClicked === 'all') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (All Searches)");
        } else if (fillters.isClicked === true) {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
        } else if (fillters.isClicked === false) {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
        } else if (fillters.isClicked === 'noResult') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        }
        let userMetricValues: any;
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            userMetricValues = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.getSearchGroups(this.range.uid, this.range, this.internalUser, fillters, userMetricValues)
          .pipe(takeUntil(this.destroy$))
          .subscribe(result => {
            if(result.status == true) {
              this.searchGroupFilters['state'] = "shown";
              this.searchGroupFilters['filters'] = result.data.searches;
              this.searchGroupFilters['total'] = result.data.total[0].count;
            } else {
              this.searchGroupFilters['state'] = "loading";
            }
          });
    }


    transform(source) {
        let result = [];
        source.map(f => {
            let filters = { filters: [] };
            if (f.facet_value[0] == null) {
                f.facet_value[0] = 'Null'
            }

            if (f.facet_type[0] == null) {
                f.facet_type[0] = 'Null'
            }

            let obj = {
                "filterType": f.facet_type[0],
                "selectedValues": []
            }

            obj.selectedValues.push(f.facet_value[0])

            for (let i = 1; i <= f.facet_type.length; i++) {
                if (i != f.facet_type.length && f.facet_type[i] == f.facet_type[i - 1]) {
                    obj.selectedValues.push(f.facet_value[i]);
                } else {
                    filters.filters.push(obj);
                    if (i != f.facet_type.length) {
                        obj = {
                            "filterType": f.facet_type[i],
                            "selectedValues": []
                        }
                        obj.selectedValues.push(f.facet_value[i])
                    }
                }
            }
            filters['exactphrase'] = f.exactphrase;
            filters['withoneormore'] = f.withoneormore;
            filters['withoutwords'] = f.withoutwords;
            filters['count'] = f.count;
            result.push(filters);
        });


        return result;
    }


    getFilters(idx: number) {
        return Object.keys(this.queryFilters["filters"][idx]);
    }

    getSessionFilter(search_term: string, filter, reportName, clickedSession) {
        if (filter == 1) {
            this.withResult = true;
        } else {
            this.withResult = false;
        }

        if (reportName == 'allSearchReport') {
            this.selectedIndexTabForAllSearches = this.isClusteringEnabled ? 2 : 1;
            this.sessionFilters.filterByReport = 'allSearches';
        } else if (reportName == 'allSucessfulSearches') {
            this.selectedIndex1Tab = this.isClusteringEnabled ? 2 : 1;
            this.sessionFilters.filterByReport = 'successfullSearches'
        } else if (reportName == 'topSearchesWithNoClicksSearchReport') {
            this.selectedIndex3Tab = this.isClusteringEnabled ? 2 : 1;
            this.sessionFilters.filterByReport = 'noClicks';
        } else {
            this.selectedIndexTabForNoResultSearch = this.isClusteringEnabled ? 2 : 1;
            this.sessionFilters.filterByReport = 'noResult';
        }

        if (typeof search_term != 'undefined') {
            this.selectedIndex = 0;
            this.sessionFilters.selectedSearchTerm = search_term
            this.sessionTotal = clickedSession;
            this.range.withResult = this.withResult;
            this.getSessionFilterService();
        }
    }
    getSessionFilterService() {
        this.sessionFilters["state"] = "loading";
        // Fetching report specific user metric values from the map
        let userMetricObject: { userMetricValues: string[], updateReport: any } | undefined;
        if (this.sessionFilters.filterByReport === 'allSearches') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (All Searches)");
        } else if (this.sessionFilters.filterByReport === 'successfullSearches') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Successful Searches)");
        } else if (this.sessionFilters.filterByReport === 'noClicks') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Clicks)");
        } else if (this.sessionFilters.filterByReport === 'noResult') {
            userMetricObject = this.userMetricsFilters.get("Search Classifications (Searches With No Result)");
        }
        let userMetricValues: any;
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            userMetricValues = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.getSessionFiltersForQueries(this.searchClients, this.range, this.internalUser, this.sessionFilters, this.isClusteringEnabled, userMetricValues)
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                // this.sessionFilters = {
                //     "state": "shown",
                //     "selectedSearchTerm": this.sessionFilters.selectedSearchTerm,
                //     "filters": result.data,
                //     sessionId:  this.sessionFillter[0].value,
                //     email: this.sessionFillter[1].value,
                //     sortType:this.sessionFilters.sortType,
                //     sortingField: this.sessionFilters.sortingField
                // }
                this.sessionFilters['state'] = "shown";
                this.sessionFilters['selectedSearchTerm'] = this.sessionFilters.selectedSearchTerm;
                this.sessionFilters['filters'] = result.data.result;
                this.sessionFilters['total'] = result.data.total[0].count;
            });
    }
    caseReport(reportData) {
        reportData.from = this.range.from;
        reportData.to = this.range.to;
        reportData.internalUser = this.internalUser;
        if (this.isEcosystemSelected) {
            reportData.ecoId = this.ecoSystem.uid;
            reportData.uid = null;
        } else{
            reportData.uid = this.searchClients.uid ? this.searchClients.uid : '';
            reportData.ecoId = null;
        }
        if (reportData.currentPage) {
            reportData.offset = reportData.currentPage;
            //reportData.limit = 10;
        }
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Cases Created");
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            reportData.userMetricsFilters = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.createdCasesData = [];
        // if (reportData.csv) {
        //     reportData.csv = 1;
        //     this.range.csv = 1;
        //     if (this.selectedValue === 'Send Via Email') {
        //         reportData.reportsDownloadValue = 1;
        //         reportData.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
        //     } else {
        //         reportData.reportsDownloadValue = 0;
        //         reportData.emailForReports = '';
        //     }
        //     this.analyticsV2Service.getCaseCreatedReportDownload(reportData).then(result => {
        //         this.range.csv = 0;
        //         var toastOptions: ToastOptions = {
        //             title: "Report created!",
        //             msg: result.message,
        //             showClose: true,
        //             timeout: 3000,
        //             theme: 'default'
        //         };
        //         this.toastyService.success(toastOptions);
        //     });
        // }
        this.analyticsV2Service.requestAnalyticsRoute('/overview/createdCases', 'post', reportData, '')
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.createdCasesData.isEmpty = false;
                if (result.status == true && result.data.cases && result.data.cases.length !== 0) {
                    this.createdCasesData = result.data.cases;
                    this.createdCasesData.count = result.data.total;
                    this.createdCasesData.currentPage = reportData.currentPage;
                    this.createdCasesData.isEmpty = false;
                } else {
                    this.createdCasesData.isEmpty = true;
                }
            });
    }

    SearchCasetitle(clickInput, value, val) {

        for (let p = 0; p < this.caseReportFilter.length; p++) {
            if (this.caseReportFilter[p].label == clickInput) {
                this.caseReportFilter[p].value = value
            }
        }

        let obj = {
            caseUid: this.caseReportFilter[0].value,
            caseSubject: this.caseReportFilter[1].value,
            cookie: this.caseReportFilter[2].value,
            emailId: this.caseReportFilter[3].value,
            currentPage: 1,
            isAscending: false
        };
        if (val === 1) {
            this.caseReport(obj)
        }
        else if (val === 2) {
            if (this.firstTime === 0) {
                this.firstTime++;
            }
            else {
                this.ascending = !this.ascending;
            }
            obj.isAscending = this.ascending;
            this.caseReport(obj);
        }
    }

    addClassToBody() {
        var addClass = document.getElementById("desktopview");
        addClass.classList.add("addClass");
    }

    removeClassToBody() {
        var removeClass = document.getElementById("desktopview");
        removeClass.classList.remove("addClass");
    }

    stopPropagation(event) {
        event.preventDefault();
    }

    getsearchClickPositionReport(csv?: number, currentPage?: any) {
        if (csv) {
            this.searchClickPosition({ csv: csv });
        } else {
            let obj = {
                searchQuery: this.clickResultReport[0].value,
                currentPage: currentPage,
                sortingField: this.sortingField,
                sortType: this.sortType
            };
            this.searchClickPosition(obj);
        }
    }

    changeSortOrder(sortingField) {
        this.sortingField = sortingField;
        if (sortingField === this.sortingFieldCopy) {
            this.sortType = this.sortType === 'desc' ? 'asc' : 'desc';
        }
        else {
            this.sortType = 'desc';
        }
        let obj = {
            searchQuery: this.clickResultReport[0].value,
            currentPage: 1,
            sortingField: this.sortingField,
            sortType: this.sortType
        };
        this.searchClickPosition(obj);
        this.sortingFieldCopy = this.sortingField;
    }

    filtersearchClickPosition(label, value) {
        if (value) {
            for (let p = 0; p < this.clickResultReport.length; p++) {
                if (this.clickResultReport[p].label == label) {
                    this.clickResultReport[p].filled = value
                }
            }
        }
    }
    searchClickPosition(reportData) {
        reportData.from = this.range.from;
        reportData.to = this.range.to;
        reportData.internalUser = this.internalUser;
        reportData.uid = this.searchClients.uid ? this.searchClients.uid : '';
        if (reportData.currentPage) {
            reportData.offset = reportData.currentPage;
        }
        this.searchclickPosition = [];
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Click Position Report");
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            reportData.userMetricsFilters = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.requestAnalyticsRoute('/overview/searchClickPosition', 'post', reportData, '')
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                this.searchclickPosition.isEmpty = false;
                if (result.status == true && result.data.clickedSearches && result.data.clickedSearches.length !== 0) {
                    this.searchclickPosition = result.data.clickedSearches;
                    this.searchclickPosition.count = result.data.total;
                    this.searchclickPosition.currentPage = reportData.currentPage;
                    this.searchclickPosition.isEmpty = false;
                } else {
                    this.searchclickPosition.isEmpty = true;
                }
            });
    }

    searchQueryClickPosition(clickInput, value) {
        for (let p = 0; p < this.clickResultReport.length; p++) {
            if (this.clickResultReport[p].label == clickInput) {
                this.clickResultReport[p].value = value
            }
        }
        let obj = {
            searchQuery: this.clickResultReport[0].value,
            currentPage: 1,
            sortingField: this.sortingField,
            sortType: this.sortType
        };
        this.searchClickPosition(obj)
    }

    getSessionClickPosition(csv?: number, currentPage?: any, click?: any) {
        if (csv) {
            this.sessionClickPosition(click, { csv: csv });
        } else {
            let obj = {
                searchQuery: this.sessionClickResultData[0].value,
                currentPage: currentPage,
                sortingField: this.sessionSortField,
                sortType: this.sessionSortType
            };
            this.sessionClickPosition(click, obj);
        }
    }

    changeSessionSortOrder(click, sortingField) {
        this.sessionSortField = sortingField;
        if (sortingField === this.sessionSortFieldCopy) {
            this.sessionSortType = this.sessionSortType === 'desc' ? 'asc' : 'desc';
        }
        else {
            this.sessionSortType = 'desc';
        }
        let obj = {
            searchQuery: this.sessionClickResultData[0].value,
            currentPage: 1,
            sortingField: this.sessionSortField,
            sortType: this.sessionSortType
        };
        this.sessionClickPosition(click, obj);
        this.sessionSortFieldCopy = this.sessionSortField;
    }

    filtersessionClickResult(label, value) {
        if (value) {
            for (let p = 0; p < this.sessionClickResultData.length; p++) {
                if (this.sessionClickResultData[p].label == label) {
                    this.sessionClickResultData[p].filled = value
                }
            }
        }
    }

    sessionSearchClickPosition(click, clickInput, value) {
        for (let p = 0; p < this.sessionClickResultData.length; p++) {
            if (this.sessionClickResultData[p].label == clickInput) {
                this.sessionClickResultData[p].value = value
            }
        }
        let obj = {
            searchQuery: this.sessionClickResultData[0].value,
            currentPage: 1,
            sortingField: this.sessionSortField,
            sortType: this.sessionSortType
        };
        this.sessionClickPosition(click, obj)
    }

    sessionClickPosition(click, reportData) {
        reportData.from = this.range.from;
        reportData.to = this.range.to;
        reportData.internalUser = this.internalUser;
        reportData.uid = this.searchClients.uid ? this.searchClients.uid : '';
        if (reportData.currentPage) {
            reportData.offset = reportData.currentPage;
        }
        // Fetching report specific user metric values from the map
        const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Click Position Report");
        if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
            reportData.userMetricsFilters = {
                [this.userMetricVariable]: userMetricObject.userMetricValues
            };
        }
        this.analyticsV2Service.getSessionActivityClickPositionReport(click.text_entered, reportData)
            .pipe(takeUntil(this.destroy$))
            .subscribe((response) => {
                this.sessionclickPosition = [];
                this.sessionclickPosition.isEmpty = false;
                if (response.status == true && response.data.clickedSessions && response.data.clickedSessions.length !== 0) {
                    this.sessionclickPosition = response.data.clickedSessions;
                    if (this.sessionclickPosition.length > 0) {
                        this.sessionclickPosition = this.sessionclickPosition.map((item) => {
                            item.active = 0;
                            return item;
                        })
                    }
                    this.sessionclickPosition.count = response.data.total;
                    this.sessionclickPosition.currentPage = reportData.currentPage;
                    if (!this.sessionSort) {
                        if (!click.hideflag) {
                            click.hideflag = true;
                            this.addClassToBody();
                        } else {
                            click.hideflag = false;
                            this.removeClassToBody();
                        }
                    }
                }
                else {
                    this.sessionclickPosition.isEmpty = true;
                }
                this.sessionSort = false;
            });
    }

    toggleClickSessionsAdvancedDetails(activityIndex) {
        this.sessionclickPosition[activityIndex].active = this.sessionclickPosition[activityIndex].active ? false : true;
    }

    clearFilterValues() {
        this.sessionClickResultData[0].value = '';
        this.sessionClickResultData[0].filled = false;
        this.sessionSortField = 'activity_time';
        this.sessionSortType = 'desc';
    }

    drawclickPositionHistogram(csv: number) {
        if (csv) {
            this.range.csv = 1;
            if (this.selectedValue === 'Send Via Email') {
                this.reportsDownloadValue = 1;
                this.emailForReports = this.emailForReports ? this.emailForReports : this.userEmail;
            } else {
                this.reportsDownloadValue = 0;
                this.emailForReports = '';
            }
            this.analyticsV2Service.getSearchSummaryChartDownload(this.searchClients, this.range, this.internalUser, csv, this.reportsDownloadValue, this.emailForReports, this.selectedConversionType, this.isEcosystemSelected, this.ecoSystem.uid).then(result => {
                this.range.csv = 0;
                var toastOptions: ToastOptions = {
                    title: "Report created!",
                    msg: result.message,
                    showClose: true,
                    timeout: 3000,
                    theme: 'default'
                };
                this.toastyService.success(toastOptions);
            });
        } else {
            this.range.csv = 0;
            this.averageTotalSearches = 0;
            this.averageTotalClicks = 0;
            this.averageTotalAvg = 0;
            this.clickPositionHistogram = undefined;
            let body = {
                from: this.range.from,
                to: this.range.to,
                internalUser: this.internalUser
            }
            if (this.searchClients.uid !== '') {
                body["uid"] = this.searchClients.uid
            }
            // Fetching report specific user metric values from the map
            const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Average Click Position");
            if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
                body["userMetricsFilters"] = {
                    [this.userMetricVariable]: userMetricObject.userMetricValues
                };
            }
            this.analyticsV2Service.requestAnalyticsRoute('/overview/averageClickPositionChart', 'post', body, '')
                .pipe(takeUntil(this.destroy$))
                .subscribe(result => {
                    if (result.data) {
                        this.averageTotalSearches = result.data.totalSearches ? result.data.totalSearches : 0;
                        this.averageTotalClicks = result.data.totalClicks ? result.data.totalClicks : 0;
                        this.averageTotalAvg = result.data.totalAvg ? result.data.totalAvg : 0;
                        let finalResult: any = {};
                        let averagerResult = result.data.result;
                        let averageResultChart: any = {};
                        averageResultChart = {};
                        averageResultChart.title = "Average";
                        averageResultChart.xAxis = {};
                        averageResultChart.xAxis.title = "Date";
                        averageResultChart.xAxis.format = "Date";
                        averageResultChart.xAxis.values = [];
                        averageResultChart.yAxis = {};
                        averageResultChart.yAxis.title = "Count";
                        averageResultChart.series = [];
                        averageResultChart.tooltip = [];

                        averageResultChart.xAxis.values = averagerResult.average.map(con => {
                            if (result.data.format == 'day') {
                                let dateParts = con.created.split("-");
                                let dateObject = dateParts[2] + '/' + dateParts[1];
                                con.created = dateObject;
                            }
                            return con.created;
                        });
                        averageResultChart.series[0] = {};
                        averageResultChart.series[0].name = 'average';
                        averageResultChart.series[0].data = [];
                        averageResultChart.series[0].type = 'lines';
                        for (let i = 0; i < Object.keys(averagerResult).length; i++) {
                            // averageResultChart.series[0].data.push(parseFloat(averagerResult.average[i].count));
                            if (Object.keys(averagerResult)[i] == 'average') {
                                averagerResult.average.map(b => {
                                    averageResultChart.series[0].data.push(b.count);
                                });
                            }

                            averageResultChart.tooltip[i] = {};
                            if (Object.keys(averagerResult)[i] == 'average') {
                                averageResultChart.tooltip[i].name = "Average Click Position";
                            } else if (Object.keys(averagerResult)[i] == 'sucessfull_searches') {
                                averageResultChart.tooltip[i].name = "Successful Searches";
                            } else {
                                averageResultChart.tooltip[i].name = "Clicks";
                            }
                            //averageResultChart.tooltip[i].name = Object.keys(averagerResult)[i];
                            averageResultChart.tooltip[i].data = [];
                            averageResultChart.tooltip[i].type = 'lines';
                            averagerResult[Object.keys(averagerResult)[i]].map(b => {
                                averageResultChart.tooltip[i].data.push(b.count);
                            });
                        }
                        this.clickPositionHistogram = averageResultChart;
                    }
                });
        }
    }



    sortSearchSubReport(col, reportType) {

        if (reportType == 'allSucessfulSearches') {
            this.sortingSearchSubReport = col
            this.getFiltersForQueriesCopy.sortType = this.getFiltersForQueriesCopy.sortType === 'desc' ? 'asc' : 'desc'
            this.PostRequestForData(this.getFiltersForQueriesCopy.search_term, this.getFiltersForQueriesCopy.searchCount, this.getFiltersForQueriesCopy);
        }
        // if(type == 'AllSearchesSubreport'){
        //     this.selectedIndexTabForAllSearches = 1
        // }else if(type == 'TopSuccessfulSearchesSubReport'){
        //     this.selectedIndex1Tab = 2
        // }

    }



    toggleViewStateSubreport(type) {
        if (type == 'AllSearchesSubreport') {
            this.selectedIndexTabForAllSearches = 0
        } else if (type == 'TopSuccessfulSearchesSubReport') {
            this.selectedIndex1Tab = 0
        }
    }



    changeSortOrderMainReport(col, sortType, reportType, label, value) {

        if (reportType == 'AllSearches') {
            if (this.isClusteringEnabled && col === 'Queries') {
                this.sortingFieldAllSearches = 'Queries';
                this.AllSearchesObj.sortingField = 'Queries';
            } else {
                this.sortingFieldAllSearches = col;
                this.AllSearchesObj.sortingField = col;
        }
        this.AllSearchesObj.sortType = this.AllSearchesObj.sortType == 'asc' ? 'desc' : 'asc';
        this.getTopSearches();
        } else if (reportType == 'allSucessfulSearches') {
            this.sortingFieldSuccessFullSearches = col
            this.allSuccessfullSearchesObj.sortingField = col
            this.allSuccessfullSearchesObj.sortType = this.allSuccessfullSearchesObj.sortType == 'asc' ? 'desc' : 'asc';

            this.getTopSuccessfulSearches();

        } else if (reportType == 'topSearchesWithNoClicksSearchReport') {
            this.topSearchesWithNoClicksObj.sortType = this.topSearchesWithNoClicksObj.sortType == 'asc' ? 'desc' : 'asc';
            this.sortingFieldTopSearchesWithNoClicksSearchReport = col
            this.topSearchesWithNoClicksObj.sortingField = col


            this.getTopSearchesWithNoClick(0)
        } else if (reportType == 'topSearchesWithNoresult') {
            this.withNoResultFillters.sortType = this.withNoResultFillters.sortType == 'asc' ? 'desc' : 'asc';
            this.sortingFieldTopSearchesWithNoResult = col
            this.withNoResultFillters.sortingField = col

            this.getTopSearchesWithNoResult(0)
        } else if (reportType == 'sessionFillter') {

            if (this.sessionFilters.sortType == 'asc') {
                this.sessionFilters.sortType = 'desc';
            } else {
                this.sessionFilters.sortType = 'asc'
            }

            this.sortingFieldNoResultSessionReport = col
            this.sessionFilters.sortingField = col

            this.getSessionFilterService();

        } else if( reportType == 'AdvertisementSorting'){
            this.advertisementSortingButton = true;
            if (this.advertisementFiltersOnInitialLoad.sortType == 'desc') {
                this.advertisementFiltersOnInitialLoad.sortType = 'asc';
                this.advertisementFiltersOnInitialLoad.pageNo = 1;
            } else {
                this.advertisementFiltersOnInitialLoad.sortType = 'desc';
                this.advertisementFiltersOnInitialLoad.pageNo = 1;
            }
            this.getAdvertisements();
        }



    }
    convertDateTimezone(Date: any, TimeZone: String) {
        let areaOffset: number = this.getOffset(TimeZone.substring(4, 10))
        Date.setMinutes(Date.getMinutes() + areaOffset)
        return Date
    }
    getOffset(offsetString: String): number {
        var hours: number = Number(offsetString.substring(1, 3))
        if (offsetString.slice(-2) === '30') {
            hours = hours + (0.5)
        }
        if (offsetString.charAt(0) === '-') {
            hours = hours * -1
        }
        return hours * 60
    }

    openTextSearchForallSearchReport(reportType, label, value) {
        if (reportType == 'AllSearches') {

            this.searchFilterAllSearchesReport.map((item, index) => {
                if (item.label == label) {
                    this.searchFilterAllSearchesReport[index].filled = value
                }
            });
        } else if (reportType == 'allSucessfulSearches') {

            this.allSucessfulSearches.map((item, index) => {
                if (item.label == label) {
                    this.allSucessfulSearches[index].filled = value
                }
            });
        } else if (reportType == 'topSearchesWithNoClicksSearchReport') {
            this.topSearchesWithNoClicksSearch.map((item, index) => {
                if (item.label == label) {
                    this.topSearchesWithNoClicksSearch[index].filled = value
                }
            });
        } else if (reportType == 'topSearchesWithNoresult') {
            this.topSearchesWithNoresult.map((item, index) => {
                if (item.label == label) {
                    this.topSearchesWithNoresult[index].filled = value
                }
            });
        } else if (reportType == 'sessionFillter') {
            this.sessionFillter.map((item, index) => {
                if (item.label == label) {
                    this.sessionFillter[index].filled = value
                }
            });
        } else if (reportType == 'searchQueryInClusterFilter') {
          this.searchQueryInClusterFilter.map((item, index) => {
              if (item.label == label) {
                  this.searchQueryInClusterFilter[index].filled = value
              }
          });
        } else if(reportType == 'AdvertismentTextFillter'){
            this.advertisementFilter.map((item, index) => {
                if (item.label == label) {
                     this.advertisementFilter[index].filled = value;
                }
            });
        }
        else if(reportType == 'searchUnifyGPTFeedback'){
            this.searchUnifyGPTFeedback.forEach((item,index)=>{
                if (item.label == label) {
                    this.searchUnifyGPTFeedback[index].filled = value;
               }
            })

        }

    }


    textSearAllSearchReport(label, value, reportType) {

        if (reportType == 'AllSearches') {
            this.AllSearchesObj.searchQuery = value.trim();

            if (this.AllSearchesObj.searchQuery) {
                this.AllSearchesObj.currentPage = 1
                this.getTopSearches();
            }
        } else if (reportType == 'allSucessfulSearches') {
            this.allSuccessfullSearchesObj.searchQuery = value.trim();
            if (this.allSuccessfullSearchesObj.searchQuery) {
                this.allSuccessfullSearchesObj.currentPage = 1
                this.getTopSuccessfulSearches();
            }

        } else if (reportType == 'topSearchesWithNoClicksSearchReport') {
            this.topSearchesWithNoClicksObj.searchQuery = value.trim();
            if (this.topSearchesWithNoClicksObj.searchQuery) {
                this.topSearchesWithNoClicksObj.currentPage = 1

                this.getTopSearchesWithNoClick(0)
            }
        } else if (reportType == 'topSearchesWithNoresult') {
            this.withNoResultFillters.searchQuery = value.trim();
            this.withNoResultFillters.currentPage = 1
            if (this.withNoResultFillters.searchQuery) {

                this.getTopSearchesWithNoResult(0)
            }
        } else if (reportType == 'sessionFillter') {

            this.sessionFillter.map((item) => {
                if (item.label === label) {
                    item.value = value.trim();
                    this.sessionFilters[label.replace(' ', '').toLowerCase()] = value.trim()
                }
            })
            this.sessionFilters.offset = 1;
            this.getSessionFilterService();

        } else if (reportType == 'searchQueryInClusterFilter') {
          this.searchQueryInClusterFilter.map((item) => {
            if (item.label === label) {
                item.value = value.trim();
                this.searchGroupFilters[label.replace(' ', '').toLowerCase()] = value.trim()
            }
          });
          this.searchGroupFilters.offset = 1;
          this.PostRequestForGroupData(this.searchGroupFilters);
        } else if(reportType == 'GPTFeedbackSearchFilter'){
            this.advertisementFiltersOnInitialLoad.pageNo = 1
            value = value.trim();
            this.advertisementSearchedQuery = value;
            if(value !=''){
                this.advertisementFilter.map((item)=>{
                    this.getAdvertisements();
                })
            }
            
        }

    }

    textSearAllSearchReportClearValue(col, reportType) {
        if (reportType == 'AllSearches') {
            // this.searchFilterAllSearchesReport.value = ''
            this.AllSearchesObj.searchQuery = ''
            this.getTopSearches();
        } else if (reportType == 'allSucessfulSearches') {
            // this.allSucessfulSearches.value = '';
            this.allSuccessfullSearchesObj.searchQuery = ''
            this.getTopSuccessfulSearches();

        } else if (reportType == 'topSearchesWithNoClicksSearchReport') {
            this.topSearchesWithNoClicksObj.searchQuery = '';
            this.getTopSearchesWithNoClick(0)

        } else if (reportType == 'topSearchesWithNoresult') {
            this.withNoResultFillters.searchQuery = '';
            this.getTopSearchesWithNoResult(0)

        } else if (reportType == 'sessionFillter') {
            this.sessionFillter.map((item, index) => {
                if (item.label == col) {
                    this.sessionFillter[index].value = ''
                    this.sessionFilters[col.replace(' ', '').toLowerCase()] = ''
                }
            });
            this.getSessionFilterService();

        } else if (reportType == 'searchQueryInClusterFilter') {
          this.searchQueryInClusterFilter.map((item, index) => {
            if (item.label == col) {
                this.searchQueryInClusterFilter[index].value = ''
                this.searchGroupFilters[col.replace(' ', '').toLowerCase()] = ''
            }
          });
          this.PostRequestForGroupData(this.searchGroupFilters);
        } else if(reportType == 'AdvertismentTextFillter'){
            this.advertisementSearchedQuery = '';
            this.getAdvertisements();
        } else if (reportType == 'groupingToggleReset') {
            this.AllSearchesObj.searchQuery = '';
            this.getTopSearches();
            this.allSuccessfullSearchesObj.searchQuery = '';
            this.getTopSuccessfulSearches();
            this.topSearchesWithNoClicksObj.searchQuery = '';
            this.getTopSearchesWithNoClick(0);
            this.withNoResultFillters.searchQuery = '';
            this.getTopSearchesWithNoResult(0);
            this.sessionFillter.map((item, index) => {
                if (item.label == col) {
                    this.sessionFillter[index].value = '';
                    this.sessionFilters[col.replace(' ', '').toLowerCase()] = '';
                }
            });
            this.getSessionFilterService();
            this.searchQueryInClusterFilter.map((item, index) => {
                if (item.label == col) {
                    this.searchQueryInClusterFilter[index].value = '';
                    this.searchGroupFilters[col.replace(' ', '').toLowerCase()] = '';
                }
            });
            this.PostRequestForGroupData(this.searchGroupFilters);
        }

    }

    getSearchFeedback(currentPage?: any) {
        this.PageRatingService.getSearchFeedbkIsEnabled({ uid: this.searchClients.uid }).then(result1 => {
            if (result1.status && result1.status == 200) {
                this.textFeedBackToggle = result1.feedBackEnabled;
            }
            let body = {
                from: this.range.from,
                to: this.range.to,
                internalUser: this.internalUser,
                offset: currentPage
            }
            if (this.searchClients.uid !== '') {
                body["uid"] = this.searchClients.uid
            }
            // Fetching report specific user metric values from the map
            const userMetricObject: { userMetricValues: string[], updateReport: any } | undefined = this.userMetricsFilters.get("Search Experience Feedback");
            if (userMetricObject && userMetricObject.userMetricValues && userMetricObject.userMetricValues.length > 0) {
                body["userMetricsFilters"] = {
                    [this.userMetricVariable]: userMetricObject.userMetricValues
                };
            }
            this.analyticsV2Service.requestAnalyticsRoute('/overview/searchFeedback', 'post', body, '')
                .pipe(takeUntil(this.destroy$))
                .subscribe(result => {
                    this.searchFeedbkData = [];
                    this.searchFeedbkData.isEmpty = false;
                    if (result && result.data && result.data.searchFeedbacks && result.data.searchFeedbacks.length) {
                        this.searchFeedbkData = result.data.searchFeedbacks;
                        this.searchFeedbkData['count'] = result.data.total;
                        this.searchFeedbkData.currentPage = currentPage;
                        this.searchFeedbkData.isEmpty = false;
                    }
                    else {
                        this.searchFeedbkData.isEmpty = true;
                        this.searchFeedbkData.push('empty');
                    }
                });
        });
    }

    showMoreSearchFeedbk(i, showLess) {
        if (this.searchFeedbkData && this.searchFeedbkData.length && this.searchFeedbkData[i]) {
            this.searchFeedbkData[i]['showLess'] = showLess;
        }
    }

    pageChangeAllSearchReport(event, reportType) {
        if (reportType == 'AllSearches') {
            this.AllSearchesObj.currentPage = event
            this.getTopSearches();
        } else if (reportType == 'allSucessfulSearches') {
            this.allSuccessfullSearchesObj.currentPage = event
            this.getTopSuccessfulSearches();

        } else if (reportType == 'topSearchesWithNoClicksSearchReport') {
            this.topSearchesWithNoClicksObj.currentPage = event
            this.getTopSearchesWithNoClick(0)

        }
        else if (reportType == 'allSearcheswithNoResult') {
            this.withNoResultFillters.currentPage = event
            this.getTopSearchesWithNoResult(0);

        }
        else if (reportType == 'allSearchesSubReport') {
            this.getFiltersForQueriesCopy.offset = event

            this.PostRequestForData(this.getFiltersForQueriesCopy.search_term, this.getFiltersForQueriesCopy.searchCount, this.getFiltersForQueriesCopy);

        } else if (['allSearchesClusterReport', 'allSearchesSuccessfullClustersReport', 'allSearchesWithNoClickClustersReport', 'allSearchesWithNoResultClustersReport'].includes(reportType)) {
            this.searchGroupFilters.offset = event
            this.PostRequestForGroupData(this.searchGroupFilters);
        } else if (['allSearchesSessionReport', 'allSearchessuccessfullSessionReport', 'allSearchesWithNoClickSessionReport', 'allSearchesWithNoResultSessionReport'].includes(reportType)) {
            this.sessionFilters.offset = event
            this.getSessionFilterService();
        }

    }
    @HostListener('window:scroll')
    scrollDownEvents() {
        if (!document.getElementsByClassName('cdk-global-scrollblock')[0]) {
            this.isTopbarShow = this.analyticsV2Service.gotoHeaderTop();
        }
        var daterangepickerClass = document.getElementsByClassName("daterangepicker")[0] as HTMLElement;
        if (daterangepickerClass) {
            daterangepickerClass.style.display = "none"   //To hide the element.
        }

    }

    contentFeedbackModal(){
        this.readRatingFromLocalStorage();
    }

    filterChange(type, val){
        if(type==='parent'){
            this.FeedbackquerySelected = val;
            if(this.lastSelectedParentFilter !== val){
                if(this.FeedbackquerySelected === 'all'){
                    this.Feedbackevent = 'most_recent'
                }
                else if(this.FeedbackquerySelected === 'votes'){
                    this.Feedbackevent = 'count'
                }
                else{
                    this.Feedbackevent = this.sampleArray;
                }
            }
            this.lastSelectedParentFilter = this.FeedbackquerySelected;
        }
        else if(type==='child'){
            this.Feedbackevent = val;
        }
        else if(type === 'childStar'){
            if(val=== 'all'){
                if(Array.isArray(this.Feedbackevent) && this.Feedbackevent.length === 5){
                    this.Feedbackevent = [];
                    this.pageRatingTitles = [];
                    this.pageRatingTitles.isEmpty = true;
                    this.pageRatingTitles.push('empty');
                }
                else{
                    this.Feedbackevent = this.sampleArray;
                }
            }
            else{
                if(!this.Feedbackevent.includes(val)){
                    this.Feedbackevent.push(val);
                }
                else{
                    this.Feedbackevent = this.Feedbackevent.filter(i=>i!==val);
                }
            }
        }
        /** Comment below line when calling apply filter from apply button directly*/
        this.applyFeedbackFilter();
    }

    private totalLikes: any;
    private totalDislikes: any;
    private totalFeedback: any;
    private currentPageNo: number = 1;
    private searchQuery: any = "";
    private reactionFilterType: any = "all";
    private feedbackLoader: boolean = false;
    extractText(html: string): string {
        if (!html) return '';
        const tmp = document.createElement('DIV');
        tmp.innerHTML = html;
        return tmp.textContent;
      }
    getResponseFeedback() {
        this.feedbackLoader = true;
        var limit = 10;
        this.GPTfeedbackData = [];
        this.analyticsV2Service.responseFeedback(this.searchClients.uid, this.range.from, this.range.to, limit, this.currentPageNo, this.reactionFilterType, this.searchQuery, this.internalUser).then((feedbackData) => {
            this.GPTfeedbackData = feedbackData.data.feedback;
            this.GPTfeedbackData = this.GPTfeedbackData.map((item: any) => {
                return {
                  ...item,
                  llm_response: this.extractText(item.llm_response)
                };
              });
            this.totalFeedback = feedbackData.data.total;     
            this.feedbackLoader = false;               
        })
    }

    setPaginationForFeedback(pageNo){
        this.currentPageNo = pageNo;
        this.getResponseFeedback();
    }

    getFeedbackForQueryString() {
        this.getResponseFeedback();
    }
    clearUserInput() {
        this.searchQuery = "";
        this.getResponseFeedback();
    }

    radioChange(event) {
        this.currentPageNo = 1;
        this.reactionFilterType = event.value;
        localStorage.setItem('reactionFilterType', event.value);
        this.getResponseFeedback();
    }

    getAllLikesDislikes() {
        this.totalLikes = 0;
        this.totalDislikes = 0;
        let limit;
        let reactionFilterType = 'all';
        this.analyticsV2Service.responseFeedback(this.searchClients.uid, this.range.from, this.range.to, limit, this.currentPageNo, reactionFilterType, this.searchQuery, this.internalUser).then((feedbackData) => {
            this.totalLikes = feedbackData && feedbackData.data && feedbackData.data.feedback.map(item => {if(item.reaction == 0) {return item.reaction;}}).filter(i => i !== undefined).length;
            this.totalDislikes = feedbackData && feedbackData.data && feedbackData.data.feedback.map(item => {if(item.reaction == 1) {return item.reaction;}}).filter(i => i !== undefined).length;               
        })
    }
 
    compareByUid(client1: any, client2: any): boolean {
    return client1 && client2 ? client1.uid === client2.uid : client1 === client2;
    }

    expandedIndex: number | null = null;

    toggleExpand(index: number): void {
      this.expandedIndex = this.expandedIndex === index ? null : index;
    }


}
