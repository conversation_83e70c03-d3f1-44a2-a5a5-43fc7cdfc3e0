<ng2-toasty></ng2-toasty>
<mat-progress-bar class="allign-top" mode="indeterminate" *ngIf="allGraphLoaded == false"></mat-progress-bar>
<div class="ad-heading-fixed">
  <div class="topHeading" [ngClass]="{'ad-heading-fixed-top' : isTopbarShow}">
    <div class="header-1 display-flex">
      <div class="header-2 margin-TB-auto-LR-0">
        <div class="heading-source">
          <span class="Search-Analytics">Search Analytics</span>
          <span class="definition">View search data filtered by users and search clients.</span>
        </div>
      </div>
      <div class="analytics-topbar">
        <div class="form-group-head">
          <div class="form-group analytics-header-block">

            <div class="analytics-header-row ad_analytics-list-item">
              <mat-form-field
              class="analytics-header-field"
              (click)="openPanel()">
                <mat-select #select panelClass="matRole test-sc-analytics" [ngModel]="platform" (selectionChange)="changeClient(select.value)"
                  placeholder="Search Client / Ecosystem" [compareWith]="compareByUid">
                  <mat-accordion class="scroll">
                    <mat-expansion-panel #first class="analytics-dropdown mat-elevation-z0 scPanel" [ngClass]="isEcosystemSelected === false && 'scPanelExpand'" (opened)="sourcePlatformEco?.length > 0 && changeEcoPanel()" (closed)="changeScPanel()">
                      <mat-expansion-panel-header style="font-size:10px; color:#919191; font-weight:semibold;">
                        Search Clients
                      </mat-expansion-panel-header>
                      <span style="overflow: auto !important; max-height: 100px !important;">
                        <ng-container *ngFor="let client of sourcePlatformSc; ; let index = index">
                          <ng-container *ngIf="client.ab_test_parent == null">
                            <mat-option [value]="client" matTooltip="{{client.name}}"
                              matTooltipPosition="below" aria-label="Button that displays a tooltip in various positions">
                              {{client.name.length> 20 ? client.name.substring(0,20) + '...' : client.name }}
                              <button *ngIf="haveDropdown[index]" type="button" class="border-0 bg-transparent ml-2 toggle-sc" (click)="$event.stopPropagation(); toggleExpand(index)">
                                <app-svg [ngClass]="expandedIndex === index ? 'toggle-child' : '' " name='analyticsIcon'></app-svg>
                              </button>
                            </mat-option>
                            <div class="child-options" [ngClass]="expandedIndex === index ? 'd-block' : 'd-none' ">
                              <!-- A/B testing clonned SC -->
                              <ng-container *ngFor="let childClient of childSC">
                                <mat-option [value]="childClient" matTooltip="{{childClient.searchTerm}}" matTooltipPosition="below" class="su__text-ellipses"
                                    *ngIf="childClient.ab_test_parent == client.uid">
                                    {{ childClient.searchTerm }}
                                </mat-option>
                              </ng-container>
                            </div>
                          </ng-container>
                        </ng-container>
                      </span>
                    </mat-expansion-panel>
                    <mat-divider style="margin: 0 5.5px 0 18.5px !important;" *ngIf = "sourcePlatformEco?.length>0"></mat-divider>
                    <mat-expansion-panel #second class="analytics-dropdown mat-elevation-z0 ecoPanel" [ngClass]="isEcosystemSelected === true && 'ecoPanelExpand'" *ngIf = "sourcePlatformEco?.length>0" (opened)="changeScPanel()" (closed)="changeEcoPanel()">
                      <mat-expansion-panel-header style="font-size:10px; color:#919191; font-weight:semibold;">
                        Ecosystems
                      </mat-expansion-panel-header>
                      <span style="overflow: auto !important; max-height: 100px !important;">
                        <mat-option *ngFor="let eco of sourcePlatformEco" [value]="eco" matTooltip="{{eco.name}}">
                          {{eco.name.length> 20 ? eco.name.substring(0,20) + '...' : eco.name }}
                        </mat-option>
                      </span>
                    </mat-expansion-panel>
                    <mat-divider style="margin: 0 5.5px 0 18.5px !important;"></mat-divider>
                  </mat-accordion>
                </mat-select>
              </mat-form-field>
              <div class="su__sc-pin-analytics">
                <svg (click)="preSelectedSCPin(false);" *ngIf = "!pinStatus" matTooltip="Pin default search client" matTooltipPosition="below" aria-label="Button that displays a tooltip in various positions" xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" fill="#8D8D8D" width="18px" height="18px">
                    <g><rect fill="none" height="24" width="24"/></g>
                    <g><path d="M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z" fill-rule="evenodd"/></g>
                </svg>
                <svg (click)="preSelectedSCPin(true);" *ngIf= "pinStatus" matTooltip="Unpin default search client" matTooltipPosition="below" aria-label="Button that displays a tooltip in various positions" xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" fill="#56C5FF" width="18px" height="18px">
                    <g><rect fill="none" height="24" width="24"/></g>
                    <g><path d="M16,9V4l1,0c0.55,0,1-0.45,1-1v0c0-0.55-0.45-1-1-1H7C6.45,2,6,2.45,6,3v0 c0,0.55,0.45,1,1,1l1,0v5c0,1.66-1.34,3-3,3h0v2h5.97v7l1,1l1-1v-7H19v-2h0C17.34,12,16,10.66,16,9z" fill-rule="evenodd"/></g>
                </svg>
            </div>
            </div>



            <div class="analytics-header-row">
              <mat-form-field
              class="analytics-header-field">
                <mat-select [(ngModel)]="internalUser" [disabled]="!(emailTrackingAddonInstalled && externalEnabled)"
                  (selectionChange)="changeUser()" placeholder="Select User">
                  <mat-option value="all">All</mat-option>
                  <mat-option value="internal">Internal User</mat-option>
                  <mat-option value="external">External User</mat-option>
                  <mat-option value="externalOnly">
                    External Only
                    <i
                      class="fas fa-info-circle tool-tip-info"
                      mat-raised-button
                      matTooltipClass="externalOnlyTooltip"
                      matTooltip="It will exclude any sessions or data that have internal user activities"
                      aria-label="Button that displays a tooltip when focused or hovered over"></i>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
           
            <div class="analytics-header-row">
              <mat-form-field class="analytics-header-field">
                <input type="text" matInput name="daterangeInput" id="select-range" placeholder="Date range"
                  class="pull-right form-control date-input" daterangepicker [options]='daterangepickerOptions'
                  (selected)="selectedDate($event, daterange)" style="float:left;">
                  <span class="analytics-header-calendar">
                    <svg height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0V0z" fill="none"/><path class="ad-dark-fill" d="M19 4h-1V3c0-.55-.45-1-1-1s-1 .45-1 1v1H8V3c0-.55-.45-1-1-1s-1 .45-1 1v1H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 15c0 .55-.45 1-1 1H6c-.55 0-1-.45-1-1V9h14v10zM7 11h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z" fill="#6b6b6b" /></svg>
                  </span>
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="sectionDiv colubridae19-sectionDiv ad_darkmode-bg4" [ngClass]="{'ad-heading-space' : isTopbarShow}">
  <div class="analyticsV2-overview" id="searchAnalytics">
    <nav mat-tab-nav-bar>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/overview">
        Overview
      </a>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/conversions">
        Conversions
      </a>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/content-gap-analysis">
        Content Gap Analysis
      </a>
      <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/leadership">
        Leadership Dashboard
      </a>
      <!-- *ngIf="showCommunity  && false" -->
      <!-- <a *ngIf="communityHelperAddonStatus"  mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/analytics-v2/community-helper-analytics">
        Community Helper Analytics
      </a> -->
    </nav>
  </div>
  <div class="sectionMainDiv darkmode" *ngIf="selectedIndex===0">
    <div class="Expand">
      <app-email-and-download-reports *ngIf="tileData"
        [searchClients]="searchClients"
        [range]="range"
        [internalUser]="internalUser"
        [tab]="OverviewTab.tabName"
        [reportName]="OverviewTab.Reports[0]"
        [hideDownload]="true"
        [isEcosystemSelected]="isEcosystemSelected"
        [ecoSystem] = "ecoSystem"
        [AllUserEmails] = "AllUserEmails"
        [userMetricVariable]="userMetricVariable"
        [userMetricsFilters]="userMetricsFilters"
      >
      </app-email-and-download-reports>
      <app-user-metric-filter
        *ngIf="userMetricEnabled"
        style="float: right;"
        [userMetricLabel]="userMetricLabel"
        [userMetricVariable]="userMetricVariable"
        [userId]="userUniqueId"
        [email]="userEmail"
        [uid]="searchClients.uid"
        [internalUser]="internalUser"
        [from]="range.from"
        [to]="range.to"
        [reportName]="'Tile Data'"
        [userMetricURL]="['/overview/tileData']"
        [body]="{
          from: range.from,
          to: range.to,
          internalUser: internalUser,
          uid: searchClients.uid
        }"
        (userMetricEvent)="sendUserMetricValues($event)"
      ></app-user-metric-filter>
      <!-- Collapse Icon Starts -->
      <svg class="su-sub" width="24" height="24" viewBox="0 0 24 24" *ngIf="!showHide" (click)="showHide = true"
        data-toggle="collapse" data-target="#cards-overview-8" style="float: right;cursor: pointer;">
        <defs>
          <clipPath id="clip-Substract-11">
            <rect width="24" height="24" />
          </clipPath>
        </defs>
        <g id="Substract-11" clip-path="url(#clip-Substract-11)">
          <g id="Group_1754" data-name="Group 1754" transform="translate(12051 8643)">
            <g id="outline-add_box-24px_3_" data-name="outline-add_box-24px (3)" transform="translate(-12051 -8643)">
              <path id="Path_1329" data-name="Path 1329" d="M0,0H24V24H0Z" fill="none" />
              <path id="Path_1330" data-name="Path 1330" d="M13,13h4V11H7v2h6Z" fill="#58c0fe" />
            </g>
          </g>
        </g>
      </svg>
      <!-- Collapse Icon Ends -->

      <!-- Expand Icon Starts -->
      <svg class="su-add" width="24" height="24" viewBox="0 0 24 24" *ngIf="showHide" (click)="showHide = false"
        data-toggle="collapse" data-target="#cards-overview-8" style="float: right;cursor: pointer;">
        <defs>
          <clipPath id="clip-Add-11">
            <rect width="24" height="24" />
          </clipPath>
        </defs>
        <g id="Add-11" clip-path="url(#clip-Add-11)">
          <g id="Group_1753" data-name="Group 1753" transform="translate(11958 8644)">
            <g id="outline-add_box-24px_3_" data-name="outline-add_box-24px (3)" transform="translate(-11958 -8644)">
              <path id="Path_1331" data-name="Path 1331" d="M0,0H24V24H0Z" fill="none" />
              <path id="Path_1332" data-name="Path 1332" d="M11,17h2V13h4V11H13V7H11v4H7v2h4Z" fill="#59bffe" />
            </g>
          </g>
        </g>
      </svg>
      <!-- Expand Icon Ends -->
    </div>

    <div id="cards-overview-8" class="collapse show">
      <div class="cards-8">
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-1 one">
          <div class="analytics-card analytics-first" [ngClass]="tileDataMetrics1Timeout ? 'analytics-card-greyed' : ''">
            <div class="analytics-section-heading-height">
              <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                <div class="float-left colubridae19Colors">
                  All Sessions
                  <i class="fas fa-info-circle tool-tip-info" mat-raised-button 
                  *ngIf="searchClients.uid != '' && emailTrackingEnabled"
                    matTooltip="{{tileDataMetrics1.emptyEmailSessionCount || 0 | thousandStuff :1 }} Empty Email Sessions"
                    aria-label="Button that displays a tooltip when focused or hovered over"></i>
                </div>
                <div *ngIf="!tileDataMetrics1Timeout" class="float-right">
                  <i class="analytics-card-image-1"></i>
                </div>
                <div *ngIf="tileDataMetrics1Timeout" class="float-right test" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics1()">
                  <i class="analytics-reload-image-1"></i>
                </div>
              </div>
            </div>
            <div class="analytics-count">
              <!-- Loader -->
              <div *ngIf="tileDataMetrics1Loading === true"
                    style="text-align: center;
                          height: 72px;
                          display: flex;
                          justify-content: center;
                          align-items: center;"
              >
                <div class="spinner">
                  <div class="bounce1"></div>
                  <div class="bounce2"></div>
                  <div class="bounce3"></div>
                </div>
              </div>
              <!-- Loader Ends -->
              <div *ngIf="tileDataMetrics1Loading === false" class="analytics-card-count" matTooltip="{{ tileDataMetrics1.visitors || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                {{tileDataMetrics1.visitors || 0 | thousandStuff :1 }}
              </div>
              <div class="extra-info-div" *ngIf="tileDataMetrics1Loading === false">
                  <div *ngIf="searchClients.uid != '' && emailTrackingEnabled" class="float-left colubridae19Colors tile-extra-info" matTooltip="{{ tileDataMetrics1.uniqueUsersByEmail || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-tooltip" >
                    Unique users by Email {{tileDataMetrics1.uniqueUsersByEmail || 0 | thousandStuff :1}}
                  </div>
                  <div class="float-left colubridae19Colors tile-extra-info" matTooltip="{{ tileDataMetrics1.uniqueUsersByDevice || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-tooltip" >
                    Unique users by Device {{tileDataMetrics1.uniqueUsersByDevice || 0 | thousandStuff :1}}
                  </div>
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer extra-info-footer-padding-tile1">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-6"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 one">
          <div class="analytics-card analytics-third" [ngClass]="tileDataMetrics1Timeout ? 'analytics-card-greyed' : ''">
            <div class="analytics-section-heading-height">
              <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                <div class="float-left colubridae19Colors">
                  Search Sessions
                </div>
                <div *ngIf="!tileDataMetrics1Timeout" class="float-right">
                  <i class="analytics-card-image-3"></i>
                </div>
                <div *ngIf="tileDataMetrics1Timeout" class="float-right" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics1()">
                  <i class="analytics-reload-image-1"></i>
                </div>
              </div>
            </div>
            <div class="analytics-count">
              <!-- Loader -->
              <div *ngIf="tileDataMetrics1Loading === true"
                    style="text-align: center;
                          height: 44px;
                          display: flex;
                          justify-content: center;
                          align-items: center;"
              >
                <div class="spinner">
                  <div class="bounce1"></div>
                  <div class="bounce2"></div>
                  <div class="bounce3"></div>
                </div>
              </div>
              <!-- Loader Ends -->
              <div *ngIf="tileDataMetrics1Loading === false"  class="analytics-card-count analytics-count-second " matTooltip="{{ tileDataMetrics1.searchUsers || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                {{tileDataMetrics1.searchUsers || 0 | thousandStuff :1 }}
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-3"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 two">
          <div class="analytics-card analytics-second" [ngClass]="tileDataMetrics2Timeout ? 'analytics-card-greyed' : ''">
            <div (mouseenter)="isEcosystemSelected && tileDataMetrics2.clicks != 0 ? showClicksStats() : null" (mouseleave)="isEcosystemSelected && tileDataMetrics2.clicks != 0 ? showClicksStats() : null">
              <div class="analytics-section-heading-height">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Clicks
                    <a href="https://docs.searchunify.com/Content/Search-Analytics/Search-Analytics.htm"
                      target="_blank">
                      <i class="fas fa-info-circle tool-tip-info" mat-raised-button
                        matTooltip="It counts multiple clicks made on the same search"
                        aria-label="Button that displays a tooltip when focused or hovered over"></i></a>
                  </div>
                  <div *ngIf="!tileDataMetrics2Timeout" class="float-right">
                    <i class="analytics-card-image-2"></i>
                  </div>
                  <div *ngIf="tileDataMetrics2Timeout" class="float-right" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics2()">
                    <i class="analytics-reload-image-1"></i>
                  </div>
                </div>
              </div>
              <div class="analytics-count">
                <!-- Loader -->
                <div *ngIf="tileDataMetrics2Loading === true"
                      style="text-align: center;
                            height: 52px;
                            display: flex;
                            justify-content: center;
                            align-items: center;"
                >
                  <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                  </div>
                </div>
                <!-- Loader Ends -->
                <div class="analytics-card-count extra-info-count-padding" *ngIf="tileDataMetrics2Loading === false && (!isEcosystemSelected || tileDataMetrics2.clicks == 0)" matTooltip="{{ tileDataMetrics2.clicks || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-upper-tooltip">
                  {{tileDataMetrics2.clicks || 0 | thousandStuff :1 }}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataMetrics2.clicks != 0 && tileDataMetrics2Loading === false" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataMetrics2.clicks || 0 | thousandStuff :1 }}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showClicksData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total clicks
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataMetrics2.clicks}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileData" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.clicks || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.clicks" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.clicks || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div *ngIf="tileDataMetrics2Loading === false">
              <div class="float-left colubridae19Colors tile-extra-info" matTooltip="{{ tileDataMetrics2.clickedSessions || 0  }}"  matTooltipPosition="below" matTooltipClass="tile-extra-info-tooltip" >
                Successful Sessions {{tileDataMetrics2.clickedSessions || 0 | thousandStuff :1}}
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer extra-info-footer-padding">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-2"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column left-1 left two">
          <div class="analytics-card analytics-fourth" [ngClass]="tileDataMetrics2Timeout ? 'analytics-card-greyed' : ''">
            <div (mouseenter)="isEcosystemSelected && tileDataMetrics2.caseCount != 0 ? showCasesStats() : null" (mouseleave)="isEcosystemSelected && tileDataMetrics2.caseCount != 0 ? showCasesStats() : null">
              <div class="analytics-section-heading-height">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Cases Logged
                  </div>
                  <div *ngIf="!tileDataMetrics2Timeout" class="float-right">
                    <i class="analytics-card-image-4"></i>
                  </div>
                  <div *ngIf="tileDataMetrics2Timeout" class="float-right" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics2()">
                    <i class="analytics-reload-image-1"></i>
                  </div>
                </div>
              </div>
              <div class="analytics-count">
                <!-- Loader -->
                <div *ngIf="tileDataMetrics2Loading === true"
                      style="text-align: center;
                            height: 44px;
                            display: flex;
                            justify-content: center;
                            align-items: center;"
                >
                  <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                  </div>
                </div>
                <!-- Loader Ends -->
                <div class="analytics-card-count " *ngIf="tileDataMetrics2Loading === false && (!isEcosystemSelected || tileDataMetrics2.caseCount == 0)" matTooltip="{{ tileDataMetrics2.caseCount || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataMetrics2.caseCount || 0 | thousandStuff :1 }}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataMetrics2.caseCount != 0 && tileDataMetrics2Loading === false" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataMetrics2.caseCount || 0 | thousandStuff :1 }}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showCasesData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total cases logged
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataMetrics2.caseCount}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileData" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.case_count || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.case_count" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.case_count || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-4"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="cards-8">
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-1 one">
          <div class="analytics-card analytics-first"  [ngClass]="tileDataMetrics2Timeout ? 'analytics-card-greyed' : ''">
            <div (mouseenter)="isEcosystemSelected && tileDataMetrics2.searches != 0 ? showSearchesStats() : null" (mouseleave)="isEcosystemSelected && tileDataMetrics2.searches != 0 ? showSearchesStats() : null">
              <div class="analytics-section-heading-height">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Total Searches
                  </div>
                  <div *ngIf="!tileDataMetrics2Timeout" class="float-right">
                    <i class="analytics-card-image-5"></i>
                  </div>
                  <div *ngIf="tileDataMetrics2Timeout" class="float-right" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics2()">
                    <i class="analytics-reload-image-1"></i>
                  </div>
                </div>
              </div>
              <div class="analytics-count">
                <!-- Loader -->
                <div *ngIf="tileDataMetrics2Loading === true"
                      style="text-align: center;
                            height: 44px;
                            display: flex;
                            justify-content: center;
                            align-items: center;"
                >
                  <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                  </div>
                </div>
                <!-- Loader Ends -->
                <div class="analytics-card-count " *ngIf="tileDataMetrics2Loading === false && (!isEcosystemSelected || tileDataMetrics2.searches == 0)" matTooltip="{{ tileDataMetrics2.searches || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataMetrics2.searches || 0 | thousandStuff :1 }}
                </div>

                  <!-- For search clients splitting in an ecosystem -->
                  <div *ngIf="isEcosystemSelected && tileDataMetrics2.searches != 0 && tileDataMetrics2Loading === false" class="analytics-card-count extra-info-count-padding" >
                    <span>{{tileDataMetrics2.searches  || 0 | thousandStuff :1 }}</span>
                  </div>
                  <div *ngIf="isEcosystemSelected && showSearchesData" class="splitOnHover">
                    <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                      Total searches
                      <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataMetrics2.searches}}</div>
                    </div>
                    <div class="splitOnHoverContent">
                      <div *ngFor="let data of splitTileData" style="display: flex; justify-content: space-between;">
                        
                        <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                          <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.searches || 0}}</span>
                        </div>
                        
                        <div class="sc-in-eco" *ngIf = "data.is_archive && data.searches" style="margin-bottom: 16px;">
                          <div class="searchClient" >
                            <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                            <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.searches || 0}}</span>
                          </div>
                          <div class="deleteInfo">
                            <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                          </div>
                        </div>

                      </div>
                    </div>
                  </div>
                  <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-5"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 two">
          <div class="analytics-card analytics-second" [ngClass]="tileDataMetrics2Timeout ? 'analytics-card-greyed' : ''">
            <div (mouseenter)="isEcosystemSelected && tileDataMetrics2.withResult != 0 ? showWithResultStats() : null" (mouseleave)="isEcosystemSelected && tileDataMetrics2.withResult != 0 ? showWithResultStats() : null">
              <div class="analytics-section-heading-height">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Searches With Results
                  </div>
                  <div *ngIf="!tileDataMetrics2Timeout" class="float-right">
                    <i class="analytics-card-image-6"></i>
                  </div>
                  <div *ngIf="tileDataMetrics2Timeout" class="float-right" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics2()">
                    <i class="analytics-reload-image-1"></i>
                  </div>
                </div>
              </div>
              <div class="analytics-count">
                <!-- Loader -->
                <div *ngIf="tileDataMetrics2Loading === true"
                      style="text-align: center;
                            height: 44px;
                            display: flex;
                            justify-content: center;
                            align-items: center;"
                >
                  <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                  </div>
                </div>
                <!-- Loader Ends -->
                <div class="analytics-card-count " *ngIf="tileDataMetrics2Loading === false && (!isEcosystemSelected || tileDataMetrics2.withResult == 0)" matTooltip="{{ tileDataMetrics2.withResult || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataMetrics2.withResult || 0 | thousandStuff :1 }}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataMetrics2.withResult != 0 && tileDataMetrics2Loading === false" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataMetrics2.withResult  || 0 | thousandStuff :1 }}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showWithResultData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total searches with results
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataMetrics2.withResult}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileData" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.withResult || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.withResult" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.withResult || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-6"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column right-2 left-2 right-3 left-3 one">
          <div class="analytics-card analytics-third" [ngClass]="tileDataMetrics2Timeout ? 'analytics-card-greyed' : ''">
            <div (mouseenter)="isEcosystemSelected && tileDataMetrics2.withoutResult != 0 ? showWithoutResultStats() : null" (mouseleave)="isEcosystemSelected && tileDataMetrics2.withoutResult != 0 ? showWithoutResultStats() : null">
              <div class="analytics-section-heading-height">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Searches With No Result
                  </div>
                  <div *ngIf="!tileDataMetrics2Timeout" class="float-right">
                    <i class="analytics-card-image-7"></i>
                  </div>
                  <div *ngIf="tileDataMetrics2Timeout" class="float-right" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics2()">
                    <i class="analytics-reload-image-1"></i>
                  </div>
                </div>
              </div>
              <div class="analytics-count">
                <!-- Loader -->
                <div *ngIf="tileDataMetrics2Loading === true"
                      style="text-align: center;
                            height: 44px;
                            display: flex;
                            justify-content: center;
                            align-items: center;"
                >
                  <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                  </div>
                </div>
                <!-- Loader Ends -->
                <div class="analytics-card-count analytics-count-second " *ngIf="tileDataMetrics2Loading === false && (!isEcosystemSelected || tileDataMetrics2.withoutResult == 0)" matTooltip="{{ tileDataMetrics2.withoutResult || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataMetrics2.withoutResult || 0 | thousandStuff :1 }}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataMetrics2.withoutResult != 0 && tileDataMetrics2Loading === false" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataMetrics2.withoutResult  || 0 | thousandStuff :1 }}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showWithoutResultData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total searches with no result
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataMetrics2.withoutResult}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileData" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.withoutResult || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.withoutResult" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.withoutResult || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-7"></i>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-12 col-md-12 col-sm-12 col-xs-12 column left-1 left two">
          <div class="analytics-card analytics-fourth" [ngClass]="tileDataMetrics2Timeout ? 'analytics-card-greyed' : ''">
            <div (mouseenter)="isEcosystemSelected && tileDataMetrics2.uniqueSearches != 0 ? showUniqueSearchesStats() : null" (mouseleave)="isEcosystemSelected && tileDataMetrics2.uniqueSearches != 0 ? showUniqueSearchesStats() : null">
              <div class="analytics-section-heading-height">
                <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-section">
                  <div class="float-left colubridae19Colors">
                    Unique Searches
                    <a href="https://docs.searchunify.com/Content/Search-Analytics/Search-Analytics.htm"
                      target="_blank">
                      <i class="fas fa-info-circle tool-tip-info" mat-raised-button
                        matTooltip="It excludes searches which are performed with facet selection"
                        aria-label="Button that displays a tooltip when focused or hovered over"></i></a>
                  </div>
                  <div *ngIf="!tileDataMetrics2Timeout" class="float-right">
                    <i class="analytics-card-image-8"></i>
                  </div>
                  <div *ngIf="tileDataMetrics2Timeout" class="float-right" matTooltip="Reload Report" matTooltipClass="tile-data-reload-tooltip" (click)="reloadTileDataMetrics2()">
                    <i class="analytics-reload-image-1"></i>
                  </div>
                </div>
              </div>
              <div class="analytics-count">
                <!-- Loader -->
                <div *ngIf="tileDataMetrics2Loading === true" 
                      style="text-align: center;
                            height: 44px;
                            display: flex;
                            justify-content: center;
                            align-items: center;"
                >
                  <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                  </div>
                </div>
                <!-- Loader Ends -->
                <div class="analytics-card-count " *ngIf="tileDataMetrics2Loading === false && (!isEcosystemSelected || tileDataMetrics2.uniqueSearches == 0)" matTooltip="{{ tileDataMetrics2.uniqueSearches || 0  }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-tiles">
                  {{tileDataMetrics2.uniqueSearches || 0 | thousandStuff :1 }}
                </div>

                <!-- For search clients splitting in an ecosystem -->
                <div *ngIf="isEcosystemSelected && tileDataMetrics2.uniqueSearches != 0 && tileDataMetrics2Loading === false" class="analytics-card-count extra-info-count-padding" >
                  <span>{{tileDataMetrics2.uniqueSearches  || 0 | thousandStuff :1 }}</span>
                </div>
                <div *ngIf="isEcosystemSelected && showUniqueSearchesData" class="splitOnHover">
                  <div style="color: #4F4E66; font: normal normal normal 10px/13px Montserrat;">
                    Total unique searches
                    <div style="font: normal normal 600 18px/22px Montserrat; color: #41405A;">{{tileDataMetrics2.uniqueSearches}}</div>
                  </div>
                  <div class="splitOnHoverContent">
                    <div *ngFor="let data of splitTileData" style="display: flex; justify-content: space-between;">
                      
                      <div class="searchClient" *ngIf = "!data.is_archive" style="margin-bottom: 16px;">
                        <span class="splitSearchClient" >{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                        <span  style="color: #707070; font: normal normal 600 14px/18px Montserrat;">{{data.uniqueSearches || 0}}</span>
                      </div>
                      
                      <div class="sc-in-eco" *ngIf = "data.is_archive && data.uniqueSearches" style="margin-bottom: 16px;">
                        <div class="searchClient" >
                          <span class="splitSearchClient" style="text-decoration:line-through;">{{data.name.length> 20 ? data.name.substring(0,20) + '...' : data.name}}</span>
                          <span style="color:#707070; font: normal normal 600 14px/18px Montserrat; margin-right: -2px;">{{data.uniqueSearches || 0}}</span>
                        </div>
                        <div class="deleteInfo" >
                          <span style="color:#FFB018; font: normal normal normal 11px/14px Montserrat;">No longer exists in Ecosystem</span>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
                <!-- Search Clients Splitting Code ends -->
              </div>
            </div>
            <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer">
              <i class="col-xl-12 col-lg-12 col-md-12 col-xs-12 col-sm-12 analytics-footer-img-8"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="sectionMainDiv" *ngIf="selectedIndex===0">
    <div *ngIf="Analyticstab1 == false && allGraphLoaded == true"
      class="col-md-12 col-sm-12 col-lg-12 col-xl-12 col-xs-12 Enable-analytics">
      <div *ngIf="!this.isEcosystemSelected" (click)="enableAnalyticsSettings()" class="enable-analytics-settings">
        <img *ngIf="!this.isEcosystemSelected" src="assets/img/Enable-analytics-under-search-client.svg" class="Enable-analytics-img">
        <h6 *ngIf="!this.isEcosystemSelected" class="Enable-analytics-under-search-client">Enable analytics under search client</h6>
      </div>
      <div *ngIf="this.isEcosystemSelected === true" (click)="enableAnalyticsSettingsEco()" class="enable-analytics-settings">
        <img *ngIf="this.isEcosystemSelected === true" src="assets/img/Group 5508.svg" class="Enable-analytics-img" style="margin-top: 13px;">
      </div>
    </div>
  </div>

  <div class="sectionMainDiv" *ngIf="selectedIndex===0">
    <div class="col-xl-12" *ngIf="reportSettings?.length>0 && reportSettings[24] && reportSettings[24].is_enabled"
      style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
      <div id="searchHitCount" class="card card-block" style="padding:0;">
        <div class="analytics-section-heading">
          {{reportSettings[24].label}}
          <app-email-and-download-reports *ngIf="searchHistogram"
            [searchClients]="searchClients"
            [range]="range"
            [internalUser]="internalUser"
            [tab]="OverviewTab.tabName"
            [selectedConversionType]="selectedConversionType"
            [reportName]="OverviewTab.Reports[1]"
            [hideDownload]="true"
            [isEcosystemSelected]="isEcosystemSelected"
            [ecoSystem] = "ecoSystem"
            [AllUserEmails] = "AllUserEmails"
            [userMetricVariable]="userMetricVariable"
            [userMetricsFilters]="userMetricsFilters"
          >
          </app-email-and-download-reports>
          <app-user-metric-filter
            *ngIf="userMetricEnabled"
            [userMetricLabel]="userMetricLabel"
            [userMetricVariable]="userMetricVariable"
            [userId]="userUniqueId"
            [email]="userEmail"
            [uid]="searchClients.uid"
            [internalUser]="internalUser"
            [from]="range.from"
            [to]="range.to"
            [reportName]="'Search Summary'"
            [userMetricURL]="['/overview/summaryChart']"
            [body]="{
              from: range.from,
              to: range.to,
              internalUser: internalUser,
              uid: searchClients.uid
            }"
            (userMetricEvent)="sendUserMetricValues($event)"
          ></app-user-metric-filter>
        </div>
        <div class="app-line-conversion-chart">
          <div class="search-summary-conversion">
            <mat-form-field>
              <mat-select [(value)]="selected" (selectionChange)="changeConversionType($event)">
                <mat-option value="allSearches">All Searches</mat-option>
                <mat-option value="uniqueSearches">Unique searches</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <span class="search-conversion">Conversion : <span
          class="search-conversion-rate">{{conversionValue}}%</span></span>
          <span class="search-conversion"
          style="padding-left: 15px;" 
          matTooltip="Conversion rate equals All Searches With Clicks divided by All Searches.
           It changes with the Date Range filter."
          matTooltipPosition="right"
          matTooltipClass="allow-cr"
          matTooltipClass= "searchSummerytooltip"
          aria-label="Button that displays a tooltip in various positions">
          <svg xmlns="http://www.w3.org/2000/svg" width="16.026" height="16.026" viewBox="0 0 16.026 16.026"><defs><style>.a{fill:none;}.b{fill:#635e7e;}</style></defs><path class="a" d="M0,0H16.026V16.026H0Z"/><path class="b" d="M8.677,2a6.677,6.677,0,1,0,6.677,6.677A6.68,6.68,0,0,0,8.677,2Zm0,10.016a.67.67,0,0,1-.668-.668V8.677a.668.668,0,0,1,1.335,0v2.671A.67.67,0,0,1,8.677,12.016Zm.668-5.342H8.01V5.339H9.345Z" transform="translate(-0.665 -0.665)"/></svg>
          </span>

          <!-- Loader -->
          <div *ngIf="!searchHistogram" style="text-align: center; padding: 20px;">
            <div class="spinner">
              <div class="bounce1"></div>
              <div class="bounce2"></div>
              <div class="bounce3"></div>
            </div>
          </div>

          <app-line-conversion-chart *ngIf="searchHistogram" [zoom1]="false" [chartType]="true"
            [colors]="['#7291f8' , '#f4961c' , '#5bbdfe']" [inputConversionData]="searchHistogram" [analyticsV2]="true">
          </app-line-conversion-chart>
        </div>

      </div>
    </div>
  </div>

  <div class="sectionMainDiv" *ngIf="selectedIndex===0">
    <div id="{{dynamicDiv}}" class="sectionMainDiv-inner-box"
      *ngIf="reportSettings?.length>0 && reportSettings[25] && reportSettings[25].is_enabled">
      <div id="SearchClassifications1">
        <div class="analytics-section-heading">
          <!-- Search Classifications -->
          Search Report

          <div style="float: right; margin-right: 4px;">
            <label class="group-searches-label" (click)="onToggleChange();textSearAllSearchReportClearValue('', 'groupingToggleReset');">
              Group Searches
            </label>
            <ng-container *ngIf="isClusteringEnabled; else falseSvg">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="14" viewBox="0 0 24 14" style="cursor: pointer;" (click)="onToggleChange()">
                <defs>
                </defs>
                <g id="on">
                  <g id="Group_4187" data-name="Group 4187">
                    <g id="Group_1479" data-name="Group 1479">
                      <g id="Rectangle_1527" data-name="Rectangle 1527" fill="none" stroke="#56c6ff" stroke-width="1">
                        <rect width="24" height="14" rx="7" stroke="none"/>
                        <rect x="0.5" y="0.5" width="23" height="13" rx="6.5" fill="none"/>
                      </g>
                      <circle id="Ellipse_86" data-name="Ellipse 86" cx="5" cy="5" r="5" transform="translate(12 2.001)" fill="#56c6ff"/>
                    </g>
                  </g>
                </g>
              </svg>
            </ng-container>
            <ng-template #falseSvg>
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="14" viewBox="0 0 24 14" style="cursor: pointer;" (click)="onToggleChange()">
                <defs>
                </defs>
                <g id="Off">
                  <g id="Group_4187" data-name="Group 4187">
                    <g id="Group_1479" data-name="Group 1479">
                      <g id="Rectangle_1527" data-name="Rectangle 1527" fill="none" stroke="#adb5bd" stroke-width="1">
                        <rect width="24" height="14" rx="7" stroke="none"/>
                        <rect x="0.5" y="0.5" width="23" height="13" rx="6.5" fill="none"/>
                      </g>
                      <circle id="Ellipse_86" data-name="Ellipse 86" cx="5" cy="5" r="5" transform="translate(2 2.001)" fill="#adb5bd"/>
                    </g>
                  </g>
                </g>
              </svg>
            </ng-template>
          </div>
        
        </div>
        <div *ngIf="reportSettings?.length>0 && reportSettings[25] && reportSettings[25].is_enabled"
          class="col-xl-12 search-classifications">
          <div class="card card-block card-1 Search-Classifications-card responsive">
            <mat-grid-list cols="4" rowHeight="507px" gutterSiz="15px">
              <mat-grid-tile>
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div class="analytics-section-header">
                    <i class="fa fa-line-chart" aria-hidden="true"></i>
                    <!-- Search Classifications -->
                    {{reportSettings[25].label}}
                  </div>
                  <div class="perfect">
                    <style>
                      .side-select-list {
                        cursor: pointer;
                      }

                      .side-select-list:hover {
                        background-color: white;
                      }

                      .side-select-list.active {
                        background-image: none;
                        background-color: white;
                        border-left: 3px solid #56c7ff;
                      }

                    </style>
                    <table class="table">
                      <tr>
                        <td [class.active]="activeReport == 0" class="side-select-list"
                          (click)="(activeReport!=0 && isReportUp) ? switchSearchReport(0) : 0;"
                          (keyup.enter)="(activeReport!=0 && isReportUp) ? switchSearchReport(0) : 0;">{{ isClusteringEnabled ? 'All Search Groups' : 'All Searches' }}</td>
                      </tr>
                      <tr style="display: none;">
                        <td [class.active]="activeReport == 1" class="side-select-list"
                          (click)="(activeReport!=1 && isReportUp) ? switchSearchReport(1) : 0 ;"
                          (keyup.enter)="(activeReport!=1 && isReportUp) ? switchSearchReport(1) : 0 ;">Converted
                          Searches
                        </td>
                      </tr>
                      <tr>
                        <td [class.active]="activeReport == 2" class="side-select-list"
                          (click)="(activeReport!=2 && isReportUp) ? switchSearchReport(2) : 0 ;"
                          (keyup.enter)="(activeReport!=2 && isReportUp) ? switchSearchReport(2) : 0 ;">{{ isClusteringEnabled ? 'Successful Search Groups' : 'Successful Searches' }}
                          </td>
                      </tr>
                      <tr>
                        <td [class.active]="activeReport == 3" class="side-select-list"
                          (click)="(activeReport!=3 && isReportUp) ? switchSearchReport(3) : 0 ;"
                          (keyup.enter)="(activeReport!=3 && isReportUp) ? switchSearchReport(3) : 0 ;">{{ isClusteringEnabled ? 'Search Groups with no Click' : 'Searches with no click' }}</td>
                      </tr>
                      <tr>
                        <td [class.active]="activeReport == 4" class="side-select-list"
                          (click)="(activeReport!=4 && isReportUp) ? switchSearchReport(4) : 0 ;"
                          (keyup.enter)="(activeReport!=4 && isReportUp) ? switchSearchReport(4) : 0 ;">{{ isClusteringEnabled ? 'Search Groups with no Result' : 'Searches with no result' }}</td>
                      </tr>
                    </table>
                  </div>
                </div>
              </mat-grid-tile>
              <mat-grid-tile colspan="3" style="box-shadow: rgba(0, 0, 0, 0.25) -5px 0px 10px 0px;">
                <div style="height: 507px; overflow:auto; width: 100%;">
                  <div *ngIf="activeReport == 0">
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <!-- All Searches -->
                      <mat-tab-group class="kcs-mat-tab" [(selectedIndex)]="selectedIndexTabForAllSearches">

                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'All Search Groups' : 'All Searches'}}

                            <app-email-and-download-reports *ngIf="topSearches && !topSearches.isEmpty"
                              [searchClients]="searchClients"
                              [range]="range"
                              [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName"
                              [reportName]="OverviewTab.Reports[2].Search_Report[0]"
                              [hideDownload]="true"
                              [searchReportFilters]='AllSearchesObj'
                              [isEcosystemSelected]="isEcosystemSelected"
                              [ecoSystem] = "ecoSystem"
                              [AllUserEmails] = "AllUserEmails"
                              [userMetricVariable]="userMetricVariable"
                              [userMetricsFilters]="userMetricsFilters"
                              [isClusteringEnabled]="isClusteringEnabled"
                            >
                            </app-email-and-download-reports>
                            <app-user-metric-filter
                              *ngIf="userMetricEnabled"
                              [userMetricLabel]="userMetricLabel"
                              [userMetricVariable]="userMetricVariable"
                              [userId]="userUniqueId"
                              [email]="userEmail"
                              [uid]="searchClients.uid"
                              [internalUser]="internalUser"
                              [from]="range.from"
                              [to]="range.to"
                              [reportName]="'Search Classifications (All Searches)'"
                              [userMetricURL]="['/overview/topSearches']"
                              [body]="{
                                from: range.from,
                                to: range.to,
                                internalUser: internalUser,
                                uid: searchClients.uid,
                                offset: 1,
                                limit: 50,
                                sortingField: 'Searches',
                                sortType: 'desc',
                                searchGrouping: isClusteringEnabled
                              }"
                              (userMetricEvent)="sendUserMetricValues($event)"
                            ></app-user-metric-filter>
                          </div>

                          <div *ngIf="topSearches" id="topSearches"
                            style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                            <table class="table table-su">
                              <thead class="t-head">
                                <th [ngStyle]="{ 'width': isClusteringEnabled ? '35%' : '40%' }" *ngFor="let filter of searchFilterAllSearchesReport">
                                  {{filter.label}} <span
                                    (click)="openTextSearchForallSearchReport('AllSearches', filter.label,!filter.filled);closeButtonToggleChange();"
                                    class="search-icons-th">&nbsp;</span>
                                  <!-- SearchBox -->
                                  <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                    <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                      class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                      (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'AllSearches')">
                                    <button class="srtableBoxclose" *ngIf="filter.filled"
                                      (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'AllSearches');">X</button>
                                  </div>

                                </th>

                                <th *ngFor="let col of AllSearchesColumns" style="font-size:14px;">
                                  {{col}}
                                  <span (click)="changeSortOrderMainReport(col, 'asc', 'AllSearches')"
                                    style="cursor: pointer;">
                                    <svg *ngIf="sortingFieldAllSearches !== col" xmlns="http://www.w3.org/2000/svg"
                                      width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .a {
                                            fill: none;
                                          }

                                          .b {
                                            fill: #707070;
                                          }
                                        </style>
                                      </defs>
                                      <path class="a" d="M0,0H16V16H0Z" />
                                      <path class="b"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                  <span *ngIf="sortingFieldAllSearches === col"
                                    (click)="changeSortOrderMainReport(col,'desc', 'AllSearches')"
                                    style="cursor: pointer;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .aa {
                                            fill: none;
                                          }

                                          .bb {
                                            fill: #53C6FF;
                                          }
                                        </style>
                                      </defs>
                                      <path class="aa" d="M0,0H16V16H0Z" />
                                      <path class="bb"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                </th>

                              </thead>
                              <ng-container *ngIf="!topSearches.isEmpty">

                                <tr
                                *ngFor="let d of topSearches | paginate: {itemsPerPage: AllSearchesObj.limit, currentPage: AllSearchesObj.currentPage, id: 'allSearchReport',totalItems: allSearchesCount};"
                                style="word-break: break-word;">
                                <!-- If Group Searches toggle is enabled -->
                                <td *ngIf="isClusteringEnabled">
                                  <span
                                    style="float: left; width: 75%; margin-right: 10px;"
                                    class="an_text_truncate"
                                    matTooltip="{{d[0]}}" 
                                    matTooltipPosition="below"
                                    matTooltipClass="an-tooltip-sessionReport"
                                  >
                                    {{ d[0] }}
                                  </span>
                                  <a
                                    href="JavaScript:void(0);"
                                    (click)="getSearchGroups(d[0], 1, d[3], 0)"
                                    matTooltip="View search queries" 
                                    matTooltipPosition="below"
                                    matTooltipClass="an-tooltip-groupSearch"
                                  >
                                    <svg id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24" xmlns="http://www.w3.org/2000/svg" width="18.251" height="12.444" viewBox="0 0 18.251 12.444">
                                      <path id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-2" data-name="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24" d="M49.125-790.045a3.6,3.6,0,0,0,2.644-1.089,3.6,3.6,0,0,0,1.089-2.644,3.6,3.6,0,0,0-1.089-2.644,3.6,3.6,0,0,0-2.644-1.089,3.6,3.6,0,0,0-2.644,1.089,3.6,3.6,0,0,0-1.089,2.644,3.6,3.6,0,0,0,1.089,2.644A3.6,3.6,0,0,0,49.125-790.045Zm0-1.493a2.16,2.16,0,0,1-1.587-.653,2.16,2.16,0,0,1-.653-1.587,2.16,2.16,0,0,1,.653-1.587,2.16,2.16,0,0,1,1.587-.653,2.16,2.16,0,0,1,1.587.653,2.16,2.16,0,0,1,.653,1.587,2.16,2.16,0,0,1-.653,1.587A2.16,2.16,0,0,1,49.125-791.538Zm0,3.982a9.6,9.6,0,0,1-5.517-1.69A9.609,9.609,0,0,1,40-793.778a9.609,9.609,0,0,1,3.609-4.532A9.6,9.6,0,0,1,49.125-800a9.6,9.6,0,0,1,5.517,1.69,9.609,9.609,0,0,1,3.609,4.532,9.609,9.609,0,0,1-3.609,4.532A9.6,9.6,0,0,1,49.125-787.556Z" transform="translate(-40 800)" fill="#afb2bc"/>
                                    </svg>                                    
                                  </a>
                                </td>

                                <!-- If Group Searches toggle is disabled -->
                                <td
                                  *ngIf="!isClusteringEnabled"
                                  matTooltip="{{ d[0] }}"
                                  matTooltipPosition="below"
                                  matTooltipClass="an-tooltip-sessionReport"
                                >
                                  <span class="an_text_truncate">{{d[0]}}</span>
                                </td>

                                <td *ngIf="isClusteringEnabled">{{d[4]}}</td>
                                <td>{{d[1]}}</td>
                                <td>
                                  <u>
                                    <a href="JavaScript:void(0);"><span
                                        (click)="getSessionFilter(d[0], 0, 'allSearchReport', d[2])"
                                        style="cursor: pointer;">{{d[2]}}</span>
                                    </a>
                                  </u>
                                </td>
                                <td>
                                  <u>
                                    <a href="JavaScript:void(0);"><span
                                        (click)="getFiltersForQueries(d[0], 1, d[3], 0)"
                                        style="cursor: pointer;">{{d[3]}}</span>
                                    </a>
                                  </u>
                                </td>

                                </tr>

                                <tr *ngIf="topSearches?.length != 0 && allSearchesCount > AllSearchesObj.limit"
                                  class="hover">
                                  <td colspan="100" style="text-align: right;">
                                    <pagination-controls id="allSearchReport"
                                      (pageChange)="pageChangeAllSearchReport($event, 'AllSearches')">
                                    </pagination-controls>
                                  </td>
                                </tr>

                              </ng-container>
                              <tr *ngIf="topSearches.isEmpty">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4" class="no-docs">
                                  {{ isClusteringEnabled ? 'No search groups to show' : 'No queries to show' }}
                                  <img class="doc-img">
                                </td>
                              </tr>
                              <tr *ngIf="topSearches?.length === 0">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4">
                                  <div style="text-align: center;">
                                    <div class="spinner">
                                      <div class="bounce1"></div>
                                      <div class="bounce2"></div>
                                      <div class="bounce3"></div>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                              <!-- *ngIf="sessionclickPosition?.length != 0" -->
                              <!-- <tr  class="hover">
                                <td colspan="100" style="text-align: right;">
                                  <pagination-controls id="sessionClickFirst" (pageChange)="sessionSort = true; getSessionClickPosition(0,$event, click)">
                                  </pagination-controls>
                                </td>
                              </tr> -->

                            </table>
                          </div>
                        </mat-tab>
                        <!-- ALl Searches Sub-report end-->

                        <mat-tab *ngIf="isClusteringEnabled">
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            All Search Groups

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="'Search Group Queries'"
                              [searchReportFilters]="searchGroupFilters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading Queries-Group">Search Queries under
                              <i>"{{searchGroupFilters.selectedSearchTerm}}"</i>
                              <span style="font-weight: normal; color: #43425D; display: block; margin-top: 10px;">
                                Number of Queries: {{searchGroupFilters.searchCount}}
                              </span>
                            </span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px; height: 40px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search group-searches">
                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="searchGroupFilter" style="width: 13%;">Sr.no</th>
                                  <th style="width: 80%;" *ngFor="let filter of searchQueryInClusterFilter">{{filter.label}} <span
                                      (click)="openTextSearchForallSearchReport('searchQueryInClusterFilter', filter.label, !filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'searchQueryInClusterFilter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value=''; textSearAllSearchReportClearValue(filter.label, 'searchQueryInClusterFilter');">X</button>
                                    </div>
                                  </th>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let row of searchGroupFilters.filters | paginate: {itemsPerPage: searchGroupFilters.limit, currentPage: searchGroupFilters.offset, id: 'allSearchesClusterReport',totalItems: searchGroupFilters.total};let i=index" 
                                  style="word-break: break-word;">
                                    <td class="sessionFilter" style="vertical-align: middle;"><span class="an_text_truncate">{{(i+1)+(searchGroupFilters.offset-1)*searchGroupFilters.limit}}</span></td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <span
                                        class="an_text_truncate"
                                        matTooltip="{{row.text_entered}}" 
                                        matTooltipPosition="below"
                                        matTooltipClass="an-tooltip-searchQueryInGroup"
                                      >{{row.text_entered}}</span>
                                    </td>
                                  </tr>

                                  <tr *ngIf="searchGroupFilters.state == 'shown' && searchGroupFilters.filters.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>
                                  <tr *ngIf="searchGroupFilters.filters?.length != 0 && searchGroupFilters.total > searchGroupFilters.limit"
                                    class="hover">
                                    <td  colspan="4"  style="text-align: right;">
                                      <pagination-controls id="allSearchesClusterReport"
                                        (pageChange)="pageChangeAllSearchReport($event, 'allSearchesClusterReport')">
                                      </pagination-controls>
                                    </td>
                                 </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </mat-tab>

                        <!-- Session Report -->
                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'All Search Groups' : 'All Searches'}}

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients"
                              [range]="range"
                              [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName"
                              [reportName]="OverviewTab.Reports[2].SearchReportSessions[0]"
                              [searchReportFilters]="sessionFilters"
                              [hideDownload]="true"
                              [isEcosystemSelected]="isEcosystemSelected"
                              [ecoSystem] = "ecoSystem"
                              [AllUserEmails] = "AllUserEmails"
                              [userMetricVariable]="userMetricVariable"
                              [userMetricsFilters]="userMetricsFilters"
                              [isClusteringEnabled]="isClusteringEnabled"
                            >
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading">{{sessionFilters.selectedSearchTerm}} <i>
                              (Number of sessions: {{sessionTotal}})</i></span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search">                                
                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="sessionFilter">Sr.no</th>
                                  <th style="width: 15%;" *ngFor="let filter of sessionFillter">{{filter.label}} <span
                                      (click)="openTextSearchForallSearchReport('sessionFillter', filter.label,!filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'sessionFillter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'sessionFillter');">X</button>
                                    </div>
                                  </th>
                                  <th *ngFor="let col of sessionFillterColumns" style="font-size:14px;width: 15%;">
                                    {{col}}
                                    <span (click)="changeSortOrderMainReport(col, 'asc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg *ngIf="sortingFieldNoResultSessionReport !== col"
                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .a {
                                              fill: none;
                                            }

                                            .b {
                                              fill: #707070;
                                            }
                                          </style>
                                        </defs>
                                        <path class="a" d="M0,0H16V16H0Z" />
                                        <path class="b"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                    <span *ngIf="sortingFieldNoResultSessionReport === col"
                                      (click)="changeSortOrderMainReport(col,'desc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .aa {
                                              fill: none;
                                            }

                                            .bb {
                                              fill: #53C6FF;
                                            }
                                          </style>
                                        </defs>
                                        <path class="aa" d="M0,0H16V16H0Z" />
                                        <path class="bb"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                  </th>

                                </thead>
                                <tbody>
                                  <tr *ngFor="let filters of sessionFilters.filters | paginate: {itemsPerPage: sessionFilters.limit, currentPage: sessionFilters.offset, id: 'allSearchesSessionReport',totalItems: sessionFilters.total};let i=index">
                                    <td class="sessionFilter" style="vertical-align: middle;">{{(i+1)+(sessionFilters.offset-1)*sessionFilters.limit}} </td>

                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <u>
                                        <a href="JavaScript:void(0);">
                                          <span style="cursor: pointer;"
                                            (click)="getSessionValuesDetails(filters.cookie, sessionFilters.selectedSearchTerm)">
                                            {{filters.cookie}}
                                          </span>
                                        </a>
                                      </u>
                                    </td>
                                    <td class="sessionFilter">{{filters.email}}</td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      {{filters.end_time | timeZone:userTimeZone:"contentSource"}}</td>
                                  </tr>

                                  <tr *ngIf="sessionFilters.state == 'shown' && sessionFilters.filters.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>

                                  <tr *ngIf="sessionFilters.filters?.length != 0 && sessionFilters.total > sessionFilters.limit"
                                      class="hover">
                                      <td  colspan="4"  style="text-align: right;">
                                        <pagination-controls id="allSearchesSessionReport"
                                          (pageChange)="pageChangeAllSearchReport($event, 'allSearchesSessionReport')">
                                        </pagination-controls>
                                      </td>
                                   </tr>

                                </tbody>
                              </table>
                            </div>
                          </div>                 
                        </mat-tab>
                        <!-- Session innser report ends -->

                        <!-- SubReport  clicks-->
                          <mat-tab class="allSearchesSubReportTab">
                            <div class="analytics-section-header">
                              <i class="fa fa-line-chart" aria-hidden="true"></i>
                              {{isClusteringEnabled ? 'All Search Groups' : 'All Searches'}}

                              <app-email-and-download-reports *ngIf="successfulSearches && !successfulSearches.isEmpty"
                                [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                                [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].Search_Report[4]"
                                [filterParameterSearchReport]="getFiltersForQueriesCopy" [hideDownload]="true"
                                [filterParameterSearchReportLabel]="'All_searches-Facet_Details'" 
                                [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                              </app-email-and-download-reports>
                            </div>

                            <div style="display: flex;padding: 10px;">
                              <span class="Detailed-Report-selected-heading">{{queryFilters.selectedSearchTerm}} <i>
                                (Total Searches: {{queryFilters.searchCount}})</i>
                              </span>
                              <button class="buttonSecondary backToReport" (click)="toggleViewState();"
                                style="float:right;width: 100px;">
                                <i class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                            </div>
                            <div class="su-topQueries">
                              <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                                <table class="table table-responsive top-search">
                                  <thead *ngIf="selectedIndex == 0" class="t-head display-table">
                                    <tr>
                                      <th style="width: 10% !important;">#</th>
                                      <th style="width: 20%;padding: 0px;">Facet Type</th>
                                      <th style="width: 20%;padding: 0px;">Facet Value</th>
                                      <th style="width: 20%;padding: 0px;">Advance Search</th>
                                      <th *ngFor="let col of searchSubReport" style="width: 20%; padding: 0px;">
                                        <span class="search-sub-report-head-sec">
                                          <span class="search-sub-report-head">{{col}}</span>
                                          <span (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                            style="cursor: pointer;">
                                            <svg *ngIf="sortingSearchSubReport !== col" xmlns="http://www.w3.org/2000/svg"
                                              width="16" height="16" viewBox="0 0 16 16">
                                              <defs>
                                                <style>
                                                  .a {
                                                    fill: none;
                                                  }

                                                  .b {
                                                    fill: #707070;
                                                  }
                                                </style>
                                              </defs>
                                              <path class="a" d="M0,0H16V16H0Z" />
                                              <path class="b"
                                                d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                                transform="translate(-1.903 -1.069)" />
                                            </svg>
                                          </span>
                                          <span *ngIf="sortingSearchSubReport === col"
                                            (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                            style="cursor: pointer;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                              viewBox="0 0 16 16">
                                              <defs>
                                                <style>
                                                  .aa {
                                                    fill: none;
                                                  }

                                                  .bb {
                                                    fill: #53C6FF;
                                                  }
                                                </style>
                                              </defs>
                                              <path class="aa" d="M0,0H16V16H0Z" />
                                              <path class="bb"
                                                d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                                transform="translate(-1.903 -1.069)" />
                                            </svg>
                                          </span>
                                          <span *ngIf="isClusteringEnabled" class="grouping-info-icon-header"
                                            style="padding-left: 5px;" 
                                            matTooltip="Counts searches done with filters and without any filters."
                                            matTooltipPosition="right"
                                            matTooltipClass="allow-cr"
                                            matTooltipClass= "groupingHeadertooltip"
                                            aria-label="Button that displays a tooltip in various positions">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16.026" height="16.026" viewBox="0 0 16.026 16.026"><defs><style>.a{fill:none;}.b{fill:#635e7e;}</style></defs><path class="a" d="M0,0H16.026V16.026H0Z"/><path class="b" d="M8.677,2a6.677,6.677,0,1,0,6.677,6.677A6.68,6.68,0,0,0,8.677,2Zm0,10.016a.67.67,0,0,1-.668-.668V8.677a.668.668,0,0,1,1.335,0v2.671A.67.67,0,0,1,8.677,12.016Zm.668-5.342H8.01V5.339H9.345Z" transform="translate(-0.665 -0.665)"/></svg>
                                          </span>
                                        </span>
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody class="display-table">

                                    <ng-container
                                      *ngFor="let row of queryFilters.filters | paginate: {itemsPerPage: getFiltersForQueriesCopy.limit, currentPage: getFiltersForQueriesCopy.offset, id: 'allSearchesSubReportPage',totalItems: queryFilters.total}; let i=index">
                                      <div class="display-table">
                                        <tr>
                                          <td [attr.rowspan]="row.filters.length-1"
                                            style="vertical-align: middle;width: 10% !important;">
                                            {{(i+1)+(getFiltersForQueriesCopy.offset-1)*getFiltersForQueriesCopy.limit}}
                                          </td>
                                          <td style="vertical-align: top;width: 40%; padding: 0;">
                                            <table class="filterBorder" width="100%"> 
                                              <tr *ngFor="let f of row.filters;let j=index">
                                                <td style="padding:12px 0px; width: 50%; text-align: left;">
                                                  <span class="filter-type-display">{{f.filterType}}</span>
                                                </td>
                                                <td class="filtertd" style="padding:12px 0px; width: 50%;">
                                                  <div class="filteralignment">
                                                    <div *ngFor="let sv of f.selectedValues;" class="filter" matTooltip="{{sv}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                                      <ng-container *ngIf="parseJson(sv) as parsed">
                                                        <ng-container *ngIf="parsed.min_value; else noMinValue">
                                                          <div>
                                                            Min: {{ parsed.min_value }}
                                                          </div>
                                                          <div>
                                                            Max: {{ parsed.max_value }}
                                                          </div>
                                                        </ng-container>
                                                      </ng-container>
                                                      <ng-template #noMinValue>
                                                        {{ sv }}
                                                      </ng-template>
                                                    </div>
                                                  </div>
                                                </td>
                                              </tr>
                                            </table> 
                                          </td>
                                          <td style="vertical-align: middle;width: 20%;padding: 12px 0px;"> 
                                            <div *ngIf ="row.exactphrase"  class="filter" matTooltip="{{row.exactphrase}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                              Exact Phrase: {{row.exactphrase}}
                                            </div>
                                            <div *ngIf ="row.withoneormore" class="filter" matTooltip="{{row.withoneormore}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                              With One or More: {{row.withoneormore}}
                                            </div>
                                            <div *ngIf ="row.withoutwords" class="filter" matTooltip="{{row.withoutwords}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                              Without the words: {{row.withoutwords}}
                                            </div>
                                            <div *ngIf ="row.exactphrase =='' && row.withoneormore=='' && row.withoutwords==''"  class="filter" matTooltip="Null" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                              Null
                                            </div>
                                          </td>
                                          <td [attr.rowspan]="row.filters.length-1"
                                            style="vertical-align: middle;width: 20%; padding: 0px;">{{row.count}} </td>
                                        </tr>

                                      </div>


                                    </ng-container>
                                    <tr
                                      *ngIf="queryFilters.filters?.length != 0 && queryFilters.total > getFiltersForQueriesCopy.limit"
                                      class="hover">
                                      <td style="text-align: right;">
                                        <pagination-controls id="allSearchesSubReportPage"
                                          (pageChange)="pageChangeAllSearchReport($event, 'allSearchesSubReport')">
                                        </pagination-controls>
                                      </td>
                                    </tr>



                                    <tr *ngIf="queryFilters.state == 'loading'">
                                      <td colspan="4">
                                        <div style="text-align: center;">
                                          <div class="spinner">
                                            <div class="bounce1"></div>
                                            <div class="bounce2"></div>
                                            <div class="bounce3"></div>
                                          </div>
                                        </div>
                                      </td>
                                    </tr>
                                    <tr *ngIf="queryFilters?.state == 'shown' && queryFilters.filters?.length == 0">
                                      <td colspan="4">All Searches performed without filters</td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </mat-tab>


                      </mat-tab-group>

                    </div>
                  </div>
                  <div *ngIf="activeReport == 1">
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <div class="analytics-section-header">
                        <i class="fa fa-line-chart" aria-hidden="true"></i>
                        Top Clicked Searches
                      </div>

                      <div *ngIf="convertedSearches" id="convertedSearches"
                        style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                        <table class="table table-su">
                          <thead class="t-head">
                            <th>Search Keyword</th>
                            <th>Count</th>
                          </thead>
                          <ng-container *ngIf="!convertedSearches.isEmpty">
                            <tr *ngFor="let d of convertedSearches" style="word-break: break-word;">
                              <td>{{d[0]}}</td>
                              <td>{{d[1]}}</td>
                            </tr>
                          </ng-container>
                          <tr *ngIf="convertedSearches?.isEmpty">
                            <td colspan="2" class="no-docs">
                              No queries to show.
                              <img class="doc-img">
                            </td>
                          </tr>
                          <tr *ngIf="convertedSearches?.length === 0">
                            <td colspan="2">
                              <div style="text-align: center;">
                                <div class="spinner">
                                  <div class="bounce1"></div>
                                  <div class="bounce2"></div>
                                  <div class="bounce3"></div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        </table>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="activeReport == 2">
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <mat-tab-group class="kcs-mat-tab" [(selectedIndex)]="selectedIndex1Tab">
                        <mat-tab>

                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Successful Search Groups' : 'Top Successful Searches'}}

                            <app-email-and-download-reports *ngIf="successfulSearches && !successfulSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].Search_Report[1]"
                              [hideDownload]="true" [searchReportFilters]="allSuccessfullSearchesObj" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                            <app-user-metric-filter
                              *ngIf="userMetricEnabled"
                              [userMetricLabel]="userMetricLabel"
                              [userMetricVariable]="userMetricVariable"
                              [userId]="userUniqueId"
                              [email]="userEmail"
                              [uid]="searchClients.uid"
                              [internalUser]="internalUser"
                              [from]="range.from"
                              [to]="range.to"
                              [reportName]="'Search Classifications (Successful Searches)'"
                              [userMetricURL]="['/overview/searchSessions']"
                              [body]="{
                                from: range.from,
                                to: range.to,
                                internalUser: internalUser,
                                uid: searchClients.uid,
                                offset: 1,
                                limit: 50,
                                sortingField: 'Searches',
                                sortType: 'desc',
                                searchGrouping: isClusteringEnabled
                              }"
                              (userMetricEvent)="sendUserMetricValues($event)"
                            ></app-user-metric-filter>
                          </div>


                          <div *ngIf="successfulSearches" id="successfulSearches"
                            style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                            <table class="table table-su">
                              <thead class="t-head">
                                <th [ngStyle]="{ 'width': isClusteringEnabled ? '35%' : '40%' }" *ngFor="let filter of allSucessfulSearches">{{filter.label}}
                                  <span
                                    (click)="openTextSearchForallSearchReport('allSucessfulSearches', filter.label,!filter.filled);closeButtonToggleChange();"
                                    class="search-icons-th">&nbsp;</span>
                                  <!-- SearchBox -->
                                  <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                    <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                      class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                      (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'allSucessfulSearches')">
                                    <button class="srtableBoxclose" *ngIf="filter.filled"
                                      (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'allSucessfulSearches');">X</button>
                                  </div>

                                </th>

                                <th *ngFor="let col of allSucessfulSearchesColumns" style="font-size:14px;">
                                  {{col}}
                                  <span (click)="changeSortOrderMainReport(col, 'asc', 'allSucessfulSearches')"
                                    style="cursor: pointer;">
                                    <svg *ngIf="sortingFieldSuccessFullSearches !== col"
                                      xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .a {
                                            fill: none;
                                          }

                                          .b {
                                            fill: #707070;
                                          }
                                        </style>
                                      </defs>
                                      <path class="a" d="M0,0H16V16H0Z" />
                                      <path class="b"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                  <span *ngIf="sortingFieldSuccessFullSearches === col"
                                    (click)="changeSortOrderMainReport(col,'desc', 'allSucessfulSearches')"
                                    style="cursor: pointer;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .aa {
                                            fill: none;
                                          }

                                          .bb {
                                            fill: #53C6FF;
                                          }
                                        </style>
                                      </defs>
                                      <path class="aa" d="M0,0H16V16H0Z" />
                                      <path class="bb"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                </th>
                              </thead>
                              <ng-container *ngIf="!successfulSearches.isEmpty">
                                <tr
                                  *ngFor="let d of successfulSearches | paginate: {itemsPerPage: allSuccessfullSearchesObj.limit, currentPage: allSuccessfullSearchesObj.currentPage, id: 'allSuccessfullSearchReport',totalItems: allSuccessfullSearchesObj.total};"
                                  style="word-break: break-word;">
                                  <!-- If Group Searches toggle is enabled -->
                                  <td *ngIf="isClusteringEnabled">
                                    <span
                                      style="float: left; width: 75%; margin-right: 10px;"
                                      class="an_text_truncate"
                                      matTooltip="{{d[0]}}" 
                                      matTooltipPosition="below"
                                      matTooltipClass="an-tooltip-sessionReport"
                                    >
                                      {{ d[0] }}
                                    </span>
                                    <a
                                      href="JavaScript:void(0);"
                                      (click)="getSearchGroups(d[0], 1, d[3], 2)"
                                      matTooltip="View search queries" 
                                      matTooltipPosition="below"
                                      matTooltipClass="an-tooltip-groupSearch"
                                    >
                                      <svg id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24" xmlns="http://www.w3.org/2000/svg" width="18"
                                        height="12" viewBox="0 0 18.251 12.444" style="margin-right: 25px;">
                                        <path id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-2"
                                          data-name="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24"
                                          d="M49.125-790.045a3.6,3.6,0,0,0,2.644-1.089,3.6,3.6,0,0,0,1.089-2.644,3.6,3.6,0,0,0-1.089-2.644,3.6,3.6,0,0,0-2.644-1.089,3.6,3.6,0,0,0-2.644,1.089,3.6,3.6,0,0,0-1.089,2.644,3.6,3.6,0,0,0,1.089,2.644A3.6,3.6,0,0,0,49.125-790.045Zm0-1.493a2.16,2.16,0,0,1-1.587-.653,2.16,2.16,0,0,1-.653-1.587,2.16,2.16,0,0,1,.653-1.587,2.16,2.16,0,0,1,1.587-.653,2.16,2.16,0,0,1,1.587.653,2.16,2.16,0,0,1,.653,1.587,2.16,2.16,0,0,1-.653,1.587A2.16,2.16,0,0,1,49.125-791.538Zm0,3.982a9.6,9.6,0,0,1-5.517-1.69A9.609,9.609,0,0,1,40-793.778a9.609,9.609,0,0,1,3.609-4.532A9.6,9.6,0,0,1,49.125-800a9.6,9.6,0,0,1,5.517,1.69,9.609,9.609,0,0,1,3.609,4.532,9.609,9.609,0,0,1-3.609,4.532A9.6,9.6,0,0,1,49.125-787.556Z"
                                          transform="translate(-40 800)" fill="#afb2bc" />
                                      </svg>
                                    </a>
                                  </td>

                                  <!-- If Group Searches toggle is disabled -->
                                  <td
                                    *ngIf="!isClusteringEnabled"
                                    matTooltip="{{ d[0] }}"
                                    matTooltipPosition="below"
                                    matTooltipClass="an-tooltip-sessionReport"
                                  >
                                    <span class="an_text_truncate">{{d[0]}}</span>
                                  </td>

                                  <td *ngIf="isClusteringEnabled">{{d[4]}}</td>
                                  <td>{{d[1]}}</td>
                                  <td>
                                    <u>
                                      <a href="JavaScript:void(0);"><span
                                          (click)="getSessionFilter(d[0], 0, 'allSucessfulSearches',d[2])" 
                                          style="cursor: pointer;">{{d[2]}}</span>
                                      </a>
                                    </u>
                                  </td>
                                  <td>
                                    <u>
                                      <a href="JavaScript:void(0);"><span
                                          (click)="getFiltersForQueries(d[0], 1, d[3], 2)"
                                          style="cursor: pointer;">{{d[3]}}</span>
                                      </a>
                                    </u>
                                  </td>
                                </tr>

                                <tr
                                  *ngIf="successfulSearches?.length != 0 && allSuccessfullSearchesObj.total > allSuccessfullSearchesObj.limit"
                                  class="hover">
                                  <td colspan="100" style="text-align: right;">
                                    <pagination-controls id="allSuccessfullSearchReport"
                                      (pageChange)="pageChangeAllSearchReport($event, 'allSucessfulSearches')">
                                    </pagination-controls>
                                  </td>
                                </tr>

                              </ng-container>
                              <tr *ngIf="successfulSearches?.isEmpty">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4" class="no-docs">
                                  {{ isClusteringEnabled ? 'No search groups to show' : 'No queries to show' }}
                                  <img class="doc-img">
                                </td>
                              </tr>
                              <tr *ngIf="successfulSearches?.length === 0">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4">
                                  <div style="text-align: center;">
                                    <div class="spinner">
                                      <div class="bounce1"></div>
                                      <div class="bounce2"></div>
                                      <div class="bounce3"></div>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            </table>
                          </div>
                        </mat-tab>

                        <mat-tab *ngIf="isClusteringEnabled">
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            Successful Search Groups

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="'Search Group Queries'"
                              [searchReportFilters]="searchGroupFilters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading Queries-Group">Search Queries under
                              <i>"{{searchGroupFilters.selectedSearchTerm}}"</i>
                              <span style="font-weight: normal; color: #43425D; display: block; margin-top: 10px;">
                                Number of Queries: {{searchGroupFilters.searchCount}}
                              </span>
                            </span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px; height: 40px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search group-searches">                                
                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="searchGroupFilter" style="width: 13%;">Sr.no</th>
                                  <th style="width: 80%;" *ngFor="let filter of searchQueryInClusterFilter">{{filter.label}} 
                                    <span
                                      (click)="openTextSearchForallSearchReport('searchQueryInClusterFilter', filter.label, !filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'searchQueryInClusterFilter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value=''; textSearAllSearchReportClearValue(filter.label, 'searchQueryInClusterFilter');">X</button>
                                    </div>
                                  </th>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let row of searchGroupFilters.filters | paginate: {itemsPerPage: searchGroupFilters.limit, currentPage: searchGroupFilters.offset, id: 'allSearchesSuccessfullClustersReport',totalItems: searchGroupFilters.total};let i=index">
                                    <td class="sessionFilter" style="vertical-align: middle;"><span class="an_text_truncate">{{(i+1)+(searchGroupFilters.offset-1)*searchGroupFilters.limit}}</span></td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <span
                                        class="an_text_truncate"
                                        matTooltip="{{row.text_entered}}" 
                                        matTooltipPosition="below"
                                        matTooltipClass="an-tooltip-searchQueryInGroup"
                                      >{{row.text_entered}}</span>
                                    </td>
                                  </tr>

                                  <tr *ngIf="searchGroupFilters.state == 'shown' && searchGroupFilters.filters.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>

                                  <tr *ngIf="searchGroupFilters.filters?.length != 0 && searchGroupFilters.total > searchGroupFilters.limit"
                                      class="hover">
                                      <td  colspan="4"  style="text-align: right;">
                                        <pagination-controls id="allSearchesSuccessfullClustersReport"
                                          (pageChange)="pageChangeAllSearchReport($event, 'allSearchesSuccessfullClustersReport')">
                                        </pagination-controls>
                                      </td>
                                   </tr>

                                </tbody>
                              </table>
                            </div>
                          </div> 
                        </mat-tab>

                        <!-- Session Report -->
                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Successful Search Groups' : 'Top Successful Searches'}}

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].SearchReportSessions[1]"
                              [searchReportFilters]="sessionFilters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading">{{sessionFilters.selectedSearchTerm}} <i>
                              (Number of sessions: {{sessionTotal}})</i></span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search">

                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="sessionFilter">Sr.no</th>
                                  <th style="width: 15%;" *ngFor="let filter of sessionFillter">{{filter.label}} <span
                                      (click)="openTextSearchForallSearchReport('sessionFillter', filter.label,!filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'sessionFillter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'sessionFillter');">X</button>
                                    </div>

                                  </th>

                                  <th *ngFor="let col of sessionFillterColumns" style="font-size:14px;width: 15%;">
                                    {{col}}
                                    <span (click)="changeSortOrderMainReport(col, 'asc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg *ngIf="sortingFieldNoResultSessionReport !== col"
                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .a {
                                              fill: none;
                                            }

                                            .b {
                                              fill: #707070;
                                            }
                                          </style>
                                        </defs>
                                        <path class="a" d="M0,0H16V16H0Z" />
                                        <path class="b"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                    <span *ngIf="sortingFieldNoResultSessionReport === col"
                                      (click)="changeSortOrderMainReport(col,'desc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .aa {
                                              fill: none;
                                            }

                                            .bb {
                                              fill: #53C6FF;
                                            }
                                          </style>
                                        </defs>
                                        <path class="aa" d="M0,0H16V16H0Z" />
                                        <path class="bb"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                  </th>

                                </thead>
                                <tbody>
                                  <tr *ngFor="let filters of sessionFilters.filters | paginate: {itemsPerPage: sessionFilters.limit, currentPage: sessionFilters.offset, id: 'allSearchessuccessfullSessionReport',totalItems: sessionFilters.total};let i=index">
                                    <td class="sessionFilter" style="vertical-align: middle;">{{(i+1)+(sessionFilters.offset-1)*sessionFilters.limit}} </td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <u>
                                        <a href="JavaScript:void(0);">
                                          <span style="cursor: pointer;"
                                            (click)="getSessionValuesDetails(filters.cookie, sessionFilters.selectedSearchTerm)">
                                            {{filters.cookie}}
                                          </span>
                                        </a>
                                      </u>
                                    </td>
                                    <td class="sessionFilter">{{filters.email}}</td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      {{filters.end_time | timeZone:userTimeZone:"contentSource"}}</td>
                                  </tr>

                                  <tr *ngIf=" sessionFilters && sessionFilters.state == 'shown' && sessionFilters.filters && sessionFilters.filters.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>
                                  <tr *ngIf="sessionFilters.filters?.length != 0 && sessionFilters.total > sessionFilters.limit"
                                    class="hover">
                                    <td  colspan="4"  style="text-align: right;">
                                      <pagination-controls id="allSearchessuccessfullSessionReport"
                                        (pageChange)="pageChangeAllSearchReport($event, 'allSearchessuccessfullSessionReport')">
                                      </pagination-controls>
                                    </td>
                                 </tr>

                                </tbody>
                              </table>
                            </div>
                          </div>
                        </mat-tab>
                        <!-- Session innser report ends -->
                        <!-- SubReport  -->
                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Successful Search Groups' : 'Top Successful Searches'}}

                            <app-email-and-download-reports *ngIf="successfulSearches && !successfulSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].Search_Report[4]"
                              [filterParameterSearchReport]="getFiltersForQueriesCopy" [hideDownload]="true"
                              [filterParameterSearchReportLabel]="'Successful_Searches-Facet_Details'"
                              [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                          </div>

                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading">{{queryFilters.selectedSearchTerm}} <i>
                              (Total Searches: {{queryFilters.searchCount}})</i>
                            </span>
                            <button class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px;">
                              <i class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search">
                                <thead *ngIf="selectedIndex == 0" class="t-head display-table">
                                  <tr>
                                    <th style="width: 10% !important;">#</th>
                                    <th style="width: 20%;padding: 0px;">Facet Type</th>
                                    <th style="width: 20%;padding: 0px;">Facet Value</th>
                                    <th style="width: 20%;padding: 0px;">Advance Search</th>
                                    <th *ngFor="let col of searchSubReport" style="width: 20%;padding: 0px;">
                                      <span class="search-sub-report-head-sec">
                                        <span class="search-sub-report-head">{{col}}</span>
                                        <span (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                          style="cursor: pointer;">
                                          <svg *ngIf="sortingSearchSubReport !== col" xmlns="http://www.w3.org/2000/svg"
                                            width="16" height="16" viewBox="0 0 16 16">
                                            <defs>
                                              <style>
                                                .a {
                                                  fill: none;
                                                }

                                                .b {
                                                  fill: #707070;
                                                }
                                              </style>
                                            </defs>
                                            <path class="a" d="M0,0H16V16H0Z" />
                                            <path class="b"
                                              d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                              transform="translate(-1.903 -1.069)" />
                                          </svg>
                                        </span>
                                        <span *ngIf="sortingSearchSubReport === col"
                                          (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                          style="cursor: pointer;">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 16 16">
                                            <defs>
                                              <style>
                                                .aa {
                                                  fill: none;
                                                }

                                                .bb {
                                                  fill: #53C6FF;
                                                }
                                              </style>
                                            </defs>
                                            <path class="aa" d="M0,0H16V16H0Z" />
                                            <path class="bb"
                                              d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                              transform="translate(-1.903 -1.069)" />
                                          </svg>
                                        </span>
                                        <span *ngIf="isClusteringEnabled" class="grouping-info-icon-header"
                                            style="padding-left: 5px;" 
                                            matTooltip="Counts searches done with filters and without any filters."
                                            matTooltipPosition="right"
                                            matTooltipClass="allow-cr"
                                            matTooltipClass= "groupingHeadertooltip"
                                            aria-label="Button that displays a tooltip in various positions">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16.026" height="16.026" viewBox="0 0 16.026 16.026"><defs><style>.a{fill:none;}.b{fill:#635e7e;}</style></defs><path class="a" d="M0,0H16.026V16.026H0Z"/><path class="b" d="M8.677,2a6.677,6.677,0,1,0,6.677,6.677A6.68,6.68,0,0,0,8.677,2Zm0,10.016a.67.67,0,0,1-.668-.668V8.677a.668.668,0,0,1,1.335,0v2.671A.67.67,0,0,1,8.677,12.016Zm.668-5.342H8.01V5.339H9.345Z" transform="translate(-0.665 -0.665)"/></svg>
                                        </span>
                                      </span>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody class="display-table">

                                  <ng-container
                                    *ngFor="let row of queryFilters.filters | paginate: {itemsPerPage: getFiltersForQueriesCopy.limit, currentPage: getFiltersForQueriesCopy.offset, id: 'allSearchesSubReportPage',totalItems: queryFilters.total}; let i=index">
                                    <div class="display-table">
                                      <tr>
                                        <td [attr.rowspan]="row.filters.length-1"
                                          style="vertical-align: middle;width: 10% !important;">
                                          {{(i+1)+(getFiltersForQueriesCopy.offset-1)*getFiltersForQueriesCopy.limit}}
                                        </td>
                                        <td style="vertical-align: top;width: 40%; padding: 0;">
                                          <table class="filterBorder" width="100%">
                                            <tr *ngFor="let f of row.filters;let j=index">
                                              <td style="padding:12px 0px; width: 50%; text-align: left;">
                                                <span class="filter-type-display">{{f.filterType}}</span>
                                              </td>
                                              <td class="filtertd" style="padding:12px 0px; width: 50%;">
                                                <div class="filteralignment">
                                                  <div *ngFor="let sv of f.selectedValues;" class="filter" matTooltip="{{sv}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip" >
                                                      <ng-container *ngIf="parseJson(sv) as parsed">
                                                        <ng-container *ngIf="parsed.min_value; else noMinValue">
                                                          <div>
                                                            Min: {{ parsed.min_value }}
                                                          </div>
                                                          <div>
                                                            Max: {{ parsed.max_value }}
                                                          </div>
                                                        </ng-container>
                                                      </ng-container>
                                                    
                                                    <ng-template #noMinValue>
                                                      {{ sv }}
                                                    </ng-template>
                                                  </div>
                                                </div>
                                              </td>
                                            </tr>
                                          </table>
                                        </td>
                                        <td style="vertical-align: middle;width: 20%;padding: 12px 0px;"> 
                                          <div *ngIf ="row.exactphrase"  class="filter" matTooltip="{{row.exactphrase}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip" >
                                            Exact Phrase: {{row.exactphrase}}
                                          </div>
                                          <div *ngIf ="row.withoneormore" class="filter" matTooltip="{{row.withoneormore}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip" >
                                            With One or More: {{row.withoneormore}}
                                          </div>
                                          <div *ngIf ="row.withoutwords" class="filter" matTooltip="{{row.withoutwords}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip" >
                                            Without the words: {{row.withoutwords}}
                                          </div>
                                          <div *ngIf ="row.exactphrase =='' && row.withoneormore=='' && row.withoutwords==''"  class="filter" matTooltip="Null" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            Null
                                          </div>
                                        </td>
                                        <td [attr.rowspan]="row.filters.length-1"
                                          style="vertical-align: middle;width: 20%; padding: 0px;">{{row.count}} </td>
                                      </tr>

                                    </div>


                                  </ng-container>
                                  <tr
                                    *ngIf="queryFilters.filters?.length != 0 && queryFilters.total > getFiltersForQueriesCopy.limit"
                                    class="hover">
                                    <td style="text-align: right;">
                                      <pagination-controls id="allSearchesSubReportPage"
                                        (pageChange)="pageChangeAllSearchReport($event, 'allSearchesSubReport')">
                                      </pagination-controls>
                                    </td>
                                  </tr>
                                
                                <tr *ngIf="queryFilters.state == 'loading'">
                                  <td colspan="4">
                                    <div style="text-align: center;">
                                      <div class="spinner">
                                        <div class="bounce1"></div>
                                        <div class="bounce2"></div>
                                        <div class="bounce3"></div>
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                                <tr *ngIf="queryFilters && queryFilters.state == 'shown' && queryFilters.filters?.length == 0">
                                  <td colspan="4">All Searches performed without filters</td>
                                </tr>
                              </tbody>
                              </table>
                            </div>
                          </div>
                        </mat-tab>

                      </mat-tab-group>

                    </div>
                  </div>
                  <div *ngIf="activeReport == 3">
                    <div class="card card-block" style="padding:0; margin: 0px;">
                      <mat-tab-group class="kcs-mat-tab" [(selectedIndex)]="selectedIndex3Tab">
                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Search Groups With No Click' : 'Top Searches With No Clicks'}}

                            <app-email-and-download-reports *ngIf="withNoClickSearches && !withNoClickSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].Search_Report[2]"
                              [hideDownload]="true" [searchReportFilters]="topSearchesWithNoClicksObj" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                            <app-user-metric-filter
                              *ngIf="userMetricEnabled"
                              [userMetricLabel]="userMetricLabel"
                              [userMetricVariable]="userMetricVariable"
                              [userId]="userUniqueId"
                              [email]="userEmail"
                              [uid]="searchClients.uid"
                              [internalUser]="internalUser"
                              [from]="range.from"
                              [to]="range.to"
                              [reportName]="'Search Classifications (Searches With No Clicks)'"
                              [userMetricURL]="['/overview/searchsWithNoClicks']"
                              [body]="{
                                from: range.from,
                                to: range.to,
                                internalUser: internalUser,
                                uid: searchClients.uid,
                                offset: 1,
                                limit: 50,
                                sortingField: 'Searches',
                                sortType: 'desc',
                                searchGrouping: isClusteringEnabled
                              }"
                              (userMetricEvent)="sendUserMetricValues($event)"
                            ></app-user-metric-filter>
                          </div>

                          <div *ngIf="withNoClickSearches" id="withNoClickSearches"
                            style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                            <table class="table table-su">
                              <thead class="t-head">
                                <th [ngStyle]="{ 'width': isClusteringEnabled ? '35%' : '40%' }" *ngFor="let filter of topSearchesWithNoClicksSearch">
                                  {{filter.label}} <span
                                    (click)="openTextSearchForallSearchReport('topSearchesWithNoClicksSearchReport', filter.label,!filter.filled);closeButtonToggleChange();"
                                    class="search-icons-th">&nbsp;</span>
                                  <!-- SearchBox -->
                                  <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                    <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                      class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                      (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'topSearchesWithNoClicksSearchReport')">
                                    <button class="srtableBoxclose" *ngIf="filter.filled"
                                      (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'topSearchesWithNoClicksSearchReport');">X</button>
                                  </div>

                                </th>

                                <th *ngFor="let col of topSearchesWithNoClicksColumns" style="font-size:14px;">
                                  {{col}}
                                  <span
                                    (click)="changeSortOrderMainReport(col, 'asc', 'topSearchesWithNoClicksSearchReport')"
                                    style="cursor: pointer;">
                                    <svg *ngIf="sortingFieldTopSearchesWithNoClicksSearchReport !== col"
                                      xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .a {
                                            fill: none;
                                          }

                                          .b {
                                            fill: #707070;
                                          }
                                        </style>
                                      </defs>
                                      <path class="a" d="M0,0H16V16H0Z" />
                                      <path class="b"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                  <span *ngIf="sortingFieldTopSearchesWithNoClicksSearchReport === col"
                                    (click)="changeSortOrderMainReport(col,'desc', 'topSearchesWithNoClicksSearchReport')"
                                    style="cursor: pointer;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .aa {
                                            fill: none;
                                          }

                                          .bb {
                                            fill: #53C6FF;
                                          }
                                        </style>
                                      </defs>
                                      <path class="aa" d="M0,0H16V16H0Z" />
                                      <path class="bb"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                </th>

                              </thead>
                              <ng-container *ngIf="!withNoClickSearches.isEmpty">
                                <tr
                                  *ngFor="let d of withNoClickSearches | paginate: {itemsPerPage: topSearchesWithNoClicksObj.limit, currentPage: topSearchesWithNoClicksObj.currentPage, id: 'searchesWithNoClick',totalItems: topSearchesWithNoClicksObj.total};"
                                  style="word-break: break-word;">
                                <!-- If Group Searches toggle is enabled -->
                                <td *ngIf="isClusteringEnabled">
                                  <span 
                                    style="float: left; width: 75%; margin-right: 10px;"
                                    class="an_text_truncate"
                                    matTooltip="{{d[0]}}" 
                                    matTooltipPosition="below"
                                    matTooltipClass="an-tooltip-sessionReport"
                                  >
                                    {{ d[0] }}
                                  </span>
                                  <a
                                    href="JavaScript:void(0);"
                                    (click)="getSearchGroups(d[0], 1, d[3], 3)"
                                    matTooltip="View search queries" 
                                    matTooltipPosition="below"
                                    matTooltipClass="an-tooltip-groupSearch"
                                  >
                                    <svg id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24" xmlns="http://www.w3.org/2000/svg" width="18"
                                      height="12" viewBox="0 0 18.251 12.444" style="margin-right: 25px;">
                                      <path id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-2"
                                        data-name="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24"
                                        d="M49.125-790.045a3.6,3.6,0,0,0,2.644-1.089,3.6,3.6,0,0,0,1.089-2.644,3.6,3.6,0,0,0-1.089-2.644,3.6,3.6,0,0,0-2.644-1.089,3.6,3.6,0,0,0-2.644,1.089,3.6,3.6,0,0,0-1.089,2.644,3.6,3.6,0,0,0,1.089,2.644A3.6,3.6,0,0,0,49.125-790.045Zm0-1.493a2.16,2.16,0,0,1-1.587-.653,2.16,2.16,0,0,1-.653-1.587,2.16,2.16,0,0,1,.653-1.587,2.16,2.16,0,0,1,1.587-.653,2.16,2.16,0,0,1,1.587.653,2.16,2.16,0,0,1,.653,1.587,2.16,2.16,0,0,1-.653,1.587A2.16,2.16,0,0,1,49.125-791.538Zm0,3.982a9.6,9.6,0,0,1-5.517-1.69A9.609,9.609,0,0,1,40-793.778a9.609,9.609,0,0,1,3.609-4.532A9.6,9.6,0,0,1,49.125-800a9.6,9.6,0,0,1,5.517,1.69,9.609,9.609,0,0,1,3.609,4.532,9.609,9.609,0,0,1-3.609,4.532A9.6,9.6,0,0,1,49.125-787.556Z"
                                        transform="translate(-40 800)" fill="#afb2bc" />
                                    </svg>
                                  </a>
                                </td>

                                <!-- If Group Searches toggle is disabled -->
                                <td
                                  *ngIf="!isClusteringEnabled"
                                  matTooltip="{{ d[0] }}"
                                  matTooltipPosition="below"
                                  matTooltipClass="an-tooltip-sessionReport"
                                >
                                  <span class="an_text_truncate">{{d[0]}}</span>
                                </td>

                                <td *ngIf="isClusteringEnabled">{{d[4]}}</td>
                                <td>{{d[1]}}</td>
                                <td>
                                  <u>
                                    <a href="JavaScript:void(0);"><span (click)="getSessionFilter(d[0], 0,'topSearchesWithNoClicksSearchReport',d[2])"
                                        style="cursor: pointer;">{{d[2]}}</span>
                                    </a>
                                  </u>
                                </td>
                                <td>
                                  <u>
                                    <a href="JavaScript:void(0);"><span
                                        (click)="getFiltersForQueries(d[0], 1, d[3], 3)"
                                        style="cursor: pointer;">{{d[3]}}</span>
                                    </a>
                                  </u>
                                </td>
                                </tr>

                                <tr
                                  *ngIf="withNoClickSearches?.length != 0 && topSearchesWithNoClicksObj.total > topSearchesWithNoClicksObj.limit"
                                  class="hover">
                                  <td colspan="100" style="text-align: right;">
                                    <pagination-controls id="searchesWithNoClick"
                                      (pageChange)="pageChangeAllSearchReport($event, 'topSearchesWithNoClicksSearchReport')">
                                    </pagination-controls>
                                  </td>
                                </tr>

                              </ng-container>
                              <tr *ngIf="withNoClickSearches.isEmpty">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4" class="no-docs">
                                  {{ isClusteringEnabled ? 'No search groups to show' : 'No queries to show' }}
                                  <img class="doc-img">
                                </td>
                              </tr>
                              <tr *ngIf="withNoClickSearches?.length === 0">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4">
                                  <div style="text-align: center;">
                                    <div class="spinner">
                                      <div class="bounce1"></div>
                                      <div class="bounce2"></div>
                                      <div class="bounce3"></div>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            </table>
                          </div>

                        </mat-tab>

                        <!-- Session Report -->
                        <mat-tab *ngIf="isClusteringEnabled">
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            Search Groups With No Click

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="'Search Group Queries'"
                              [searchReportFilters]="searchGroupFilters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 21px;">
                            <span class="Detailed-Report-selected-heading Queries-Group">Search Queries under
                              <i>"{{searchGroupFilters.selectedSearchTerm}}"</i>
                              <span style="font-weight: normal; color: #43425D; display: block; margin-top: 10px;">
                                Number of Queries: {{searchGroupFilters.searchCount}}
                              </span>
                            </span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px; height: 40px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search group-searches">                                
                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="searchGroupFilter" style="width: 13%;">Sr.no</th>
                                  <th style="width: 80%;" *ngFor="let filter of searchQueryInClusterFilter">{{filter.label}} 
                                    <span
                                      (click)="openTextSearchForallSearchReport('searchQueryInClusterFilter', filter.label, !filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'searchQueryInClusterFilter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value=''; textSearAllSearchReportClearValue(filter.label, 'searchQueryInClusterFilter');">X</button>
                                    </div>
                                  </th>


                                </thead>
                                <tbody>
                                  <tr *ngFor="let row of searchGroupFilters.filters | paginate: {itemsPerPage: searchGroupFilters.limit, currentPage: searchGroupFilters.offset, id: 'allSearchesWithNoClickClustersReport',totalItems: searchGroupFilters.total};let i=index">
                                    <td class="sessionFilter" style="vertical-align: middle;"><span class="an_text_truncate">{{(i+1)+(searchGroupFilters.offset-1)*searchGroupFilters.limit}}</span></td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <span
                                        class="an_text_truncate"
                                        matTooltip="{{row.text_entered}}" 
                                        matTooltipPosition="below"
                                        matTooltipClass="an-tooltip-searchQueryInGroup"
                                      >{{row.text_entered}}</span>
                                    </td>
                                  </tr>

                                  <tr *ngIf="searchGroupFilters.state == 'shown' && searchGroupFilters.filters.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>

                                  <tr *ngIf="searchGroupFilters.filters?.length != 0 && searchGroupFilters.total > sessionFilters.limit"
                                      class="hover">
                                      <td  colspan="4"  style="text-align: right;">
                                        <pagination-controls id="allSearchesWithNoClickClustersReport"
                                          (pageChange)="pageChangeAllSearchReport($event, 'allSearchesWithNoClickClustersReport')">
                                        </pagination-controls>
                                      </td>
                                   </tr>

                                </tbody>
                              </table>
                            </div>
                          </div> 
                        </mat-tab>

                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Search Groups With No Click' : 'Top Searches With No Clicks'}}

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].SearchReportSessions[2]"
                              [searchReportFilters]="sessionFilters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading">{{sessionFilters.selectedSearchTerm}} <i>
                              (Number of sessions: {{sessionTotal}})</i></span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search">

                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="sessionFilter">Sr.no</th>
                                  <th style="width: 15%;" *ngFor="let filter of sessionFillter">{{filter.label}} <span
                                      (click)="openTextSearchForallSearchReport('sessionFillter', filter.label,!filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'sessionFillter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'sessionFillter');">X</button>
                                    </div>

                                  </th>

                                  <th *ngFor="let col of sessionFillterColumns" style="font-size:14px;width: 15%;">
                                    {{col}}
                                    <span (click)="changeSortOrderMainReport(col, 'asc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg *ngIf="sortingFieldNoResultSessionReport !== col"
                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .a {
                                              fill: none;
                                            }

                                            .b {
                                              fill: #707070;
                                            }
                                          </style>
                                        </defs>
                                        <path class="a" d="M0,0H16V16H0Z" />
                                        <path class="b"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                    <span *ngIf="sortingFieldNoResultSessionReport === col"
                                      (click)="changeSortOrderMainReport(col,'desc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .aa {
                                              fill: none;
                                            }

                                            .bb {
                                              fill: #53C6FF;
                                            }
                                          </style>
                                        </defs>
                                        <path class="aa" d="M0,0H16V16H0Z" />
                                        <path class="bb"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                  </th>

                                </thead>
                                <tbody>
                                  <tr *ngFor="let filters of sessionFilters.filters | paginate: {itemsPerPage: sessionFilters.limit, currentPage: sessionFilters.offset, id: 'allSearchesWithNoClickSessionReport',totalItems: sessionFilters.total};let i=index">
                                    <td class="sessionFilter" style="vertical-align: middle;">{{(i+1)+(sessionFilters.offset-1)*sessionFilters.limit}} </td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <u>
                                        <a href="JavaScript:void(0);">
                                          <span style="cursor: pointer;"
                                            (click)="getSessionValuesDetails(filters.cookie, sessionFilters.selectedSearchTerm)">
                                            {{filters.cookie}}
                                          </span>
                                        </a>
                                      </u>
                                    </td>
                                    <td class="sessionFilter">{{filters.email}}</td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      {{filters.end_time | timeZone:userTimeZone:"contentSource"}}</td>
                                  </tr>

                                  <tr *ngIf="sessionFilters.state == 'shown' && sessionFilters.filters.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>
                                  <tr *ngIf="sessionFilters.filters?.length != 0 && sessionFilters.total > sessionFilters.limit"
                                    class="hover">
                                    <td  colspan="4"  style="text-align: right;">
                                      <pagination-controls id="allSearchesWithNoClickSessionReport"
                                        (pageChange)="pageChangeAllSearchReport($event, 'allSearchesWithNoClickSessionReport')">
                                      </pagination-controls>
                                    </td>
                                 </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </mat-tab>
                        <!-- Session innser report ends -->

                        <!-- SubReport  -->
                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Search Groups With No Click' : 'Top Searches With No Clicks'}}

                            <app-email-and-download-reports *ngIf="successfulSearches && !successfulSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].Search_Report[4]"
                              [filterParameterSearchReport]="getFiltersForQueriesCopy" [hideDownload]="true"
                              [filterParameterSearchReportLabel]="'Searches_with_no_click-Facet_Details'" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                          </div>

                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading">{{queryFilters.selectedSearchTerm}} <i>
                              (Total Searches: {{queryFilters.searchCount}})</i>
                            </span>
                            <button class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px;">
                              <i class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search">
                                <thead *ngIf="selectedIndex == 0" class="t-head display-table">
                                  <tr>
                                    <th style="width: 10% !important;">#</th>
                                    <th style="width: 20%;padding: 0px;">Facet Type</th>
                                    <th style="width: 20%;padding: 0px;">Facet Value</th>
                                    <th style="width: 20%;padding: 0px;">Advance Search</th>
                                    <th *ngFor="let col of searchSubReport" style="width: 20%; padding: 0px;">
                                      <span class="search-sub-report-head-sec">
                                        <span class="search-sub-report-head">{{col}}</span>
                                        <span (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                          style="cursor: pointer;">
                                          <svg *ngIf="sortingSearchSubReport !== col" xmlns="http://www.w3.org/2000/svg"
                                            width="16" height="16" viewBox="0 0 16 16">
                                            <defs>
                                              <style>
                                                .a {
                                                  fill: none;
                                                }

                                                .b {
                                                  fill: #707070;
                                                }
                                              </style>
                                            </defs>
                                            <path class="a" d="M0,0H16V16H0Z" />
                                            <path class="b"
                                              d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                              transform="translate(-1.903 -1.069)" />
                                          </svg>
                                        </span>
                                        <span *ngIf="sortingSearchSubReport === col"
                                          (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                          style="cursor: pointer;">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 16 16">
                                            <defs>
                                              <style>
                                                .aa {
                                                  fill: none;
                                                }

                                                .bb {
                                                  fill: #53C6FF;
                                                }
                                              </style>
                                            </defs>
                                            <path class="aa" d="M0,0H16V16H0Z" />
                                            <path class="bb"
                                              d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                              transform="translate(-1.903 -1.069)" />
                                          </svg>
                                        </span>
                                        <span *ngIf="isClusteringEnabled" class="grouping-info-icon-header"
                                            style="padding-left: 5px;" 
                                            matTooltip="Counts searches done with filters and without any filters."
                                            matTooltipPosition="right"
                                            matTooltipClass="allow-cr"
                                            matTooltipClass= "groupingHeadertooltip"
                                            aria-label="Button that displays a tooltip in various positions">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16.026" height="16.026" viewBox="0 0 16.026 16.026"><defs><style>.a{fill:none;}.b{fill:#635e7e;}</style></defs><path class="a" d="M0,0H16.026V16.026H0Z"/><path class="b" d="M8.677,2a6.677,6.677,0,1,0,6.677,6.677A6.68,6.68,0,0,0,8.677,2Zm0,10.016a.67.67,0,0,1-.668-.668V8.677a.668.668,0,0,1,1.335,0v2.671A.67.67,0,0,1,8.677,12.016Zm.668-5.342H8.01V5.339H9.345Z" transform="translate(-0.665 -0.665)"/></svg>
                                        </span>
                                      </span>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody class="display-table">
                                  <ng-container
                                    *ngFor="let row of queryFilters.filters | paginate: {itemsPerPage: getFiltersForQueriesCopy.limit, currentPage: getFiltersForQueriesCopy.offset, id: 'allSearchesSubReportPage',totalItems: queryFilters.total}; let i=index">
                                    <div class="display-table">
                                      <tr>
                                        <td [attr.rowspan]="row.filters.length-1"
                                          style="vertical-align: middle;width: 10% !important;">
                                          {{(i+1)+(getFiltersForQueriesCopy.offset-1)*getFiltersForQueriesCopy.limit}}
                                        </td>
                                        <td style="vertical-align: top;width: 40%; padding: 0;">
                                          <table class="filterBorder" width="100%">
                                            <tr *ngFor="let f of row.filters;let j=index">
                                              <td style="padding:12px 0px; width: 50%; text-align: left;">
                                                <span class="filter-type-display">{{f.filterType}}</span>
                                              </td>
                                              <td class="filtertd" style="padding:12px 0px; width: 50%;">
                                                <div class="filteralignment">
                                                  <div *ngFor="let sv of f.selectedValues;" class="filter" matTooltip="{{sv}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                                      <ng-container *ngIf="parseJson(sv) as parsed">
                                                        <ng-container *ngIf="parsed.min_value; else noMinValue">
                                                          <div>
                                                            Min: {{ parsed.min_value }}
                                                          </div>
                                                          <div>
                                                            Max: {{ parsed.max_value }}
                                                          </div>
                                                        </ng-container>
                                                      </ng-container>
                                                    
                                                    <ng-template #noMinValue>
                                                      {{ sv }}
                                                    </ng-template>
                                                  </div>
                                                </div>
                                              </td>
                                            </tr>
                                          </table>
                                        </td>
                                        <td style="vertical-align: middle;width: 20%;padding: 12px 0px;"> 
                                          <div *ngIf ="row.exactphrase"  class="filter" matTooltip="{{row.exactphrase}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            Exact Phrase: {{row.exactphrase}}
                                          </div>
                                          <div *ngIf ="row.withoneormore" class="filter" matTooltip="{{row.withoneormore}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            With One or More: {{row.withoneormore}}
                                          </div>
                                          <div *ngIf ="row.withoutwords" class="filter" matTooltip="{{row.withoutwords}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            Without the words: {{row.withoutwords}}
                                          </div>
                                          <div *ngIf ="row.exactphrase =='' && row.withoneormore=='' && row.withoutwords==''"  class="filter" matTooltip="Null" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            Null
                                          </div>
                                        </td>
                                        <td [attr.rowspan]="row.filters.length-1"
                                          style="vertical-align: middle;width: 20%; padding: 0px;">{{row.count}} </td>
                                      </tr>

                                    </div>


                                  </ng-container>
                                  <tr *ngIf="queryFilters.filters?.length != 0 && queryFilters.total > getFiltersForQueriesCopy.limit" class="hover">
                                    <td style="text-align: right;">
                                      <pagination-controls id="allSearchesSubReportPage"
                                        (pageChange)="pageChangeAllSearchReport($event, 'allSearchesSubReport')">
                                      </pagination-controls>
                                    </td>
                                  </tr>

                                  <tr *ngIf="queryFilters.state == 'loading'">
                                    <td colspan="4">
                                      <div style="text-align: center;">
                                        <div class="spinner">
                                          <div class="bounce1"></div>
                                          <div class="bounce2"></div>
                                          <div class="bounce3"></div>
                                        </div>
                                      </div>
                                    </td>
                                  </tr>
                                  <tr *ngIf=" queryFilters && queryFilters.state == 'shown' && queryFilters.filters?.length == 0">
                                    <td colspan="4">All Searches performed without filters</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </mat-tab>


                      </mat-tab-group>
                      
                    </div>
          
                  </div>
                  <div *ngIf="activeReport == 4">
                    <div class="card card-block" style="padding:0; margin: 0px;">
                    
                      <mat-tab-group class="kcs-mat-tab" [(selectedIndex)]="selectedIndexTabForNoResultSearch">
                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Search Groups With No Result' : 'Top Searches With No Result'}}

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].Search_Report[3]"
                              [searchReportFilters]="withNoResultFillters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                            <app-user-metric-filter
                              *ngIf="userMetricEnabled"
                              [userMetricLabel]="userMetricLabel"
                              [userMetricVariable]="userMetricVariable"
                              [userId]="userUniqueId"
                              [email]="userEmail"
                              [uid]="searchClients.uid"
                              [internalUser]="internalUser"
                              [from]="range.from"
                              [to]="range.to"
                              [reportName]="'Search Classifications (Searches With No Result)'"
                              [userMetricURL]="['/overview/searchesWithNoResult']"
                              [body]="{
                                from: range.from,
                                to: range.to,
                                internalUser: internalUser,
                                uid: searchClients.uid,
                                offset: 1,
                                limit: 50,
                                sortingField: 'Searches',
                                sortType: 'desc',
                                searchGrouping: isClusteringEnabled
                              }"
                              (userMetricEvent)="sendUserMetricValues($event)"
                            ></app-user-metric-filter>
                          </div>
                          <div *ngIf="withNoResultSearches" id="withNoResultSearches"
                            style="height: 457px; margin: 0 auto; overflow-y:auto; overflow-x: hidden" class="perfect">
                            <table class="table table-su">

                              <thead class="t-head">
                                <th [ngStyle]="{ 'width': isClusteringEnabled ? (searchClients.name != 'All' && isEcosystemSelected === false ? '29%' : '35%' ) : '40%' }" *ngFor="let filter of topSearchesWithNoresult">{{filter.label}}
                                  <span
                                    (click)="openTextSearchForallSearchReport('topSearchesWithNoresult', filter.label,!filter.filled);closeButtonToggleChange();"
                                    class="search-icons-th">&nbsp;</span>
                                  <!-- SearchBox -->
                                  <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                    <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                      class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                      (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'topSearchesWithNoresult')">
                                    <button class="srtableBoxclose" *ngIf="filter.filled"
                                      (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'topSearchesWithNoresult');">X</button>
                                  </div>

                                </th>

                                <th *ngFor="let col of topSearchesWithNoResultsColumns" style="font-size:14px;">
                                  {{col}}
                                  <span (click)="changeSortOrderMainReport(col, 'asc', 'topSearchesWithNoresult')"
                                    style="cursor: pointer;">
                                    <svg *ngIf="sortingFieldTopSearchesWithNoResult !== col"
                                      xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .a {
                                            fill: none;
                                          }

                                          .b {
                                            fill: #707070;
                                          }
                                        </style>
                                      </defs>
                                      <path class="a" d="M0,0H16V16H0Z" />
                                      <path class="b"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                  <span *ngIf="sortingFieldTopSearchesWithNoResult === col"
                                    (click)="changeSortOrderMainReport(col,'desc', 'topSearchesWithNoresult')"
                                    style="cursor: pointer;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                      <defs>
                                        <style>
                                          .aa {
                                            fill: none;
                                          }

                                          .bb {
                                            fill: #53C6FF;
                                          }
                                        </style>
                                      </defs>
                                      <path class="aa" d="M0,0H16V16H0Z" />
                                      <path class="bb"
                                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                        transform="translate(-1.903 -1.069)" />
                                    </svg>
                                  </span>
                                </th>

                                <th style="border-bottom: 1px solid #70707036; z-index: 1;" *ngIf="searchClients.name != 'All' && isEcosystemSelected === false">
                                  Action
                                  <span class="cursor-pointer filterActionDropdown">
                                      <form [formGroup]="selectActionForm" autocomplete="off" style="margin: 0px">
                                        <mat-select formControlName="actionType" panelClass="actionDropdownFilterSelect" [disableOptionCentering]="true" multiple>
                                          <mat-option #allSelectedInside [value]="'all'" (click)="selectAllActionFilters()">
                                            All
                                          </mat-option>
                                          <mat-option *ngFor="let filters of actionListNames;let i = index;" [value]="filters" (click)="selectActionFilter(filters)">
                                            {{filters}}
                                          </mat-option>
                                        </mat-select>
                                      </form>
                                  </span>
                                </th>
                              </thead>

                              <ng-container *ngIf="!withNoResultSearches.isEmpty">
                                <tr
                                  *ngFor="let d of withNoResultSearches | paginate: {itemsPerPage: withNoResultFillters.limit, currentPage: withNoResultFillters.currentPage, id: 'allSearchesWithNoResult',totalItems: withNoResultFillters.total};let i=index;"
                                  style="word-break: break-word;">
                                  <!-- If Group Searches toggle is enabled -->
                                  <td *ngIf="isClusteringEnabled">
                                    <span
                                      style="float: left; width: 75%; margin-right: 3px;"
                                      class="an_text_truncate"
                                      matTooltip="{{d[0]}}" 
                                      matTooltipPosition="below"
                                      matTooltipClass="an-tooltip-sessionReport"
                                    >
                                      {{ d[0] }}
                                    </span>
                                    <a
                                      href="JavaScript:void(0);"
                                      (click)="getSearchGroups(d[0], 1, d[3], 4)"
                                      matTooltip="View search queries" 
                                      matTooltipPosition="below"
                                      matTooltipClass="an-tooltip-groupSearch"
                                    >
                                      <svg id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24" xmlns="http://www.w3.org/2000/svg" width="18"
                                        height="12" viewBox="0 0 18.251 12.444" style="margin-right: 25px;">
                                        <path id="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-2"
                                          data-name="visibility_24dp_5F6368_FILL1_wght400_GRAD0_opsz24"
                                          d="M49.125-790.045a3.6,3.6,0,0,0,2.644-1.089,3.6,3.6,0,0,0,1.089-2.644,3.6,3.6,0,0,0-1.089-2.644,3.6,3.6,0,0,0-2.644-1.089,3.6,3.6,0,0,0-2.644,1.089,3.6,3.6,0,0,0-1.089,2.644,3.6,3.6,0,0,0,1.089,2.644A3.6,3.6,0,0,0,49.125-790.045Zm0-1.493a2.16,2.16,0,0,1-1.587-.653,2.16,2.16,0,0,1-.653-1.587,2.16,2.16,0,0,1,.653-1.587,2.16,2.16,0,0,1,1.587-.653,2.16,2.16,0,0,1,1.587.653,2.16,2.16,0,0,1,.653,1.587,2.16,2.16,0,0,1-.653,1.587A2.16,2.16,0,0,1,49.125-791.538Zm0,3.982a9.6,9.6,0,0,1-5.517-1.69A9.609,9.609,0,0,1,40-793.778a9.609,9.609,0,0,1,3.609-4.532A9.6,9.6,0,0,1,49.125-800a9.6,9.6,0,0,1,5.517,1.69,9.609,9.609,0,0,1,3.609,4.532,9.609,9.609,0,0,1-3.609,4.532A9.6,9.6,0,0,1,49.125-787.556Z"
                                          transform="translate(-40 800)" fill="#afb2bc" />
                                      </svg>
                                    </a>
                                  </td>

                                  <!-- If Group Searches toggle is disabled -->
                                  <td
                                    *ngIf="!isClusteringEnabled"
                                    matTooltip="{{ d[0] }}"
                                    matTooltipPosition="below"
                                    matTooltipClass="an-tooltip-sessionReport"
                                  >
                                    <span class="an_text_truncate">{{d[0]}}</span>
                                  </td>

                                  <td *ngIf="isClusteringEnabled">{{d[5]}}</td>
                                  <td>{{d[1]}}</td>
                                  <td>
                                    <u>
                                      <a href="JavaScript:void(0);"><span
                                          (click)="getSessionFilter(d[0], 0, withNoResultFillters, d[2])"
                                          style="cursor: pointer;">{{d[2]}}</span>
                                      </a>
                                    </u>
                                  </td>
                                  <td>
                                    <u>
                                      <a href="JavaScript:void(0);"><span
                                          (click)="getFiltersForQueries(d[0], 1, d[3], 4)"
                                          style="cursor: pointer;">{{d[3]}}</span>
                                      </a>
                                    </u>
                                  </td>
                                  <td *ngIf="searchClients.name != 'All' && isEcosystemSelected === false">
                                    <span style="margin-right: 10px;" matTooltip="{{ d[4] }}" matTooltipPosition="below" matTooltipClass="actionTooltip">
                                      <img src="assets/img/{{ actionListObj[d[4]] }}">
                                    </span>
                                    <span class="actionDropdown">
                                      <mat-form-field>
                                          <mat-select #select [(value)]="d[4]" (selectionChange)="changeActionStatus(select.value,d[0])" panelClass="actionDropdownSelect" [disableOptionCentering]="true">
                                              <mat-option *ngFor="let action of actionListNames" [value]="action">
                                                {{action}}
                                              </mat-option>
                                          </mat-select>
                                      </mat-form-field>
                                    </span>
                                  </td>
                                </tr>


                              </ng-container>
                              <tr
                                *ngIf="!withNoResultSearches.isEmpty && withNoResultFillters.total > withNoResultFillters.limit"
                                class="hover">
                                <td colspan="100" style="text-align: right;">
                                  <pagination-controls id="allSearchesWithNoResult"
                                    (pageChange)="pageChangeAllSearchReport($event, 'allSearcheswithNoResult')">
                                  </pagination-controls>
                                </td>
                              </tr>

                              <tr *ngIf="withNoResultSearches.isEmpty && searchClients.name == 'All'">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4" class="no-docs">
                                  {{ isClusteringEnabled ? 'No search groups to show' : 'No queries to show' }}
                                  <img class="doc-img">
                                </td>
                              </tr>

                              <tr *ngIf="withNoResultSearches.isEmpty && searchClients.name != 'All'">
                                <td [colSpan]="isClusteringEnabled ? 6 : 5" class="no-docs">
                                  {{ isClusteringEnabled ? 'No search groups to show' : 'No queries to show' }}
                                  <img class="doc-img">
                                </td>
                              </tr>

                              <tr *ngIf="withNoResultSearches?.length === 0">
                                <td [colSpan]="isClusteringEnabled ? 5 : 4">
                                  <div style="text-align: center;">
                                    <div class="spinner">
                                      <div class="bounce1"></div>
                                      <div class="bounce2"></div>
                                      <div class="bounce3"></div>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            </table>
                          </div>
                        </mat-tab>

                        <mat-tab *ngIf="isClusteringEnabled">
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            Search Groups With No Result

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="'Search Group Queries'"
                              [searchReportFilters]="searchGroupFilters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading Queries-Group">Search Queries under
                              <i>"{{searchGroupFilters.selectedSearchTerm}}"</i>
                              <span style="font-weight: normal; color: #43425D; display: block; margin-top: 10px;">
                                Number of Queries: {{searchGroupFilters.searchCount}}
                              </span>
                            </span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px; height: 40px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search group-searches">                                
                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="searchGroupFilter" style="width: 13%;">Sr.no</th>
                                  <th style="width: 80%;" *ngFor="let filter of searchQueryInClusterFilter">{{filter.label}} 
                                    <span
                                      (click)="openTextSearchForallSearchReport('searchQueryInClusterFilter', filter.label, !filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'searchQueryInClusterFilter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value=''; textSearAllSearchReportClearValue(filter.label, 'searchQueryInClusterFilter');">X</button>
                                    </div>
                                  </th>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let row of searchGroupFilters.filters | paginate: {itemsPerPage: searchGroupFilters.limit, currentPage: searchGroupFilters.offset, id: 'allSearchesWithNoResultClustersReport',totalItems: searchGroupFilters.total};let i=index">
                                    <td class="sessionFilter" style="vertical-align: middle;"><span class="an_text_truncate">{{(i+1)+(searchGroupFilters.offset-1)*searchGroupFilters.limit}}</span></td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <span
                                        class="an_text_truncate"
                                        matTooltip="{{row.text_entered}}" 
                                        matTooltipPosition="below"
                                        matTooltipClass="an-tooltip-searchQueryInGroup"
                                      >{{row.text_entered}}</span>
                                    </td>
                                  </tr>

                                  <tr *ngIf="searchGroupFilters.state == 'shown' && searchGroupFilters.filters.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>

                                  <tr *ngIf="searchGroupFilters.filters?.length != 0 && searchGroupFilters.total > searchGroupFilters.limit"
                                      class="hover">
                                      <td  colspan="4"  style="text-align: right;">
                                        <pagination-controls id="allSearchesWithNoResultClustersReport"
                                          (pageChange)="pageChangeAllSearchReport($event, 'allSearchesWithNoResultClustersReport')">
                                        </pagination-controls>
                                      </td>
                                   </tr>

                                </tbody>
                              </table>
                            </div>
                          </div> 
                        </mat-tab>


                        <!-- Session Report -->

                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Search Groups With No Result' : 'Top Searches With No Result'}}

                            <app-email-and-download-reports
                              *ngIf="withNoResultSearches && !withNoResultSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].SearchReportSessions[3]"
                              [searchReportFilters]="sessionFilters" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                          </div>
                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading">{{sessionFilters.selectedSearchTerm}} <i>
                              (Number of sessions: {{sessionTotal}})</i></span>
                            <button _ngcontent-c12="" class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px;">
                              <i _ngcontent-c12="" class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search">
                                <!-- <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <tr>
                                    <th class="sessionFilter">No.</th>
                                    <th class="sessionFilter">Session ID</th>
                                    <th class="sessionFilter">Email</th>
                                    <th class="sessionFilter">Last search</th>
                                  </tr>
                                </thead> -->
                                <thead *ngIf="selectedIndex == 0" class="t-head">
                                  <th class="sessionFilter">Sr.no</th>
                                  <th style="width: 15%;" *ngFor="let filter of sessionFillter">{{filter.label}} <span
                                      (click)="openTextSearchForallSearchReport('sessionFillter', filter.label,!filter.filled);closeButtonToggleChange();"
                                      class="search-icons-th">&nbsp;</span>
                                    <!-- SearchBox -->
                                    <div [id]="filter.id" *ngIf="closeButtonToggle === false">
                                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                                        class="filled" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                        (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'sessionFillter')">
                                      <button class="srtableBoxclose" *ngIf="filter.filled"
                                        (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'sessionFillter');">X</button>
                                    </div>

                                  </th>

                                  <th *ngFor="let col of sessionFillterColumns" style="font-size:14px;width: 15%;">
                                    {{col}}
                                    <span (click)="changeSortOrderMainReport(col, 'asc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg *ngIf="sortingFieldNoResultSessionReport !== col"
                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .a {
                                              fill: none;
                                            }

                                            .b {
                                              fill: #707070;
                                            }
                                          </style>
                                        </defs>
                                        <path class="a" d="M0,0H16V16H0Z" />
                                        <path class="b"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                    <span *ngIf="sortingFieldNoResultSessionReport === col"
                                      (click)="changeSortOrderMainReport(col,'desc', 'sessionFillter')"
                                      style="cursor: pointer;">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 16 16">
                                        <defs>
                                          <style>
                                            .aa {
                                              fill: none;
                                            }

                                            .bb {
                                              fill: #53C6FF;
                                            }
                                          </style>
                                        </defs>
                                        <path class="aa" d="M0,0H16V16H0Z" />
                                        <path class="bb"
                                          d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                          transform="translate(-1.903 -1.069)" />
                                      </svg>
                                    </span>
                                  </th>

                                </thead>
                                <tbody>
                                  <tr *ngFor="let filters of sessionFilters.filters | paginate: {itemsPerPage: sessionFilters.limit, currentPage: sessionFilters.offset, id: 'allSearchesWithNoResultSessionReport',totalItems: sessionFilters.total};let i=index">
                                    <td class="sessionFilter" style="vertical-align: middle;">{{(i+1)+(sessionFilters.offset-1)*sessionFilters.limit}} </td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      <u>
                                        <a href="JavaScript:void(0);">
                                          <span style="cursor: pointer;"
                                            (click)="getSessionValuesDetails(filters.cookie, sessionFilters.selectedSearchTerm)">
                                            {{filters.cookie}}
                                          </span>
                                        </a>
                                      </u>
                                    </td>
                                    <td class="sessionFilter">{{filters.email}}</td>
                                    <td class="sessionFilter" style="vertical-align: middle;">
                                      {{filters.end_time | timeZone:userTimeZone:"contentSource"}}</td>
                                  </tr>

                                  <tr *ngIf="sessionFilters && sessionFilters.state == 'shown' && sessionFilters.filters?.length == 0">

                                    <td colspan="4">No document found</td>
                                  </tr>
                                  <tr *ngIf="sessionFilters.filters?.length != 0 && sessionFilters.total > sessionFilters.limit"
                                    class="hover">
                                    <td  colspan="4"  style="text-align: right;">
                                      <pagination-controls id="allSearchesWithNoResultSessionReport"
                                        (pageChange)="pageChangeAllSearchReport($event, 'allSearchesWithNoResultSessionReport')">
                                      </pagination-controls>
                                    </td>
                                 </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </mat-tab>
                        <!-- Session innser report ends -->


                        <!-- Clicks report -->

                        <!-- SubReport  -->
                        <mat-tab>
                          <div class="analytics-section-header">
                            <i class="fa fa-line-chart" aria-hidden="true"></i>
                            {{isClusteringEnabled ? 'Search Groups With No Result' : 'Top Searches With No Result'}}

                            <app-email-and-download-reports *ngIf="successfulSearches && !successfulSearches.isEmpty"
                              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                              [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[2].Search_Report[4]"
                              [filterParameterSearchReport]="getFiltersForQueriesCopy" [hideDownload]="true"
                              [filterParameterSearchReportLabel]="'Searches_with_no_click-Facet_Details'" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters" [isClusteringEnabled]="isClusteringEnabled">
                            </app-email-and-download-reports>
                          </div>

                          <div style="display: flex;padding: 10px;">
                            <span class="Detailed-Report-selected-heading">{{queryFilters.selectedSearchTerm}} <i>
                              (Total Searches: {{queryFilters.searchCount}})</i>
                            </span>
                            <button class="buttonSecondary backToReport" (click)="toggleViewState();"
                              style="float:right;width: 100px;">
                              <i class="fa fa-arrow-left" style="margin-right: 5px;"></i>Back</button>
                          </div>
                          <div class="su-topQueries">
                            <div class="queryFilters-div perfect" *ngIf="selectedIndex == 0">
                              <table class="table table-responsive top-search">
                                <thead *ngIf="selectedIndex == 0" class="t-head display-table">
                                  <tr>
                                    <th style="width: 10% !important;">#</th>
                                    <th style="width: 20%;padding:0px">Facet Type</th>
                                    <th style="width: 20%;padding:0px">Facet Value</th>
                                    <th style="width: 20%;padding:0px;">Advance Search</th>
                                    <th *ngFor="let col of searchSubReport" style="width: 20%; padding: 0px;">
                                      <span class="search-sub-report-head-sec">
                                        <span class="search-sub-report-head">{{col}}</span>
                                        <span (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                          style="cursor: pointer;">
                                          <svg *ngIf="sortingSearchSubReport !== col" xmlns="http://www.w3.org/2000/svg"
                                            width="16" height="16" viewBox="0 0 16 16">
                                            <defs>
                                              <style>
                                                .a {
                                                  fill: none;
                                                }

                                                .b {
                                                  fill: #707070;
                                                }
                                              </style>
                                            </defs>
                                            <path class="a" d="M0,0H16V16H0Z" />
                                            <path class="b"
                                              d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                              transform="translate(-1.903 -1.069)" />
                                          </svg>
                                        </span>
                                        <span *ngIf="sortingSearchSubReport === col"
                                          (click)="sortSearchSubReport(col, 'allSucessfulSearches')"
                                          style="cursor: pointer;">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 16 16">
                                            <defs>
                                              <style>
                                                .aa {
                                                  fill: none;
                                                }

                                                .bb {
                                                  fill: #53C6FF;
                                                }
                                              </style>
                                            </defs>
                                            <path class="aa" d="M0,0H16V16H0Z" />
                                            <path class="bb"
                                              d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                              transform="translate(-1.903 -1.069)" />
                                          </svg>
                                        </span>
                                        <span *ngIf="isClusteringEnabled" class="grouping-info-icon-header"
                                            style="padding-left: 5px;" 
                                            matTooltip="Counts searches done with filters and without any filters."
                                            matTooltipPosition="right"
                                            matTooltipClass="allow-cr"
                                            matTooltipClass= "groupingHeadertooltip"
                                            aria-label="Button that displays a tooltip in various positions">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16.026" height="16.026" viewBox="0 0 16.026 16.026"><defs><style>.a{fill:none;}.b{fill:#635e7e;}</style></defs><path class="a" d="M0,0H16.026V16.026H0Z"/><path class="b" d="M8.677,2a6.677,6.677,0,1,0,6.677,6.677A6.68,6.68,0,0,0,8.677,2Zm0,10.016a.67.67,0,0,1-.668-.668V8.677a.668.668,0,0,1,1.335,0v2.671A.67.67,0,0,1,8.677,12.016Zm.668-5.342H8.01V5.339H9.345Z" transform="translate(-0.665 -0.665)"/></svg>
                                        </span>
                                      </span>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody class="display-table">
                                  <ng-container
                                    *ngFor="let row of queryFilters.filters | paginate: {itemsPerPage: getFiltersForQueriesCopy.limit, currentPage: getFiltersForQueriesCopy.offset, id: 'allSearchesSubReportPageNoResult',totalItems: queryFilters.total}; let i=index">
                                    <div class="display-table">
                                      <tr>
                                        <td [attr.rowspan]="row.filters.length-1"
                                          style="vertical-align: middle;width: 10% !important;">
                                          {{(i+1)+(getFiltersForQueriesCopy.offset-1)*getFiltersForQueriesCopy.limit}}
                                        </td>
                                        <td style="vertical-align: top;width: 40%; padding: 0;">
                                          <table class="filterBorder" width="100%">
                                            <tr *ngFor="let f of row.filters;let j=index">
                                              <td style="padding:12px 0px; width: 50%; text-align: left;">
                                                <span class="filter-type-display">{{f.filterType}}</span>
                                              </td>
                                              <td class="filtertd" style="padding:12px 0px; width: 50%;">
                                                <div class="filteralignment">
                                                  <div *ngFor="let sv of f.selectedValues;" class="filter" matTooltip="{{sv}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                                      <ng-container *ngIf="parseJson(sv) as parsed">
                                                        <ng-container *ngIf="parsed.min_value; else noMinValue">
                                                          <div>
                                                            Min: {{ parsed.min_value }}
                                                          </div>
                                                          <div>
                                                            Max: {{ parsed.max_value }}
                                                          </div>
                                                        </ng-container>
                                                      </ng-container>
                                                    
                                                    <ng-template #noMinValue>
                                                      {{ sv }}
                                                    </ng-template>
                                                  </div>
                                                </div>
                                              </td>
                                            </tr>
                                          </table>
                                        </td>
                                        <td style="vertical-align: middle;width: 20%;padding: 12px 0px;"> 
                                          <div *ngIf ="row.exactphrase"  class="filter" matTooltip="{{row.exactphrase}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            Exact Phrase: {{row.exactphrase}}
                                          </div>
                                          <div *ngIf ="row.withoneormore" class="filter" matTooltip="{{row.withoneormore}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            With One or More: {{row.withoneormore}}
                                          </div>
                                          <div *ngIf ="row.withoutwords" class="filter" matTooltip="{{row.withoutwords}}" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            Without the words: {{row.withoutwords}}
                                          </div>
                                          <div *ngIf ="row.exactphrase =='' && row.withoneormore=='' && row.withoutwords==''"  class="filter" matTooltip="Null" matTooltipPosition="right" matTooltipClass= "filterAlignToolTip">
                                            Null
                                          </div>
                                        </td>
                                        <td [attr.rowspan]="row.filters.length-1"
                                          style="vertical-align: middle;width: 20%; padding: 0px;">{{row.count}} </td>
                                      </tr>

                                    </div>


                                  </ng-container>
                                  <tr
                                    *ngIf="queryFilters.filters?.length != 0 && queryFilters.total > getFiltersForQueriesCopy.limit"
                                    class="hover">
                                    <td style="text-align: right;">
                                      <pagination-controls id="allSearchesSubReportPageNoResult"
                                        (pageChange)="pageChangeAllSearchReport($event, 'allSearchesSubReport')">
                                      </pagination-controls>
                                    </td>
                                  </tr>
                                  <tr *ngIf="queryFilters.state == 'loading'">
                                    <td colspan="4">
                                      <div style="text-align: center;">
                                        <div class="spinner">
                                          <div class="bounce1"></div>
                                          <div class="bounce2"></div>
                                          <div class="bounce3"></div>
                                        </div>
                                      </div>
                                    </td>
                                  </tr>
                                  <tr *ngIf="queryFilters && queryFilters.state == 'shown' && queryFilters.filters?.length == 0">
                                    <td colspan="4">All Searches performed without filters</td>
                                  </tr>
                                </tbody>

                              </table>
                            </div>
                          </div>
                        </mat-tab>

                      </mat-tab-group>

                    </div>
                  </div>
                </div>
              </mat-grid-tile>
            </mat-grid-list>
          </div>
        </div>

        <div class="overlay" *ngIf="hideflag">
          <div class="filter-container shadowDiv animate-div table-responsive"
            style="padding: 0px;overflow-y: hidden;overflow-x: auto;">
            <div style="width: 100%;display: inline-block;background: #00000012;">
              <span class="seesionIDdetails">Session ID : {{sessionCookies}}</span>
              <img (click)="hideflag =false;removeClassToBody();" src="assets/img/Remove.svg"
                style="float: right; height:11px; margin: 15px; cursor: pointer;">
              <div style="float: right; margin-top: 6px;">
                <label style="font-size: 14px; font-weight: 600;color: #707070;">View Full Details :</label>
                <mat-slide-toggle [(ngModel)]="myCheckbox" name="javascript_enabled"
                  (change)="getSessionValues(sessionFilters.filters, sessionCookies, myCheckbox, sessionFilters.selectedSearchTerm)"
                  style="position: relative;top: 3px;"></mat-slide-toggle>
              </div>
            </div>
            <div class="shadowDiv sessionAdvance">

              <table class="table table-su card-1">
                <thead class="t-head">
                  <tr class="analytics-section-header">
                    <th class="activityDetails">Activity Type</th>
                    <th class="activityDetails">Activity Detail</th>
                    <th *ngIf="isEcosystemSelected" class="activityDetails">Search Client</th>
                    <th class="activityDetails" style="width: 25%;">Time</th>
                    <th class="advanceActivityDetails">Advanced Details</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let filters of sessionResult;let activityIndex = index">
                    <tr class="activityTypeDetails"
                      [attr.class]="filters.active?'activity-detail-sea activity-detail-act':'activity-detail-sea'">
                      <td [ngClass]="{'textSearchResult':filters.type =='search' && filters.result_count == '0'}">
                        {{filters.activityType}}
                      </td>
                      <td *ngIf="filters.activityType =='Clicked Search Result'">
                        <a target="_blank" matTooltip="{{ filters.title ? (filters.title ) : (filters.url )}}"  matTooltipPosition="below" matTooltipClass="tooltip-sessionReport-swnresult" href="{{filters.url}}">
                          <span class="an_text_truncate">
                            {{filters.title ? (filters.title ) : (filters.url )}} 
                          </span>
                        </a>
                      </td>
                      <td *ngIf="filters.activityType=='Viewed Page' || filters.activityType=='Visited Support'  || filters.activityType=='Clicked Search Result (Support Page)'">
                        <a target="_blank" matTooltip="{{ filters.title || filters.window_url}}"  matTooltipPosition="below" matTooltipClass="tooltip-sessionReport-swnresult" href="{{filters.window_url}}"><span class="an_text_truncate"> {{(filters.title || filters.window_url) }} </span> </a>
                      </td>
                      <td matTooltip="{{ filters.subject }}"  matTooltipPosition="below" matTooltipClass="tooltip-sessionReport-swnresult" *ngIf="filters.activityType=='Created a Case'">
                        <span class="an_text_truncate">
                          {{filters.subject }}
                        </span>
                      </td>
                      <td matTooltip="{{ filters.text_entered.charAt(0) === '#' ? 'Wildcard: ' + filters.text_entered : filters.text_entered }}"  matTooltipPosition="below" matTooltipClass="tooltip-sessionReport-swnresult" [ngClass]="{'textSearchResult':filters.type =='search' && filters.result_count == '0'}"
                        *ngIf="filters.activityType=='Text Searched' || filters.activityType=='Text Searched (Support Page)'">
                        <span class="an_text_truncate">
                          {{filters.text_entered }}
                        </span>
                      </td>
                      <td *ngIf="isEcosystemSelected">{{filters.name}}</td>
                      <td [ngClass]="{'textSearchResult':filters.type =='search' && filters.result_count == '0'}">
                        {{filters.ts | timeZone:userTimeZone:"contentSource"}}
                      </td>
                      <td>
                        <a href="JavaScript:void(0);">
                          <span (click)="toggleSessionsAdvancedDetails(sessionResult, activityIndex)"
                            *ngIf="filters.activityType=='Text Searched' && ((filters.filters && filters.filters.length>0)||(filters.exactphrase && filters.exactphrase.length>0) ||(filters.withoneormore && filters.withoneormore.length>0) ||(filters.withoutwords && filters.withoutwords.length>0))"
                            style="float: left; cursor: pointer;">More Detail</span>
                        </a>
                        <svg (click)="toggleSessionsAdvancedDetails(sessionResult, activityIndex)" class="svgRight"
                          *ngIf="filters.activityType=='Text Searched' && ((filters.filters && filters.filters.length>0)||(filters.exactphrase && filters.exactphrase.length>0) ||(filters.withoneormore && filters.withoneormore.length>0) ||(filters.withoutwords && filters.withoutwords.length>0))"
                          width="24" height="24" viewBox="0 0 24 24">
                          <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" fill="grey" />
                          <path d="M0 0h24v24H0z" fill="none" />
                        </svg>
                        <svg (click)="toggleSessionsAdvancedDetails(sessionResult, activityIndex)" class="svgMore"
                          *ngIf="filters.activityType=='Text Searched' && ((filters.filters && filters.filters.length>0)||(filters.exactphrase && filters.exactphrase.length>0) ||(filters.withoneormore && filters.withoneormore.length>0) ||(filters.withoutwords && filters.withoutwords.length>0))"
                          width="24" height="24" viewBox="0 0 24 24">
                          <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z" fill="grey" />
                          <path d="M0 0h24v24H0z" fill="none" />
                        </svg>
                      </td>
                    </tr>
                    <tr
                      [attr.class]="filters.active?'activity-detail-search activity-detail-active':'activity-detail-search'">
                      <td class="popupDetails" colspan="4">
                        <table style="width:100%">
                          <thead>
                            <tr class="popupDetails">
                              <th class="advanceFacetsDetails">Facets Type</th>
                              <th class="advanceFacetsDetails">Facets Value</th>
                              <th class="advanceFacetsDetails">Exact Phrase</th>
                              <th class="advanceFacetsDetails">With One Or More</th>
                              <th class="advanceFacetsDetails">Without The Words</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr class="popupDetails">
                              <td>
                                <span *ngIf="filters.filters && filters.filters.length>0">
                                  {{filters.filters[0].name}} </span>                             
                              </td>
                              <td>
                                <span *ngIf="filters.filters && filters.filters.length>0">
                                  <ng-container *ngIf="filters.filters[0].selectedValues[0].min_value; else noMinValue">
                                    <div>
                                      Min: {{ filters.filters[0].selectedValues[0].min_value }}
                                    </div>
                                    <div>
                                      Max: {{ filters.filters[0].selectedValues[0].max_value }}
                                    </div>
                                    </ng-container>
                                  </span>
                                  
                                  <ng-template #noMinValue>
                                    {{ filters.filters[0].selectedValues[0] }}
                                  </ng-template>
                              </td>
                              <td>
                                {{filters.exactphrase}}
                              </td>
                              <td>
                                {{filters.withoneormore}}
                              </td>
                              <td>
                                {{filters.withoutwords}}
                              </td>
                            </tr>
                            <ng-container *ngFor="let facets of filters.filters;let j=index">
                              <tr class="popupDetails" *ngIf="j!=0">
                                <td>{{facets.name}}</td>
                                <td *ngIf="facets.selectedValues && facets.selectedValues.length>0">
                                    <ng-container *ngIf="facets.selectedValues[0].min_value; else noMinValue">
                                      <div>
                                        Min: {{ facets.selectedValues[0].min_value }}
                                      </div>
                                      <div>
                                        Max: {{ facets.selectedValues[0].max_value }}
                                      </div>
                                      </ng-container>
                                    
                                    <ng-template #noMinValue>
                                      {{ facets.selectedValues[0] }}
                                    </ng-template>
                                </td>
                              </tr>
                              <ng-container *ngFor="let facetValue of facets.selectedValues; let k = index">
                                <tr class="popupDetails" *ngIf="k!=0" style="border-top: none">
                                  <td class="seesionfacets"></td>
                                  <td class="seesionfacets">
                                    <ng-container *ngIf="facetValue.min_value; else noMinValue">
                                      <div>
                                        Min: {{ facetValue.min_value }}
                                      </div>
                                      <div>
                                        Max: {{ facetValue.max_value }}
                                      </div>
                                      </ng-container>
                                    
                                    <ng-template #noMinValue>
                                      {{ facetValue }}
                                    </ng-template>
                                  </td>
                                </tr>
                              </ng-container>
                            </ng-container>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Average Click Position chart report-->
  <div class="sectionMainDiv" *ngIf="selectedIndex===0">
    <div class="col-xl-12" *ngIf="reportSettings && reportSettings.length>0 && reportSettings[59] && reportSettings[59].is_enabled"
      style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
      <div id="searchHitCount" class="card card-block" style="padding:0;">
        <div class="analytics-section-heading">
          {{reportSettings[59].label}}

          <app-email-and-download-reports *ngIf="clickPositionHistogram" [searchClients]="searchClients" [range]="range"
            [internalUser]="internalUser" [tab]="OverviewTab.tabName" [selectedConversionType]="selectedConversionType"
            [reportName]="OverviewTab.Reports[12]" [hideDownload]="true" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
          </app-email-and-download-reports>
          <app-user-metric-filter
            *ngIf="userMetricEnabled"
            [userMetricLabel]="userMetricLabel"
            [userMetricVariable]="userMetricVariable"
            [userId]="userUniqueId"
            [email]="userEmail"
            [uid]="searchClients.uid"
            [internalUser]="internalUser"
            [from]="range.from"
            [to]="range.to"
            [reportName]="'Average Click Position'"
            [userMetricURL]="['/overview/averageClickPositionChart']"
            [body]="{
              from: range.from,
              to: range.to,
              internalUser: internalUser,
              uid: searchClients.uid
            }"
            (userMetricEvent)="sendUserMetricValues($event)"
          ></app-user-metric-filter>
        </div>
        <div class="app-line-conversion-chart">
          <span class="search-conversion">Successful Searches: <span
          class="search-conversion-rate">{{averageTotalSearches}}</span></span>
          <span class="search-conversion">Clicks: <span
            class="search-conversion-rate">{{averageTotalClicks}}</span></span>
            <span class="search-conversion">Average Click Position: <span
              class="search-conversion-rate">{{averageTotalAvg}}</span></span>

          <!-- Loader -->
          <div *ngIf="!clickPositionHistogram" style="text-align: center;">
            <div class="spinner">
              <div class="bounce1"></div>
              <div class="bounce2"></div>
              <div class="bounce3"></div>
            </div>
          </div>

          <app-line-conversion-chart *ngIf="clickPositionHistogram" [zoom1]="false" [chartType]="false"
            [colors]="['#7291f8' , '#f4961c' , '#5bbdfe']" [inputConversionData]="clickPositionHistogram" [analyticsV2]="true" [singleLineMultipleLegends]="true">
          </app-line-conversion-chart>

        </div>

      </div>
    </div>
  </div>
  <!-- Average Click Position chart report end-->

  <!-- Click Position Report -->
  <div class="sectionMainDiv" *ngIf="selectedIndex===0">
    <div *ngIf="reportSettings && reportSettings.length>0 && reportSettings[58] && reportSettings[58].is_enabled"
      class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
      <div>
        <div class="card card-block" id="types-of-docs" style="position: static;padding:0; min-height:auto;">
          <div class="analytics-section-heading">
            {{reportSettings[58].label}}
            <app-email-and-download-reports *ngIf="searchclickPosition && !searchclickPosition?.isEmpty"
              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="OverviewTab.tabName"
              [reportName]="OverviewTab.Reports[10]" [clickResultReport]="clickResultReport" [hideDownload]="true" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
            </app-email-and-download-reports>
            <app-user-metric-filter
              *ngIf="userMetricEnabled"
              [userMetricLabel]="userMetricLabel"
              [userMetricVariable]="userMetricVariable"
              [userId]="userUniqueId"
              [email]="userEmail"
              [uid]="searchClients.uid"
              [internalUser]="internalUser"
              [from]="range.from"
              [to]="range.to"
              [reportName]="'Click Position Report'"
              [userMetricURL]="['/overview/searchClickPosition']"
              [body]="{
                from: range.from,
                to: range.to,
                internalUser: internalUser,
                uid: searchClients.uid
              }"
              (userMetricEvent)="sendUserMetricValues($event)"
            ></app-user-metric-filter>
          </div>
          <div class="searchClickPositionReport" *ngIf="searchClickPositionReport">
            <table class="searchClickPosition table table-su card-1" style="width: 100%;">
              <tr class="search-click-header" style="white-space: nowrap;">
                <th class="searchClickPosition">#({{searchclickPosition?.count}})</th>
                <th class="searchClickPosition" style="text-align:left;" *ngFor="let filter of clickResultReport">{{filter.label}} <span
                    (click)="filtersearchClickPosition(filter.label,!filter.filled)" class="search-icons-th">&nbsp;</span>
                  <div class="left">
                    <div id="search">
                      <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder" class="filled"
                        [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                        (keyup.enter)="searchQueryClickPosition(filter.label,$event.target.value)">
                      <button class="tableBoxclose" *ngIf="filter.filled"
                        (click)="filter.filled=false; searchQueryClickPosition(filter.label,$event.target.value)">X</button>
                    </div>
                  </div>
                </th>
                <th *ngFor="let col of columnsArray" style="font-size:14px;">
                  {{columnsMap[col]}}
                  <span (click)="changeSortOrder(col)" style="cursor: pointer;">
                    <svg *ngIf="sortingField !== col" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                      viewBox="0 0 16 16">
                      <defs>
                        <style>
                          .a {
                            fill: none;
                          }

                          .b {
                            fill: #707070;
                          }
                        </style>
                      </defs>
                      <path class="a" d="M0,0H16V16H0Z" />
                      <path class="b"
                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                        transform="translate(-1.903 -1.069)" />
                    </svg>
                  </span>
                  <span *ngIf="sortingField === col" (click)="changeSortOrder(col)" style="cursor: pointer;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                      <defs>
                        <style>
                          .aa {
                            fill: none;
                          }

                          .bb {
                            fill: #53C6FF;
                          }
                        </style>
                      </defs>
                      <path class="aa" d="M0,0H16V16H0Z" />
                      <path class="bb"
                        d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                        transform="translate(-1.903 -1.069)" />
                    </svg>
                  </span>
                </th>
              </tr>
              <tbody *ngIf="!searchclickPosition?.isEmpty;else noData">
                <ng-container *ngFor="let click of searchclickPosition | paginate: {itemsPerPage: 10, currentPage: searchclickPosition?.currentPage, id: 'searchClickFirst',totalItems: searchclickPosition?.count};let i=index">
                  <tr [attr.class]="(click.hideflag) ? 'active ' : ''">
                    <td class="searchClickPosition" style="vertical-align: middle;">{{(i+1)+(searchclickPosition.currentPage-1)*10}}</td>
                    <td class="searchClickPosition" style="text-align:left;" matTooltip="{{click.text_entered}}" matTooltipPosition="below"  matTooltipClass="an-tooltip-sessionReport"> <span class="an_text_truncate"> {{click.text_entered }} </span> </td>      
                    <td class="searchClickPosition" style="vertical-align: middle;">
                    <u>
                      <a href="JavaScript:void(0);" (click)="getSessionClickPosition(0, 1, click)">
                          <span style="cursor: pointer;">
                            {{click.session_count}}
                          </span>
                        </a>
                      </u>
                    </td>
                    <td class="searchClickPosition">{{click.search_count}}</td>
                    <td class="searchClickPosition">{{click.click_count}}</td>
                    <td class="searchClickPosition">{{click.max_rank}}</td>
                    <td class="searchClickPosition">{{click.acp}}</td>
                  </tr>
                  <div class="overlay" [attr.class]="(click.hideflag) ? 'overlaySession ':''" *ngIf="click.hideflag " [hidden]="!click.hideflag">
                    <div class="filter-container shadowDiv animate-div table-responsive" [@opened]=""
                      style="padding: 0px;overflow: hidden;">
                      <div style="width: 100%;display: flex;padding: 5px;border-bottom: 1px solid #DDDFE1;">
                        <div style="display: flex;margin: 0;flex: 1;width: 80%;">
                          <span matTooltip="{{ click.text_entered }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport"
                            style="font-size: 13px;font-weight: 900;color: #707070;display: inline-block;padding: 10px; letter-spacing: 0px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 30%;" >
                            Search Query : {{click.text_entered}}
                          </span>
                          <span
                            style="font-size: 13px;font-weight: 900;color: #A3A3A3;margin-left: 1px;display: inline-block;padding: 10px; letter-spacing: 0px">
                            Number of clicked results ({{sessionclickPosition.count}})
                          </span>
                        </div>
                        <img (click)="click.hideflag = false; clearFilterValues(); removeClassToBody();" src="assets/img/crossClick.svg"
                          style="cursor: pointer;float: right;height: 15px;margin: 14px;margin-left: 18px;order: 3;">
                        <app-email-and-download-reports *ngIf="sessionclickPosition && !sessionclickPosition?.isEmpty"
                          [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="OverviewTab.tabName"
                          [reportName]="OverviewTab.Reports[11]" [sessionClickText] = "click.text_entered" 
                          [sessionClickPositionReport]="sessionClickResultData" [hideDownload]="true" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                        </app-email-and-download-reports>
                      </div>
                      <div class="Session-Tracking-Details-popup">
                        <table class="table table-su card-1" *ngIf="sessionclickPosition">
                          <thead class="t-head">
                            <tr class="search-click-header">
                              <th class="sessionClickPosition" *ngFor="let filter of sessionClickResultData">{{filter.label}} <span
                                (click)="filtersessionClickResult(filter.label,!filter.filled)" class="search-icons-th">&nbsp;</span>
                              <div class="left">
                                <div id="sessionClick">
                                  <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder" class="filled"
                                    [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                                    (keyup.enter)="sessionSort = true;sessionSearchClickPosition(click, filter.label,$event.target.value)">
                                  <button class="tableBoxclose" *ngIf="filter.filled"
                                    (click)="sessionSort = true;filter.filled=false; sessionSearchClickPosition(click, filter.label,$event.target.value)">X</button>
                                </div>
                              </div>
                            </th>
                            <th class="sessionClickPosition">
                              {{sessionClickMap[sessionClickArray[0]]}}
                              <span (click)="sessionSort = true;changeSessionSortOrder(click, sessionClickArray[0])" style="cursor: pointer;">
                                <svg *ngIf="sessionSortField !== sessionClickArray[0]" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                  viewBox="0 0 16 16">
                                  <defs>
                                    <style>
                                      .a {
                                        fill: none;
                                      }
            
                                      .b {
                                        fill: #707070;
                                      }
                                    </style>
                                  </defs>
                                  <path class="a" d="M0,0H16V16H0Z" />
                                  <path class="b"
                                    d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                    transform="translate(-1.903 -1.069)" />
                                </svg>
                              </span>
                              <span *ngIf="sessionSortField === sessionClickArray[0]" (click)="sessionSort = true;changeSessionSortOrder(click, sessionClickArray[0])" style="cursor: pointer;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                  <defs>
                                    <style>
                                      .aa {
                                        fill: none;
                                      }
            
                                      .bb {
                                        fill: #53C6FF;
                                      }
                                    </style>
                                  </defs>
                                  <path class="aa" d="M0,0H16V16H0Z" />
                                  <path class="bb"
                                    d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                    transform="translate(-1.903 -1.069)" />
                                </svg>
                              </span>
                            </th>
                            <th class="sessionClickPosition" style="text-align:left;">Article Title</th>
                            <th class="sessionClickPosition">Facet Selected</th>
                            <th class="sessionClickPosition">
                              {{sessionClickMap[sessionClickArray[1]]}}
                              <span (click)="sessionSort = true;changeSessionSortOrder(click, sessionClickArray[1])" style="cursor: pointer;">
                                <svg *ngIf="sessionSortField !== sessionClickArray[1]" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                  viewBox="0 0 16 16">
                                  <defs>
                                    <style>
                                      .a {
                                        fill: none;
                                      }
            
                                      .b {
                                        fill: #707070;
                                      }
                                    </style>
                                  </defs>
                                  <path class="a" d="M0,0H16V16H0Z" />
                                  <path class="b"
                                    d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                    transform="translate(-1.903 -1.069)" />
                                </svg>
                              </span>
                              <span *ngIf="sessionSortField === sessionClickArray[1]" (click)="sessionSort = true;changeSessionSortOrder(click, sessionClickArray[1])" style="cursor: pointer;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                  <defs>
                                    <style>
                                      .aa {
                                        fill: none;
                                      }
            
                                      .bb {
                                        fill: #53C6FF;
                                      }
                                    </style>
                                  </defs>
                                  <path class="aa" d="M0,0H16V16H0Z" />
                                  <path class="bb"
                                    d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                    transform="translate(-1.903 -1.069)" />
                                </svg>
                              </span>
                            </th>
                            </tr>
                          </thead>
                          <ng-container *ngFor="let activity of sessionclickPosition | paginate: {itemsPerPage: 10, currentPage: sessionclickPosition?.currentPage, id: 'sessionClickFirst',totalItems: sessionclickPosition?.count};let activityIndex = index">
                            <tr *ngIf="sessionclickPosition">
                              <td style="text-align: left; padding-left: 20px;">{{activity.cookie}}</td>
                              <td style="text-align: left; padding-left: 20px;">{{activity.ts | timeZone:userTimeZone:"contentSource"}}</td>
                              <td style="text-align: left; padding-left: 20px; width: 20%;">
                                <a target="_blank" href="{{activity.url}}" matTooltip="{{activity.title}}" matTooltipPosition="below"  matTooltipClass="custom-tooltip-casereport"> <span class="an_text_truncate"> {{activity.title ? (activity.title ) : (activity.url )}} </span></a>
                              </td>
                              <td style="text-align: left; padding-left: 20px;">
                                  <a href="JavaScript:void(0);">
                                    <span (click)="toggleClickSessionsAdvancedDetails(activityIndex)"
                                      *ngIf="((activity && activity.filters && activity.filters.length > 0)||(activity && activity.exactphrase && activity.exactphrase.length > 0) ||(activity && activity.withoneormore && activity.withoneormore.length > 0) ||(activity && activity.withoutwords && activity.withoutwords.length > 0))"
                                      style="float: left; cursor: pointer;">
                                      More Detail
                                    </span>
                                  </a>
                                <svg class="svgRight" (click)="toggleClickSessionsAdvancedDetails(activityIndex)"
                                *ngIf="((activity && activity.filters && activity.filters.length > 0)||(activity && activity.exactphrase && activity.exactphrase.length > 0) ||( activity && activity.withoneormore && activity.withoneormore.length > 0) ||( activity && activity.withoutwords && activity.withoutwords.length > 0))"
                                  width="24" height="24" viewBox="0 0 24 24">
                                  <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" fill="grey" />
                                  <path d="M0 0h24v24H0z" fill="none" /></svg>
                                <svg class="svgMore" (click)="toggleClickSessionsAdvancedDetails(activityIndex)"
                                  *ngIf="(( activity && activity.filters && activity.filters.length > 0)||( activity && activity.exactphrase && activity.exactphrase.length > 0) ||( activity && activity.withoneormore && activity.withoneormore.length > 0) ||(activity && activity.withoutwords && activity.withoutwords.length > 0))"
                                  width="24" height="24" viewBox="0 0 24 24">
                                  <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z" fill="grey" />
                                  <path d="M0 0h24v24H0z" fill="none" /></svg>
                              </td>
                              <td style="text-align: left; padding-left: 20px;">{{activity.rank}}</td>  
                            </tr>
                            <tr *ngIf="((activity && activity.filters && activity.filters.length > 0)||(activity && activity.exactphrase && activity.exactphrase.length > 0) ||(activity && activity.withoneormore && activity.withoneormore.length > 0) ||( activity && activity.withoutwords && activity.withoutwords.length > 0))"
                              [attr.class]="activity.active?'activity-detail-search activity-detail-active':'activity-detail-search'">
                              <td class="popupDetails" colspan="100">
                                <table class="exactPhaseDetails sessionFacetBorder"  style="width:100%">
                                  <thead>
                                    <tr class="popupDetails">
                                      <th class ="sessionFacetBorder">Facets Type</th>
                                      <th class ="sessionFacetBorder">Facets Value</th>
                                      <th class ="sessionFacetBorder">Exact Phrase</th>
                                      <th class ="sessionFacetBorder">With One Or More
                                      </th>
                                      <th class ="sessionFacetBorder">Without The Words
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr class="popupDetails">
                                      <td class ="sessionFacetBorder">
                                        <span *ngIf="activity.filters && activity.filters.length>0">
                                          {{activity.filters[0].name}}
                                        </span>
                                      </td>
                                      <td class ="sessionFacetBorder">
                                        <span *ngIf="activity.filters && activity.filters.length>0">
                                          <ng-container *ngIf="activity.filters[0].selectedValues[0].min_value; else noMinValue">
                                            <div>
                                              Min: {{ formatValue(activity.filters[0].selectedValues[0].min_value) }}
                                            </div>
                                            <div>
                                              Max: {{ formatValue(activity.filters[0].selectedValues[0].max_value) }}
                                            </div>
                                            </ng-container>
                                          </span>
                                          <ng-template #noMinValue>
                                            {{ activity.filters[0].selectedValues[0] }}
                                          </ng-template>
                                      </td>
                                      <td class ="sessionFacetBorder">
                                        <span> {{activity.exactphrase}}</span>
                                      </td>
                                      <td class ="sessionFacetBorder">
                                        {{activity.withoneormore}}
                                      </td>
                                      <td class ="sessionFacetBorder">
                                        {{activity.withoutwords}}
                                      </td>
                                    </tr>
                                    <ng-container *ngFor="let filters of activity.filters;let j=index">
                                      <tr class="popupDetails" *ngIf="j != 0">
                                          <td class ="sessionFacetBorder">{{filters.name}}</td>
                                          <td class ="sessionFacetBorder" *ngIf="filters && filters.selectedValues && filters.selectedValues.length > 0">
                                              <ng-container *ngIf="filters.selectedValues[0].min_value; else noMinValue">
                                                <div>
                                                  Min: {{ formatValue(filters.selectedValues[0].min_value) }}
                                                </div>
                                                <div>
                                                  Max: {{ formatValue(filters.selectedValues[0].max_value) }}
                                                </div>
                                                </ng-container>
                                              
                                              <ng-template #noMinValue>
                                                {{ filters.selectedValues[0] }}
                                              </ng-template>
                                            </td>
                                      </tr>
                                      <ng-container *ngFor="let facetValue of filters.selectedValues;let k = index">
                                          <tr class="popupDetails" *ngIf="k!=0" style="border-top: none">
                                              <td class="seesionfacets sessionFacetBorder"></td>
                                              <td class="seesionfacets sessionFacetBorder">
                                                <ng-container *ngIf="facetValue.min_value; else noMinValue">
                                                  <div>
                                                    Min: {{ formatValue(facetValue.min_value) }}
                                                  </div>
                                                  <div>
                                                    Max: {{ formatValue(facetValue.max_value) }}
                                                  </div>
                                                  </ng-container>
                                                
                                                <ng-template #noMinValue>
                                                  {{ facetValue }}
                                                </ng-template>
                                              </td>
                                          </tr>
                                      </ng-container>
                                    </ng-container>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </ng-container>
                          <tr *ngIf="sessionclickPosition?.length != 0" class="hover">
                            <td colspan="100" style="text-align: right;">
                              <pagination-controls id="sessionClickFirst" (pageChange)="sessionSort = true; getSessionClickPosition(0,$event, click)">
                              </pagination-controls>
                            </td>
                          </tr>
                        </table>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <!-- Pagination -->
                <tr *ngIf="searchclickPosition?.length != 0" class="hover">
                  <td colspan="100" style="text-align: right;">
                    <pagination-controls id="searchClickFirst" (pageChange)="getsearchClickPositionReport(0,$event)">
                    </pagination-controls>
                  </td>
                </tr>
                <!-- Pagination Ends -->

                <!-- Loader -->
                <tr *ngIf="searchclickPosition?.length === 0">
                  <td colspan="7">
                    <div style="text-align: center;">
                      <div class="spinner">
                        <div class="bounce1"></div>
                        <div class="bounce2"></div>
                        <div class="bounce3"></div>
                      </div>
                    </div>
                  </td>
                </tr>
                <!-- Loader Ends -->
              </tbody>
            </table>
          </div>
          <div>
            <ng-template #noData>
              <tr>
                <td colspan="8" class="no-docs">No Documents to show.
                  <img class="doc-img">
                </td>
              </tr>
            </ng-template>
          </div>
        </div>
      </div>
      <div>
      </div>
    </div>
  </div>
  <!-- Click Position Report Ends -->

  <div class="sectionMainDiv" *ngIf="selectedIndex===0">
    <div *ngIf="reportSettings?.length>0 && reportSettings[57] && reportSettings[57].is_enabled"
      class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
      <div>
        <div class="card card-block" id="types-of-docs" style="position: static;padding:0; min-height:auto;">
          <div class="analytics-section-heading">
            <!-- add -->
            {{reportSettings[57].label}}
            <app-email-and-download-reports *ngIf="createdCasesData && !createdCasesData?.isEmpty"
              [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="OverviewTab.tabName"
              [reportName]="OverviewTab.Reports[9]" [caseReportFilter]="caseReportFilter" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
            </app-email-and-download-reports>
            <app-user-metric-filter
              *ngIf="userMetricEnabled"
              [style.visibility]="caseDetails ? 'hidden' : 'visible'"
              [userMetricLabel]="userMetricLabel"
              [userMetricVariable]="userMetricVariable"
              [userId]="userUniqueId"
              [email]="userEmail"
              [uid]="searchClients.uid"
              [internalUser]="internalUser"
              [from]="range.from"
              [to]="range.to"
              [reportName]="'Cases Created'"
              [userMetricURL]="['/overview/createdCases']"
              [body]="{
                from: range.from,
                to: range.to,
                internalUser: internalUser,
                uid: searchClients.uid
              }"
              (userMetricEvent)="sendUserMetricValues($event)"
            ></app-user-metric-filter>
          </div>
          <div class="caseCreatedTable" *ngIf="caseCreatedDiv">
            <!-- LOADER -->
            <table class="caseTable table table-su card-1" style="width: 100%;">
              
                <tr class="analytics-section-header" style="white-space: nowrap;">
                  <th class="caseReport">#({{createdCasesData?.count}})</th>
                  <th class="caseReport" *ngFor="let filter of caseReportFilter">{{filter.label}} <span
                      (click)="filtersSearch(filter.label,!filter.filled)" class="search-icons-th">&nbsp;</span>
                    <!-- SearchBox -->
                    <div class="left">
                      <!-- <div class="caseTitleClass"> -->
                      <div id="search">
                        <input type="search"
                          [(ngModel)]="filter.value"
                          [placeholder]="filter.placeholder" 
                          class="filled"
                          [ngClass]= "(filter.filled == true)? 'show' : 'hide'"
                          (keyup.enter)="SearchCasetitle(filter.label,$event.target.value,1)">
                        <button class="tableBoxclose" *ngIf="filter.filled"
                          (click)="filter.filled=false; SearchCasetitle(filter.label,$event.target.value,1)">X</button>
                      </div>
                      <!-- </div> -->
                    </div>
                  </th>
                  <!-- session -->
                  <th class="caseReport">User</th>
                  <th class="caseReport" style="padding-right: 40px;">Date Time
                    <div>
                      <div class="swapverticalImage" (click)="SearchCasetitle(null,null,2)">
                      </div>
                    </div>
                  </th>
                </tr>
                <tbody *ngIf="!createdCasesData?.isEmpty;else noData">
                  <tr
                    *ngFor="let case of createdCasesData | paginate: {itemsPerPage: 10, currentPage: createdCasesData?.currentPage, id: 'first',totalItems: createdCasesData?.count};let i=index">
                    <td class="caseReport" style="vertical-align: middle;">{{(i+1)+(createdCasesData.currentPage-1)*10}}</td>
                    <td class="caseReport">{{case.case_uid}}</td>
                    <td class="caseReport" matTooltip="{{case.subject}}"  matTooltipPosition="below"  matTooltipClass="custom-tooltip-casereport">{{case.subject | truncate:[20, '...']}}</td>
                    <td class="caseReport" style="vertical-align: middle;">
                      <u>
                        <a href="JavaScript:void(0);" (click)="sessionIdDiv()">
                          <span style="cursor: pointer;" (click)="getSessionAndActivityReport(case.cookie)">
                            {{case.cookie}}
                          </span>
                        </a>
                      </u>
                    </td>
                    <td class="caseReport">{{case.email}}</td>
                    <td class="caseReport">{{case.is_internal ? 'Internal': 'External'}}</td>
                    <td class="caseReport" style="vertical-align: middle;">
                      {{case.ts.replace('T', ' ').split('.')[0] | timeZone : userTimeZone:"contentSource"}}</td>
                  </tr>
                  <tr *ngIf="createdCasesData?.length != 0" class="hoverNone">
                    <td colspan="100" style="text-align: right;">
                      <pagination-controls id="first" (pageChange)="getCaseCreatedReport(0,$event)">
                      </pagination-controls>
                    </td>
                  </tr>
                  <!-- Loader -->
                  <tr *ngIf="createdCasesData?.length === 0">
                    <td colspan="7">
                      <div style="text-align: center;">
                        <div class="spinner">
                          <div class="bounce1"></div>
                          <div class="bounce2"></div>
                          <div class="bounce3"></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- Loader Ends -->
              </tbody>
            </table>
          </div>
          <div>
            <ng-template #noData>
              <tr>
                <td colspan="8" class="no-docs">No Documents to show.
                  <img class="doc-img">
                </td>
              </tr>
            </ng-template>
          </div>
        </div>
      </div>
      <div>
        <div *ngIf="caseDetails">
          <div class="CaseReportbck" style="height: 50px;">
            <div id="exampleModalLongTitle CaseReportbckWhite" style="float: left; margin: 6px 22px;">
              <button type="button" class="btn CaseReportbckWhite" data-dismiss="modal" (click)="backToCaseReports()">
                <i class="fas fa-long-arrow-alt-left" style="font-size: 16px; color:gray; margin-right: 10px;"></i>Back
              </button>
            </div>
            <app-email-and-download-reports class="sessionIdReport" *ngIf="caseDetails" [searchClients]="searchClients"
              [range]="range" [internalUser]="internalUser" [tab]="OverviewTab.tabName"
              [reportName]="OverviewTab.Reports[8]" [cookie]="cookie" [hideDownload]="true" [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
            </app-email-and-download-reports>
          </div>
          <!-- <div class="loader" *ngIf="loader">
              <div class="spinner-border" style="width: 5rem; height: 5rem;color: blue;margin-bottom: 130px;"
                role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div> -->
          <div class="CaseReportbckWhite" style="padding: 22px;">
            <div class="CaseReportbckWhiteSmke" >
              <div style="padding: 15px;">
                <table style="width: 100%;" *ngIf="sessionActivityData?.sessionData">
                  <tbody>
                    <tr class="analytics-section-header CaseReportbckWhite" >
                      <th class="case-session-th-type">Session Id</th>
                      <th class="case-session-th-type">Searches</th>
                      <th class="case-session-th-type">Clicks</th>
                      <th class="case-session-th-type">Support Visit</th>
                      <th class="case-session-th-type">Case Logged</th>
                    </tr>
                    <tr class="analytics-section-header CaseReportbckWhite" *ngFor="let session of sessionActivityData.sessionData;">
                      <td class="case-session-th-type">{{session.cookie}}</td>
                      <td class="case-session-th-type">{{session.search_count}}</td>
                      <td class="case-session-th-type">{{session.click_count}}</td>
                      <td class="case-session-th-type">{{session.is_support_visit}}</td>
                      <td class="case-session-th-type">{{session.case_count}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <!-- ACTIVITY -->
            <div class="CaseReportbckWhite">
              <div style="padding: 15px;height: 400px;overflow: auto;">
                <table style="width: 100%;">
                  <tbody>
                    <tr class="analytics-section-header CaseReportbckWhite" >
                      <th class="case-session-th-type">Activity Type</th>
                      <th style="font-size:14px; width: 0.10%;">
                        <form autocomplete="off" style="margin: 0px">
                          <mat-select [(value)]="modeselectInside" multiple>
                            <mat-option #allSelectedInside (click)="toggleActivityInside('all')" [value]="'all'">All</mat-option>
                            <mat-option *ngFor="let filters of activityTypesInside; let i=index" [value]="filters" (click)="toggleActivityInside(filters)">
                                {{selectedActivityFilterType[filters]}}
                            </mat-option>
                        </mat-select>
                        </form>
                      </th>
                      <th class="case-session-th">Activity Details</th>
                      <th class="case-session-th">Time</th>
                    </tr>
                    <ng-container *ngFor="let activity of sessionActivityData?.activityData; let activityIndex = index">
                      <tr class="analytics-section-header CaseReportbckWhite" *ngIf="selectedActivityFilterInside[activity.type]==1">
                        <td colspan="2" class="case-session-th-type">{{activity.typeLabel}}</td>
                        <td class="case-session-th" *ngIf="activity.type=='search'">
                          {{activity.text_entered}}
                        </td>
                        <td class="case-session-th" *ngIf="activity.type=='conversion'">
                          <a target="_blank" href="{{activity.url}}">{{activity.title ? activity.title : activity.url}}</a>
                        </td>
                        <td class="case-session-th" *ngIf="activity.type=='pageView' || activity.type=='supportVisit'">
                          <a target="_blank" href="{{activity.window_url}}">{{activity.title ? activity.title : activity.window_url}}</a>
                        </td>
                        <td class="case-session-th" *ngIf="activity.type=='caseCreated'">
                          {{activity.caseSubject}}
                        </td>
                        <td class="case-session-th" *ngIf="activity.type=='caseDeflection'">
                          {{activity.caseSubject}}
                        </td>
                        <td class="case-session-th">{{activity.ts.replace('T', ' ').split('.')[0] | timeZone:userTimeZone:"contentSource"}}</td>
                      </tr>
                    </ng-container>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <!-- end of case details -->
      </div>
    </div>
  </div>
  
    <div class="sectionMainDiv" *ngIf="selectedIndex===0">
      <div *ngIf="reportSettings?.length>0 && reportSettings[26] && reportSettings[26].is_enabled"
        class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
        <div id="indexBySource">
          <div class="card card-block" id="types-of-docs" style="position: static;padding:0;min-height: 385px;">
            <div class="analytics-section-heading">
              <!-- add -->
              {{reportSettings[26].label}}
              <app-email-and-download-reports [searchClients]="searchClients" [range]="range"
                [internalUser]="internalUser" [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[3]"
                [hideDownload]="true"  [isEcosystemSelected]="isEcosystemSelected" [ecoSystem] = "ecoSystem" [AllUserEmails] = "AllUserEmails">
              </app-email-and-download-reports>
            </div>
  
            <div id="chartsdiv">
              <div class="col-xl-12 app-new-pie-chart-small">
                <app-new-pie-chart-small style="padding:0px;"></app-new-pie-chart-small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sectionMainDiv" *ngIf="selectedIndex===0">
      <div *ngIf="reportSettings?.length>0 && reportSettings[27] && reportSettings[27].is_enabled"
        class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
        <div class="col-xl-12" style="padding-right: 0px; padding-left: 0px;">
          <div class="card card-block" style="padding:0;" id="newlyAddedContentSource">
            <div class="analytics-section-heading">
              {{reportSettings[27].label}}
  
              <app-email-and-download-reports [searchClients]="searchClients" [range]="range"
                [internalUser]="internalUser" [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[4]"
                [hideDownload]="true" [AllUserEmails] = "AllUserEmails">
              </app-email-and-download-reports>
            </div>
  
            <div class="center-align" style="padding: 20px;">
              <div class="col-xl-12 responsive" style="padding-left: 0px;padding-right: 0px;">
                <app-bar-chart [range]="range" [uid]="searchClients.uid" [reportSettings]="reportSettings[27]">
                </app-bar-chart>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  
    <div class="sectionMainDiv" *ngIf="selectedIndex===0">
      <div class="">
        <div *ngIf="reportSettings?.length>0 && reportSettings[41] && reportSettings[41].is_enabled"
          class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
          <div class="col-xl-12" style="padding-right: 0px; padding-left: 0px;">
            <div class="card card-block" style="padding:0;" id="topRatedFeaturedResults">
              <div class="analytics-section-heading">
                {{reportSettings[41].label}}
  
                <app-email-and-download-reports *ngIf="topRatedFeaturedResult && (!topRatedFeaturedResult.isEmpty)"
                  [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                  [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[5]" [hideDownload]="true" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                </app-email-and-download-reports>
                <app-user-metric-filter
                  *ngIf="userMetricEnabled"
                  [userMetricLabel]="userMetricLabel"
                  [userMetricVariable]="userMetricVariable"
                  [userId]="userUniqueId"
                  [email]="userEmail"
                  [uid]="searchClients.uid"
                  [internalUser]="internalUser"
                  [from]="range.from"
                  [to]="range.to"
                  [reportName]="'Top Rated Featured Results'"
                  [userMetricURL]="['/overview/featuredSnippet']"
                  [body]="{
                    from: range.from,
                    to: range.to,
                    internalUser: internalUser,
                    uid: searchClients.uid
                  }"
                  (userMetricEvent)="sendUserMetricValues($event)"
                ></app-user-metric-filter>
              </div>
  
              <div style="padding: 10px; margin: 0;" class="card">
                <div *ngIf="topRatedFeaturedResult" class="perfect"
                  style="height: 355px; margin: 0 auto; overflow-y:auto; overflow-x: hidden; border: 2px solid rgb(239, 239, 239);">
                  <table class="table table-su" style="margin: 0px;">
                    <thead class="t-head">
                      <tr>
                        <th>Featured Link</th>
                        <th>Votes (Yes/No)</th>
                      </tr>
                    </thead>
                    <ng-container *ngIf="!topRatedFeaturedResult.isEmpty">
                      <tr *ngFor="let d of topRatedFeaturedResult" style="word-break: break-word;">
                        <td>
                          <a class="su-docs-link" [href]="d.url" target="_blank">{{d.title || d.url}}</a>
                        </td>
                        <td>
                          <span style="color: #5abefe;">{{d.yes_votes}}</span>/
                          <span style="color: #f4961c;">{{d.no_votes}}</span>
                        </td>
                      </tr>
                    </ng-container>
                    <tr *ngIf="topRatedFeaturedResult.isEmpty">
                      <td colspan="2" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                    <tr *ngIf="topRatedFeaturedResult?.length === 0">
                      <td colspan="2" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="sectionMainDiv" *ngIf="selectedIndex===0">
      <div class="">
        <div *ngIf="reportSettings?.length>0 && reportSettings[42] && reportSettings[42].is_enabled"
          class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
          <div class="col-xl-12" style="padding-right: 0px; padding-left: 0px;">
            <div class="card card-block" style="padding:0;" id="knowledgeGraphTitles">
              <div class="analytics-section-heading">
                {{reportSettings[42].label}}
  
                <app-email-and-download-reports *ngIf="topKnowledgeGraphTitles && (!topKnowledgeGraphTitles.isEmpty)"
                  [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                  [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[6]" [hideDownload]="true" [AllUserEmails] = "AllUserEmails">
                </app-email-and-download-reports>
              </div>
              <div style="padding: 10px; margin: 0;" class="card">
                <div *ngIf="topKnowledgeGraphTitles" class="perfect"
                  style="height: 355px; margin: 0 auto; overflow-y:auto; overflow-x: hidden; border: 2px solid rgb(239, 239, 239);">
                  <table class="table table-su" style="margin: 0px;">
                    <thead class="t-head">
                      <tr>
                        <th>Title</th>
                        <th>Votes (Yes/No)</th>
                      </tr>
                    </thead>
                    <ng-container *ngIf="!topKnowledgeGraphTitles.isEmpty">
                      <tr *ngFor="let d of topKnowledgeGraphTitles" style="word-break: break-word;">
                        <td>
                          <a class="su-docs-link" [href]="d.url" target="_blank">{{d.title || d.url}}</a>
                        </td>
                        <td>
                          <span style="color: #5abefe;">{{d.yes_votes}}</span>/
                          <span style="color: #f4961c;">{{d.no_votes}}</span>
                        </td>
                      </tr>
                    </ng-container>
                    <tr *ngIf="topKnowledgeGraphTitles.isEmpty">
                      <td colspan="2" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                    <tr *ngIf="topKnowledgeGraphTitles?.length === 0">
                      <td colspan="2" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- code for GPT Feedback  report  -->
    
    <div class="sectionMainDiv" *ngIf="selectedIndex===0">
      <div *ngIf="reportSettings.length>0 && reportSettings[65] && reportSettings[65].is_enabled"
        class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
        <div>
          <div class="card card-block" id="types-of-docs" style="position: static;padding:0; min-height:auto;">
            <div class="analytics-section-heading d-flex align-items-center">
              SearchUnifyGPT Feedback

              <div class="mr-auto">
                <span class="su__gpt_icons_container margin-left-20px">
                  <span class="total-likes">
                    <svg class="margin-right-8px" xmlns="http://www.w3.org/2000/svg" width="11.962" height="10.889"
                      viewBox="0 0 11.962 10.889">
                      <path id="thumb_down_FILL1_wght400_GRAD0_opsz48_1_"
                        data-name="thumb_down_FILL1_wght400_GRAD0_opsz48 (1)"
                        d="M13.107,9.6a.9.9,0,0,1,.854.854V11.62a2.17,2.17,0,0,1-.036.363,1.488,1.488,0,0,1-.107.363l-1.652,3.816a1.236,1.236,0,0,1-.42.513,1.031,1.031,0,0,1-.619.214h-5.8a.851.851,0,0,1-.854-.854V9.94a.822.822,0,0,1,.064-.313.813.813,0,0,1,.178-.271l2.948-3.1a.839.839,0,0,1,.477-.242.766.766,0,0,1,.52.1.9.9,0,0,1,.363.4.812.812,0,0,1,.064.527L8.55,9.6Zm-10.3,7.291A.827.827,0,0,1,2,16.078V10.41A.827.827,0,0,1,2.812,9.6a.827.827,0,0,1,.812.812v5.668a.827.827,0,0,1-.812.812Z"
                        transform="translate(-2 -6)" fill="#56c5fe" />
                    </svg>
                    <span class="margin-right-20px">{{totalLikes}}</span>
                  </span>
                  <span class="total-dislikes"><svg class="margin-right-8px" xmlns="http://www.w3.org/2000/svg" width="11.962" height="10.889"
                      viewBox="0 0 11.962 10.889">
                      <path id="thumb_down_FILL1_wght400_GRAD0_opsz48_1_"
                        data-name="thumb_down_FILL1_wght400_GRAD0_opsz48 (1)"
                        d="M13.107,9.6a.9.9,0,0,1,.854.854V11.62a2.17,2.17,0,0,1-.036.363,1.488,1.488,0,0,1-.107.363l-1.652,3.816a1.236,1.236,0,0,1-.42.513,1.031,1.031,0,0,1-.619.214h-5.8a.851.851,0,0,1-.854-.854V9.94a.822.822,0,0,1,.064-.313.813.813,0,0,1,.178-.271l2.948-3.1a.839.839,0,0,1,.477-.242.766.766,0,0,1,.52.1.9.9,0,0,1,.363.4.812.812,0,0,1,.064.527L8.55,9.6Zm-10.3,7.291A.827.827,0,0,1,2,16.078V10.41A.827.827,0,0,1,2.812,9.6a.827.827,0,0,1,.812.812v5.668a.827.827,0,0,1-.812.812Z"
                        transform="translate(-2 -6)" fill="#ff9b04" />
                    </svg>
                    <span>{{totalDislikes}}</span></span>
                </span>
              </div>
    
    
              <app-email-and-download-reports *ngIf="GPTfeedbackData && GPTfeedbackData.length != 0"
                [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="OverviewTab.tabName"
                [reportName]="OverviewTab.Reports[15]" [hideDownload]="true" [textFeedBackToggle]="textFeedBackToggle"
                [advertisementFiltersOnInitialLoad]="advertisementFiltersOnInitialLoad.sortType"
                [searchQuery1]="searchQuery" [reactionFilterType]="reactionFilterType" [AllUserEmails]="AllUserEmails">
              </app-email-and-download-reports>
            </div>
    
            <div id="searchExperienceFeedbackReport" style="padding: 10px; margin: 0 0 20px 0;" class="card">
              <div class="perfect gptfeedbackdata" style="height: 355px;">
                <table class="searchFeedbkTable table table-su card-1" style="width: 100%;">
    
                  <colgroup>
                    <col width="54%">
                    <col width="14%">
                    <col width="14%">
                    <col width="18%">
                  </colgroup>
    
                  <thead class="search-feedbk-header gpt-feedback" style="white-space: nowrap;">
                    <ng-container class="d-flex" *ngFor="let filter of searchUnifyGPTFeedback">
                      <th *ngIf="!filter.filled">{{filter.label}} <span
                          (click)="openTextSearchForallSearchReport('searchUnifyGPTFeedback', filter.label,!filter.filled)"
                          class="search-icons-th">&nbsp;</span>
                      </th>
                      <th style="width: 25%;" *ngIf="filter.filled">
                        <div [id]="filter.id" class="su_search_label">
                          <input type="search" [(ngModel)]="searchQuery" [placeholder]="filter.placeholder"
                            class="filled su_input_align" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                            (keyup.enter)="getFeedbackForQueryString()">
                          <button class="srtableBoxclose su_button_align" *ngIf="filter.filled"
                            (click)="filter.filled=false; filter.value=''; clearUserInput();">X</button>
                        </div>
                      </th>
                    </ng-container>
                    <th>Response</th>
                    <th class="su__gptFilters_modal" style="position:relative;">Reaction <span style="cursor: pointer;">
                        <button class="su__comment-btn-response" [matMenuTriggerFor]="menuOne"
                          (menuOpened)=" menuOneOpen=true" (menuClosed)="menuOneOpen=false"> <svg
                          [ngClass]="reactionFilterType !== 'all' ? 'su__filter_svg_align' : ''" id="filter_alt_black_24dp_1_" data-name="filter_alt_black_24dp (1)"
                            xmlns="http://www.w3.org/2000/svg" width="15.494" height="15.494" viewBox="0 0 15.494 15.494">
                            <path id="Path_18373" data-name="Path 18373" d="M0,0H15.494m0,15.494H0" fill="none" />
                            <path class="highlight" id="Path_18374" data-name="Path 18374"
                              d="M4.175,5.039C5.673,6.963,7.887,9.81,7.887,9.81v3.228a1.3,1.3,0,0,0,1.291,1.291h0a1.3,1.3,0,0,0,1.291-1.291V9.81s2.214-2.847,3.712-4.771A.645.645,0,0,0,13.665,4H4.685A.644.644,0,0,0,4.175,5.039Z"
                              transform="translate(-1.432 -1.418)" fill="#707070" />
                            <path id="Path_18375" data-name="Path 18375" d="M0,0H15.494V15.494H0Z" fill="none" />
                          </svg></button>
                      </span>
                      <mat-menu #menuOne="matMenu">
                        <div (click)="$event.stopPropagation();">
                          <button mat-menu-item> <mat-radio-button (change)="radioChange($event)" value="all" [checked]="reactionFilterType == 'all' ">Select All</mat-radio-button></button>
                          <button mat-menu-item><mat-radio-button (change)="radioChange($event)" value="true" [checked]="reactionFilterType == 'true' ">Thumbs up</mat-radio-button></button>
                          <button mat-menu-item> <mat-radio-button (change)="radioChange($event)" value="false" [checked]="reactionFilterType == 'false'">Thumbs down</mat-radio-button> </button>
                        </div>
                      </mat-menu>
                    </th>
                    <th>Email</th>
                  </thead>
                  <tbody>
                    <ng-container class="searchFeedbkBody">
                      <!-- <tr *ngFor="let item of GPTfeedbackData | paginate: {itemsPerPage: 10, currentPage:advertisementFiltersOnInitialLoad.pageNo , id: 'advertisementClick',totalItems:advertismentSearchedStrings.total[0].count };let activityIndex = index"> -->
                      <tr
                        *ngFor="let item of GPTfeedbackData | paginate: {itemsPerPage: 10, currentPage: currentPageNo , id: 'advertisementClick1', totalItems: totalFeedback };let activityIndex = index">
                        <td class="">
                          <!-- matTooltip={{item.text_entered}} -->
                          <span matTooltip={{item.text_entered}} maTooltipPosition="bottom">
                            {{item.text_entered.length > 74 ? item.text_entered.substring(0, 74)+"..." : item.text_entered}}
                          </span>
                        </td>
                        <td class="searchFeedbkFeedback">
                          <button class="su__comment-btn " (menuOpened)="item.menuResponseOpen=true"
                            (menuClosed)="item.menuResponseOpen=false" [matMenuTriggerFor]="menuResponse">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                              <path id="Path_18749" data-name="Path 18749"
                                d="M20,2H4A2,2,0,0,0,2.01,4L2,22l4-4H20a2.006,2.006,0,0,0,2-2V4A2.006,2.006,0,0,0,20,2ZM8,14H6V12H8Zm0-3H6V9H8ZM8,8H6V6H8Zm6,6H11a1,1,0,0,1,0-2h3a1,1,0,0,1,0,2Zm3-3H11a1,1,0,0,1,0-2h6a1,1,0,0,1,0,2Zm0-3H11a1,1,0,0,1,0-2h6a1,1,0,0,1,0,2Z"
                                transform="translate(-2 -2)" [style.fill]="item.menuResponseOpen ? '#56C6FF' : '#8b8b8b'" />
                            </svg>
                          </button>
                          <mat-menu #menuResponse="matMenu" class="custom-mat-menu">
                            <div class="menu-content">
                              {{item.llm_response}}
                            </div>
                          </mat-menu> 
                        </td>
                        <td class="searchFeedbkReportedBy">
    
                          <svg *ngIf="item.reaction == 0 && item.has_feedback == false" xmlns="http://www.w3.org/2000/svg" width="17.051"
                            height="15.522" viewBox="0 0 17.051 15.522">
                            <path id="thumb_down_FILL1_wght400_GRAD0_opsz48_1_"
                              data-name="thumb_down_FILL1_wght400_GRAD0_opsz48 (1)"
                              d="M17.833,11.129a1.288,1.288,0,0,1,1.218,1.218v1.664a3.093,3.093,0,0,1-.051.518,2.121,2.121,0,0,1-.152.518l-2.355,5.44a1.762,1.762,0,0,1-.6.731,1.47,1.47,0,0,1-.883.3H6.75A1.213,1.213,0,0,1,5.532,20.3V11.616a1.172,1.172,0,0,1,.091-.447,1.159,1.159,0,0,1,.254-.386l4.2-4.425a1.2,1.2,0,0,1,.68-.345,1.093,1.093,0,0,1,.741.142,1.277,1.277,0,0,1,.518.568,1.157,1.157,0,0,1,.091.751l-.771,3.654ZM3.157,21.522a1.111,1.111,0,0,1-.812-.345A1.111,1.111,0,0,1,2,20.365V12.286a1.111,1.111,0,0,1,.345-.812,1.128,1.128,0,0,1,1.624,0,1.111,1.111,0,0,1,.345.812v8.079a1.111,1.111,0,0,1-.345.812A1.111,1.111,0,0,1,3.157,21.522Z"
                              transform="translate(-2 -6)" fill="#56c5fe" />
                          </svg>  
                          <button class="su__comment-btn  su__padding-zero" [matMenuTriggerFor]="menuTwo"
                            *ngIf="item.reaction == 1 && item.has_feedback == true">
                            <svg xmlns="http://www.w3.org/2000/svg" width="26.496" height="24.5" viewBox="0 0 26.496 24.5">
                              <g id="Group_19699" data-name="Group 19699" transform="translate(-2304.037 -1559.903)">
                                <rect id="Rectangle_11208" data-name="Rectangle 11208" width="14.953" height="13.638"
                                  transform="translate(2307.024 1562.181)" fill="none" />
                                <rect id="Rectangle_11209" data-name="Rectangle 11209" width="14.953" height="13.638"
                                  transform="translate(2307.024 1562.181)" fill="none" />
                                <path id="thumb_down_FILL1_wght400_GRAD0_opsz48_1_"
                                  data-name="thumb_down_FILL1_wght400_GRAD0_opsz48 (1)"
                                  d="M15.833,5.129a1.165,1.165,0,0,1,.842.376,1.165,1.165,0,0,1,.376.842V8.012A3.093,3.093,0,0,1,17,8.529a2.121,2.121,0,0,1-.152.518l-2.355,5.44a1.762,1.762,0,0,1-.6.731,1.47,1.47,0,0,1-.883.3H4.75A1.213,1.213,0,0,1,3.532,14.3V5.616a1.172,1.172,0,0,1,.091-.447,1.159,1.159,0,0,1,.254-.386L8.079.359a1.2,1.2,0,0,1,.68-.345A1.093,1.093,0,0,1,9.5.156a1.277,1.277,0,0,1,.518.568,1.157,1.157,0,0,1,.091.751L9.337,5.129ZM1.157,15.522a1.111,1.111,0,0,1-.812-.345A1.111,1.111,0,0,1,0,14.365V6.286a1.111,1.111,0,0,1,.345-.812,1.111,1.111,0,0,1,.812-.345,1.111,1.111,0,0,1,.812.345,1.111,1.111,0,0,1,.345.812v8.079a1.111,1.111,0,0,1-.345.812A1.111,1.111,0,0,1,1.157,15.522Z"
                                  transform="translate(2330.532 1584.403) rotate(180)" fill="#ff9b04" />
                                <path id="thumb_down_FILL1_wght400_GRAD0_opsz48_1_2"
                                  data-name="thumb_down_FILL1_wght400_GRAD0_opsz48 (1)"
                                  d="M15.833,5.129a1.165,1.165,0,0,1,.842.376,1.165,1.165,0,0,1,.376.842V8.012A3.093,3.093,0,0,1,17,8.529a2.121,2.121,0,0,1-.152.518l-2.355,5.44a1.762,1.762,0,0,1-.6.731,1.47,1.47,0,0,1-.883.3H4.75A1.213,1.213,0,0,1,3.532,14.3V5.616a1.172,1.172,0,0,1,.091-.447,1.159,1.159,0,0,1,.254-.386L8.079.359a1.2,1.2,0,0,1,.68-.345A1.093,1.093,0,0,1,9.5.156a1.277,1.277,0,0,1,.518.568,1.157,1.157,0,0,1,.091.751L9.337,5.129ZM1.157,15.522a1.111,1.111,0,0,1-.812-.345A1.111,1.111,0,0,1,0,14.365V6.286a1.111,1.111,0,0,1,.345-.812,1.111,1.111,0,0,1,.812-.345,1.111,1.111,0,0,1,.812.345,1.111,1.111,0,0,1,.345.812v8.079a1.111,1.111,0,0,1-.345.812A1.111,1.111,0,0,1,1.157,15.522Z"
                                  transform="translate(2330.532 1584.403) rotate(180)" fill="#ff9b04" />
                                <path id="Subtraction_7" data-name="Subtraction 7"
                                  d="M4.737,16.358a.976.976,0,0,1-.838-.466,1.626,1.626,0,0,1-.2-.755c-.022-.322-.017-.642-.013-.981,0-.144,0-.29,0-.439H2.152A2.088,2.088,0,0,1,0,11.571C0,8.108,0,5.025,0,2.145A2.069,2.069,0,0,1,2.18,0H15.746a2.084,2.084,0,0,1,2.185,2.166c0,1.821,0,3.678,0,5.519a6.573,6.573,0,0,0-1.053-.422c0-1.643,0-3.339,0-5.041A1.078,1.078,0,0,0,15.7,1.053l-6.734,0-6.735,0A1.079,1.079,0,0,0,1.055,2.22c0,3.074,0,6.192,0,9.267a1.076,1.076,0,0,0,1.175,1.171H3.19l.4,0a1.063,1.063,0,0,1,1.169,1.168c0,.294,0,.588,0,.906v.045c0,.154,0,.313,0,.481.266-.224.527-.442.784-.656.594-.5,1.154-.963,1.71-1.448a1.947,1.947,0,0,1,1.179-.494,6.679,6.679,0,0,0-.063.915q0,.086,0,.172A1.09,1.09,0,0,0,7.9,14c-.865.759-1.655,1.437-2.416,2.071A1.188,1.188,0,0,1,4.737,16.358Z"
                                  transform="translate(2304.532 1560.403)" fill="#ff9b04" stroke="rgba(0,0,0,0)"
                                  stroke-width="1" />
                                <path id="Path_18411" data-name="Path 18411"
                                  d="M44.1,38.22q2.548,0,5.1,0a1.042,1.042,0,0,1,.39.057.479.479,0,0,1,.31.546.486.486,0,0,1-.443.444,2.618,2.618,0,0,1-.339.014H39.074c-.538,0-.781-.164-.784-.525s.256-.539.774-.539H44.1"
                                  transform="translate(2269.405 1525.339)" fill="#ff9b04" />
                                <path id="Path_18412" data-name="Path 18412"
                                  d="M44.08,71.326H39.046a2.553,2.553,0,0,1-.278,0,.532.532,0,0,1-.479-.547.516.516,0,0,1,.492-.5c.092-.008.185-.005.278-.005H49.127c.527,0,.778.173.773.533s-.254.523-.787.523H44.08"
                                  transform="translate(2269.406 1495.906)" fill="#ff9b04" />
                                <path id="Path_18413" data-name="Path 18413"
                                  d="M41.644,103.115c-.915,0-1.831.01-2.746-.008a.887.887,0,0,1-.53-.181.5.5,0,0,1,.2-.844,1.066,1.066,0,0,1,.333-.045q2.746,0,5.493,0c.4,0,.629.158.673.449.054.362-.195.626-.616.628-.936.006-1.872,0-2.808,0"
                                  transform="translate(2269.478 1466.751)" fill="#ff9b04" />
                              </g>
                            </svg>
                          </button>
                          <mat-menu #menuTwo="matMenu" class="custom-mat-menu">
                            <div class="menu-content">
                              <ul *ngIf="item.feedback_tags && item.feedback_tags.length > 0" class="feedback-tags" [ngClass]="item.feedback.length === 0 ? 'mb-0' : '' ">
                                <li *ngFor="let tags of item.feedback_tags">{{tags}}</li>
                              </ul>
                              {{item.feedback}}
                            </div>
                          </mat-menu>
                           <button class="su__comment-btn  su__padding-zero" [matMenuTriggerFor]="menuThree"
                            *ngIf="item.reaction == 0 && item.has_feedback == true">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25.766" height="24.5" viewBox="0 0 25.766 24.5">
                              <g id="Group_19698" data-name="Group 19698" transform="translate(-16083.665 -1039)">
                                <path id="thumb_down_FILL1_wght400_GRAD0_opsz48_1_"
                                  data-name="thumb_down_FILL1_wght400_GRAD0_opsz48 (1)"
                                  d="M17.833,11.129a1.288,1.288,0,0,1,1.218,1.218v1.664a3.093,3.093,0,0,1-.051.518,2.121,2.121,0,0,1-.152.518l-2.355,5.44a1.762,1.762,0,0,1-.6.731,1.47,1.47,0,0,1-.883.3H6.75A1.213,1.213,0,0,1,5.532,20.3V11.616a1.172,1.172,0,0,1,.091-.447,1.159,1.159,0,0,1,.254-.386l4.2-4.425a1.2,1.2,0,0,1,.68-.345,1.093,1.093,0,0,1,.741.142,1.277,1.277,0,0,1,.518.568,1.157,1.157,0,0,1,.091.751l-.771,3.654ZM3.157,21.522a1.111,1.111,0,0,1-.812-.345A1.111,1.111,0,0,1,2,20.365V12.286a1.111,1.111,0,0,1,.345-.812,1.128,1.128,0,0,1,1.624,0,1.111,1.111,0,0,1,.345.812v8.079a1.111,1.111,0,0,1-.345.812A1.111,1.111,0,0,1,3.157,21.522Z"
                                  transform="translate(16081.665 1033)" fill="#56c5fe" />
                                <g id="Group_19625" data-name="Group 19625" transform="translate(16091 1046.642)">
                                  <rect id="Rectangle_11208" data-name="Rectangle 11208" width="14.953" height="13.638"
                                    transform="translate(0.491 0.942)" fill="none" />
                                  <rect id="Rectangle_11209" data-name="Rectangle 11209" width="14.953" height="13.638"
                                    transform="translate(0.491 0.942)" fill="none" />
                                  <path id="Subtraction_7" data-name="Subtraction 7"
                                    d="M4.737,16.358a.976.976,0,0,1-.838-.466,1.626,1.626,0,0,1-.2-.755c-.022-.322-.017-.642-.013-.981,0-.144,0-.29,0-.439H2.152A2.088,2.088,0,0,1,0,11.571C0,8.108,0,5.025,0,2.145A2.069,2.069,0,0,1,2.18,0H15.747a2.084,2.084,0,0,1,2.185,2.166c0,1.821,0,3.678,0,5.519a6.573,6.573,0,0,0-1.053-.422c0-1.643,0-3.339,0-5.041A1.078,1.078,0,0,0,15.7,1.053l-6.734,0-6.735,0A1.079,1.079,0,0,0,1.055,2.22c0,3.074,0,6.192,0,9.267a1.076,1.076,0,0,0,1.175,1.171H3.19l.4,0a1.063,1.063,0,0,1,1.169,1.168c0,.294,0,.588,0,.906v.045c0,.154,0,.313,0,.481.266-.224.527-.442.784-.656.594-.5,1.154-.963,1.71-1.448a1.947,1.947,0,0,1,1.179-.494,6.679,6.679,0,0,0-.063.915q0,.086,0,.172A1.09,1.09,0,0,0,7.9,14c-.865.759-1.655,1.437-2.416,2.071A1.188,1.188,0,0,1,4.737,16.358Z"
                                    transform="translate(17.936 16.358) rotate(180)" fill="#56c5fe" stroke="rgba(0,0,0,0)"
                                    stroke-width="1" />
                                  <path id="Path_18411" data-name="Path 18411"
                                    d="M44.093,39.284q-2.548,0-5.1,0a1.042,1.042,0,0,1-.39-.057.479.479,0,0,1-.31-.546.486.486,0,0,1,.443-.444,2.618,2.618,0,0,1,.339-.014H49.117c.538,0,.781.164.784.525s-.256.539-.774.539H44.093"
                                    transform="translate(-35.127 -26.082)" fill="#56c5fe" />
                                  <path id="Path_18412" data-name="Path 18412"
                                    d="M44.109,70.272h5.034a2.554,2.554,0,0,1,.278,0,.532.532,0,0,1,.479.547.516.516,0,0,1-.492.5c-.092.008-.185.005-.278.005H39.062c-.527,0-.778-.173-.773-.533s.254-.523.787-.523h5.034"
                                    transform="translate(-35.126 -60.742)" fill="#56c5fe" />
                                  <path id="Path_18413" data-name="Path 18413"
                                    d="M41.641,102.037c.916,0,1.831-.01,2.746.008a.887.887,0,0,1,.53.181.5.5,0,0,1-.2.844,1.066,1.066,0,0,1-.333.045q-2.747,0-5.493,0c-.4,0-.629-.158-.673-.449-.054-.362.195-.626.616-.628.936-.006,1.872,0,2.808,0"
                                    transform="translate(-30.296 -95.142)" fill="#56c5fe" />
                                </g>
                              </g>
                            </svg>
                          </button>
                          <mat-menu #menuThree="matMenu" class="custom-mat-menu">
                            <div class="menu-content">
                              <ul *ngIf="item.feedback_tags && item.feedback_tags.length > 0" class="feedback-tags" [ngClass]="item.feedback.length === 0 ? 'mb-0' : '' ">
                                <li *ngFor="let tags of item.feedback_tags">{{tags}}</li>
                              </ul>
                              {{item.feedback}}
                            </div>
                          </mat-menu>
                        <svg *ngIf="item.reaction == 1 && item.has_feedback == false" xmlns="http://www.w3.org/2000/svg" width="17.051"
                            height="15.522" viewBox="0 0 17.051 15.522">
                            <path id="thumb_down_FILL1_wght400_GRAD0_opsz48_1_"
                              data-name="thumb_down_FILL1_wght400_GRAD0_opsz48 (1)"
                              d="M17.833,11.129a1.288,1.288,0,0,1,1.218,1.218v1.664a3.093,3.093,0,0,1-.051.518,2.121,2.121,0,0,1-.152.518l-2.355,5.44a1.762,1.762,0,0,1-.6.731,1.47,1.47,0,0,1-.883.3H6.75A1.213,1.213,0,0,1,5.532,20.3V11.616a1.172,1.172,0,0,1,.091-.447,1.159,1.159,0,0,1,.254-.386l4.2-4.425a1.2,1.2,0,0,1,.68-.345,1.093,1.093,0,0,1,.741.142,1.277,1.277,0,0,1,.518.568,1.157,1.157,0,0,1,.091.751l-.771,3.654ZM3.157,21.522a1.111,1.111,0,0,1-.812-.345A1.111,1.111,0,0,1,2,20.365V12.286a1.111,1.111,0,0,1,.345-.812,1.128,1.128,0,0,1,1.624,0,1.111,1.111,0,0,1,.345.812v8.079a1.111,1.111,0,0,1-.345.812A1.111,1.111,0,0,1,3.157,21.522Z"
                              transform="translate(19.051 21.522) rotate(180)" fill="#ff9b04" />
                          </svg>
    
                          <!-- <div (click)="$event.stopPropagation();" class="su__gpt_filters_container">
                                                  <mat-radio-group aria-label="Select an option" class="su__gpt_radio_group">
                                                    <mat-radio-button value="1">Select All</mat-radio-button>
                                                    <mat-radio-button value="2">Thumbs up</mat-radio-button>
                                                    <mat-radio-button value="2">Thumbs down</mat-radio-button>
                        
                                                  </mat-radio-group>
                                              </div> -->
    
                        </td>
                        <td>
                          <span *ngIf="item.email && item.email.length">
                            {{item.email}}
                          </span>
                          <span *ngIf="!item.email">
                            N/A
                          </span>
                        </td>
                      </tr>
                    </ng-container>
                    <tr *ngIf="GPTfeedbackData && GPTfeedbackData.length === 0 && !feedbackLoader">
                      <td colspan="5" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                    <tr *ngIf="feedbackLoader">
                      <td colspan="4" style="text-align: center;">
                        <div>
                          <div class="spinner">
                            <div class="bounce1"></div>
                            <div class="bounce2"></div>
                            <div class="bounce3"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr class="hover search-feedbk-footer" *ngIf="GPTfeedbackData && GPTfeedbackData.length != 0">
                      <td colspan="100" style="text-align: right;">
                        <pagination-controls id="advertisementClick1" (pageChange)="setPaginationForFeedback($event)">
                        </pagination-controls>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div>
            </div>
          </div>
        </div>
        <div>
        </div>
      </div>
    </div>
    
    <div class="sectionMainDiv " *ngIf="selectedIndex===0">
      <div class="">
        <div *ngIf="reportSettings?.length>0 && reportSettings[43] && reportSettings[43].is_enabled"
          class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 0px;padding-left: 0px;margin-top: 20px;">
          <div class="col-xl-12" style="padding-right: 0px; padding-left: 0px;">
            <div class="card card-block" style="padding:0;" id="pageRatingFeedback">
              <div class="analytics-section-heading">
                {{reportSettings[43].label}}
  
                <app-email-and-download-reports *ngIf="pageRatingTitles && (!pageRatingTitles.isEmpty)"
                  [searchClients]="searchClients" [range]="range" [internalUser]="internalUser"
                  [tab]="OverviewTab.tabName" [reportName]="OverviewTab.Reports[7]" [hideDownload]="true" [filterSelected]="FeedbackquerySelected" [starsdownloadReport]="starsdownloadReport" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
                </app-email-and-download-reports>
                <app-user-metric-filter
                  *ngIf="userMetricEnabled"
                  [userMetricLabel]="userMetricLabel"
                  [userMetricVariable]="userMetricVariable"
                  [userId]="userUniqueId"
                  [email]="userEmail"
                  [uid]="searchClients.uid"
                  [internalUser]="internalUser"
                  [from]="range.from"
                  [to]="range.to"
                  [reportName]="'Content Experience Feedback'"
                  [userMetricURL]="['/overview/PageRating']"
                  [body]="{
                    from: range.from,
                    to: range.to,
                    internalUser: internalUser,
                    uid: searchClients.uid
                  }"
                  (userMetricEvent)="sendUserMetricValues($event)"
                ></app-user-metric-filter>
              </div>
  
              <div id="pageRatingFeedbackReport" style="padding: 10px; margin: 0 0 20px 0;" class="card">
                <div *ngIf="pageRatingTitles" class="perfect"
                  style="height: 355px; margin: 0 auto; overflow-y:auto; overflow-x: hidden; border: 2px solid rgb(239, 239, 239);">
                  <table class="table table-su" style="margin: 0px;">
                    <thead class="t-head">
                      <tr>
                        <th style="width: 70%;">Document</th>
                        <th>Rating
                          <svg (menuOpened)="contentFeedbackModal(); menuOpen=true" (menuClosed)="menuOpen=false" [matMenuTriggerFor]="menu" #filterRef xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20">
                            <defs>
                              <clipPath id="clip-filteer">
                                <rect width="20" height="20" />
                              </clipPath>
                            </defs>
                            <g id="filteer" clip-path="url(#clip-filteer)">
                              <g id="filter_alt_black_24dp_2_" data-name="filter_alt_black_24dp (2)">
                                <path id="Path_3624" data-name="Path 3624" d="M0,0H20m0,20H0" fill="none" />
                                <path id="Path_3625" data-name="Path 3625"
                                  d="M4.217,5.358c1.958,2.514,4.852,6.235,4.852,6.235v4.219A1.693,1.693,0,0,0,10.757,17.5h0a1.693,1.693,0,0,0,1.688-1.687V11.594S15.34,7.873,17.3,5.358A.843.843,0,0,0,16.622,4H4.884A.842.842,0,0,0,4.217,5.358Z"
                                  transform="translate(-0.757 -0.75)" fill="#aaa" />
                                <path id="Path_3626" data-name="Path 3626" d="M0,0H20V20H0Z" fill="none" />
                              </g>
                            </g>
                          </svg>
                          <mat-menu class="su__matMenu" backdropClass="page-rating-menu" #menu="matMenu" xPosition="after">
                            <ng-template matMenuContent>
                            <div (click)="$event.stopPropagation();" *ngIf="menuOpen">
                              <mat-accordion class="feedback-rating" multi="true">
                                <mat-expansion-panel [expanded]="FeedbackquerySelected === 'all'" (opened)="FeedbackquerySelected='all'">
                                  <mat-expansion-panel-header [ngClass]="FeedbackquerySelected !== 'all' ? 'showDialog' : ''" (click)="filterChange('parent','all');">
                                    <mat-radio-button name="all" class="feedback_radio" value="0" [checked]="FeedbackquerySelected === 'all'">All</mat-radio-button>
                                  </mat-expansion-panel-header>
                                  <ng-template matExpansionPanelContent>
                                  <p class="su__text-accordion su__sort"> Sort By</p>
                                  <mat-radio-group   aria-label="Select an option" class="su__radio-btn-column">
                                    <mat-radio-button name="sortBy" [checked]="Feedbackevent === 'most_recent'" value="most_recent" (change)="filterChange('child','most_recent')" >Most recent</mat-radio-button>
                                    <mat-radio-button name="sortBy" [checked]="Feedbackevent === 'least_recent'" value="least_recent" (change)="filterChange('child','least_recent')">Least recent</mat-radio-button>
                                  </mat-radio-group>
                                </ng-template>
                                </mat-expansion-panel>
                                <mat-expansion-panel  [expanded]="FeedbackquerySelected === 'votes'" (opened)="FeedbackquerySelected='votes'">
                                  <mat-expansion-panel-header [ngClass]="FeedbackquerySelected !== 'votes' ? 'showDialog' : ''" (click)="filterChange('parent','votes');">
                                    <mat-radio-button name="votes"  [checked]="FeedbackquerySelected === 'votes'" class="feedback_radio" value="1" >Yes/No</mat-radio-button>
                                  </mat-expansion-panel-header>
                                  <ng-template matExpansionPanelContent>
                                  <p class="su__text-accordion" >Thumbs up/down</p>
                                  <mat-radio-group aria-label="Select an option" class="su__radio-btn-column" >
                                    <mat-radio-button name="votes_option" [checked]="Feedbackevent === 'count'"  value="count" (change)="filterChange('child','count')">Sort by count</mat-radio-button>
                                    <mat-radio-button name="votes_option" [checked]="Feedbackevent === 'likes'"  value="likes" (change)="filterChange('child','likes')">Sort by Likes</mat-radio-button>
                                    <mat-radio-button name="votes_option" [checked]="Feedbackevent === 'dislikes'"  value="dislikes" (change)="filterChange('child','dislikes')">Sort by Dislikes</mat-radio-button>
                                  </mat-radio-group>
                                  </ng-template>
                                </mat-expansion-panel>
                                <mat-expansion-panel [expanded]="FeedbackquerySelected === 'stars'" (opened)="FeedbackquerySelected='stars'" class="su__filter_rating">
                                  <mat-expansion-panel-header class='position-relative' [ngClass]="FeedbackquerySelected !== 'stars' ? 'showDialog' : ''" (click)="filterChange('parent','stars');">
                                    <mat-radio-button name="stars" [checked]="FeedbackquerySelected === 'stars'" class="feedback_radio" value="2" >Stars/Emoticons</mat-radio-button>
                                    <svg class="info-align" matTooltip="Explanation:&#13;choose 1+ for the records with rating 1 or higher, but less than 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="15.36" height="15.36" viewBox="0 0 15.36 15.36">
                                      <defs>
                                        <clipPath id="clip-info_icon">
                                          <rect width="15.36" height="15.36"/>
                                        </clipPath>
                                      </defs>
                                      <g id="info_icon" data-name="info icon" clip-path="url(#clip-info_icon)">
                                        <g id="Component_12_7" data-name="Component 12 – 7" transform="translate(0)">
                                          <path id="Path_11380" data-name="Path 11380" d="M9.178,1.5a7.679,7.679,0,0,1,2.989,14.752A7.679,7.679,0,0,1,6.189,2.1,7.63,7.63,0,0,1,9.178,1.5Zm0,13.959A6.282,6.282,0,1,0,2.9,9.178,6.289,6.289,0,0,0,9.178,15.459Z" transform="translate(-1.5 -1.5)" fill="#707070"/>
                                          <path id="Path_11381" data-name="Path 11381" d="M17.2,20.688a.7.7,0,0,1-.7-.7V17.2a.7.7,0,1,1,1.4,0V19.99A.7.7,0,0,1,17.2,20.688Z" transform="translate(-9.537 -9.52)" fill="#707070"/>
                                          <circle id="Ellipse_827" data-name="Ellipse 827" cx="0.715" cy="0.715" r="0.715" transform="translate(6.963 5.174)" fill="#707070"/>
                                        </g>
                                      </g>
                                    </svg>
                                  </mat-expansion-panel-header>
                                  <ng-template matExpansionPanelContent>
                                  <mat-checkbox [checked]="Feedbackevent?.length==5"  class="su__star-emoticons su_star"  (change)="filterChange('childStar','all')" value="all">All</mat-checkbox>
                                  <div class="su__star_position" *ngFor="let newStar of sampleArray;let last = last;let index = index;" >
                                    <mat-checkbox [checked]="Feedbackevent.includes(newStar)" [attr.name]="newStar"  class="su__star-emoticons"  (change)="filterChange('childStar',index+1)" [value]="newStar">{{newStar}}{{last ? '' : '+'}}</mat-checkbox>
                                    {{myVal}}
                                  </div> 
                                </ng-template>
                                </mat-expansion-panel>
                              </mat-accordion>
                            </div>
                            <!-- <div class="su__apply-btn" (click)="applyFeedbackFilter()">
                              <p class="su__apply_style">Apply</p>
                            </div> -->
                            </ng-template>
                          </mat-menu>
                        </th>
                      </tr>
                    </thead>
                    <!-- && (show_stars) -->
                    <ng-container *ngIf="(!pageRatingTitles.isEmpty) && !noFilterSelected">
                      <tr *ngFor="let d of pageRatingTitles" style="word-break: break-word;">
                        <td class="su__url_align">
                          <a class="su-docs-link" [href]="d.url" target="_blank">{{d.title || d.url}}</a>
                        </td>
                        <td class="su__url_align">
                          <div class="d-flex" *ngIf="d.feedback_type==0">
                            <div class="container-voting">

                           
                          <span class="su__align_yes_vote">{{d.yes_votes}}</span>
                          <figure> 
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17" height="16" viewBox="0 0 17 16" >
                              <defs>
                                <clipPath id="clip-like">
                                  <rect width="17" height="16"/>
                                </clipPath>
                              </defs>
                              <g id="like" clip-path="url(#clip-like)">
                                <path id="thumb_down_FILL1_wght400_GRAD0_opsz48" d="M17.786,11.287a1.144,1.144,0,0,1,.84.387,1.22,1.22,0,0,1,.374.868v1.716a3.293,3.293,0,0,1-.051.534,2.242,2.242,0,0,1-.152.534L16.45,20.933a1.806,1.806,0,0,1-.6.753,1.435,1.435,0,0,1-.88.314H6.736a1.152,1.152,0,0,1-.86-.366,1.232,1.232,0,0,1-.354-.889V11.789a1.243,1.243,0,0,1,.091-.46,1.2,1.2,0,0,1,.253-.4L10.055,6.37a1.179,1.179,0,0,1,.678-.356,1.06,1.06,0,0,1,.739.146,1.3,1.3,0,0,1,.516.586,1.229,1.229,0,0,1,.091.774l-.769,3.766ZM3.154,22a1.09,1.09,0,0,1-.81-.356A1.164,1.164,0,0,1,2,20.807V12.48a1.164,1.164,0,0,1,.344-.837,1.1,1.1,0,0,1,1.619,0,1.164,1.164,0,0,1,.344.837v8.328a1.164,1.164,0,0,1-.344.837A1.09,1.09,0,0,1,3.154,22Z" transform="translate(-2 -6)" fill="#56C5FE"/>
                              </g>
                            </svg>
                            </figure>
                          <span class="su__align_thumbs" >{{d.no_votes}}</span>
                         
                          <figure>
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17" height="16"
                              viewBox="0 0 17 16">
                              <defs>
                                <clipPath id="clip-dislike">
                                  <rect width="17" height="16" />
                                </clipPath>
                              </defs>
                              <g id="dislike" clip-path="url(#clip-dislike)">
                                <path id="thumb_down_FILL1_wght400_GRAD0_opsz48"
                                  d="M3.214,16.713a1.144,1.144,0,0,1-.84-.387A1.22,1.22,0,0,1,2,15.457V13.742a3.293,3.293,0,0,1,.051-.534,2.242,2.242,0,0,1,.152-.534L4.55,7.067a1.806,1.806,0,0,1,.6-.753A1.435,1.435,0,0,1,6.027,6h8.237a1.152,1.152,0,0,1,.86.366,1.232,1.232,0,0,1,.354.889v8.955a1.243,1.243,0,0,1-.091.46,1.2,1.2,0,0,1-.253.4L10.945,21.63a1.179,1.179,0,0,1-.678.356,1.06,1.06,0,0,1-.739-.146,1.3,1.3,0,0,1-.516-.586,1.229,1.229,0,0,1-.091-.774l.769-3.766ZM17.846,6a1.09,1.09,0,0,1,.81.356A1.164,1.164,0,0,1,19,7.193V15.52a1.164,1.164,0,0,1-.344.837,1.1,1.1,0,0,1-1.619,0,1.164,1.164,0,0,1-.344-.837V7.193a1.164,1.164,0,0,1,.344-.837A1.09,1.09,0,0,1,17.846,6Z"
                                  transform="translate(-2 -6)" fill="#FF9B04" />
                              </g>
                            </svg>
                          
                          </figure>
                        </div>

                          <span class='su__align_avgRating'>({{d.total_votes}} Ratings)</span>
                         
                        </div>
                        <div class="d-flex" *ngIf="d.feedback_type==1">
                          <div class="container-voting">
                          <figure *ngFor="let star of d.new_rounded_stars" class="su__star_align">
                            <svg *ngIf="star==1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18" height="18"
                              viewBox="0 0 18 18">
                              <defs>
                                <clipPath id="clip-star">
                                  <rect width="18" height="18" />
                                </clipPath>
                              </defs>
                              <g id="star" clip-path="url(#clip-star)">
                                <path id="star_FILL1_wght400_GRAD0_opsz48"
                                  d="M11.522,25.507a.715.715,0,0,1-.947.027.728.728,0,0,1-.307-.9l1.761-5.714L7.492,15.673a.734.734,0,0,1-.307-.89.726.726,0,0,1,.761-.545h5.631l1.788-5.953a.727.727,0,0,1,.307-.439.819.819,0,0,1,.934,0,.727.727,0,0,1,.307.439L18.7,14.238h5.631a.726.726,0,0,1,.761.545.734.734,0,0,1-.307.89l-4.537,3.242L22.01,24.63a.728.728,0,0,1-.307.9.715.715,0,0,1-.947-.027L16.139,22Z"
                                  transform="translate(-7.139 -7.7)" fill="#56c5fe" />
                              </g>
                            </svg>
                            <svg *ngIf="star==0.5" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18" height="18"
                            viewBox="0 0 18 18">
                            <defs>
                              <clipPath id="clip-half_filed_star">
                                <rect width="18" height="18" />
                              </clipPath>
                            </defs>
                            <g id="half_filed_star" data-name="half filed star" clip-path="url(#clip-half_filed_star)">
                              <g id="Group_13475" data-name="Group 13475" transform="translate(-13867.521 -335)">
                                <path id="star_FILL1_wght400_GRAD0_opsz48"
                                  d="M11.453,25.3a.7.7,0,0,1-.933.026.721.721,0,0,1-.3-.893l1.734-5.648-4.466-3.2a.727.727,0,0,1-.3-.88.714.714,0,0,1,.749-.538h5.543l1.76-5.884a.718.718,0,0,1,.3-.433A1.532,1.532,0,0,1,16,7.7V21.832Z"
                                  transform="translate(13860.383 327.3)" fill="#56c5fe" />
                                <path id="star_FILL1_wght400_GRAD0_opsz48-2" data-name="star_FILL1_wght400_GRAD0_opsz48"
                                  d="M16,7.7a.836.836,0,0,1,.46.144.718.718,0,0,1,.3.433l1.76,5.884h5.543a.714.714,0,0,1,.749.538.727.727,0,0,1-.3.88l-4.466,3.2,1.734,5.648a.721.721,0,0,1-.3.893.7.7,0,0,1-.933-.026L16,21.832S15.839,7.7,16,7.7Z"
                                  transform="translate(13860.383 327.3)" opacity="0.19" />
                              </g>
                            </g>
                          </svg>
                            <svg  *ngIf="star==0" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18" height="18"
                            viewBox="0 0 18 18">
                            <defs>
                              <clipPath id="clip-star">
                                <rect width="18" height="18" />
                              </clipPath>
                            </defs>
                            <g id="star" clip-path="url(#clip-star)">
                              <path id="star_FILL1_wght400_GRAD0_opsz48"
                                d="M11.522,25.507a.715.715,0,0,1-.947.027.728.728,0,0,1-.307-.9l1.761-5.714L7.492,15.673a.734.734,0,0,1-.307-.89.726.726,0,0,1,.761-.545h5.631l1.788-5.953a.727.727,0,0,1,.307-.439.819.819,0,0,1,.934,0,.727.727,0,0,1,.307.439L18.7,14.238h5.631a.726.726,0,0,1,.761.545.734.734,0,0,1-.307.89l-4.537,3.242L22.01,24.63a.728.728,0,0,1-.307.9.715.715,0,0,1-.947-.027L16.139,22Z"
                                transform="translate(-7.139 -7.7)" fill="lightgrey" />
                            </g>
                          </svg>
                          </figure>
                          </div>
                          <span class="su_align_star_rating">({{d.total_rating}} Ratings)</span>
                        
                        </div>
                        </td>
                      </tr>
                    </ng-container>
                    <!-- <div *ngIf="pageRatingTitles.isEmpty">
                      <p>no doc to show</p>
                    </div> -->
                    <tr *ngIf="pageRatingTitles.isEmpty && !noFilterSelected">
                      <td colspan="2" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                    <div *ngIf="noFilterSelected" class="su__no_filter">
                      <div class="su__no_filter_align">
                        <img class="su__no_filter_img" src="assets/img/No_Intent_found.svg">
                        <p class="su__no_filter_img_text">Oops...</p>
                        <p class="su__no_filter_img_text2">No Filter Selected</p>
                      </div>
 
                    </div>
                    <tr *ngIf="pageRatingTitles?.length === 0">
                      <td>
                        <div style="text-align: center;">
                          <div class="spinner">
                            <div class="bounce1"></div>
                            <div class="bounce2"></div>
                            <div class="bounce3"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
     
  </div>
    </div>
    

    <div class="sectionMainDiv" *ngIf="selectedIndex===0">
      <div *ngIf="reportSettings?.length>0 && reportSettings[62] && reportSettings[62].is_enabled"
        class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 20px;padding-left: 20px;margin-top: 20px;">
        <div>
          <div class="card card-block" id="types-of-docs" style="position: static;padding:0; min-height:auto;">
            <div class="analytics-section-heading">
              {{reportSettings[62].label}}
              <app-email-and-download-reports *ngIf="searchFeedbkData && !searchFeedbkData.isEmpty && searchFeedbkData.length != 0"
                [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="OverviewTab.tabName"
                [reportName]="OverviewTab.Reports[13]" [hideDownload]="true" [textFeedBackToggle]="textFeedBackToggle" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
              </app-email-and-download-reports>
              <!-- <app-user-metric-filter
                *ngIf="userMetricEnabled"
                [userMetricLabel]="userMetricLabel"
                [userMetricVariable]="userMetricVariable"
                [userId]="userUniqueId"
                [email]="userEmail"
                [uid]="searchClients.uid"
                [internalUser]="internalUser"
                [from]="range.from"
                [to]="range.to"
                [reportName]="'Search Experience Feedback'"
                [userMetricURL]="['/overview/searchFeedback']"
                [body]="{
                  from: range.from,
                  to: range.to,
                  internalUser: internalUser,
                  uid: searchClients.uid
                }"
                (userMetricEvent)="sendUserMetricValues($event)"
              ></app-user-metric-filter> -->
            </div>
            <div id="searchExperienceFeedbackReport" style="padding: 10px; margin: 0 0 20px 0;" class="card">
              <div class="perfect searchFeedbkData" *ngIf="searchFeedbkData">
                <table class="searchFeedbkTable table table-su card-1" style="width: 100%;">
                  <thead class="search-feedbk-header" style="white-space: nowrap;">
                    <th style="width: 25%;">Search Query	</th>
                    <th style="width: 25%;">Clicked Result	</th>
                    <th style="width: 10%;">Ratings</th>
                    <th style="width: 25%;"  *ngIf="textFeedBackToggle">Text Feedback	</th>
                    <th style="width: 15%;">Reported By</th>
                  </thead>
                  <tbody>
                    <ng-container *ngIf="searchFeedbkData && !searchFeedbkData.isEmpty" class="searchFeedbkBody">
                      <tr *ngFor="let item of searchFeedbkData | paginate: {itemsPerPage: 10, currentPage: searchFeedbkData?.currentPage, id: 'searchFeedbkFirst',totalItems: searchFeedbkData?.count};let i = index">
                        <td class="">
                          <span matTooltip={{item.text_entered}} maTooltipPosition="above" *ngIf="item.text_entered">
                            {{(item.text_entered.length>50)? (item.text_entered | slice:0:50)+' ...':(item.text_entered) }}
                          </span>
                          <span matTooltip="No query performed" maTooltipPosition="above" 
                            *ngIf="!item.text_entered">
                            N/A
                          </span>
                        </td>
                        <td class="searchFeedbkClick">
                          <span *ngIf="item.conversion_position && item.conversion_position != ''" class="searchFeedbkClickedPos" 
                          matTooltip="Clicked Position" maTooltipPosition="above">
                            {{item.conversion_position}}
                          </span>
                          <span class="searchFeedbkClickRes">
                            <a target="_blank" 
                            href="{{item.conversion_url}}"
                            matTooltip={{item.conversion_title}} maTooltipPosition="above"
                              *ngIf="item.conversion_title && item.conversion_title != ''" >
                                {{(item.conversion_title.length>50)? (item.conversion_title | slice:0:50)+' ...':(item.conversion_title) }}
                            </a>
                            <a target="_blank" 
                            href="{{item.conversion_url}}"
                            matTooltip={{item.conversion_url}} maTooltipPosition="above"
                            *ngIf="(!item.conversion_title || item.conversion_title == '') && item.conversion_url">
                                {{(item.conversion_url.length>50)? (item.conversion_url | slice:0:50)+' ...':(item.conversion_url) }}
                            </a>
                          </span>
                          <span matTooltip="No result clicked" 
                            maTooltipPosition="above" 
                            *ngIf="(!item.conversion_title || item.conversion_title == '') && !item.conversion_url">
                              N/A
                          </span>
                        </td>
                        <td class="">
                          <span *ngIf="item.rating && 
                            item.rating != ''">
                            {{item.rating}}/5
                          </span>
                          <span matTooltip="No ratings given" 
                            maTooltipPosition="above" 
                            *ngIf="!item.rating || item.rating == ''">
                            N/A
                          </span>
                        </td>
                        <td class="searchFeedbkFeedback" *ngIf="textFeedBackToggle">
                          <span *ngIf="item.feedback">
                            {{ (item.feedback.length>100 && !item.showLess)? (item.feedback | slice:0:100)+' ...':(item.feedback) }}
                            <span class="showMoreSearchFeedbk" *ngIf="item.feedback.length > 100 && !item.showLess"  (click)="showMoreSearchFeedbk(i, true)">
                              show more
                            </span>
                            <span class="showMoreSearchFeedbk" *ngIf="item.feedback.length > 100 && item.showLess" (click)="showMoreSearchFeedbk(i, false)">
                              show less
                            </span>
                          </span>
                          <span matTooltip="No feedback received" 
                            maTooltipPosition="above" 
                            *ngIf="!item.feedback">
                              N/A
                          </span>
                        </td>
                        <td class="searchFeedbkReportedBy">
                          <span *ngIf="item.reported_by">
                            {{item.reported_by}}
                          </span>
                          <span matTooltip="Email not tracked" 
                          maTooltipPosition="above" 
                          *ngIf="!item.reported_by">
                            N/A
                          </span>
                        </td>
                      </tr>
                    </ng-container>
                    <tr *ngIf="searchFeedbkData.isEmpty">
                      <td colspan="5" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                    <tr *ngIf="!searchFeedbkData.isEmpty && searchFeedbkData.length === 0">
                      <td>
                        <div style="text-align: center;">
                          <div class="spinner">
                            <div class="bounce1"></div>
                            <div class="bounce2"></div>
                            <div class="bounce3"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr *ngIf="!searchFeedbkData.isEmpty && searchFeedbkData.length != 0" class="hover search-feedbk-footer">
                      <td colspan="100" style="text-align: right;">
                        <pagination-controls id="searchFeedbkFirst" (pageChange)="getSearchFeedback($event)">
                        </pagination-controls>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div>
              <ng-template #noData>
                <tr>
                  <td colspan="5" class="no-docs">No Documents to show.
                    <img class="doc-img">
                  </td>
                </tr>
              </ng-template>
            </div>
          </div>
        </div>
        <div>
        </div>
      </div>
    </div>
    <!-- code for advertisment report  -->

    <div class="sectionMainDiv" *ngIf="selectedIndex===0">
      <div *ngIf="reportSettings.length>0 && reportSettings[64] && reportSettings[64].is_enabled"
        class="col-xl-12 col-lg-12 col-sm-12 col-xs-12" style="padding-right: 20px;padding-left: 20px;margin-top: 20px;">
        <div>
          <div class="card card-block" id="types-of-docs" style="position: static;padding:0; min-height:auto;">
            <div class="analytics-section-heading">
              Advertisement Performance Report
              <app-email-and-download-reports *ngIf="advertismentSearchedStrings?.length != 0"
                [searchClients]="searchClients" [range]="range" [internalUser]="internalUser" [tab]="OverviewTab.tabName"
                [reportName]="OverviewTab.Reports[14]" [hideDownload]="true" [textFeedBackToggle]="textFeedBackToggle" [advertisementFiltersOnInitialLoad]="advertisementFiltersOnInitialLoad.sortType" [advertisementSearchedQuery]="advertisementSearchedQuery" [AllUserEmails] = "AllUserEmails" [userMetricVariable]="userMetricVariable" [userMetricsFilters]="userMetricsFilters">
              </app-email-and-download-reports>
              <!-- <app-user-metric-filter
                *ngIf="userMetricEnabled"
                [userMetricLabel]="userMetricLabel"
                [userMetricVariable]="userMetricVariable"
                [userId]="userUniqueId"
                [email]="userEmail"
                [uid]="searchClients.uid"
                [internalUser]="internalUser"
                [from]="range.from"
                [to]="range.to"
                [reportName]="'Advertisement Performance Report'"
                [userMetricURL]="['/overview/advertisements']"
                [body]="{
                  from: range.from,
                  to: range.to,
                  internalUser: internalUser,
                  uid: searchClients.uid
                }"
                (userMetricEvent)="sendUserMetricValues($event)"
              ></app-user-metric-filter> -->
            </div>
            <div id="searchExperienceFeedbackReport" style="padding: 10px; margin: 0 0 20px 0;" class="card">
              <div class="perfect advertisementData">
                <table class="searchFeedbkTable table table-su card-1" style="width: 100%;">
                  <thead class="search-feedbk-header" style="white-space: nowrap;">
                    <ng-container class="d-flex" *ngFor="let filter of advertisementFilter">
                      <th style="width: 25%;" *ngIf="!filter.filled">{{filter.label}} <span
                          (click)="openTextSearchForallSearchReport('AdvertismentTextFillter', filter.label,!filter.filled)"
                          class="search-icons-th">&nbsp;</span>
                      </th>
                      <th style="width: 25%;" *ngIf="filter.filled">
                        <div [id]="filter.id" class="su_search_label">
                          <input type="search" [(ngModel)]="filter.value" [placeholder]="filter.placeholder"
                            class="filled su_input_align" [ngClass]="(filter.filled == true)? 'show' : 'hide'"
                            (keyup.enter)="textSearAllSearchReport(filter.label,$event.target.value,'AdvertismentTextFillter')">
                          <button class="srtableBoxclose su_button_align" *ngIf="filter.filled"
                            (click)="filter.filled=false; filter.value='';textSearAllSearchReportClearValue(filter.label, 'AdvertismentTextFillter');">X</button>
                        </div>
                      </th>
                    </ng-container>
                    <th style="width: 25%;">Appearances <span
                        (click)="changeSortOrderMainReport(col, 'asc', 'AdvertisementSorting')" style="cursor: pointer;">
                        <svg [ngClass]="advertisementSortingButton ? 'su__advertsiment_sorting_svg' : ''" *ngIf="sortingFieldAllSearches !== col" xmlns="http://www.w3.org/2000/svg" width="16"
                          height="16" viewBox="0 0 16 16">
                          <defs>
                            <style>
                              .a {
                                fill: none;
                              }
    
                              .b {
                                fill: #707070;
                              }
                            </style>
                          </defs>
                          <path class="a" d="M0,0H16V16H0Z" />
                          <path class="b"
                            d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                            transform="translate(-1.903 -1.069)" />
                        </svg>
                      </span></th>
                    <th style="width: 10%;">Clicks</th>
                  </thead>
                  <tbody>
                    <ng-container class="searchFeedbkBody"  *ngIf="advertismentSearchedStrings && advertismentSearchedStrings.advertisements">
                      <tr
                        *ngFor="let item of advertismentSearchedStrings.advertisements | paginate: {itemsPerPage: 10, currentPage:advertisementFiltersOnInitialLoad.pageNo , id: 'advertisementClick',totalItems:advertismentSearchedStrings && advertismentSearchedStrings.total && advertismentSearchedStrings.total[0] &&   advertismentSearchedStrings.total[0].count };let activityIndex = index">
                        <td class="">
    
                          <span matTooltip={{item.text_entered}} maTooltipPosition="above">
                            {{item.text_entered}}
                          </span>
                        </td>
                        <td class="searchFeedbkFeedback">
                          <span matTooltip="No feedback received" maTooltipPosition="above">
                            {{item.views}}
                          </span>
                        </td>
                        <td class="searchFeedbkReportedBy">
                          <span>
                            {{item.clicks}}
                          </span>
    
                        </td>
                      </tr>
                    </ng-container>
                    <tr *ngIf="advertismentSearchedStrings.isEmpty">
                      <td colspan="5" class="no-docs">No Documents to show.
                        <img class="doc-img">
                      </td>
                    </tr>
                    <tr *ngIf="advertismentSearchedStrings.showLoader">
                      <td colspan="2" style="text-align: center;">
                        <div>
                          <div class="spinner">
                            <div class="bounce1"></div>
                            <div class="bounce2"></div>
                            <div class="bounce3"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr *ngIf="advertismentSearchedStrings.advertisements != 0" class="hover search-feedbk-footer">
                      <td colspan="100" style="text-align: right;">
                        <pagination-controls id="advertisementClick" (pageChange)="setPaginationForAdvertisement($event)">
                        </pagination-controls>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div>
            </div>
          </div>
        </div>
        <div>
        </div>
      </div>
    </div>


  
  
  
