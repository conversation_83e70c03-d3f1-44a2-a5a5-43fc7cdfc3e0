<ng2-toasty></ng2-toasty>
<div class="col-sm-12 session-report-detail" style="padding-right: 0px; padding-left: 0px;">
    <div id="trackingDetail">
        <div class="su-session">
            <div class="analytics-section-heading" style="display: inline-block;">
                Session Tracking - Details
            </div>
            <!-- <svg class="downloadImg" width="40" height="40" viewBox="-5 -5 40 40" *ngIf="sessionActivityList && sessionActivityList.buckets && sessionActivityList.buckets.length != 0" (click)="getSessionTrackingDetails(1, sessionActivityDetailsCheck, undefined, searchSessionsText)" style="margin: 7px;width: 30px;height: 30px;">
                <path data-name="Path 1128" d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
            </svg> -->
            <app-email-and-download-reports 
                [searchClients]="searchClients" 
                [range]="range" 
                [internalUser]="internalUser" 
                [tab]="'Conversions'" 
                [reportName]="'SessionTrackingDetails'" 
                [hideDownload]="false" 
                [searchFilterMenuValue]="searchFilterMenuValue" 
                [clickFilterMenuValue]="clickFilterMenuValue" 
                [supportFilterMenuValue]="supportFilterMenuValue" 
                [caseFilterMenuValue]="caseFilterMenuValue" 
                [articleFilter] = "articleFilter"
                [GlobalSearchfilter] = "GlobalSearchfilter"
                [GlobalConversion] = "GlobalConversion"
                [SupportSearchFilter]="SupportSearchFilter"
                [SupportConversonfilter] ="SupportConversonfilter"
                [sortType]="sortType"
                [sortByField]="sortByField"
                [searchingType]="searchingType"
                [selectedActivityFilter] = "selectedActivityFilter"
                [SearchInputText]="searchTextValue"
                [contentFacetsFilterObject]="contentFacetsFilterObject"
                [chipFilterArray]="chipFilterArray"
                [exactSearch]="exactSearch"
                [isEcosystemSelected]="isEcosystemSelected"
                [ecoSystem] = "ecoSystem"
                [AllUserEmails] = "AllUserEmails"
                [userMetricVariable]="userMetricVariable"
                [userMetricValues]="userMetricValues"
            >
            
            </app-email-and-download-reports> 
            <app-user-metric-filter
                *ngIf="userMetricEnabled"
                [applyMargin]="true"
                [userMetricLabel]="userMetricLabel"
                [userMetricVariable]="userMetricVariable"
                [userId]="userId"
                [email]="userMetricEmail"
                [uid]="searchClients.uid"
                [internalUser]="internalUser"
                [from]="range.from"
                [to]="range.to"
                [reportName]="'Session Tracking - Details'"
                [userMetricURL]="['/conversion/sessionDetails']"
                [body]="{
                    from: range.from,
                    to: range.to,
                    internalUser: internalUser,
                    uid: searchClients.uid
                }"
                (userMetricEvent)="sendUserMetricValues($event)"
            ></app-user-metric-filter>
        </div>
        <div class="panel-body">
            <div>
                <div class="col-sm-12 col-header">
                    <div class="sessionHeader col-sm-12 col-header">
                        <div class="searchboxFilter col-sm-9">
                            <div class="searchIcon col-sm-1 col-md-1 col-lg-1 col-xs-1 col-xl-1"
                                style="display:inline-block" (click)="applyFilter($event,1,searchTextValue);">
                                <i class="fa fa-search " style="color:#8b8b8b;"></i>
                            </div>
                            <input class="searchFont col-sm-8 col-md-8 col-lg-8 col-xs-8 col-xl-8" type="search"
                                id="mySearch" [(ngModel)]="searchTextValue" placeholder="{{searchPlaceHolder}}"
                                (keyup)="applyFilter($event,0,searchTextValue)" [ngClass]="{'slideTogglePadding' : searchingType === 1}">
                                <mat-slide-toggle class="exactSearchToggle" style="position: absolute;right: 162px;top: 2px;" size="small" [labelPosition]="labelPosition" [(ngModel)]="exactSearch" *ngIf="searchingType == 1 " (change)="applyToggleFilter(searchTextValue)">Exact Search</mat-slide-toggle>
                            <mat-form-field class="searchFilter col-sm-3 col-md-3 col-lg-3 col-xs-3 col-xl-3"
                                title="Filter by support visit/case created">
                                <mat-select panelClass="searchPanel" style="margin-top: 0%;" [(ngModel)]="searchingType"
                                    (selectionChange)="searchingTypeChanged();" class="sessionTrackingOptions">
                                    <mat-option class="matOptionFilter" [value]="1">
                                        All Text
                                    </mat-option>
                                    <mat-option class="matOptionFilter" [value]="2">
                                        Session Id
                                    </mat-option>
                                    <mat-option  *ngIf="email" class="matOptionFilter" [value]="3">
                                        Email
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <div class="col-sm-3 sessionFilters" (mouseover)="openFilters()" (mouseout)="closeFilters()">
                            <button class="sessionFilterButton">
                                <span class="material-icons-outlined sessions-filter-icon">
                                    tune
                                </span>
                                <span>Apply Filters</span>
                            </button>
                            <div class="filter-sidebar-container" [ngClass]="hoverfilter">
                                <div class="filter-sidebar">
                                    <ul class="filter-menu">
                                        <li class="filter-menu-hader">
                                            <span>Apply Filters</span>
                                        </li>
                                        <div class="filter-menu-items" id="accordion" >
                                            <div class="panel filters-value quick-filter" [ngClass]="(quickFilterDisable)?'disable-filter':''">
                                                <li class="filter-items">
                                                    <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="18px" viewBox="0 0 24 24"
                                                        width="18px" fill="#A3A3A3">
                                                        <g>
                                                            <path d="M0,0h24 M24,24H0" fill="none" />
                                                            <path
                                                                d="M4.25,5.61C6.57,8.59,10,13,10,13v5c0,1.1,0.9,2,2,2h0c1.1,0,2-0.9,2-2v-5c0,0,3.43-4.41,5.75-7.39 C20.26,4.95,19.79,4,18.95,4H5.04C4.21,4,3.74,4.95,4.25,5.61z" />
                                                            <path d="M0,0h24v24H0V0z" fill="none" />
                                                        </g>
                                                    </svg>
                                                    <span data-i18n="" class="menu-title">Quick Filters</span>
                                                    <span class="addons-dropdown cursor-pointer filter-arrows"
                                                        data-toggle="collapse" data-target="#quick-filter">
                                                        <svg class="down dropdown-arrow" width="10.65" height="10.399" viewBox="0 0 17.65 10.399">
                                                            <defs>
                                                                <style>
                                                                    .a {
                                                                        fill: #333;
                                                                    }
                                                                </style>
                                                            </defs>
                                                            <path class="down-arrow"
                                                                d="M14.946,9.937,8.817,3.81,2.689,9.937A1.575,1.575,0,0,1,.462,7.711L7.712.462a1.573,1.573,0,0,1,2.227,0l7.25,7.249a1.573,1.573,0,0,1,0,2.227A1.607,1.607,0,0,1,14.946,9.937Z"
                                                                transform="translate(17.65 10.399) rotate(180)" />
                                                        </svg>
                                                    </span>
                                                </li>
                                                <div id="quick-filter" class="installed-addons collapse" data-parent="#accordion">
                                                    <!-- <mat-menu class="quickFiltermenu sessiontrackFilter"> -->
                                                    <mat-radio-group [(ngModel)]="clearValueQuickFilter" (change)="applyQuickFilter($event)"
                                                        aria-label="Select an option">
                                                        <mat-option class="matOption quickFilterOption" *ngFor="let filterdata of QuickFilters">
                                                            <mat-radio-button class="quickFilterRadio"
                                                                [disabled]="(filterdata.FilterValue=='Value3' && articleFilterSettings == true) || ((filterdata.FilterValue=='Value4' || filterdata.FilterValue=='Value5') && is_case_deflected_shown == true)"
                                                                [value]="filterdata.FilterValue">{{ filterdata.label }}</mat-radio-button>
                                                        </mat-option>
                                                    </mat-radio-group>
                                                </div>
                                            </div>
                                            <div class="panel filters-value activity-type" [ngClass]="(filterByReportMenuClickDisable)?'disable-filter':''">
                                                <li class="filter-items" matTooltip="Sessions are filtered with the selected activities in the report" matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-title">
                                                    <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#A3A3A3">
                                                        <path d="M0 0h24v24H0z" fill="none" />
                                                        <path
                                                            d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM4 12h4v2H4v-2zm10 6H4v-2h10v2zm6 0h-4v-2h4v2zm0-4H10v-2h10v2z" />
                                                    </svg>
                                                    <span data-i18n="" class="menu-title">Filter by Activity Type</span>
                                                    <span class="addons-dropdown cursor-pointer filter-arrows"
                                                        data-toggle="collapse" data-target="#activity-type">
                                                        <svg class="down dropdown-arrow" width="10.65" height="10.399" viewBox="0 0 17.65 10.399">
                                                            <defs>
                                                                <style>
                                                                    .a {
                                                                        fill: #333;
                                                                    }
                                                                </style>
                                                            </defs>
                                                            <path class="down-arrow"
                                                                d="M14.946,9.937,8.817,3.81,2.689,9.937A1.575,1.575,0,0,1,.462,7.711L7.712.462a1.573,1.573,0,0,1,2.227,0l7.25,7.249a1.573,1.573,0,0,1,0,2.227A1.607,1.607,0,0,1,14.946,9.937Z"
                                                                transform="translate(17.65 10.399) rotate(180)" />
                                                        </svg>
                                                    </span>
                                                </li>
                                                <div id="activity-type" class="installed-addons collapse activity-filter-options" data-parent="#accordion">
                                                    <!-- <button [ngClass]="{'selectedFilter' : searchFilterMenu}" class="menuButton" mat-menu-item
                                                        (click)="searchFilterMenu = !searchFilterMenu" (click)="$event.stopPropagation()">
                                                        <span>Searches</span>
                                                    </button> -->
                                                    <span class="filter-avtivity_label">Searches</span>
                                                    <div class="optionsFilter session-optionsFilter">
                                                        <mat-radio-group [(ngModel)]="searchFilterMenuValue" (change)="onMenuFilterChange('Searches', $event)">
                                                            <mat-radio-button value="all" (click)="$event.stopPropagation()">All</mat-radio-button>
                                                            <mat-radio-button value="yes" (click)="$event.stopPropagation()">Yes</mat-radio-button>
                                                            <mat-radio-button value="no" (click)="$event.stopPropagation()">No</mat-radio-button>
                                                        </mat-radio-group>
                                                    </div>
                                                    <!-- <button [ngClass]="{'selectedFilter' : clickFilterMenu}" class="menuButton" mat-menu-item
                                                        (click)="clickFilterMenu = !clickFilterMenu" (click)="$event.stopPropagation()">
                                                        Clicks
                                                    </button> -->
                                                    <span class="filter-avtivity_label">Clicks</span>
                                                    <div class="optionsFilter session-optionsFilter">
                                                        <mat-radio-group [(ngModel)]="clickFilterMenuValue" (change)="onMenuFilterChange('Clicks', $event)">
                                                            <mat-radio-button value="all" (click)="$event.stopPropagation()">All</mat-radio-button>
                                                            <mat-radio-button value="yes" (click)="$event.stopPropagation()">Yes</mat-radio-button>
                                                            <mat-radio-button value="no" (click)="$event.stopPropagation()">No</mat-radio-button>
                                                        </mat-radio-group>
                                                    </div>
                                                    <!-- <button [ngClass]="{'selectedFilter' : supportFilterMenu}" class="menuButton" mat-menu-item
                                                        (click)="supportFilterMenu = !supportFilterMenu" (click)="$event.stopPropagation()">
                                                        Support Visit
                                                    </button> -->
                                                    <span class="filter-avtivity_label">Support Visit</span>
                                                    <div class="optionsFilter session-optionsFilter">
                                                        <mat-radio-group [(ngModel)]="supportFilterMenuValue" (change)="onMenuFilterChange('Support Visit', $event)">
                                                            <mat-radio-button value="all" (click)="$event.stopPropagation()">All</mat-radio-button>
                                                            <mat-radio-button value="yes" (click)="$event.stopPropagation()">Yes</mat-radio-button>
                                                            <mat-radio-button value="no" (click)="$event.stopPropagation()">No</mat-radio-button>
                                                        </mat-radio-group>
                                                    </div>
                                                    <!-- <button [ngClass]="{'selectedFilter' : caseFilterMenu}" class="menuButton" mat-menu-item
                                                        (click)="caseFilterMenu = !caseFilterMenu" (click)="$event.stopPropagation()">
                                                        Cases Logged
                                                    </button> -->
                                                    <span class="filter-avtivity_label">Cases Logged</span>
                                                    <div  class="optionsFilter session-optionsFilter">
                                                        <mat-radio-group [(ngModel)]="caseFilterMenuValue" (change)="onMenuFilterChange('Cases Logged', $event)">
                                                            <mat-radio-button value="all" (click)="$event.stopPropagation()">All</mat-radio-button>
                                                            <mat-radio-button value="yes" (click)="$event.stopPropagation()">Yes</mat-radio-button>
                                                            <mat-radio-button value="no" (click)="$event.stopPropagation()">No</mat-radio-button>
                                                        </mat-radio-group>
                                                    </div>
                                                </div>
                                            </div>
                                            <div *ngIf="searchClients.isConsoleSearch || (ecoSystem && ecoSystem.isConsoleSearch && (ecoSystem.isConsoleSearch === true || ecoSystem.isConsoleSearch === 'true'))" class="panel filters-value search-type-filter">
                                                <li class="filter-items" matTooltip="Sessions are filtered with the selected search activity type in the report" matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-title">
                                                    <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#A3A3A3">
                                                        <path d="M0 0h24v24H0z" fill="none" />
                                                        <path
                                                            d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM4 12h4v2H4v-2zm10 6H4v-2h10v2zm6 0h-4v-2h4v2zm0-4H10v-2h10v2z" />
                                                    </svg>
                                                    <span data-i18n="" class="menu-title">Filter by Search Type</span>
                                                    <span class="addons-dropdown cursor-pointer filter-arrows"
                                                        data-toggle="collapse" data-target="#search-type-filter">
                                                        <svg class="down dropdown-arrow" width="10.65" height="10.399" viewBox="0 0 17.65 10.399">
                                                            <defs>
                                                                <style>
                                                                    .a {
                                                                        fill: #333;
                                                                    }
                                                                </style>
                                                            </defs>
                                                            <path class="down-arrow"
                                                                d="M14.946,9.937,8.817,3.81,2.689,9.937A1.575,1.575,0,0,1,.462,7.711L7.712.462a1.573,1.573,0,0,1,2.227,0l7.25,7.249a1.573,1.573,0,0,1,0,2.227A1.607,1.607,0,0,1,14.946,9.937Z"
                                                                transform="translate(17.65 10.399) rotate(180)" />
                                                        </svg>
                                                    </span>
                                                </li>
                                                <div id="search-type-filter" class="installed-addons collapse activity-filter-options" data-parent="#accordion">
                                                    <!-- <button [ngClass]="{'selectedFilter' : searchFilterMenu}" class="menuButton" mat-menu-item
                                                        (click)="searchFilterMenu = !searchFilterMenu" (click)="$event.stopPropagation()">
                                                        <span>Searches</span>
                                                    </button> -->
                                                    <div class="optionsFilter session-optionsFilter">
                                                        <mat-radio-group [(ngModel)]="searchActivityType" (change)="onMenuSearchTypeChange($event)">
                                                            <mat-radio-button value="all" (click)="$event.stopPropagation()">All</mat-radio-button>
                                                            <mat-radio-button value="active" (click)="$event.stopPropagation()">Active</mat-radio-button>
                                                            <mat-radio-button value="default" (click)="$event.stopPropagation()">Default</mat-radio-button>
                                                        </mat-radio-group>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel filters-value show-activity-filter">
                                                <li class="filter-items" matTooltip="Session details are filtered on selected activities" matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-title">
                                                    <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#A3A3A3">
                                                        <path d="M0 0h24v24H0V0z" fill="none" />
                                                        <path
                                                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM9.29 16.29L5.7 12.7c-.39-.39-.39-1.02 0-1.41.39-.39 1.02-.39 1.41 0L10 14.17l6.88-6.88c.39-.39 1.02-.39 1.41 0 .39.39.39 1.02 0 1.41l-7.59 7.59c-.38.39-1.02.39-1.41 0z" />
                                                    </svg>
                                                    <span data-i18n="" class="menu-title">Show Activity Type</span>
                                                    <span class="addons-dropdown cursor-pointer filter-arrows"
                                                        data-toggle="collapse" data-target="#show-quick-filter">
                                                        <svg class="down dropdown-arrow" width="10.65" height="10.399" viewBox="0 0 17.65 10.399">
                                                            <defs>
                                                                <style>
                                                                    .a {
                                                                        fill: #333;
                                                                    }
                                                                </style>
                                                            </defs>
                                                            <path class="down-arrow"
                                                                d="M14.946,9.937,8.817,3.81,2.689,9.937A1.575,1.575,0,0,1,.462,7.711L7.712.462a1.573,1.573,0,0,1,2.227,0l7.25,7.249a1.573,1.573,0,0,1,0,2.227A1.607,1.607,0,0,1,14.946,9.937Z"
                                                                transform="translate(17.65 10.399) rotate(180)" />
                                                        </svg>
                                                    </span>
                                                </li>
                                                <div id="show-quick-filter" class="installed-addons collapse" data-parent="#accordion">
                                                    <section class="show-activity-filter-options">
                                                        <span class="show-activity-filter-options-span">
                                                            <ul class="show-activity-filter-options-ui">
                                                                <li class="toggleActivity-data-li">
                                                                    <mat-checkbox class="toggleActivity-label" [checked]="allComplete" [value]="'all'" (change)="toggleActivity('all')">
                                                                        All Activities
                                                                    </mat-checkbox>
                                                                </li>
                                                                <li class="toggleActivity-data-li" *ngFor="let filters of activityTypes">
                                                                    <mat-checkbox class="toggleActivity-label" [(ngModel)]="selectedActivityFilter[filters]" (ngModelChange)="toggleActivity(filters)">
                                                                        {{selectedActivityFilterType[filters]}}
                                                                    </mat-checkbox>
                                                                </li>
                                                            </ul>
                                                        </span>
                                                    </section>
                                                </div>
                                            </div>
                                            <div class="panel filters-value content-facets" [ngClass]="(disableContentFilter)?'disable-filter':'' ">
                                                <li class="filter-items" matTooltip="Sessions are filtered based on the clicked documents per content type" matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-title">
                                                    <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="18px" viewBox="0 0 24 24" width="18px"
                                                        fill="#A3A3A3">
                                                        <rect fill="none" fill-rule="evenodd" height="24" width="24" />
                                                        <path
                                                            d="M13,9.5h5v-2h-5V9.5z M13,16.5h5v-2h-5V16.5z M19,21H5c-1.1,0-2-0.9-2-2V5 c0-1.1,0.9-2,2-2h14c1.1,0,2,0.9,2,2v14C21,20.1,20.1,21,19,21z M6,11h5V6H6V11z M7,7h3v3H7V7z M6,18h5v-5H6V18z M7,14h3v3H7V14z"
                                                            fill-rule="evenodd" />
                                                    </svg>
                                                    <span data-i18n="" class="menu-title">Content Facets</span>
                                                    <span class="addons-dropdown cursor-pointer filter-arrows"
                                                        data-toggle="collapse" data-target="#content-facets">
                                                        <svg class="down dropdown-arrow" width="10.65" height="10.399" viewBox="0 0 17.65 10.399">
                                                            <defs>
                                                                <style>
                                                                    .a {
                                                                        fill: #333;
                                                                    }
                                                                </style>
                                                            </defs>
                                                            <path class="down-arrow"
                                                                d="M14.946,9.937,8.817,3.81,2.689,9.937A1.575,1.575,0,0,1,.462,7.711L7.712.462a1.573,1.573,0,0,1,2.227,0l7.25,7.249a1.573,1.573,0,0,1,0,2.227A1.607,1.607,0,0,1,14.946,9.937Z"
                                                                transform="translate(17.65 10.399) rotate(180)" />
                                                        </svg>
                                                    </span>
                                                </li>
                                                <ul id="content-facets" class="installed-addons collapse content-facets-filter-data"  data-parent="#accordion">
                                                    <li class="panel nav-item content-filter-main" *ngFor="let ContentFlters of contentFacetsData; let i = index">
                                                        <a>
                                                            <i class=""></i>
                                                            <span class="cotent-facets-label-container">
                                                            <span data-i18n="" class="content-filter-labels" matTooltip="{{ContentFlters.field_label}}" matTooltipPosition="below" mattooltipclass="an-tooltip-sessionReport-filters">{{ContentFlters.field_label}}</span>
                                                            <span data-i18n="" class="content-source-labels" matTooltip="{{ContentFlters.objectName}}({{ContentFlters.cs_name}})" matTooltipPosition="below" mattooltipclass="an-tooltip-sessionReport-filters">{{ContentFlters.objectName}}</span>
                                                            <i class="content-filter-collapse" data-toggle="collapse" [attr.data-target]="'#content-facets-options' + i"></i>
                                                            </span>
                                                            </a>
                                                            <span class="content-filter-indeterminate">
                                                                <svg xmlns="http://www.w3.org/2000/svg" height="15px" viewBox="0 0 24 24" width="15px" fill="#A3A3A3">
                                                                    <path d="M0 0h24v24H0z" fill="none" />
                                                                    <path
                                                                        d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM7 11h10v2H7z" />
                                                                </svg>
                                                            </span>
                                                            <div id="{{'content-facets-options' + i}}" class="installed-addons collapse" data-parent="#content-facets">
                                                                <section class="content-facets-checkbox-container" >
                                                                    <span class="content-factes-itrations" *ngFor="let filterValue of ContentFlters.value; let p = index">
                                                                        <mat-checkbox matTooltip="{{filterValue.filed_values}}" [(ngModel)]="filterValue.checked" matTooltipPosition="below" mattooltipclass="an-tooltip-sessionReport-filters" class="content-facets-checkbox" (change)="getContentFacetsValue(filterValue,ContentFlters.field_name,filterValue.filed_values,ContentFlters.field_label,ContentFlters.index_name,ContentFlters.objectName,ContentFlters.cs_name,ContentFlters.index_type,$event)">
                                                                            {{filterValue.filed_values}}
                                                                        </mat-checkbox>
                                                                        <span class="content-facets-count">({{filterValue.count}})</span>
                                                                    </span>
                                                                </section>
                                                            </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row chipArrayClass">
                        <mat-chip-list aria-label="Filter Chip Array">
                            <mat-chip class="matChipItems"
                            *ngFor="let chip of chipFilterArray"
                            [selectable]="chipSelectable"
                            [removable]="chipRemovable"
                            (removed)="removeFilterChip(chip)">
                            <span class="matChipFont">{{chip | SplitString}}</span>
                            <span matChipRemove *ngIf="chipRemovable" class="matChipCancel"></span>
                            <!-- <span matChipRemove *ngIf="chipRemovable" class="matChipCancel">
                                cancel
                            </span> -->
                            <!-- <mat-icon matChipRemove *ngIf="chipRemovable">cancel</mat-icon> -->
                          </mat-chip>
                        </mat-chip-list>
                        <button *ngIf="chipFilterArray && chipFilterArray.size != 0" class="chipClear" (click)="clearChipArray()">Clear all</button>
                    </div> 
                    <div class="sessionDetail perfect perfect-body">
                        <style>
                        .detail-sidebar .nav-stacked li {
                            padding: 0px 10px;
                            border-bottom: 1px solid #fff;
                            min-height: 30px;
                        }

                        .detail-container .nav-stacked li span {
                            line-height: normal;
                            float: right;
                            background-color: #10A69A;
                            margin: 7px;
                            padding: 0px 6px;
                            color: white;
                            font-weight: 600;
                            right: 0px;
                            top: 0px;
                            position: absolute;
                        }

                        .detail-container .s-detail {
                            display: inline-block;
                            padding: 10px 10px 10px 210px;
                            width: 100%;
                            min-height: 100px;
                        }

                        .detail-sidebar .active-list {
                            background-color: white;
                            color: #2ca4c5;
                        }

                        .detail-sidebar .nav-stacked li {
                            margin: 0px;
                        }
                        </style>
                        <table class="table table-su card-1 table-session-detail">
                            <thead class="t-head">
                                <tr class="analytics-section-header">
                                    <th style="font-size:14px;">Session Identifier</th>
                                    <th *ngIf="email" style="font-size:14px;">Email</th>
                                    <th *ngIf="entitlements" style="font-size:14px;">User Entitlements</th>
                                    <th *ngIf="externalUser" style="font-size:14px;">Internal User</th>
                                    <th *ngFor="let col of columnsArray" style="font-size:14px;">
                                        {{columnsMap[col]}}                   
                                        <span (click)="changeSortOrder(col)" style="cursor: pointer;">
                                            <svg *ngIf="sortByField !== col" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                                <defs>
                                                    <style>
                                                        .a {
                                                            fill: none;
                                                        }
                                        
                                                        .b {
                                                            fill: #707070;
                                                        }
                                                    </style>
                                                </defs>
                                                <path class="a" d="M0,0H16V16H0Z" />
                                                <path class="b"
                                                    d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                                    transform="translate(-1.903 -1.069)" />
                                            </svg>
                                        </span>
                                        <span *ngIf="sortByField === col" (click)="changeSortOrder(col)" style="cursor: pointer;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                                <defs>
                                                    <style>
                                                        .aa {
                                                            fill: none;
                                                        }
                                        
                                                        .bb {
                                                            fill: #53C6FF;
                                                        }
                                                    </style>
                                                </defs>
                                                <path class="aa" d="M0,0H16V16H0Z" />
                                                <path class="bb"
                                                    d="M12.569,12.409V8.4a.667.667,0,1,0-1.333,0v4.007H10.043a.33.33,0,0,0-.233.567l1.86,1.853a.342.342,0,0,0,.473,0L14,12.976a.331.331,0,0,0-.233-.567ZM7.669,3.3l-1.86,1.86a.33.33,0,0,0,.233.567H7.236V9.736a.667.667,0,0,0,1.333,0V5.729H9.763A.33.33,0,0,0,10,5.162L8.136,3.3a.334.334,0,0,0-.467,0Z"
                                                    transform="translate(-1.903 -1.069)" />
                                            </svg>
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngIf="sessionActivityList && sessionActivityList.buckets && sessionActivityList.buckets.length === 0 && !sessionActivityList.empty && !noSupportUrl">
                                    <td colspan="7">
                                        <div class="col-sm-12">
                                            <div style="text-align: center;">
                                                <div class="spinner">
                                                    <div class="bounce1"></div>
                                                    <div class="bounce2"></div>
                                                    <div class="bounce3"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <ng-container *ngFor="let session of sessionActivityList.buckets | paginate: {itemsPerPage: 10, currentPage: sessionActivityList.cp, id: 'first',totalItems: sessionActivityList.count}; let i=index">
                                    <tr [attr.class]="(session.hideflag) ? 'active ' : ''">
                                        <td (click)="getSessionDetails(0, session, searchTextValue); session.hideflag = true;">
                                            <u>
                                                <a href="JavaScript:void(0);" style="cursor: pointer;">{{session.cookie}}</a>
                                            </u>
                                        </td>
                                        <td *ngIf="email">{{session.email}}</td>
                                        <td *ngIf="entitlements" class="entitlements"> {{session.entitlements | excerpt: 60 }} 
                                            <div class="hover-list">
                                              <ul >
                                                <li *ngFor="let item of session.entitlements">
                                                  {{ item }}
                                                </li>
                                              </ul>
                                            </div>
                                        </td>
                                        <td *ngIf="externalUser">{{session.is_internal ? session.is_internal : false}}</td>
                                        <td>{{session.search_count}}</td>
                                        <td>{{session.click_count}}</td>
                                        <td>{{session.case_count}}</td>
                                        <td>{{session.is_support_visit ? 'Yes' : 'No'}}</td>
                                         <td>{{session.start_time | date: "yyyy-MM-dd HH:mm:ss": '+0' | timeZone : userTimeZone:'contentSource' }}</td>
                                        <td>{{session.end_time | date: "yyyy-MM-dd HH:mm:ss": '+0' | timeZone : userTimeZone:'contentSource' }}</td>
                                    </tr>
                                    <div class="overlay" [attr.class]="(session.hideflag) ? 'overlaySession ':''" *ngIf="session.hideflag " [hidden]="!session.hideflag">
                                        <div class="filter-container shadowDiv animate-div table-responsive" [@opened]="" style="padding: 0px;overflow: hidden;">
                                            <div style="width: 100%;display: inline-block;padding: 5px;">
                                                <span style="font-size: 13px;font-weight: 900;color: #707070;margin-left: 13px;display: inline-block;padding: 10px;">
                                                    Session ID :{{session.cookie}}
                                                </span>
                                                <img (click)="session.hideflag = false;removeClassToBody();" src="assets/img/Remove.svg" style="cursor: pointer;float: right; height:11px; margin: 16px;">
                                                <!-- <svg class="downloadSession" width="40" height="40" viewBox="-5 -5 40 40" *ngIf="sessionActivityList && sessionActivityList.buckets && sessionActivityList.buckets.length != 0" (click)="getSessionDetails(1,session)" style="margin: 7px;width: 30px;height: 30px;">
                                                    <path data-name="Path 1128" d="M24,14l-1.763-1.763L15.25,19.213V4h-2.5V19.213L5.775,12.225,4,14,14,24Z" />
                                                </svg> -->
                                                <app-email-and-download-reports
                                                    [searchClients]="searchClients"
                                                    [range]="range"
                                                    [internalUser]="internalUser"
                                                    [internalUser]="internalUser"
                                                    [internalUserInside]="internalUserInside"
                                                    [tab]="'Conversions'"
                                                    [reportName]="'SessionTrackingActivityDetails'"
                                                    [Session]="session"
                                                    [hideDownload]="false"
                                                    [isEcosystemSelected]="isEcosystemSelected"
                                                    [ecoSystem] = "ecoSystem"
                                                    [activitySortType] = "activitySortType"
                                                    [selectedActivityFilterInside]="selectedActivityFilterInside"
                                                    [AllUserEmails] = "AllUserEmails"
                                                    [userMetricVariable]="userMetricVariable"
                                                    [userMetricValues]="userMetricValues"
                                                >
                                                </app-email-and-download-reports>
                                            </div>

                                            <div class="Session-Tracking-Details-popup">
                                                <table class="table table-su card-1" *ngIf="session.activityLog">
                                                    <thead class="t-head">
                                                        <tr class="analytics-section-header">
                                                            <th style="font-size:14px;width: 13%;">Activity Type</th>
                                                            <th style="font-size:14px;width: 1%;">
                                                                <form autocomplete="off" style="margin: 0px">
                                                                    <mat-select panelClass="innerFilterPanel" [(value)]="modeselectInside" multiple>
                                                                        <mat-option #allSelectedInside (click)="toggleActivityInside('all', session)" [value]="'all'">All</mat-option>
                                                                        <mat-option *ngFor="let filters of activityTypesInside; let i=index" [value]="filters" (click)="toggleActivityInside(filters, session)">
                                                                            {{selectedActivityFilterType[filters]}}
                                                                        </mat-option>
                                                                    </mat-select>
                                                                </form>
                                                            </th>
                                                            <th style="font-size:14px;width:18%">Activity Detail</th>
                                                            <th *ngIf="externalUser" style="font-size:14px;width:13%;">Activity Status</th>
                                                            <th *ngIf="externalUser" style="font-size:14px;margin:0px; width: 6%;">
                                                                <mat-form-field class="InternalActivityDropdown">
                                                                    <mat-select panelClass="innerFilterPanel"
                                                                       [ngModel]="selectedStautsType"
                                                                       (ngModelChane)="selectedStautsType" multiple>
                                                                        <mat-option *ngFor="let type of statusTypeArray;"
                                                                        [value]="type"
                                                                        (click)="toggleActivityStatus(type, session);">
                                                                            {{type}}
                                                                        </mat-option>
                                                                    </mat-select>
                                                                </mat-form-field>
                                                            </th>
                                                            <th *ngIf="isEcosystemSelected" style="font-size:14px;width: 12%;">Search Client</th>
                                                            <th style="font-size:14px;width:10%;">Page No.</th>
                                                            <th style="font-size:14px;width: 17%;">Time
                                                              <span *ngIf="activitySortType === 'asc';" (click)="session.activityLog.reverse(); activitySortType = 'desc';" style="cursor: pointer;">
                                                                <img src="assets/img/sort_gray.svg">
                                                              </span>
                                                              <span *ngIf="activitySortType === 'desc';" (click)="session.activityLog.reverse(); activitySortType = 'asc';" style="cursor: pointer;">
                                                                <img src="assets/img/sort_blue.svg">
                                                            </span>
                                                            </th>
                                                            <th></th>
                                                        </tr>
                                                    </thead>
                                                    <ng-container *ngFor="let activity of session.activityLog; let activityIndex = index">
                                                      <tr *ngIf="selectedActivityFilterInside[activity.type]==1 && ((statusTypeMap['Internal'] && activity.internal==true) || (statusTypeMap['External'] && activity.internal==false))">
                                                            <td colspan="2">{{activity.typeLabel}}</td>
                                                            <td style="width: 26%" *ngIf="activity.type=='search'">
                                                                <span matTooltip="{{ activity.text_entered.charAt(0) === '#' ? 'Wildcard: ' + activity.text_entered : activity.text_entered }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-sr" class="an_text_truncate" > {{activity.text_entered}} </span>
                                                            </td>
                                                            <td style="width: 26%" *ngIf="activity.type=='conversion'">
                                                                <a target="_blank" matTooltip="{{activity.title ? activity.title : activity.url }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-sr" href="{{activity.url}}"> <span class="an_text_truncate"> {{activity.title ? activity.title : activity.url}} </span>  </a>
                                                            </td>
                                                            <td style="width: 26%" *ngIf="activity.type=='pageView' || activity.type=='supportVisit'">
                                                                <a target="_blank" matTooltip="{{activity.title ? activity.title : activity.window_url }}"  matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-sr" href="{{activity.window_url}}"> <span class="an_text_truncate">  {{ (activity.title ? activity.title : activity.window_url) }} </span> </a>
                                                            </td>
                                                            <td style="width: 26%" *ngIf="activity.type=='caseCreated'">
                                                                <span matTooltip="{{activity.caseSubject}} | {{activity.caseNumber}}"  matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-sr" class="an_text_truncate"> {{activity.caseSubject}} | {{activity.caseNumber}} </span>
                                                            </td>
                                                            <td style="width: 26%" *ngIf="activity.type=='caseDeflection'">
                                                                <span matTooltip="{{activity.caseSubject}} | {{activity.caseNumber}}"  matTooltipPosition="below" matTooltipClass="an-tooltip-sessionReport-sr" class="an_text_truncate" > {{activity.caseSubject}} | {{activity.caseNumber}} </span>
                                                            </td>
                                                            <td *ngIf="externalUser" colspan="2">{{activity.internal ? "Internal":"External"}}</td>
                                                            <td *ngIf="isEcosystemSelected" matTooltip="{{activity.name}}">{{activity.name.length> 10 ? activity.name.substring(0,10) + '...' : activity.name }}</td>
                                                            <td>{{activity.page_no}}</td>
                                                            <td>{{activity.ts | timeZone:userTimeZone:"contentSource"}}</td>
                                                            <td style="white-space:nowrap;">
                                                                <a href="JavaScript:void(0);"> 
                                                                    <span (click)="toggleSessionsAdvancedDetails(session, activityIndex)" *ngIf="activity.type=='search' && ((activity.filters && activity.filters.length > 0)||(activity.exactphrase && activity.exactphrase.length > 0) ||(activity.withoneormore && activity.withoneormore.length > 0) ||(activity.withoutwords && activity.withoutwords.length > 0))" style="float: left; cursor: pointer;">
                                                                        More Detail
                                                                    </span>
                                                                </a>
                                                                <svg class="svgRight" (click)="toggleSessionsAdvancedDetails(session, activityIndex)" *ngIf="activity.type=='search' && ((activity.filters && activity.filters.length > 0)||(activity.exactphrase && activity.exactphrase.length > 0) ||(activity.withoneormore && activity.withoneormore.length > 0) ||(activity.withoutwords && activity.withoutwords.length > 0))" width="24" height="24" viewBox="0 0 24 24">
                                                                    <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" fill="grey" />
                                                                    <path d="M0 0h24v24H0z" fill="none" /></svg>
                                                                <svg class="svgMore" (click)="toggleSessionsAdvancedDetails(session, activityIndex)" *ngIf="activity.type=='search' && ((activity.filters && activity.filters.length > 0)||(activity.exactphrase && activity.exactphrase.length > 0) ||(activity.withoneormore && activity.withoneormore.length > 0) ||(activity.withoutwords && activity.withoutwords.length > 0))" width="24" height="24" viewBox="0 0 24 24">
                                                                    <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z" fill="grey" />
                                                                    <path d="M0 0h24v24H0z" fill="none" /></svg>
                                                            </td>
                                                        </tr>
                                                        <tr *ngIf="activity.type=='search'" [attr.class]="activity.active?'activity-detail-search activity-detail-active':'activity-detail-search'">
                                                            <td class="popupDetails" colspan="100">
                                                                <table class="exactPhaseDetails" style="width:100%">
                                                                    <thead>
                                                                        <tr class="popupDetails">
                                                                            <th style="font-size:14px;">Facets Type</th>
                                                                            <th style="font-size:14px;">Facets Value</th>
                                                                            <th style="font-size:14px;">Exact Phrase</th>
                                                                            <th style="font-size:14px;">With One Or More
                                                                            </th>
                                                                            <th style="font-size:14px;">Without The Words
                                                                            </th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <ng-container *ngFor="let filter of activity.filters;let j=index">
                                                                            <tr class="popupDetails">
                                                                                <td>{{filter.name}}</td>
                                                                                <td *ngIf="filter.selectedValues && filter.selectedValues.length > 0 ">
                                                                                    <ng-container *ngIf="validateMinValue(filter); else noMinValue">
                                                                                        <div>
                                                                                            Min: {{ formatValue(filter.selectedValues[0].min_value) }}
                                                                                        </div>
                                                                                        <div>
                                                                                            Max: {{ formatValue(filter.selectedValues[0].max_value) }}
                                                                                        </div>
                                                                                    </ng-container>
                                                                
                                                                                    <ng-template #noMinValue>
                                                                                        {{ filter.selectedValues[0] }}
                                                                                    </ng-template>
                                                                                </td>
                                                                                <td>
                                                                                    <span> {{activity.exactphrase}}</span>
                                                                                </td>
                                                                                <td>
                                                                                    {{activity.withoneormore}}
                                                                                </td>
                                                                                <td>
                                                                                    {{activity.withoutwords}}
                                                                                </td>
                                                                            </tr>
                                                                        </ng-container>
                                                                        <ng-container
                                                                            *ngIf="(activity.exactphrase !== '' || activity.withoneormore || activity.withoutwords) && activity.filters.length === 0">
                                                                            <tr class="popupDetails">
                                                                                <td></td>
                                                                                <td></td>
                                                                                <td>{{ activity.exactphrase }}</td>
                                                                                <td>{{ activity.withoneormore }}</td>
                                                                                <td>{{ activity.withoutwords }}</td>
                                                                            </tr>
                                                                        </ng-container>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </ng-container>
                                                </table>

                                                <div *ngIf="session.activityLog.isEmpty">
                                                    <div class="no-docs">
                                                      No results found.
                                                      <img class="doc-img">
                                                    </div>
                                                </div>

                                                <!-- Loader -->
                                                <div *ngIf="session.activityLog.length === 0" style="text-align: center; padding: 5px;">
                                                    <div class="spinner">
                                                        <div class="bounce1"></div>
                                                        <div class="bounce2"></div>
                                                        <div class="bounce3"></div>
                                                    </div>
                                                </div>
                                                <!-- Loader Ends -->
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                                <tr *ngIf="sessionActivityList.empty">
                                    <td colspan="7" style="text-align: center">No data available for selected query.</td>
                                </tr>
                                <tr *ngIf="noSupportUrl">
                                    <td colspan="7" style="text-align: center">No Support Url.</td>
                                </tr>
                                <tr *ngIf="sessionActivityList && sessionActivityList.buckets && sessionActivityList.buckets.length != 0" class="hoverNone">
                                    <td colspan="100" style="text-align: right;">
                                        <pagination-controls id="first" (pageChange)="getSessionTrackingDetails(0, searchingType, searchTextValue, exactSearch, $event)">
                                        </pagination-controls>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>