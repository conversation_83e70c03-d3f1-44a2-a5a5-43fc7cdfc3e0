<ng2-toasty></ng2-toasty>
<div class="topHeading">
    <section class="heading-source header-aha">
        <div>
            <span class="Search-Analytics">Analytics For Agent Helper</span>
            <span class="definition" style="margin-top:5px"
                >Delivers insights into agent performance and support
                efficiency.
            </span>
        </div>
        <div class="ah-analytics-header-filters">
          <div class="analytics-header-row">
            <mat-form-field
            class="analytics-header-field">
              <mat-select [(ngModel)]="selectedUID"
                (selectionChange)="productFilterHandler()" placeholder="Agent Helper">
                <mat-option *ngFor="let options of productFilterOptions; let i = index" [value]="options.uid">{{options.label}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="analytics-header-row">
            <mat-form-field class="analytics-header-field">
              <input type="text" matInput name="daterangeInput" id="select-range" placeholder="Date range"
                class="pull-right form-control date-input" daterangepicker [options]='daterangepickerOptions'
                (selected)="selectedDate($event, daterange)" style="float:left;">
                <span class="analytics-header-calendar">
                  <svg height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0V0z" fill="none"/><path class="ad-dark-fill" d="M19 4h-1V3c0-.55-.45-1-1-1s-1 .45-1 1v1H8V3c0-.55-.45-1-1-1s-1 .45-1 1v1H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 15c0 .55-.45 1-1 1H6c-.55 0-1-.45-1-1V9h14v10zM7 11h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z" fill="#6b6b6b" /></svg>
                </span>
            </mat-form-field>
          </div>
        </div>
      </section>
    </div>
<div>
    <div class="sectionDiv colubridae19-sectionDiv ad_darkmode-bg4" [ngClass]="{'ad-heading-space' : isTopbarShow}">
        <div class="analyticsV2-adoption" id="searchAnalytics">
            <nav mat-tab-nav-bar>
                <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/ah-analytics/agent-helper-adoption">
                    Agent Helper Adoption
                </a>
                <!-- <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/ah-analytics/advanced-analytics">
                    Advanced Analytics & Insights
                </a> -->
                <a mat-tab-link routerLinkActive="router-link-active" routerLink="/dashboard/ah-analytics/feedback-analysis">
                    Feedback Analysis
                </a>
            </nav>
        </div>
        <div class="col-xl-12 col-md-12 su-label p-0 mt-2 mb-0" id="buisness-label-adoption-dashboard">
            <div class="ad_buisness-on-page-label-adoption-dashboard w-100 ad_d-block mb-1">
                <div class="ad_buisness-on-page-label-inner-adoption-dashboard">Business on Page</div>
                <div class="ad_buisness-on-page-line-adoption-dashboard"></div>
            </div>
        </div>
        
    <section *ngIf="!noUID" class="ah-analytics-table-container">
        <header class="aha-table-header">
          <div class="table-header-left">
            <span>Feedback Analysis</span>
            <div *ngIf="!isLoading && !noDataToShow" class="total-account-container-aha">
              <div class="thumbsUp-count">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g id="Group_42131" data-name="Group 42131" transform="translate(-587.001 -385.001)">
                      <circle id="Ellipse_538" data-name="Ellipse 538" cx="12" cy="12" r="12" transform="translate(587.001 385.001)" fill="rgba(213,255,220,0.43)"/>
                      <path id="Icon_feather-thumbs-up" data-name="Icon feather-thumbs-up" d="M9.753,6.939V4.688A1.688,1.688,0,0,0,8.064,3L5.814,8.064v6.19h6.347a1.125,1.125,0,0,0,1.125-.957l.777-5.064a1.125,1.125,0,0,0-1.125-1.294ZM5.814,14.254H4.125A1.125,1.125,0,0,1,3,13.129V9.19A1.125,1.125,0,0,1,4.125,8.064H5.814" transform="translate(590.671 388.144)" fill="none" stroke="#16a842" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                    </g>
                  </svg>
                {{totalFeedbackData.totalThumbsUp}}
              </div>
              <div class="thumbsDown-count">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g id="Group_42132" data-name="Group 42132" transform="translate(-587 -487)">
                      <circle id="Ellipse_538" data-name="Ellipse 538" cx="12" cy="12" r="12" transform="translate(587 487)" fill="#ffeded"/>
                      <path id="Icon_feather-thumbs-up" data-name="Icon feather-thumbs-up" d="M6.752,3.939V1.688A1.688,1.688,0,0,0,5.064,0L2.813,5.064v6.189H9.16a1.125,1.125,0,0,0,1.125-.957l.776-5.064A1.125,1.125,0,0,0,9.937,3.939ZM2.813,11.253H1.125A1.125,1.125,0,0,1,0,10.128V6.189A1.125,1.125,0,0,1,1.125,5.064H2.813" transform="translate(604.745 504.889) rotate(180)" fill="none" stroke="#e91b37" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                    </g>
                  </svg>
                {{totalFeedbackData.totalThumbsDown}}
              </div>
            </div>
          </div>
          <div class="table-header-right">
            <app-email-and-download-reports 
            *ngIf="!isLoading && !noDataToShow"
            [searchClients]="selectedUID"
            [range]="range" 
            [tab]="agentHelperAnalytics.tabName"
            [reportName]="agentHelperAnalytics.Reports[0]" 
            [hideDownload]="true"
            [featureTypes]="selectedFeedbackFilter">
        </app-email-and-download-reports>
            <mat-select 
              #feedbackSelect 
              [formControl]="feedbackFilter" 
              multiple 
              (selectionChange)="onFeedbackSelectionChange($event)" 
              (openedChange)="isFeedbackFilterOpen = $event"
              panelClass="custom-select-panel" 
              disableRipple
            >
            <mat-option *ngFor="let feedbackFilter of feedbackFilterList" [value]="feedbackFilter">
              {{ feedbackFilter }}
            </mat-option>
          </mat-select>
              <span (click)="openSelect()" [class.custom-select-open]="isFeedbackFilterOpen"   class="feedback-filter-trigger">
                <app-svg name="feedback-sort-by"></app-svg>
              </span>
          </div>
        </header>
        <div class="aha-table-content">
          <table class="ah-analytics-table">
            <thead class="ah-analytics-table-header">
              <tr>
                <th class="th-svg caseID-search-header-aha">
                  <span *ngIf="!caseIdHeaderSearchBar">
                    Case Id 
                      <svg *ngIf="!caseIdHeaderSearchBar" (click)="caseIdSearchHandler()" xmlns="http://www.w3.org/2000/svg" width="13.504" height="13.505" viewBox="0 0 13.504 13.505">
                        <path id="Path_2764" data-name="Path 2764" d="M12.641,11.484h-.609l-.216-.208a5.021,5.021,0,1,0-.54.54l.208.216v.609L15.34,16.49l1.149-1.149Zm-4.628,0a3.466,3.466,0,1,1,2.456-1.015,3.471,3.471,0,0,1-2.456,1.015Z" transform="translate(-2.985 -2.985)" fill="#707070"/>
                      </svg>
                  </span>
                  <span *ngIf="caseIdHeaderSearchBar">
                    <div class="caseIdSearchBar">
                        <input type="search"
                          #caseSearchInput
                          placeholder="Search Case Id" 
                          class="filled show searchForCaseID"
                          (keyup.enter)="searchCaseTitle($event.target.value,1)">
                        <button class="tableBoxclose" (click)="searchCaseTitle($event.target.value,0)">X</button>
                    </div>
                  </span>
                </th>
                <th>Case Created Date</th>
                <th>Last Feedback Date</th>
                <th style="width: 20%;">
                  <div class="totalFeedbackSortHolder">
                    Total Feedback Count
                    <div>
                      <svg (click)="totalFeedbackSortHandler(false)" [ngClass]="totalFeedbackSortPattern === 'asc' ? 'sortSelected':''" fill="#707070" height="8px" width="8px" version="1.1" id="Layer_1" viewBox="-29.7 -29.7 389.40 389.40" xml:space="preserve" stroke="#707070" stroke-width="18.81">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                        <g id="SVGRepo_iconCarrier"> <path id="XMLID_225_" d="M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001l-139.39,139.393L25.607,79.393 c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393 s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,325.607,79.393z"/> </g>
                      </svg>
                      <svg (click)="totalFeedbackSortHandler(true)" [ngClass]="totalFeedbackSortPattern === 'desc' ? 'sortSelected':''" style="transform: rotate(180deg);" fill="#707070" height="8px" width="8px" version="1.1" id="Layer_1" viewBox="-29.7 -29.7 389.40 389.40" xml:space="preserve" stroke="#707070" stroke-width="18.81">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                        <g id="SVGRepo_iconCarrier"> <path id="XMLID_225_" d="M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001l-139.39,139.393L25.607,79.393 c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393 s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,325.607,79.393z"/> </g>
                      </svg>
                    </div>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <!-- main data loop -->
              <ng-container *ngIf="!isLoading && !noDataToShow">
                <tr *ngFor="let row of feedbackData | paginate: {itemsPerPage: 10, currentPage: pageNumber, id: 'allSearchReport',totalItems: totalFeedbacks}; let i = index" class="aha-table-row" [id]="'ahrow' + i">
                  <td class="caseId-cell-aha-table"><a [attr.href]="row.url" target="_blank">{{ row.case_id }}</a></td>
                  <td>{{ row.case_created_date }}</td>
                  <td>{{ row.last_feedback_date }}</td>
                  <td class="feedbackCount-expandSVG">{{ row.total_feedback_count }}  
                    <div [id]="i" (click)="expandedRowHandler(i,row.case_id)" class="expand-row-svg">
                      <svg xmlns="http://www.w3.org/2000/svg" width="12.988" height="8.118" viewBox="0 0 12.988 8.118">
                        <path id="Path_1680" data-name="Path 1680" d="M11,.361,6.488,5.144,1.979.361A1.111,1.111,0,0,0,.34.361,1.281,1.281,0,0,0,.34,2.1L5.675,7.757a1.111,1.111,0,0,0,1.639,0L12.648,2.1a1.281,1.281,0,0,0,0-1.738,1.135,1.135,0,0,0-1.65,0Z" transform="translate(12.988 8.118) rotate(180)" fill="#868686" opacity="0.668"/>
                      </svg>                  
                    </div>
                  </td>
                </tr>
              </ng-container>
            </tbody>
              <tr *ngIf="isLoading">
                <td colspan="5" style="text-align: center; padding:14%">
                  <div class="loadingScreen ah-analytics-laoder">
                    <div class="spinner">
                      <div class="bounce1"></div>
                      <div class="bounce2"></div>
                      <div class="bounce3"></div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr *ngIf="noDataToShow">
                <td style="text-align: center; padding:12%" class="no-docs" colspan="4">No results found. <img class="doc-img"></td>
              </tr>   
              <tr *ngIf="!noDataToShow && totalFeedbacks > 10">
                <td colspan="4" style="text-align: right;">
                  <pagination-controls id="allSearchReport"
                    (pageChange)="handlePagination($event)">
                  </pagination-controls>
                </td>
              </tr>       
          </table>
  
        </div>
        <section class="aha-popUp-container" id="rowPopUpAHA">
          <table class="expanded-row-modal-table-aha">
            <thead>
              <tr>
                <th>Timestamp</th>
                <th class="filterable-header" id="agentHelper-header-filter">
                  Agent Name 
                  <svg (click)="toggleFilter('agent')" id="Icon_awesome-filter" data-name="Icon awesome-filter" xmlns="http://www.w3.org/2000/svg" width="13.26" height="13.26" viewBox="0 0 13.26 13.26">
                    <path id="Icon_awesome-filter-2" data-name="Icon awesome-filter" d="M12.638,0H.622A.622.622,0,0,0,.183,1.061l4.79,4.79v5.337a.622.622,0,0,0,.265.509l2.072,1.45a.622.622,0,0,0,.978-.509V5.851l4.79-4.79A.622.622,0,0,0,12.638,0Z" fill="#707070"/>
                </svg>
                <div id="agent-name-filter" class="filter-div" *ngIf="agentNameFilter">
                  <ul>
                    <li [ngClass]=" agentName?.isSelected ? 'selected-svg':'' " *ngFor="let agentName of agentNames; let i = index" [id]="agentName?.agent_name" (click)="agentNameFilterHandler(agentName?.agent_name)">
                      <div>
                        <svg class="tick-svg" width="9.409" height="6.703" viewBox="0 0 9.409 6.703">
                        <path id="Icon_feather-check" data-name="Icon feather-check"
                          d="M13.994,9,8.5,14.5,6,12" transform="translate(-5.293 -8.293)" fill="none"
                          stroke="white" stroke-linecap="round" stroke-linejoin="round"
                          stroke-width="1" />
                                        </svg>
                      </div>{{agentName?.agent_name}}</li>
                  </ul>
                </div>
                </th>
                <th class="filterable-header" id="feature-header-filter">
                  Feature 
                  <svg (click)="toggleFilter('feature')" id="Icon_awesome-filter" data-name="Icon awesome-filter" xmlns="http://www.w3.org/2000/svg" width="13.26" height="13.26" viewBox="0 0 13.26 13.26">
                    <path id="Icon_awesome-filter-2" data-name="Icon awesome-filter" d="M12.638,0H.622A.622.622,0,0,0,.183,1.061l4.79,4.79v5.337a.622.622,0,0,0,.265.509l2.072,1.45a.622.622,0,0,0,.978-.509V5.851l4.79-4.79A.622.622,0,0,0,12.638,0Z" fill="#707070"/>
                  </svg>
                  <div id="feature-filter" class="filter-div" *ngIf="featureFilterHandler">
                    <ul>
                      <li [ngClass]=" item.isSelected ? 'selected-svg':'' " *ngFor="let item of features; let i = index" [id]="item.feature_type" (click)="featureFilterHandlerFunc(item.feature_type)"> 
                      <div >
                        <svg class="tick-svg" width="9.409" height="6.703" viewBox="0 0 9.409 6.703">
                        <path id="Icon_feather-check" data-name="Icon feather-check"
                          d="M13.994,9,8.5,14.5,6,12" transform="translate(-5.293 -8.293)" fill="none"
                          stroke="white" stroke-linecap="round" stroke-linejoin="round"
                          stroke-width="1" />
                                        </svg>
                      </div>{{item.feature_type_name}}</li>
                    </ul>
                  </div>
                </th>
                <th class="center-align-css">Generative Content</th>
                <th class="center-align-css">Feedback</th>
                <th>Feedback Category</th>
                <th>Feedback Comments</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngIf="!expandedRowLoader && !expandedRowError">
              <tr class="aha-analytics-tableBody-tr" *ngFor="let feedback of rowFeedbackFilterData; let i = index" >
                <td>{{feedback.ts}}</td>
                <td>{{feedback.agent_name}}</td>
                <td>{{feedback.feature_type_name}}</td>
                <td class="center-align-css generative-content-cell">
                  <svg (click)="generativeContendHandler(i)" id="Icon_ionic-md-chatbubbles" data-name="Icon ionic-md-chatbubbles" xmlns="http://www.w3.org/2000/svg" width="21.249" height="21.25" viewBox="0 0 21.249 21.25">
                    <path id="Path_25706" data-name="Path 25706" d="M7.707,21.875a2.666,2.666,0,0,1-2.288-2.288V11.25h-.49A1.559,1.559,0,0,0,3.375,12.8V26.779l2.988-2.963H17.351A1.578,1.578,0,0,0,18.9,22.238v-.363Z" transform="translate(-3.375 -5.529)" fill="#73c2f8"/>
                    <path id="Path_25707" data-name="Path 25707" d="M24.131,3.375H9.869A1.788,1.788,0,0,0,8.086,5.158v11.55A1.792,1.792,0,0,0,9.869,18.5H22.036L25.913,21.2V5.158A1.788,1.788,0,0,0,24.131,3.375Z" transform="translate(-4.664 -3.375)" fill="#73c2f8"/>
                  </svg>      
                  <div [id]="'generativeContent' + i" class="expanded-generative-content">
                    <div>
                      <div *ngIf="(feedback.feature_type === 'response-assist')" [innerHTML]="feedback.ah_response"></div>
                      <div *ngIf="(feedback.feature_type === 'case-summary-brief')" class="brief-case-summary-aha-modal">
                        <div *ngFor="let briefData of feedback.ah_response; let i = index" class="su-breifData-modal">
                          <div>{{briefData.Title}}</div>
                          <ul>
                            <li *ngFor="let value of briefData.Value">{{value}}</li>
                          </ul>
                        </div>
                      </div> 
                      <div *ngIf="(feedback.feature_type === 'case-timeline')" class="caseTimeline-modal-aha">
                        <div class="casetimeLineHolder">
                          <div class="caseTimeLineData" *ngFor="let timeLineData of feedback.ah_response; let i = index">
                            <div *ngIf="timeLineData.actorType" class="caseTimeLineNode">
                              <div class="caseTimeLineNodeSvg" [ngClass]="timeLineData.actorType == 'user' ? 'caseTimeLineNodeUser':''">
                                <svg *ngIf="timeLineData.actorType == 'agent'" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"><g class="agent-hover-bg"><path d="M12.625 25.0938C9.35291 25.0938 6.27666 23.8195 3.96294 21.5058C1.64922 19.1921 0.375 16.1158 0.375 12.8438C0.375 9.57166 1.64922 6.49541 3.96294 4.18169C6.27666 1.86797 9.35291 0.59375 12.625 0.59375C15.8971 0.59375 18.9733 1.86797 21.2871 4.18169C23.6008 6.49541 24.875 9.57166 24.875 12.8438C24.875 16.1158 23.6008 19.1921 21.2871 21.5058C18.9733 23.8195 15.8971 25.0938 12.625 25.0938Z" fill="white"></path><path d="M12.625 0.84375C9.41968 0.84375 6.40622 2.09197 4.13972 4.35847C1.87322 6.62497 0.625 9.63843 0.625 12.8438C0.625 16.0491 1.87322 19.0625 4.13972 21.329C6.40622 23.5955 9.41968 24.8438 12.625 24.8438C15.8303 24.8438 18.8438 23.5955 21.1103 21.329C23.3768 19.0625 24.625 16.0491 24.625 12.8438C24.625 9.63843 23.3768 6.62497 21.1103 4.35847C18.8438 2.09197 15.8303 0.84375 12.625 0.84375ZM12.625 0.34375C19.5286 0.34375 25.125 5.94019 25.125 12.8438C25.125 19.7473 19.5286 25.3438 12.625 25.3438C5.72144 25.3438 0.125 19.7473 0.125 12.8438C0.125 5.94019 5.72144 0.34375 12.625 0.34375Z" fill="#D0D0D5"></path></g><mask id="mask0_26_31-517" maskUnits="userSpaceOnUse" x="3" y="5" width="19" height="17" style="mask-type: luminance;"><path d="M21.318 5.45074H3.93298V21.2367H21.318V5.45074Z" fill="white"></path></mask><g class="agent-hover" mask="url(#mask0_26_31)"><path d="M12.6259 7.15274C13.9222 7.15406 15.1649 7.66958 16.0815 8.58615C16.9981 9.50273 17.5136 10.7455 17.5149 12.0417V14.1317C17.5149 15.4284 16.9998 16.6719 16.083 17.5888C15.1661 18.5056 13.9226 19.0207 12.6259 19.0207C11.3293 19.0207 10.0858 18.5056 9.1689 17.5888C8.25203 16.6719 7.73694 15.4284 7.73694 14.1317V12.0417C7.74351 10.7471 8.2607 9.5074 9.17615 8.59195C10.0916 7.67651 11.3313 7.15931 12.6259 7.15274ZM12.6259 7.63274C11.4581 7.63748 10.3394 8.10352 9.51353 8.92934C8.68771 9.75516 8.22167 10.8739 8.21693 12.0417V14.1317C8.21635 14.7108 8.33008 15.2844 8.55162 15.8194C8.77316 16.3545 9.09815 16.8405 9.50793 17.2497C10.3352 18.0764 11.4569 18.5407 12.6264 18.5407C13.796 18.5407 14.9176 18.0764 15.7449 17.2497C16.1547 16.8405 16.4797 16.3545 16.7012 15.8194C16.9228 15.2844 17.0365 14.7108 17.0359 14.1317V12.0417C17.0312 10.8739 16.5652 9.75516 15.7393 8.92934C14.9135 8.10352 13.7948 7.63748 12.6269 7.63274" fill="#00A2FF"></path><path d="M11.072 21.0657C10.9729 21.0471 10.8787 21.0087 10.7948 20.9527C10.7109 20.8968 10.6392 20.8246 10.5839 20.7403C10.5285 20.6561 10.4908 20.5615 10.4728 20.4624C10.4548 20.3632 10.4571 20.2614 10.4793 20.1631C10.5016 20.0648 10.5434 19.972 10.6023 19.8902C10.6613 19.8084 10.7361 19.7394 10.8223 19.6872C10.9085 19.6349 11.0044 19.6006 11.1041 19.5863C11.2039 19.572 11.3056 19.5779 11.403 19.6037C11.6025 19.6491 11.8045 19.6825 12.008 19.7037C12.202 19.7147 12.408 19.7267 12.625 19.7267C12.842 19.7267 13.048 19.7157 13.242 19.7037C13.4455 19.6825 13.6475 19.6491 13.847 19.6037C13.9916 19.5698 14.1432 19.5803 14.2818 19.6338C14.4204 19.6874 14.5396 19.7814 14.624 19.9037C15.4833 19.6002 16.264 19.1086 16.909 18.4647C18.0262 17.3528 18.6656 15.8488 18.691 14.2727V11.8507H18.68V11.8247C18.6797 11.5718 18.6566 11.3195 18.611 11.0707V11.0507C18.4064 9.78887 17.8119 8.62269 16.911 7.71574C16.3497 7.15098 15.6823 6.70278 14.9472 6.39693C14.2121 6.09108 13.4237 5.93363 12.6275 5.93363C11.8313 5.93363 11.0429 6.09108 10.3078 6.39693C9.57264 6.70278 8.90524 7.15098 8.34398 7.71574C7.44161 8.62215 6.84573 9.78838 6.63998 11.0507V11.0707C6.59437 11.3195 6.57128 11.5718 6.57098 11.8247V11.8587L6.55998 11.8697V14.1887C6.55998 14.2887 6.57098 14.3947 6.57098 14.4887C6.57298 14.5917 6.58066 14.6946 6.59398 14.7967C6.59786 14.8284 6.59525 14.8605 6.58631 14.8911C6.57737 14.9218 6.56229 14.9503 6.54199 14.9749C6.52169 14.9995 6.49659 15.0197 6.46822 15.0343C6.43985 15.0489 6.40881 15.0575 6.37698 15.0597H5.53298C5.11036 15.0597 4.70491 14.8925 4.40513 14.5947C4.10536 14.2968 3.93562 13.8923 3.93298 13.4697V12.3697C3.93774 11.9475 4.10541 11.5434 4.40098 11.2417L4.41298 11.2307C4.71506 10.9422 5.11528 10.7789 5.53298 10.7737H6.19598C6.4815 9.27725 7.27996 7.92718 8.45384 6.9561C9.62772 5.98501 11.1035 5.4537 12.627 5.4537C14.1505 5.4537 15.6263 5.98501 16.8001 6.9561C17.974 7.92718 18.7725 9.27725 19.058 10.7737H19.721C20.1383 10.7791 20.5382 10.9424 20.84 11.2307L20.851 11.2417C21.1473 11.5441 21.315 11.9494 21.319 12.3727V13.4727C21.3158 13.895 21.1458 14.2989 20.8461 14.5964C20.5464 14.8938 20.1413 15.0607 19.719 15.0607H19.114C18.9475 16.2608 18.4531 17.3917 17.6852 18.3289C16.9174 19.2662 15.9059 19.9734 14.762 20.3727C14.7568 20.5377 14.6966 20.6962 14.591 20.8231C14.4853 20.9499 14.3403 21.0378 14.179 21.0727C13.9231 21.1323 13.6635 21.1744 13.402 21.1987C12.8858 21.2597 12.3642 21.2597 11.848 21.1987C11.5864 21.1744 11.3268 21.1323 11.071 21.0727M6.07998 14.1887V11.7677C6.09098 11.5967 6.10298 11.4247 6.12598 11.2677H5.53298C5.24367 11.2648 4.96473 11.3754 4.75598 11.5757L4.74498 11.5867C4.64025 11.6895 4.55704 11.8121 4.50021 11.9474C4.44338 12.0827 4.41407 12.228 4.41398 12.3747V13.4747C4.41408 13.6196 4.44334 13.763 4.50004 13.8963C4.55673 14.0296 4.6397 14.1502 4.74398 14.2507C4.8466 14.3557 4.96919 14.4391 5.10452 14.4959C5.23985 14.5527 5.3852 14.5819 5.53198 14.5817H6.09198V14.5087C6.08098 14.3947 6.07998 14.2807 6.07998 14.1887ZM19.125 11.2647C19.148 11.4247 19.159 11.5957 19.171 11.7647V14.2807C19.171 14.3807 19.16 14.4747 19.16 14.5807H19.72C19.8668 14.5809 20.0121 14.5517 20.1474 14.4949C20.2828 14.4381 20.4054 14.3547 20.508 14.2497C20.6126 14.1491 20.6958 14.0285 20.7527 13.895C20.8095 13.7615 20.8389 13.6179 20.839 13.4727V12.3727C20.8389 12.226 20.8096 12.0807 20.7528 11.9454C20.6959 11.8101 20.6127 11.6875 20.508 11.5847L20.497 11.5737C20.3942 11.4737 20.2726 11.3949 20.1393 11.3421C20.0059 11.2892 19.8634 11.2633 19.72 11.2657L19.125 11.2647Z" fill="#00A2FF"></path><path d="M10.0219 9.95174C10.0051 9.92432 9.99408 9.89371 9.98955 9.86184C9.98502 9.82997 9.98709 9.79752 9.99562 9.76649C10.0042 9.73545 10.019 9.70651 10.0391 9.68144C10.0593 9.65636 10.0844 9.6357 10.1129 9.62074C10.1397 9.60483 10.1694 9.59456 10.2003 9.59054C10.2312 9.58652 10.2626 9.58884 10.2925 9.59736C10.3225 9.60588 10.3504 9.6204 10.3745 9.64007C10.3987 9.65974 10.4185 9.68413 10.4329 9.71174C10.5755 9.9411 10.736 10.1588 10.9129 10.3627C10.9214 10.38 10.9329 10.3956 10.9469 10.4087C11.5752 11.1723 12.3653 11.7867 13.2602 12.2075C14.155 12.6282 15.1321 12.8449 16.1209 12.8417C16.1833 12.8457 16.2419 12.8733 16.2847 12.9189C16.3275 12.9645 16.3513 13.0247 16.3513 13.0872C16.3513 13.1498 16.3275 13.2099 16.2847 13.2555C16.2419 13.3011 16.1833 13.3288 16.1209 13.3327C15.1091 13.3344 14.1083 13.1214 13.1849 12.7076C12.2614 12.2939 11.4363 11.689 10.7639 10.9327C10.3971 11.2939 9.96128 11.5775 9.48244 11.7665C9.00359 11.9555 8.49157 12.046 7.97694 12.0327C7.91337 12.0325 7.85248 12.0071 7.80753 11.9622C7.76257 11.9172 7.7372 11.8563 7.73694 11.7927C7.73977 11.7281 7.76747 11.667 7.81424 11.6223C7.86101 11.5776 7.92322 11.5527 7.98793 11.5527C8.44433 11.5702 8.89924 11.4907 9.32262 11.3193C9.74599 11.148 10.1282 10.8887 10.4439 10.5587C10.2868 10.3688 10.1453 10.1665 10.0209 9.95374" fill="#00A2FF"></path><path d="M12.6249 17.6037C12.3991 17.6023 12.1738 17.5832 11.951 17.5467C11.7291 17.5047 11.5112 17.4435 11.3 17.3637C11.2694 17.3524 11.2415 17.335 11.2179 17.3126C11.1943 17.2901 11.1756 17.2631 11.1628 17.2331C11.1499 17.2032 11.1433 17.171 11.1434 17.1384C11.1434 17.1059 11.1501 17.0736 11.163 17.0437C11.1739 17.0143 11.1906 16.9874 11.2121 16.9645C11.2336 16.9416 11.2595 16.9233 11.2882 16.9105C11.3169 16.8978 11.3478 16.8909 11.3792 16.8902C11.4106 16.8896 11.4418 16.8952 11.4709 16.9067C11.656 16.9773 11.8472 17.0309 12.042 17.0667C12.2341 17.1038 12.4293 17.1229 12.6249 17.1237C12.8242 17.1223 13.023 17.1032 13.219 17.0667C13.4098 17.0293 13.5972 16.9758 13.779 16.9067C13.8081 16.8952 13.8393 16.8896 13.8707 16.8902C13.9021 16.8909 13.933 16.8978 13.9617 16.9105C13.9904 16.9233 14.0162 16.9416 14.0378 16.9645C14.0593 16.9874 14.076 17.0143 14.0869 17.0437C14.0998 17.0736 14.1065 17.1059 14.1065 17.1384C14.1066 17.171 14.1 17.2032 14.0872 17.2331C14.0743 17.2631 14.0556 17.2901 14.032 17.3126C14.0084 17.335 13.9805 17.3524 13.95 17.3637C13.7387 17.4435 13.5208 17.5047 13.299 17.5467C13.0761 17.5832 12.8507 17.6023 12.6249 17.6037Z" fill="#00A2FF"></path></g><defs><filter id="filter0_d_26_31-517" x="-5.875" y="-2.65625" width="37" height="37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feOffset dy="3"></feOffset><feGaussianBlur stdDeviation="3"></feGaussianBlur><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0"></feColorMatrix><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_26_3"></feBlend><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_26_3" result="shape"></feBlend></filter></defs></svg>
                                <svg *ngIf="timeLineData.actorType == 'user'" width="25" height="25" viewBox="0 0 17 17"><g id="Group_419143-517" data-name="Group 419142" transform="translate(-763 -439)"><g class="user-hover-bg" id="Path_184663-517" data-name="Path 184662" transform="translate(763 439)" fill="#fff"><path d="M 8.5 16.75 C 3.950930118560791 16.75 0.25 13.04907035827637 0.25 8.5 C 0.25 3.950930118560791 3.950930118560791 0.25 8.5 0.25 C 13.04907035827637 0.25 16.75 3.950930118560791 16.75 8.5 C 16.75 13.04907035827637 13.04907035827637 16.75 8.5 16.75 Z" stroke="none"></path><path d="M 8.5 0.5 C 4.088789939880371 0.5 0.5 4.088789939880371 0.5 8.5 C 0.5 12.91121006011963 4.088789939880371 16.5 8.5 16.5 C 12.91121006011963 16.5 16.5 12.91121006011963 16.5 8.5 C 16.5 4.088789939880371 12.91121006011963 0.5 8.5 0.5 M 8.5 0 C 13.19441986083984 0 17 3.805580139160156 17 8.5 C 17 13.19441986083984 13.19441986083984 17 8.5 17 C 3.805580139160156 17 0 13.19441986083984 0 8.5 C 0 3.805580139160156 3.805580139160156 0 8.5 0 Z" stroke="none" fill="#d0d0d5"></path></g><g class="user-hover" id="Icon_feather-user3-517" data-name="Icon feather-user2" transform="translate(767.619 443.08)"><path id="Path_184643-517" data-name="Path 184642" d="M13.763,25.411v-.97A1.941,1.941,0,0,0,11.822,22.5H7.941A1.941,1.941,0,0,0,6,24.441v.97" transform="translate(-6 -16.678)" fill="none" stroke="#ad2aad" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"></path><path id="Path_184653-517" data-name="Path 184652" d="M15.881,6.441A1.941,1.941,0,1,1,13.941,4.5,1.941,1.941,0,0,1,15.881,6.441Z" transform="translate(-10.059 -4.5)" fill="none" stroke="#ad2aad" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"></path></g></g></svg>
                                <span class="ahAnalytics-caseTimeline-tooltip">
                                  {{timeLineData.actorType == 'agent' ? "Agent" : "User"}}
                                </span>
                              </div>
                                <div>
                                  <div *ngFor="let response of timeLineData.llm_response.activityData; let i = index" class="userSection-caseTimeline">
                                    <div [ngStyle]="{'color':(timeLineData.actorType == 'agent')?'#00A2FF':'#AD2AAD'}" class="title-response">{{response.title}} :</div>
                                    <ul class="caseTimeLineUl">
                                      <li *ngFor="let value of response.value; let i = index">{{value}}</li>
                                    </ul>
                                  </div>
                                </div>
                            </div>               
                          </div>
                          <div class="lastNodeCaseTimelineData"></div>
                        </div>
                      </div>
                      <div *ngIf="(feedback.feature_type === 'case-summary-detailed')" class="brief-case-summary-aha-modal">
                        {{feedback.ah_response}}
                      </div>
                      <svg (click)="generativeContendHandler(i)" xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
                        <path id="Icon_ionic-ios-close" data-name="Icon ionic-ios-close" d="M17.47,16.289l3.572-3.573a.837.837,0,1,0-1.184-1.184L16.286,15.1l-3.572-3.573a.837.837,0,1,0-1.184,1.184L15.1,16.289l-3.572,3.573a.837.837,0,0,0,1.184,1.184l3.572-3.573,3.572,3.573a.837.837,0,0,0,1.184-1.184Z" transform="translate(-11.285 -11.289)" fill="#b0adad"/>
                      </svg>
                      <section class="ahAnalytics-generative-Content-heading">
                        Generative Content 
                        <span>({{feedback.feature_type_name}})</span>
                      </section>
                    </div>
                  </div>
                </td>
                <td class="center-align-css">
                  <svg *ngIf="!feedback.reaction" id="Group_42040" data-name="Group 42040" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 22 22">
                    <g id="Group_42042" data-name="Group 42042">
                      <circle id="Ellipse_537" data-name="Ellipse 537" cx="11" cy="11" r="11" transform="translate(22 22) rotate(180)" fill="#ffeded"/>
                      <path id="Icon_feather-thumbs-up" data-name="Icon feather-thumbs-up" d="M8.514,6.216V4.378A1.378,1.378,0,0,0,7.135,3L5.3,7.135V12.19H10.48a.919.919,0,0,0,.919-.781l.634-4.135a.919.919,0,0,0-.919-1.057ZM5.3,12.19H3.919A.919.919,0,0,1,3,11.271V8.054a.919.919,0,0,1,.919-.919H5.3" transform="translate(18.462 19.583) rotate(180)" fill="none" stroke="#e91b37" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
                    </g>
                  </svg>  
                  <svg *ngIf="feedback.reaction" id="Group_42048" data-name="Group 42048" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 22 22">
                    <g id="Group_42039" data-name="Group 42039">
                      <circle class="positive-feedback-svgCircle" id="Ellipse_87" data-name="Ellipse 87" cx="11" cy="11" r="11" fill="rgba(213,255,220,0.43)"/>
                    </g>
                    <path id="Icon_feather-thumbs-up" data-name="Icon feather-thumbs-up" d="M8.514,6.216V4.378A1.378,1.378,0,0,0,7.135,3L5.3,7.135V12.19H10.48a.919.919,0,0,0,.919-.781l.634-4.135a.919.919,0,0,0-.919-1.057ZM5.3,12.19H3.919A.919.919,0,0,1,3,11.271V8.054a.919.919,0,0,1,.919-.919H5.3" transform="translate(3.418 3.393)" fill="none" stroke="#16a842" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
                  </svg>
                </td>
                <td class="center-align-css">
                  <ul class="feedback-category-cell">
                    <li *ngFor="let categorys of feedback.tags; let i = index">{{categorys}}</li>
                  </ul>
                </td>
                <td class="feedback-comments-aha-expanded-table">
                  <textarea [value]="feedback.comment" (input)="feedback.comment = $event.target.value" disabled></textarea>
                  <!-- <div>{{feedback.comment}}</div> -->
                </td>
              </tr>
            </ng-container>
            </tbody>
            <tr *ngIf="expandedRowLoader">
              <td colspan="7" class="extra-tr-expandedRowData">
                <div class="loadingScreen ah-analytics-laoder">
                  <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                  </div>
                </div>
              </td>
            </tr>
            <tr *ngIf="expandedRowError">
              <td class="no-docs extra-tr-expandedRowData" colspan="7">No results found. <img class="doc-img"></td>
            </tr> 
          </table>
        </section>
      </section>
      
      <section *ngIf="noUID">
        <div style="text-align: center; padding:12%" class="no-docs" colspan="4">No Agent Helper Configured. <img class="doc-img"></div>
      </section>
    </div>
</div>