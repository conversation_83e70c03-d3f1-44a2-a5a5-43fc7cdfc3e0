.agent-helper-analytics-container{
    height: 82vh;
}
.case-email-adoption-dashboard{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: row-reverse;
}
// aha -> agent helper analytics

.header-aha {
    display: flex;
    justify-content: space-between;
    // padding: 20px 15px;
    background-color: #ffffff;
}

.header-date-container-aha{
    width: fit-content;
    height: min-content;
}

.ah-analytics-header-filters{
    display: flex;
    align-items: center;
    gap:10px;
}

// .header-date-container-aha {
//     width: 206px;
//     height: 42px;
//     border: 1px solid #d7d7d7;
//     border-radius: 4px;
// }

.thumbsUp-count > div {
    //svg container background css
    border-radius: 100%;
    background-color: #56c5fe;
    opacity: 0.12;
    padding: 5px;
    width: 22px;
    height: 22px;
}

.thumbsDown-count,
.thumbsUp-count {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 18px;
}

.thumbsDown-count > div {
    border-radius: 100%;
    background-color: #ff9b04;
    opacity: 0.09;
    padding: 5px;
    width: 22px;
    height: 22px;
}

.aha-table-header {
    background-color: #e0e8ec;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    height: 52px;
}
.ah-analytics-table-container {
    padding: 20px 10px;
    padding-bottom: 0;
}

.table-header-left {
    display: flex;
    gap: 30px;
    align-items: center;
}

.total-account-container-aha {
    display: flex;
    gap: 22px;
    padding: 4px 9px;
    background: transparent linear-gradient(92deg, #FFFFFF 0%, #FFFFFF 100%) 0% 0% no-repeat padding-box;;
    border-radius: 3px;
    align-items: center;
    color: #43425d;
}

.total-account-container-aha > div > svg > g > path{
    cursor: default;
}

.table-header-left > span {
    font-size: 14px;
    font-weight: 500;
}

.aha-table-content {
    padding: 10px;
    background-color: white;
}

.ah-analytics-table-header {
    background-color: #f3f8f9;
    color: #707070;
}

.aha-table-content > table {
    width: 100%;
}

.ah-analytics-table th,
td {
    padding: 19px 10px;
    font-size: 14px;
}
.ah-analytics-table > tbody {
    background-color: white;
}
.totalFeedbackSortHolder{
    display: flex;
    gap:5px;
    width: 100%;
    align-items: center;
}

.totalFeedbackSortHolder > div{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column-reverse;
}

.sortSelected{
    fill: black;
    stroke: black ;
    stroke-width: 40px;
}

.totalFeedbackSortHolder > div > svg{
    cursor: pointer;
}

.feedbackCount-expandSVG{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.ah-analytics-table > tbody tr,
.expanded-row-modal-table-aha > tbody tr {
    border-bottom: 1px solid #e0e0e0;
}

.expanded-row-modal-table-aha > tbody tr:last-child {
    border-bottom: none !important;
}

.ah-analytics-table td {
    color: #444444;
}

.caseId-cell-aha-table {
    color: #73c2f8 !important;
    text-decoration: underline;
    font-size: 13px;
}

.th-svg {
    display: flex;
    align-items: center;
    gap: 10px;
}

.th-svg > svg{
    width: 100%;
}

.expand-row-svg {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
    width: 21px;
    height: 21px;
    border: 1px solid #aeaeae;
    transform: rotate(-0.5turn);
}

.expand-row-svg:hover {
    border: 1px solid #74c2f8;
    cursor: pointer;
}

.expand-row-svg:hover > svg > path {
    fill: #74c2f8;
}

.aha-table-pagination-footer {
    background-color: white;
    display: flex;
    justify-content: flex-end;
    padding: 15px 32px;
    font-size: 14px;
    color: #919192;
    height: max-content;
    gap: 12px;
    font-weight: 600;
    margin-top: 12px;
}

.aha-table-pagination-footer .direct-pages-components .active {
    background-color: #74c3f9;
    color: white !important;
}

.aha-table-pagination-footer .direct-pages-components span {
    border-radius: 4px;
    padding: 10px 15px;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 1; 
}

.cantGoThisWay {
    color: #d7d7d7 !important;
    cursor: not-allowed !important;
}

.aha-pagination-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.aha-pagination-buttons > svg {
    transform: rotate(-0.25turn);
}

.expanded-row-modal-table-aha thead{
    position: sticky;
    top: 0;
    background-color: white;
}

.expanded-row-modal-table-aha thead::before{
    content:"";
    width: 100%;
    height: 35px;
    background-color: white;
    position: absolute;
    top: -28px;
}

.expanded-row-modal-table-aha thead tr th {
    padding: 10px;
}

.aha-popUp-container {
    display: none;
    margin: 0 10px;
    background-color: white;
    padding: 10px;
    box-shadow: 0px 6px 17px 9px #00000029;
    position: absolute;
    max-height: 300px;
    overflow-y: auto;
    width: calc(100% - 322px);
    margin-right: 2rem;
}

.aha-popUp-container .ah-analytics-laoder{
    margin: 16px 0 !important;
}

.center-align-css {
    text-align: center;
}

.feedback-comments-aha-expanded-table {
    text-wrap: wrap;
    width: 20%;
}

.feedback-comments-aha-expanded-table > textarea{
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    height: 52px;
    overflow-y: auto;
    resize: none;
}

::ng-deep .main-content[data-background-color=black] .feedback-comments-aha-expanded-table,
::ng-deep .main-content[data-background-color=black] .feedback-comments-aha-expanded-table > textarea {
    color: #fff;
}

.feedback-comments-aha-expanded-table > textarea:active{
    outline: none; border: none;
}

.feedback-comments-aha-expanded-table > div{
    max-height: 72px;
    overflow-y: auto;
}

.expanded-row-modal-table-aha{
    width: 100%;
}

.expanded-row-modal-table-aha > thead {
    color: #707070;
}

.feedback-category-cell {
    list-style-type: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.feedback-category-cell > li {
    text-indent: -5px;
    line-height: 1rem !important;
    // text-align: left;
    margin-top: 5px;
}

.feedback-category-cell > li:before {
    content: "-";
    text-indent: -5px;
}

.visibiltyClassAhAnalytics{
    visibility: visible !important;
}
.expanded-generative-content {
    visibility: hidden;
    color: white;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0px;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: max-content;
    background-color: #7a7474b0;
    width: 100vw;
    height: 100vh;
    padding: 12px;
    z-index: 12;
}

.generative-content-cell > svg{
    cursor: pointer;
}

.expanded-generative-content > div{
    width: 80%;
    height: fit-content;
    background-color: white;
    color: black;
    text-align: left;
    padding: 14px 24px;
    box-shadow: 0px 6px 20px #00000029;
    position: relative;
    display: flex;
    flex-direction: column-reverse;
    gap: 12px;
}

.ahAnalytics-generative-Content-heading{
    font-weight: 600;
}

.ahAnalytics-generative-Content-heading > span{
    font-weight: 100;
    font-size: small;
}

.expanded-generative-content > div > svg{
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
}

.expanded-generative-content > div > svg:hover + .expanded-generative-content > div > svg > path{
    fill: black;
}

.expanded-generative-content > div > div {
    overflow-y: auto;
    max-height: 400px;
}

.filterable-header{
    position: relative;
}

.filterable-header > svg:hover {
    cursor: pointer;
}

.filterable-header> svg:hover  > path {
    fill: #73c2f8;
}

.filter-div {
    display: block;
    position: absolute;
    background-color: white;
    top: 2rem;
}

.filter-div > ul {
    padding: 0;
    margin: 0;
    list-style: none;
    font-weight: 500;
    color: #707070;
    padding: 8px;
    box-shadow: 0px 6px 20px #00000029;
}

.filter-div > ul > li {
    display: flex;
    gap: 8px;
    align-items: center;
    width: max-content;
    cursor: pointer;
}

.filter-div > ul > li > div {
    border: 1px solid rgba(112, 112, 112, 0.3);
    border-radius: 3px;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-div > ul > li > div > svg {
    visibility: hidden;
}

.selected-svg > div{
    background-color: #73C2F8 !important;
    border: 1px solid #73C2F8 !important;
}

.selected-svg > div > svg{
    visibility: visible !important;
}

.css-rotate-180Degree > svg{
    transform: rotateX(180deg);
}

.css-rotate-180Degree > svg > path {
    fill: #74c2f8;
}

.css-rotate-180Degree{
    border: 1px solid #74c2f8;
}

.date-form-aha{
    display: flex;
    align-items: flex-end;
}

.date-form-aha > div{
    position: relative;
}

.date-form-aha > div > label{
    color:#949494;
    font-size: 13px;
    margin: 0;
    font-weight: 500;
}

.date-form-aha > div > input{
    font-size: 13px;
    padding: 0;
    border: none;
    height: fit-content;
    float: left;
    z-index: 1200;
    position: relative;
    background: transparent;
    cursor: pointer;
}
.date-form-aha > div> span{
    position: absolute;
    bottom: 1px; right:0;
}

.header-filters-aha{
    padding:5px 10px;
    border: 1px solid #D7D7D7;
    background-color: white;
    border-radius: 4px;
}

.caseID-search-header-aha{
    width: 12rem;
    position: relative;
}

.caseID-search-header-aha span svg{
    cursor: pointer;
    margin-left: 10px;
}

.caseID-search-header-aha span{
    height: 16px;
}

.caseIdSearchBar{
    display: flex;
}

.searchForCaseID{
  width: 172px !important;
}

.caseIdSearchBar > input{
    background-color: white;
    border: none;
    color: #4a83c0;
    font-size: 14px;
}

.caseIdSearchBar > input:focus{
    box-shadow: 0 0 5px rgba(109, 207, 246, 0.5);
}

.caseIdSearchBar > button{
    border: none;
}

.aha-analytics-tableBody-tr td:nth-child(1){
    width: 10%;
}

.su-breifData-modal > div{
    font-weight: 600;
}


.casetimeLineHolder{
    display: flex;
    height: max-content !important;
}

.caseTimeLineUl{
    padding: 0;
    margin: 0;
    list-style-type: none;
}

.title-response{
    margin-bottom: 5px;
}

.caseTimeLineUl li {
    line-height: 20px;
}

.caseTimeLineUl li:before{
    content:"-";
    color: black;
    margin-right: 2px;
}

.casetimeLineHolder{
    display: flex;
    margin-top: 12px;
}

.lastNodeCaseTimelineData{
    min-width: 20vw;
    flex: 1 1 100%;
    position: relative;
}

.lastNodeCaseTimelineData::before{
    background-color: transparent;
    border: 1px dashed #c3c3c3;
    position: absolute;
    left: 50%;
    top: -9px;
    height: 2px;
    width: 100%;
    content: "";
    transform: translateX(-50%); 
}


.caseTimeLineData{
    flex: 0 0 20%;
    position: relative;
}
.caseTimeLineData::before{
    background-color: #7290f882;
    position: absolute;
    left: 54%;
    top: -9px;
    height: 2px;
    width: 100%;
    content: "";
    transform: translateX(-50%); 
}

.caseTimeLineNodeSvg{
    cursor: default;
    position: relative;
    height: fit-content;
}

.caseTimeLineNodeSvg > svg:hover + .ahAnalytics-caseTimeline-tooltip{
    opacity: 1;
    visibility: visible;
}

.ahAnalytics-caseTimeline-tooltip{
    opacity: 0;
    visibility: hidden;
    position: absolute;
    bottom: -27px;
    left: 2px;
    padding: 2px 10px;
    background-color: white;
    border-radius: 25px;
    box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px, rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;
    transition: opacity 500ms, visibility 500ms;
}
.caseTimeLineNodeSvg::before{
    background-color: black;
    position: absolute;
    height: 2px;
    width: 21%;
    content: "";
    left: 12px;
    top: -6px;
    transform: translateX(-50%) rotate(90deg);
}
// .caseTimeLineNodeUser::before{
//     left: 22px !important;
// }

.caseTimeLineNode{
    display: flex;
    gap:13px;
}

.caseTimeLineNode div:nth-child(2){
    flex: 0 0 80%;
}

.userSection-caseTimeline{
    margin-bottom: 5px;
}

.agent-helper-analytics-container .ah-analytics-laoder{
    text-align: center;
    margin-top: 16px;
}

.noDataDIV{
    background-color: white;
    padding: 12%;
    margin: 0 10px;
}

.extra-tr-expandedRowData{
    text-align: center;
    padding: 4%;
    width: 110%;
}

//css changes
.topHeading {
    background-color: #fff;
    border-bottom: 1px solid #00000014;
    padding: 10px 20px;
}


// dark mode css 

::ng-deep .main-content[data-background-color="black"] .agent-helper-analytics-container .topHeading{
    background-color: #2c2b4c !important;
}

::ng-deep .main-content[data-background-color="black"] .header-aha{
    background-color: transparent;
    color: #FFFFFF;
    border: none;
}


::ng-deep .main-content[data-background-color="black"] .aha-table-header{
    background-color: #1f1e40;
    color: aliceblue !important;
    border: none;
}

::ng-deep .main-content[data-background-color="black"] .ah-analytics-table-header{
    background-color: #2C2B4C;
    border: 1px solid #413A5E;
    color: #ffffff;
    /* margin: 0 10px; */
    border: none;
}

::ng-deep .main-content[data-background-color="black"] .aha-table-content{
    background-color: #121230;
    border: 1px solid #413A5E;
    color: #ffffff;
    border: none;
}


::ng-deep .main-content[data-background-color="black"] .ah-analytics-table td{
    background-color: #1a193f;
    color: #ffffff !important;
}


::ng-deep .main-content[data-background-color="black"] .ah-analytics-table > tbody tr,
::ng-deep .main-content[data-background-color="black"] .expanded-row-modal-table-aha > tbody tr {
    border-bottom: 1px solid #121230 !important;
}

::ng-deep .main-content[data-background-color="black"] .ah-analytics-table > tbody {
    background-color: #1a193f !important;
}

::ng-deep .main-content[data-background-color="black"] .ah-analytics-table-container .aha-table-header .total-account-container-aha{
    background: #1b193f;
    border: 1px solid #413a5e;
    color: #fff;
}


::ng-deep .main-content[data-background-color="black"] .ah-analytics-table-container .aha-table-header .total-account-container-aha .thumbsUp-count > svg > g > circle{
    fill:#e6f7ea;
}

::ng-deep .main-content[data-background-color="black"] .aha-popUp-container .expanded-row-modal-table-aha >thead{
    background-color: #2C2B4C;
    color: #ffffff;
    border: none;
}

::ng-deep .main-content[data-background-color="black"] .agent-helper-analytics-container .aha-popUp-container{
    background-color: #1B193F;
    color: #fff;
}

::ng-deep .main-content[data-background-color="black"] .expanded-generative-content > div{
    background-color: #1B193F;
    color: #ffffff;
}

::ng-deep .main-content[data-background-color="black"] .aha-analytics-tableBody-tr .positive-feedback-svgCircle{
    fill:#e6f7ea;
}

::ng-deep .main-content[data-background-color="black"] .expanded-row-modal-table-aha thead::before{
    background-color: #2C2B4C !important;
}

::ng-deep .main-content[data-background-color="black"] .caseTimeLineNodeSvg::before{
    background-color: #c5cff3;
}

::ng-deep .main-content[data-background-color="black"] .ah-analytics-table .aha-table-row .caseId-cell-aha-table > a{
    color: #fff !important;
}

::ng-deep .main-content[data-background-color="black"] .ah-analytics-table .aha-table-row .caseId-cell-aha-table > a:hover{
    color: #73c2f8 !important;
}


::ng-deep .main-content[data-background-color="black"] .expanded-row-modal-table-aha > tbody .aha-analytics-tableBody-tr .expanded-generative-content{
    background-color: rgba(0, 0, 0, 0.5);
}

::ng-deep .main-content[data-background-color="black"] .ahAnalytics-caseTimeline-tooltip{
    background-color: #12132F;
}

::ng-deep .main-panel[data-background-color="black"] .modal-content-new {
    background: #1f1e40;
}

::ng-deep .main-panel[data-background-color="black"] .modal-header-new {
    border-bottom: none;
}


::ng-deep .main-panel[data-background-color="black"] .buttonPrimary:disabled, 
::ng-deep .main-panel[data-background-color="black"] .buttonPrimary:disabled:hover {
    cursor: not-allowed;
    border: 2px solid #625f7c !important;
    background-color: #121230 !important;
    color: #625f7c !important;
}

::ng-deep .main-panel[data-background-color="black"]  .modal-title-new p button, 
.main-panel[data-background-color="black"] .modal-title-new {
    color: #fff;
}

::ng-deep .main-panel[data-background-color="black"] .mat-input-element {
    color: #fff !important;
}

::ng-deep .main-panel[data-background-color="black"] .modal-content-new .mat-input-element {
  color: #fff !important;
}

::ng-deep .main-content[data-background-color="black"] .mat-form-field-label {
    color: #fff !important;
}

::ng-deep .main-content[data-background-color="white"] .agent-helper-analytics-container .downloadImg:hover path.b,
::ng-deep .main-content[data-background-color="white"] .agent-helper-analytics-container .downloadImg:hover path.gdownarraw{
  background-color: #5cbafd !important;
  fill: #ffffff !important;
}


















@font-face {
    font-family: Montserrat;
  }
  #highchart-flexbox{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    overflow-x: scroll;
    overflow-y: hidden;
  }
  @media screen and (max-width: 1444px) {
    #highchart-flexbox{
      display: block;
      justify-content: flex-start;
    }
    @media screen and (min-width: 1200px) and (max-width: 1227px) {
      #highchart-flexbox{
        display: flex;
        justify-content: center;
        width: 76vw;
        transform: translateX(-9px);
      }
    }
  }
  .rangePicker * {
    border-radius: 0px;
  }
  
  .nav-tabs.nav-justified {
    width: 100%;
    border-bottom: 0;
  }
  
  .nav-tabs.nav-justified>li {
    float: none;
  }
  
  .nav-tabs.nav-justified>li>a {
    text-align: center;
    margin-bottom: 5px;
  }
  
  .nav-tabs.nav-justified>.dropdown .dropdown-menu {
    top: auto;
    left: auto;
  }
  
  @media (min-width: 768px) {
    .analytics-header-group {
      display: inline-block;
      border-right: solid 2px #e9ebf0;
      margin: 0px 5px;
    }
    .nav-tabs.nav-justified>li {
      display: table-cell;
      width: 1%;
    }
  
    .nav-tabs.nav-justified>li>a {
      margin-bottom: 0;
    }
  
    .top-right-nav {
      display: block;
    }
  }
  
  .nav-tabs.nav-justified>li>a {
    margin-right: 0;
    border-radius: 0px;
  }
  
  .nav-tabs.nav-justified>.active>a,
  .nav-tabs.nav-justified>.active>a:hover,
  .nav-tabs.nav-justified>.active>a:focus {
    border: 1px solid #2179B8;
  }
  
  @media (min-width: 768px) {
    .nav-tabs.nav-justified>li>a {
      border-bottom: 1px solid #ddd;
      border-radius: 0px 0px 0 0;
    }
  
    .nav-tabs.nav-justified>.active>a,
    .nav-tabs.nav-justified>.active>a:hover,
    .nav-tabs.nav-justified>.active>a:focus {
      border-bottom-color: #fff;
    }
  }
  
  .nav-justified {
    width: 100%;
  }
  
  .nav-justified>li {
    float: none;
  }
  
  .nav-justified>li>a {
    text-align: center;
    margin-bottom: 5px;
  }
  
  .nav-justified>.dropdown .dropdown-menu {
    top: auto;
    left: auto;
  }
  
  @media (min-width: 768px) {
    .nav-justified>li {
      display: table-cell;
      width: 1%;
    }
  
    .nav-justified>li>a {
      margin-bottom: 0;
    }
  }
  
  .nav-tabs-justified {
    border-bottom: 0;
  }
  
  .nav-tabs-justified>li>a {
    margin-right: 0;
    border-radius: 4px;
  }
  
  .nav-tabs-justified>.active>a,
  .nav-tabs-justified>.active>a:hover,
  .nav-tabs-justified>.active>a:focus {
    border: 1px solid #2179B8;
  }
  
  @media (min-width: 768px) {
    .nav-tabs-justified>li>a {
      border-bottom: 1px solid #ddd;
      border-radius: 4px 4px 0 0;
    }
  
    .nav-tabs-justified>.active>a,
    .nav-tabs-justified>.active>a:hover,
    .nav-tabs-justified>.active>a:focus {
      border-bottom-color: #fff;
    }
  }
  
  .data-table .table-header {
    font-weight: 600;
    padding: 3px 20px;
    background-color: white;
    font-size: 0.8em;
  }
  
  .data-table .table-row {
    margin: 0px;
    padding: 0px 15px;
  }
  
  .table-row>div {
    background-color: rgba(0, 128, 0, 0.2);
    padding: 0px;
  }
  
  .table-row .card-header {
    cursor: pointer;
    padding: 3px 20px;
    font-size: 0.8em;
    background-color: white;
  }
  
  .table-row .card-header .badge {
    background-color: #4add4a;
    padding: 0px 10px;
    display: inline-block;
    border-radius: 3px;
    color: white;
    font-weight: 600;
    margin: 0px;
  }
  
  .table-row .panel-body {
    color: #1b4071;
    font-size: .7em;
    padding: 5px 35px;
  
  }
  
  // .table tr.active {
  //   background-color: #fbfff8;
  //   border: 1px solid #5fb72a;
  //   border-left: 4px solid #5fb72a;
  // }
  
  // .table tr.activechart {
  //   background-color: white;
  //   border: 1px solid #5fb72a;
  // }
  
  .table-row .search-detail {
    margin-top: 10px;
    border: 1px solid white;
    margin-left: 0px;
    margin-right: 0px;
  }
  
  .table-row table tr th,
  .table-row table tr td {
    padding: 2px;
    line-height: normal;
  }
  
  .table-row .nav-tabs>li.active>a,
  .table-row .nav-tabs>li.active>a:hover,
  .table-row .nav-tabs>li.active>a:focus {
    background-color: rgb(204, 230, 204);
    border-bottom-color: white;
  }
  
  .search analytics .nav>li>a:hover,
  .search analytics .nav>li>a:focus {
    background-color: #2179B8;
    color: white;
  }
  
  .input-group-addon {
    cursor: pointer;
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1;
    color: #55595c;
    text-align: center;
    background-color: #eceeef;
    border: 1px solid #ccc;
    border-radius: .25rem;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    border-left: 0;
  }
  
  .mat-tab-analytics {
    width: 100%;
  }
  
  .date-input {
    width: 86%;
    letter-spacing: -0.2px;
  }
  
  .analytics-card {
    height: 200px;
    mix-blend-mode: undefined;
    background-color: #ffffff;
    border: solid 2px #e9ebf0;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.04);
    // margin: 15px 0px 0px 0px;
  }
  
  .analytics-card-greyed {
    background-color: #F2F2F2;
    opacity: 0.88;
    pointer-events: none;
  }
  
  .analytics-card-greyed .analytics-count {
    visibility: hidden;
  }
  
  .analytics-card-greyed .tile-extra-info {
    visibility: hidden;
  }
  
  .analytics-card-greyed .analytics-section-heading-height .colubridae19Colors {
    opacity: 0.2;
  }
  
  // .analytics-footer{
  //   height: 48px;
  //   line-height: 4;
  //   text-align: center;
  // }
  .analytics-footer-text {
    letter-spacing: -0.4px;
    text-align: center;
    color: #111820;
    font-size: 14px;
    opacity: 1;
  }
  
  .analytics-section {
    font-size: 15px;
    letter-spacing: -0.5px;
    // height: 70px;
    padding: 15px 15px 0px 15px;
    font-weight: 500;
    float: none;
  }
  
  .analytics-card-count {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 5px 15px 5px 15px;
    color: #43425d;
    z-index: 0;
    font-size: 46px;
    font-weight: bold;
    position:relative;
  }
  
  .analytics-section-header {
    font-size: 16px;
    background-color: #e0e8ec;
    border: solid 1px #e0e8ec;
    height: 49px;
    padding: 0.75em 1em;
    font-weight: 500;
    line-height: 1.7;
    letter-spacing: -0.2px;
    text-align: left;
    color: #707070;
    position: relative;
  }
  
  .analytics-section-heading {
    font-size: 16px;
    background-color: #e0e8ec;
    border: solid 1px #e0e8ec;
    height: 49px;
    padding: 0.75em 1em;
    font-weight: 500;
    line-height: 1.5;
    letter-spacing: -0.2px;
    text-align: left;
    color: #43425d;
    padding-left: 20px;
  }
  
  .search-text {
    width: 43px;
    display: inline-block;
    height: 100%;
    padding: 5px 12px;
    background-color: #f3f6fa;
    cursor: pointer;
  }
  
  .search-text-area {
    width: 584px;
    height: 36px;
    border: solid 1px #e7ecf4;
    display: inline-block;
    margin-top: 7px;
    margin-bottom: 0;
  }
  
  .search-text-area>input {
    display: inline-block;
    outline: none;
    border: none;
    height: 100%;
    width: 92%;
    padding: 5px;
  }
  
  .selectable {
    cursor: pointer;
  }
  
  .selectable:focus {
    outline: 1px dashed;
  }
  
  .selected {
    background-color: #fbfff8;
    border-left: 3px solid #56c7ff;
  }
  
  .selected>td {
    font-weight: bold;
  }
  
  .selected:focus {
    outline: none;
  }
  
  
  //Colubridae19 css changes //
  
  .topHeading {
    background-color: #fff;
    border-bottom: 1px solid #00000014;
    padding: 10px 20px;
  }
  
  .heading-source {
    padding-top: 0px;
  }
  
  .colubridae19Color {
    color: #43425d;
    width: 175px;
    word-break: break-word;
  }
  
  .colubridae19-sectionDiv {
    background: #f3f8f9;
    padding: 20px;
    border: none;
    margin: 0px;
    border-radius: 0px;
  }
  
  .mat-tab-custom-colubridae19 {
    background: white;
    border-radius: 10px;
    font-family: Montserrat;
  }
  
  .mat-tab-header {
    border-bottom: 2px solid #00000014;
  }
  
  .mat-tab-label-container {
    flex-grow: 0.085;
  }
  
  // .analytics-card {
  //   height: 150px;
  // }
  // .analytics-count {
  //   padding: 12px 14px;
  //   font-weight: bold;
  // }
  #searchAnalytics mat-ink-bar {
    background-color: #55c6ff !important;
  }
  
  .sectionMainDiv {
    background: #f3f8f9;
    padding: 0;
  }
  
  .analytics-footer {
    padding: 30px 15px 5px 15px;
  }
  
  .analytics-footer-img {
    width: auto;
    padding: 0px;
  }
  
  .column {
    padding-right: 10px;
    padding-left: 10px;
  }
  
  .row {
    width: 100%;
  }
  
  .card {
    margin: 0;
  }
  
  .date-input {
    width: 88%;
    height: auto;
  }
  
  .analytics-section-heading-height {
    height: 50px;
  }
  
  .analytics-card:hover .colubridae19Colors {
    color: #56c6ff;
  }
  
  .analytics-card-image-1 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/SiteVisits.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-1 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/SiteVisits1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-reload-image-1 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/reload.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
    pointer-events: auto;
  }
  
  .analytics-reload-image-1:hover {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/reload-hover.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    cursor: pointer;
  }
  
  .analytics-footer-img-1 {
    width: 225px;
    background: url(../../../../../assets/img/SiteVisitsFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-1 {
    width: 225px;
    background: url(../../../../../assets/img/SiteVisitsFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card-image-2 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/Clicks.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-2 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/Clicks1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-footer-img-2 {
    width: 225px;
    background: url(../../../../../assets/img/ClicksFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-2 {
    width: 225px;
    background: url(../../../../../assets/img/ClicksFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card-image-3 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/SearchClientUsers.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-3 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/SearchClientUsers1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-footer-img-3 {
    width: 225px;
    background: url(../../../../../assets/img/SearchClientUsersFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-3 {
    width: 225px;
    background: url(../../../../../assets/img/SearchClientUsersFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card-image-4 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/CasesLogged.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-4 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/CasesLogged1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-footer-img-4 {
    width: 225px;
    background: url(../../../../../assets/img/CasesLoggedFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-4 {
    width: 225px;
    background: url(../../../../../assets/img/CasesLoggedFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card-image-5 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/TotalSearches.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-5 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/TotalSearches1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-footer-img-5 {
    width: 225px;
    background: url(../../../../../assets/img/TotalSearchesFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-5 {
    width: 225px;
    background: url(../../../../../assets/img/TotalSearchesFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card-image-6 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/SearchesWithResults.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-6 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/SearchesWithResults1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-footer-img-6 {
    width: 225px;
    background: url(../../../../../assets/img/SearchesWithResultsFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-6 {
    width: 225px;
    background: url(../../../../../assets/img/SearchesWithResultsFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card-image-7 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/WithNoResult.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-7 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/WithNoResult1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-footer-img-7 {
    width: 225px;
    background: url(../../../../../assets/img/WithNoResultFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-7 {
    width: 225px;
    background: url(../../../../../assets/img/WithNoResultFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card-image-8 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/UnsuccessfulSearches.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
    float: right;
  }
  
  .analytics-card:hover .analytics-card-image-8 {
    width: 30px;
    height: 30px;
    background: url(../../../../../assets/img/UnsuccessfulSearches1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 30px;
  }
  
  .analytics-footer-img-8 {
    width: 225px;
    background: url(../../../../../assets/img/UnsuccessfulSearchesFooter1.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .analytics-card:hover .analytics-footer-img-8 {
    width: 225px;
    background: url(../../../../../assets/img/UnsuccessfulSearchesFooter.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 41px;
  }
  
  .Enable-analytics {
    margin-top: 20px;
    height: 89px;
    padding: 10px;
    background-image: linear-gradient(to left, #55c7ff, #7886f7);
  }
  
  .Enable-analytics-img {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
  
  .Enable-analytics-under-search-client {
    text-align: center;
    color: #ffffff;
    font-weight: bold;
    position: absolute;
    font-size: 22px;
    margin: 0;
    width: 100%;
    left: 0;
    bottom: 30px;
  }
  
  .table>tbody {
    display: contents;
  }
  
  .table .t-head {
    display: contents;
  }
  
  .buttonPrimary {
    // background: #e0e8ec;
    box-shadow: none !important;
  }
  
  .table thead th {
    vertical-align: middle;
    border: none;
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.75;
    letter-spacing: normal;
    text-align: left;
    color: #707070;
    padding-left: 20px;
  }
  
  .highConversionSession thead th{
    background-color: #F4F8F9;
    position: relative !important
  }
  
  .table td {
    font-size: 14px;
    font-weight: normal !important;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.43;
    letter-spacing: normal;
    text-align: left;
    color: #4b4b4b;
    padding-left: 20px;
  }
  
  .caseTable td,
  .caseTable th {
      line-height: 2.2;
      padding: 0.75rem;
      vertical-align: inherit;
      font-size: 14px;
  }
  
  .caseTable td {
    //font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 2;
    letter-spacing: normal;
    text-align: left;
    color: #4b4b4b;
    //padding-left: 20px;
    // white-space: nowrap;
  }
  .caseTable td.no-docs{
    padding: 10% 10%;
  }
  .mat-form-field {
    margin-right: 0px;
  }
  
  .mat-select-arrow {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid;
    margin: 0 4px !important;
  }
  
  .analytics-topbar {
    margin: auto 0px auto auto;
    // float: right;
    // background: #ffffff;
    border-radius: 5px;
    // display: inline-block;
  }
  
  .main-panel[data-background-color='black'] .form-group {
    background-color: #121230;
    color: #a0a0ac;
  }
  
  .main-panel[data-background-color='black'] .colubridae19-sectionDiv {
    background-color: #121230;
    color: #a0a0ac;
  }
  
  .search-classifications {
    position:relative;
    padding-right: 0px;
    padding-left: 0;
    padding: 30px;
    background: white;
  }
  
  .Search-Classifications-card {
    padding: 0;
    margin-top: 0;
    border: solid 2px #f3f8f9;
  }
  
  .perfect {
    background-color: #ffffff;
    height: 398px;
    margin: 0 auto;
    overflow-y: auto;
    overflow-x: hidden
  }
  
  
  ::ng-deep .mat-tab-body.mat-tab-body-active{
  overflow-y: hidden;
  }
  
  .cards-8 {
    display: inline-block;
    padding-bottom: 15px;
    width: 100%;
  }
  
  .darkmode {
    padding: 15px 15px 0px 15px;
    background-color: white;
  }
  
  .searches-with-no-result {
    padding: 30px;
    background: white;
    width: 100%;
    display: inline-block;
  }
  
  .articles-usage-by-agent {
    padding: 30px;
    background: white;
    width: 100%;
    display: inline-block;
  }
  
  .app-line-conversion-chart {
    // height: 480px;
    margin: 0 auto;
    padding: 20px;
  }
  
  // .form-group-head {
    // display: inline-block;
  // }
  
  .form-group {
    // padding: 5px 5px 5px 5px;
    margin: 0;
    opacity: 0.79;
  }
  
  #search_tracking_details_row {
    background-color: #ffffff;
  }
  
  @media only screen and (min-width: 320px) and (max-width: 1024px) {
    .topHeading {
      padding: 10px 15px;
    }
  
    .header-2 {
      display: inline-block;
      width: 100%;
    }
  
    .heading-source {
      max-width: 100%;
      display: block;
    }
  
    .analytics-header-group {
      // float: none !important;
      border: none;
    }
  
    .analytics-topbar {
      float: none;
      width: 100%;
    }
  
    .form-group-head {
      width: 100%;
    }
  
    .form-group {
      display: inline-block;
      width: 100%;
    }
  
    mat-form-field {
      width: 100% !important;
    }
  
  
    #searchAnalytics {
      padding: 10px 15px;
    }
  
    .darkmode {
      padding: 15px 0px;
      width: 100% !important;
    }
  
    .column {
      padding: 10px;
    }
  
    .cards-8 {
      padding-bottom: 0px;
    }
  
    .analytics-footer {
      display: none;
    }
  
    .analytics-section-heading {
      height: auto !important;
    }
  
    .Enable-analytics {
      height: 45px;
      border-radius: 10px;
    }
  
    .Enable-analytics-img {
      display: none;
    }
  
    .Enable-analytics-under-search-client[_ngcontent-c4] {
      font-size: 14px;
      padding: 15px;
      top: 0;
    }
  
    .app-line-conversion-chart,
    .app-new-pie-chart-small,
    .col-xl-12.search-classifications,
    #chartsdiv,
    .center-align,
    .pie,
    .col-xl-12.responsive,
    .session-report-graph,
    .app-bar-chart-filter,
    .session-report-detail,
    .general-bar-chart {
      overflow-x: scroll;
      display: inline-block;
      width: 100vw;
    }
  
    .card.card-block.card-1.Search-Classifications-card.responsive {
      width: 1070px;
    }
  
    .card.card-block.card-1,
    .row.chartContainer {
      width: 1100px;
    }
  
    .app-line-conversion-chart {
      overflow-y: hidden;
    }
  
    #newly-added-contentsource {
      height: 450px !important;
    }
  
    .Expand {
      padding-bottom: 0px !important;
    }
  
    .analytics-card-count {
      font-size: 20px !important;
    }
    .analytics-section-heading-height {
      height: 75px;
    }
    .analytics-section{
      font-size: 12px;
    }
    .analytics-card{
      height:115px;
    }
  }
  
  .su-add,
  .su-sub {
    fill: #b3b2bd;
    background-color: #eaf0f4;
  }
  
  .su-add:hover,
  .su-sub:hover {
    background-color: #e1e8ec;
    fill: #ffffff;
  }
  
  .header-1 {
    display: flex;
    // width: 100%;
  }
  
  @media (min-width: 992px) and (max-width: 1440px) {
    .analytics-card-count {
      font-size: 28px;
    }
  
    .analytics-card {
      height: 175px;
    }
    .analytics-section-header {
      font-size: 14px;
    }
    .analytics-section-heading{
      font-size: 14px;
      height: 42px;
      padding: 6px 6px 5px 10px;
      line-height: 2;
    }
    .table td{
      font-size:13px;
    }
    .table thead th{
      font-size: 14px;
    }
    .downloadImg{
      height:30px;
      width:30px;
  
    }
  }
  
  .no-docs {
    padding-top: 100px;
    text-align: center !important;
  }
  
  .Search-Analytics {
    // float: left;
    // width: 100%;
    // line-height: 1;
  }
  
  .Expand {
    padding-bottom: 10px;
    display: inline-block;
    width: 100%;
    padding-right: 10px;
  }
  
  @media only screen and (min-width: 480px) and (max-width: 1024px) {
    .header-1 {
      display: inline-block !important;
    }
    .analytics-header-group {
      width: 100%;
      padding: 5px;
      margin: 0;
      margin-bottom: 5px;
    }
    .topHeading {
      padding: 10px 30px;
    }
  
    #searchAnalytics {
      padding: 10px 30px;
    }
  
    .form-group {
      padding: 5px 5px 5px 5px;
    }
  
    .Search-Analytics {
      width: 100%;
      margin-bottom: 10px
    }
  
    .Expand {
      padding-right: 20px;
      padding-bottom: 0px;
    }
  
    .column {
      padding: 10px 20px 10px 20px;
      width: 50%;
      max-width: 50%;
      float: left;
    }
  
    .card.card-block.card-1.Search-Classifications-card.responsive {
      width: 1100px;
    }
  
    .card.card-block.card-1 {
      width: 1140px;
    }
  
    .one {
      padding-right: 10px;
    }
  
    .two {
      padding-left: 10px;
    }
  
    .analytics-header-group {
      width: 33%;
      padding: 5px;
      margin: 0;
    }
  
    .analytics-header-group {
      float: none;
    }
  }
  
  app-bar-chart .chartLegends {
    padding-left: 0px;
  }
  
  @media only screen and (min-width: 320px) and (max-width: 480px) {
    .header-1 {
      display: inline-block !important;
    }
    .header-2 {
      display: inline-block;
      width: 100% !important;
    }
    .topHeading .heading-source{
      padding: 4px 10px 0px 10px;
      font-size: 16px;
    }
  }
  
  @media only screen and (min-width: 480px) and (max-width: 768px) {
    .analytics-topbar {
      float: none !important;
      width: 100% !important;
    }
  }
  
  @media only screen and (min-width: 768px) and (max-width: 1024px) {
    .header-2 {
      display: inline-block;
      width: 35% !important;
    }
  
    .analytics-topbar {
      float: right;
      width: 65%;
    }
  
    .downloadImg {
      float: right;
      width: 25px;
      height: 25px;
    }
  
    .form-group-head {
      margin-top: 5px;
    }
  }
  .main-panel[data-background-color="black"] ah-analytics .perfect {
    background-color: #1a193f !important;
    border: #1a193f solid !important;
}
  ah-analytics .perfect {
    background: #ffffff;
  }
  .main-panel[data-background-color="black"]
    ah-analytics
    .analytics-section-heading {
    background-color: #1f1e40;
    color: #a0a0ac;
    border: none;
    border-radius: 0px;
}
.main-panel[data-background-color="black"] ah-analytics .sectionMainDiv {
  background-color: #19183d;
  color: #a0a0ac;
}
.main-content[data-background-color="black"] ah-analytics .mat-select-value {
  background-color: transparent !important;
}

.main-content[data-background-color="black"] ah-analytics text {
  fill: #ffffff !important;
}

.main-content[data-background-color="black"] ah-analytics .legends_test {
  color: #ffffff !important;
}

.main-content[data-background-color="black"] ah-analytics .tick line {
  stroke: #121230 !important;
}
  @media (max-width: 426px) {
    .analytics-section-header {
      font-size: 14px;
    }
    .analytics-section-heading{
      font-size: 14px;
    }
    .table td{
      font-size:12px;
    }
    .table thead th{
      font-size: 14px;
    }
  
    .downloadImg {
      float: right;
      width: 25px;
      height: 25px;
    }
  }
  .allign-top {
    position: fixed;
    top: 0;
    z-index: 1001;
  }
  .sectionMainDiv-inner-box {
    width:100%;
    display: inline-block;
    margin-top: 20px;
  }
  
  @media (min-width: 1440px) {
    .downloadImg{
      width: 30px;
      height: 30px;
    }
  }
  @media (min-width: 1340px) and (max-width: 1439px) {
    .downloadImg{
      height:30px;
      width:30px;
    }
  }
  
  .downloadImg {
    cursor: pointer;
    float: right;
    background-color: #e8e8e8;
  }
  
  .su-max:hover,
  .su-min:hover,
  .downloadImg:hover {
    background-color: #5cbafd;
    fill: #ffffff;
  }
  
  ::ng-deep .downloadImg:hover {
    background-color: #5cbafd !important;
    fill: #ffffff !important;
  }
  
  .attached .mat-tab-header{
    display:none !important;
  }
  .Top-Clicked-Searches {
    height: 376px;
    width: 100%;
    overflow: auto;
  }
  .search-summary-conversion {
    display: inline-block;
    width: 220px;
    height: 42px;
    border:1px solid #EAF0F4;
    border-radius:2px;
  }
  .search-conversion {
    width: 152px;
    height: 22px;
    font-family: Montserrat;
    font-size: 14px;
    padding-left: 20px;
  }
  .search-conversion-rate{
    color: #5BBDFE;
    font-weight: 600;
  }
  ::ng-deep .search-summary-conversion .mat-form-field>.mat-input-wrapper>.mat-form-field-underline,
  ::ng-deep .search-summary-conversion .mat-form-field>.mat-input-wrapper>.mat-form-field-underline>.mat-form-field-ripple {
    background-color: transparent;
  }
  ::ng-deep .search-summary-conversion>.mat-form-field>.mat-input-wrapper>.mat-input-flex span>label{
    font-size: 14px !important;
    color: #43425D;
    padding-right: 12px;
    padding-left: 12px;
  }
  ::ng-deep .search-summary-conversion>.mat-form-field>.mat-input-wrapper>.mat-input-flex>.mat-input-infix>.mat-select span>span{
    font-size: 14px !important;
    color: #43425D;
    padding-right: 12px;
    padding-left: 12px;
  }
  
  ::ng-deep .search-summary-conversion .mat-select-arrow {
    background-image: url("../../../../../assets/img/drop-down-dark.svg");
    border-top: none!important;
    padding: 10px;
    background-repeat: no-repeat;
    background-position: bottom;
  }
  
  ::ng-deep .search-summary-conversion .mat-form-field-infix{
    margin-top: 7px !important;
    margin-left: 10px;
  }
  
  .Detailed-Report-selected-heading {
    width: 80%;
    float: left;
    font-weight: 600;
    font-size: 14px;
    margin: auto;
    color: #43425d;
  }
  
  ::ng-deep .main-content[data-background-color="black"] .Detailed-Report-selected-heading {
    color: #ffffff;
  }
  
  ::ng-deep .mat-tab-hide-header .mat-tab-header{
    display: none;
  }
  .sessionFilter{
    width: 13%;
  }
  .seesionfacets{
    padding-top: 0px;
    padding-bottom: 3px;
  }
  // .overlaySession {
  //   position: fixed;
  //   width: 100%;
  //   height: 100%;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   background-color: rgba(0, 0, 0, 0.5);
  //   z-index: 2;
  // }
  
  .activity-detail-search{
    display:none;
  }
  
  .activity-detail-active{
    display: table-row;
  }
  
  .activity-detail-act{
    background-color: #FBFBFB
  }
  .activityDetails{
    font-size: 12px;
    width: 19%;
  }
  .advanceActivityDetails{
    font-size: 12px;
    width: 35%;
  }
  .advanceFacetsDetails{
    font-size: 12px;
    width: 20%
  }
  .popupDetails{
    background-color: #FBFBFB
  }
  .seesionIDdetails{
    font-size: 14px;
    font-weight: 600;
    color: #707070;
    padding: 12px 0px 0px 15px;
    display: inline-block;
  }
  .sessionAdvance{
    padding: 0px;
    height: 458px !important;
    overflow: auto;
    border-top: 1px solid #ebebeb;
    box-shadow: 1.4px 4.8px 10px 0 rgba(149, 149, 149, 0.18);
  }
  
  ::ng-deep  .sessionAdvance .analytics-section-header{
    border: none;
  }
  
  ::ng-deep ah-analytics .sessionAdvance table thead.t-head th{
    box-shadow: 0 2px 2px 0px #0000001a;
  }
  
  ::ng-deep ah-analytics .sessionAdvance table th.advanceFacetsDetails {
    font-size: 12px;
  }
  .svgRight{
    display: none;
  }
  .svgMore{
    display: none;
  }
  .activity-detail-act.activity-detail-sea .svgMore{
    display: block;
    cursor: pointer;
  }
  .activity-detail-sea:not(.activity-detail-act) .svgRight{
    display: block;
    cursor: pointer;
  
  }
  .popupDetails td{
    width: 20%;
    word-break: break-all;
  }
  .activityTypeDetails td{
    width: 20%;
    word-break: break-all;
  }
  .viewFullDetails{
    padding: 11px;
  }
  .textSearchResult span{
    font-weight: 900 !important;
  }
  .tool-tip-info {
    padding-left: 1px;
    color: #6C757D;
    font-size: 12px;
    cursor: pointer;
  }
  
  .Detailed-Report-selected-heading-subreport{
    width: 80%;
      float: left;
      font-weight: 500;
      font-size: 14px;
      margin: auto;
      color: #43425d;
  }
  .Detailed-Report-selected-heading-subreport span{
    font-weight: 600;
  }
  
  ::ng-deep .filterAlignToolTip{
    position: relative !important;
    right:  120px;
  
    word-break: break-all;
    width: 140px;
    max-width: 180px;
  }
  
  ::ng-deep .mat-tooltip {
    max-width: 180px;
  }
  
  ::ng-deep .externalOnlyTooltip {
    max-width: 183px !important;
    margin-top: 20% !important;
    margin-left: -65px !important;
  }
  .modal-footer-new{
    padding: 10px;
    text-align: center;
    margin-bottom: 15px;
  }
  
  .modal-container-new{
    transform: translate(0, 0);
    margin: auto;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    position: absolute;
    height: 166px;
    max-width: 50vw;
    width: 50vw;
    background-color: #fff;
    transition: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    border-radius: 10px;
    pointer-events: none;
  }
  .modal-content-new{
    position: relative;
    background-color: #fafafa;
    /* border: 1px solid rgba(16, 15, 15, 0.2); */
    border-radius: 8px;
    outline: 0;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    width:100%;
    pointer-events: auto;
  }
  
  .modal-header-new{
    word-break: break-word;
    padding: 20px;
    border-bottom: 1px solid #e5e5e5;
    display: block;
  }
  
  .modal-title{
    margin: 0;
    line-height: 1.5;
    font-weight: 400;
  }
  .modalError{
    font-size: smaller;
  }
  .enable-analytics-settings{
    cursor: pointer;
  }
  ::ng-deep .main-panel[data-background-color="black"] .modal-content-new .mat-select-value-text {
    color: #fff;
    font-size: 12px;
  }
  
  ::ng-deep .main-panel[data-background-color="black"] .modal-content-new .mat-input-element {
    color:#fff !important;
  }
  
  ::ng-deep .main-content[data-background-color="black"] .modal-content-new .mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{
    color: #fff !important;
  }
  
  ::ng-deep .main-content[data-background-color="black"] .mat-form-field-appearance-legacy .mat-form-field-label{
    color: #fff;
  }
  
  ::ng-deep .main-panel[data-background-color="black"] ah-analytics .with-searches-toggle{
    background-color: #1f1e40 !important;
    border: #121230 !important;
  }
  
  ::ng-deep .main-panel[data-background-color="black"] ah-analytics  .downloadSession{
    background-color: #1a193f !important;
    fill:white;
  }
  ::ng-deep .main-panel[data-background-color="black"] ah-analytics .adoption-dashboard {
    .mat-accordian,
    .mat-expansion-panel {
      border-radius: 0px;
    }
  }
  
  .modal-body-interactive-search{
      padding: 15px;
      height: 68px;
  }
  .modal-dialog.modal-sm {
    margin: auto;
    top: 40%;
  }
  
  .modal-content{
    position: relative;
    background-color: #fafafa;
    border: 1px solid rgba(16, 15, 15, 0.2);
    border-radius: 8px;
    outline: 0;
    word-wrap: break-word;
    width: 350px;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.04);
  }
  
  .interactive-search-dialog{
    color: #43425D;
    font-size: 18px;
    line-height: 15px;
    font-weight: 500;
    padding-left: 31px;
    top: 10px;
    position: relative;
  }
  .cancel-interactive-alert{
    width: 80px;
    position: relative;
    top: -4px;
    margin-left: 138px;
    margin-bottom: 12px;
  }
  .lightThemeTitleBackground{
    background-color: #f4f8f9;
  }
  .kcsReportBorder{
    border: 2px solid #efefef;
  }
  .kcsReportBackground-theme{
    background-color: #ffffff;
  }
  a.mat-tab-link.router-link-active {
    border-bottom: 2px solid #55c6ff;
    color: #55c6ff;
    font-weight: 500;
    opacity: 0.8;
    font-family: 'Montserrat';
  }
  .analyticsV2 {
    background: #f3f8f9;
    border-radius: 0px;
    border: none;
  }
  .analyticsV2-overview,
  .analyticsV2-conversions,
  .analyticsV2-content-gap-analysis,
  .analyticsV2-adoption {
    background: #fff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .margin-left-20px {
    margin-left: 20px;
  }
  .margin-custom {
    margin: 10px 10px 10px auto;
  }
  .csvOptions{
    height: 26px;
    width: 26px;
    background-color: white;
    float: right;
  }
  .tableLinks{
    color: #7887F7;
    text-decoration: underline;
    cursor: pointer;
  }
  .underline{
    text-decoration: underline;
  }
  .reportHeader{
    display: grid;
    background-color: #F4F8F9;
    text-align: left;
    font-size: 12px;
    color: #707070;
    height: 54px;
    padding-top: 19px;
    padding-left: 10px;
    padding-right: 33px;
  }
  .kcsDetails tr th{
    background-color:#F4F8F9;
  }
  .sharedArticlesTable td{
    font-size: 12px;
    padding: 10px;
  }
  .right {
    float: right
  }
  
  .left{
    float:left;
  }
  
  input::-webkit-search-decoration,
  input::-webkit-search-cancel-button {
    display: none;
  }
  
  input[type=search] {
    ///resources/images/misc/bg_search-open.png
    // background: url('../../../../../assets/img/searchIcon.svg') no-repeat 4px center;
    border: none;
    //padding: 0px 27px 0px 10px;
    //padding: 0px 34px 0px 10px;
    width: 10px;
    // -webkit-border-radius: 10em;
    // -moz-border-radius: 10em;
    // border-radius: 10em;
    -webkit-transition: all .5s;
    -moz-transition: all .5s;
    transition: all .5s;
    -webkit-box-sizing: content-box;
    -webkit-appearance: textfield;
    outline: none;
    cursor: pointer;
    z-index: 2;
    font-size: 12px;
    position: absolute;
    right: 0;
    color: transparent;
    background-color: transparent;
  }
  span.search-icons-th + .left, span.highConversion-icons-th + .left {
    position: absolute;
    top: 15px;
    right: 19px;
    width: 100%;
    height: 70%;
  }
  
  .sessionIdReport{
    position: absolute;
      right: 10px;
      top: 12px;
  }
  
  form#search {
    width: 100%;
  }
  
  #sessionClick input[type=search].filled, #search input[type=search].filled,
  #highConversion input[type=search].filled
  {
    width: calc(100% - 35px);
      padding: 0px 19px 0px 6px;
      color: #4a83c0;
      background-color: #fff;
      cursor: pointer;
      font-size: 12px;
      position: absolute;
      right: 0;
  }
   
  #sessionClick input[type=search].filled{
    width: calc(100% - 65px)!important;
    border: 1px solid #4a83c0;
    border-radius: 7px;
  }
  
  
  #allSucessfulSearches input[type=search].filled ,
  #topSearchesWithNoClicksSearch input[type=search].filled ,
  #AllSearches input[type=search].filled{
      background: #fff;
      width: calc(95% - 55px);
      border: 1px solid #5dc4fc;
      padding-left: 15px;
      padding-right: 19px;
      border-radius: 5px;
      top: 10px;
      left: 20px;
      color: #737373;
  
  }
  #sessionFilterSubReportSearch input[type=search].filled{
    background: #fff;
    width: calc(95% - 55px);
    border: 1px solid #5dc4fc;
    padding-left: 15px;
    padding-right: 19px;
    border-radius: 5px;
    top: 12px;
    left: 20px;
    color: #737373;
  }
  
  #AllSearches input[type=search]:focus {
    width: calc(95% - 55px);
    color: #737373;
  }
  
  #AllSearches .srtableBoxclose{
    // left: 52% ;
    top: 11px;
  }
  #allSucessfulSearches  .srtableBoxclose{
    left: 86% !important;
    top: 11px;
  }
  
   #topSearchesWithNoClicksSearch .srtableBoxclose{
    // left: 52% ;
    top: 11px;
  }
  
  #sessionFilterSubReportSearch .srtableBoxclose{
    top: 13px;
  }
  
  input[type=search]:focus {
    width: calc(100% - 35px);
    background-color: #fff;
    padding-left: 15px;
    cursor: pointer;
    -webkit-box-shadow: 0 0 5px rgba(109, 207, 246, .5);
    -moz-box-shadow: 0 0 5px rgba(109, 207, 246, .5);
    box-shadow: 0 0 5px rgba(109, 207, 246, .5);
    position: absolute;
    right: 0;
    color: #272b2b;
    // width: 70%;
    // background-color: #fff;
    // border-color: #66CC75;
    // padding-left: 69px;
    // cursor: auto;
    // -webkit-box-shadow: 0 0 5px rgba(109, 207, 246, .5);
    // -moz-box-shadow: 0 0 5px rgba(109, 207, 246, .5);
    // box-shadow: 0 0 5px rgba(109, 207, 246, 0.5);
    // position: absolute;
    // right: 0;
    // color: #272b2b;
  }
  
  // #search input[type=search]:hover {
  //   background-color: #fff;
  // }
  
  .filter{
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      width: 150px;
      text-overflow: ellipsis;
  }
  
  .filteralignmentss .filter{
    text-align: left;
    margin-left: 31%;
  }
  
  table.filterBorder tr, table.filterBorder td {
    border: 0px;
  }
  
  #search input:-moz-placeholder {
    color: transparent;
  }
  
  #search input::-webkit-input-placeholder {
    color: transparent;
  }
  #search input[type=search].filled:-moz-placeholder{
    color:#4B4B4B;
  }
  #search input[type=search].filled::-webkit-input-placeholder{
    color:#4B4B4B;
  }
  
  span.Detailed-Report-selected-heading i {
    font-size: 13px;
    font-weight: 500;
    margin-left: 10px;
  }
  
  .kcstableBoxclose
  {
    // z-index: 20;
    border: none;
    background: none;
    font-size: 12px;
    // display:none;
    position: absolute;
    top:-3px;
    right: 5px;
    padding-top:3px
  }
  
  
  .kcsSearchBar{
    background: url('../../../../../assets/img/searchIcon.svg') no-repeat 9px center !important;
    width: 10px;
    background-color: #ffffff !important;
    height: 18px;
    border: 1px solid #707070;
    border-radius: 1px;
    opacity: 1;
    float : right;
    // display:none;
    // position: absolute;
    padding-top: 2px;
    padding-right:9px;
    padding-left:9px;
   -webkit-transition: all .5s;
    -moz-transition: all .5s;
    transition: all .5s;
    -webkit-box-sizing: content-box;
    -webkit-appearance: textfield;
  }
  .showSearch{
    float:right;
    width:122px;
    padding-right:16px;
    padding-left:28px;
  }
  .closeSearxchBox{
    width:0px;
    float:right;
  }
  .hideSearch{
     display:none;
  }
  .showCross{
    display:block;
  }
  .showSearxchBox{
    display:block;
  }
  
  .tableBoxclose{
  z-index:10;
  border: none;
  background: none;
  font-size: 12px;
  position: absolute;
  right: 7px;
  width: 10px;
  padding-top: 1px;
  }
  
  .caseSearch{
  background: url('../../../../../assets/img/searchIcon.svg') no-repeat 9px center !important;
  width: 167px;
  background-color: #ffffff;
  height: 22px;
  border: 1px solid #707070;
  border-radius: 1px;
  opacity: 1;
  padding-left:32px;
  -webkit-transition: all .5s;
  -moz-transition: all .5s;
  transition: all .5s;
  -webkit-box-sizing: content-box;
  -webkit-appearance: textfield;
  }
  
  .swapverticalImage
  {
  background: url('../../../../../assets/img/swap_vert.svg') no-repeat 4px center;
  // background-size: contain;
  // top: -8px;
  // position: absolute;
  // right: 30px;
  // height: 72px;
  // width: 26px;
  background-size: contain;
  top: 14px;
  position: absolute;
  right: 0;
  height: 28px;
  width: calc(100% - 84px);
  }
  
  .caseTable tbody tr.hoverNone {
  border: none;
  border-left: none;
  }
  .caseTable tbody tr.hoverNone:hover {
  border: none;
  border-left: none;
  }
  
  .table-su tbody tr.hoverNone {
    border: none;
    border-left: none;
  }
  .table-su tbody tr.hoverNone:hover {
    border: none;
    border-left: none;
  }
  ///////////////////////////////////////////////////////
  // .column {
  //   float: left;
  //   width: 168px;
  //   padding: 10px;
  //   // height: 50px; /* Should be removed. Only for demonstration */
  // }
  .row:after {
  content: "";
  display: table;
  clear: both;
  }
  .heading2{
  font-size: 12px;
  margin-bottom: 0px;
  margin-top: 15px;
  }
  
  .containerMiddle{
  width: 95%;
  // height: 51px;
  background: #FFFFFF;
  margin: 10px 2%;
  margin-top: 35px;
  }
  
  ::ng-deep {
    .main-panel[data-background-color="black"] .containerMiddle{
      background-color: #121230 !important;
    }
  
    .main-panel[data-background-color="black"] .columnCenter{
      background-color:  #1e1c44 !important;
      border: 0px;
      box-shadow: 0 4px 20px 0 #020913c7 !important;
    }
  
    .main-panel[data-background-color="black"] .containerBottom{
      background-color:  #1e1c44 !important;
      color:white
    }
    .main-panel[data-background-color="black"] .bottomHeading{
      background-color:  #1e1c44 !important;
      color:white
    }
    .main-panel[data-background-color="black"] .titleRow{
      background-color:  #1e1c44 !important;
      color:white
    }
    .main-panel[data-background-color="black"] .titleRow .botColumn{
      background-color:  #1e1c44 !important;
      color:white;
      font-weight:500;
    }
    .main-panel[data-background-color="black"] .svgText {
      color:white;
    }
    .main-panel[data-background-color="black"] .svgCount {
      color:white;
    }
  }
  
  
  
  
  .containerBottom{
  width: 95%;
  // height: 92px;
  // background: wheat;
  margin: 10px 2%;
  background: #FFFFFF;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -ms-overflow-style: none;
  }
  .containerBottom::-webkit-scrollbar {
  display: none;
  }
  
  .containerRow:after{
      content: "";
      display: table;
      clear: both;
  }
  .columnCenter{
    margin: 20px;
    float: left;
    width: 45.5%;
    padding: 10px;
    border: 1px solid #EEF1F7;
    box-shadow: 1px 10px 15px #C2D6FF;
  }
  .font-size-19{
    font-size: 20px;
  }
  .m-t-20{
    margin-top: 20px; 
  }
  //   .float-right{
  //       .float-right
  //   }
  .bottomHeading{
    background: #E0E8EC;
    padding: 13px;
    font-size: 16px;
    width: 100%;
    
  }
  .bottomHeadingTitle{
    letter-spacing: 0px;
    color: #707070;
    font-weight: 500;
    font-size: 16px;
    font-family: Montserrat;
  }
  .botsContainer{
  width: 135%;
  // width: 122%;
  // background: bisque;
  }
  .titleRow:after {
  content: "";
  display: table;
  clear: both;
  
  }
  .titleRow{
  background: #F4F8F9;
  margin-left: 15px;
  margin-top: 15px;
  }
  .botColumnTitle{
  font-size: 12px;
  color: #707070;
  font-weight: 500;
  }
  .botColumn.botColumns{
    width: 20%;
  }
  
  .addedBotRow *{
    border-bottom: 1px solid #f3f8f9;
  }
  
  .no-doc{
    text-align: center;
    padding-top:20px
  }
  
  .titleHelper{
    margin:15px 15px 0px 15px !important;
  }
  .botColumn{
  // float: left;
  display: inline-block;
  width: 14%;
  // width: 18.286%;
  padding: 15px;
  // height: 50px; /* Should be removed. Only for demonstration */
  white-space: normal;
  // text-align: center
  font-size: 12px;    
  word-break: break-word;
  font-family: "Montserrat";
  }
  
  .addedBotRow:after{
  content: "";
  display: table;
  clear: both;
  }
  
  ::-webkit-scrollbar {
    width: 4px;
    height: 0px;
    background-color: #F5F5F5;
    }
  
  
    ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #c1c1c1;  
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    }
  
    ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    background-color: #F5F5F5;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    }
  
  .addedBotRow{
  margin: 0px 15px;
  display: flex;
  }
  .arrowSvg{
  background: white;
  padding: 3px;
  padding-right: 7px;
  padding-left: 7px;
  }
  .svgDiv{
    margin-left: -16px;
      // margin-top: -14px;
      float: left;
      width: 92%;
  }
  .su-artical-failed-deflaction .mat-select-arrow{
  display:none !important;
  }
  .spanBottom {
    padding: 15px;
    line-height: 1.43;
    margin-bottom: 1rem;
  }
  .tdBottom{
    line-height: 1.43;
    margin-bottom: 1rem;
  }
  .display-table{
    display:table !important;
    width:100%
  }
  
  .svgText{
      margin-top: 17px;
      padding: 0px;
      display: inline-block;
      margin-left: 29px;
      font-size: 14px;
      color: #707070;
      font-weight: 600;
  }
  
  .svgLine1{
    height: 5px;
      margin-left: 77px;
      width: 70%;
      background: #F8F7F8 0% 0% no-repeat padding-box;
      border-radius: 20px;
      background-image: linear-gradient(to right,  #55C7FF , #7886F7);
      margin-top: 8px;
      margin-bottom: 5px;
      padding: 4px;
  
  }
  
  .svgLine2{
    height: 5px;
      margin-left: 77px;
      width: 70%;
      background: #F8F7F8 0% 0% no-repeat padding-box;
      border-radius: 20px;
      background-image: linear-gradient(to right, #FFB300  , #FF8800 );
      margin-top: 8px;
      margin-bottom: 5px;
      padding: 4px;
  }
  .svgCount{
    font-weight: 600;
      color: #707070;
  }
  .sharedArticlesHeader th{
    padding-left:7px;
  }
  span.search-icons-th, span.highConversion-icons-th {
    background: url('../../../../../assets/img/searchIcon.svg') no-repeat;
    width: 20px;
    height: 20px;
    position: relative;
    z-index: 1;
    padding: 0 18px;
    top: 2px;
    left: 4px;
    line-height: normal;
    cursor: pointer;
  }
  
  .caseReport{
    font-size:14px;
    position: relative;
    width: auto
  }
  
  .shareResultsAnalyticsReport{
    font-size:14px;
    position: relative;
    width: auto
  }
  
  .case-session-th {
    font-size:14px;
    width: 18%;
    text-align: left;
    padding-left: 12.5rem
  }
  
  .case-session-th-type {
    font-size: 14px;
    width: 12%;
    text-align: left;
    padding-left: 2.5rem;
  }
  
  ::ng-deep .custom-tooltip-casereport{
    position: relative !important; 
    // bottom: 13px !important;
    // left: -25px;
    word-break: break-all;
    top: -30px;
    width: 50%;
    left: 30%;
    top: -10px;
  }
  
  ::ng-deep .custom-tooltip-highConversionResultNotFirstPage{
    position: relative !important; 
    word-break: break-all;
    top: 0px;
    width: 92%;
    left: 0%;
  }
  
  .CaseReportbck{
    background-color:  #e0e8ec; 
  }
  
  .CaseReportbckWhite{
    background-color:  #fff; 
  }
  
  .CaseReportbckWhiteSmke{
    background-color: #F4F8F9;
  }
  
  .show{
    display: block;
  }
  
  .hide{
    display: none;
  }
  .botsRowContainer{
    height: 300px;
    overflow-y: auto;
  }
  
  .searchClickPosition{
    font-size:14px;
    position: relative;
    width: auto
  }
  
  .searchClickPosition td,
  .searchClickPosition th {
      line-height: 2.2;
      padding: 0.75rem;
      vertical-align: inherit;
      font-size: 14px;
  }
  
  .searchClickPosition td {
    //font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 2;
    letter-spacing: normal;
    text-align: center;
    color: #4b4b4b;
  }
  
  .searchClickPosition td.no-docs{
    padding: 10% 10%;
  }
  
  .search-click-header {
    font-size: 16px;
    background-color: #f3f8f9;
    // border: solid 1px #e0e8ec;
    height: 49px;
    padding: 0.75em 1em;
    font-weight: 500;
    line-height: 1.7;
    letter-spacing: 0.2px;
    text-align: center;
    color: #868585;
    position: relative;
  }
  
  .searchClickPosition tbody tr.hover {
    border: none;
    border-left: none;
  }
  
  .searchClickPosition tbody tr.hover:hover {
    border: none;
    border-left: none;
  }
  
  .overlaySession {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 12;
    // cursor: pointer;
  }
  
  .sessionClickPosition{
    font-size:14px;
    position: relative;
    width: auto
  }
  
  .sessionClickPosition td,
  .sessionClickPosition th {
      line-height: 2.2;
      padding: 0.75rem;
      vertical-align: inherit;
      font-size: 14px;
  }
  
  .sessionClickPosition td {
    //font-size: 14px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 2;
    letter-spacing: normal;
    text-align: left;
    color: #4b4b4b;
  }
  
  .sessionFacetBorder{
    border-right:1px solid #DDDFE1!important;
    font-size:14px!important;
    text-align: center!important;
  }
  
  table .exactPhaseDetails .sessionFacetBorder, 
  table .exactPhaseDetails .sessionFacetBorder .popupDetails th, 
  table .exactPhaseDetails .sessionFacetBorder .popupDetails td {
    border: 1px solid #DDDFE1!important;
  }
  .marginLeft30{
    margin-left:30px;
  }
  .marginRight15{
    margin-right:15px;
    font-size:12px;
  }
  .borderGrey{
    border-left: 1px solid #DADADA;
  }
  .main-panel[data-background-color="black"] .search-conversion {
    color: #fff;
    font-weight: 500;
  }
  .marginRight3{
    margin-right:3px;
    font-size:12px;
  }
  
  .iconMarginRight15{
    margin-right:15px;
  }
  .analytics-header-row mat-form-field.analytics-header-field mat-datepicker-toggle.mat-datepicker-toggle button {
    display: none;
  }
  
  .topSearchQueryClass tr{
    word-break: break-all !important;
  }
  
  ::ng-deep .tooltip-topSearchQueryClass{
    position: relative !important; 
    bottom: 15px !important;
    right: -34px !important;
    
  }
  
  .an_text_truncate{
    display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      max-height: 36px;
      word-break: break-all;
  }
  
  ::ng-deep .an-tooltip-sessionReport{
    position: relative !important; 
    bottom: 13px !important;
    left: -35px;
    word-break: break-all;
    width: 300px;
    // right: -34px !important;
  }
  ::ng-deep .an-tooltip-linkSharingReport{
    position: relative !important; 
    bottom: 13px !important;
    left: 35px !important;
    word-break: break-all;
    width:fit-content;
    max-width: 300px;
    // right: -34px !important;
  }
  
  ::ng-deep .an-tooltip-tiles{
    position: absolute !important; 
    bottom: -8px !important;
    left: -50px !important;
    padding: 5px !important;
    // word-break: break-all;
    // width: 235px;
    width: auto;
    font-size: 14px !important;
    // right: -34px !important;
  }
  
  ::ng-deep .tile-extra-info-upper-tooltip{
    position: absolute !important; 
    bottom: -12px !important;
    left: -50px;
    padding: 5px !important;
    // word-break: break-all;
    // width: 235px;
    width: auto;
    font-size: 14px !important;
    // right: -34px !important;
  }
  
  
  ::ng-deep .tile-extra-info-tooltip{
    position: absolute !important; 
    bottom: -13px !important;
    left: 65px;
    padding: 5px 5px !important;
    // word-break: break-all;
    // width: 235px;
    width: auto;
    font-size: 11px !important;
  }
  
  ::ng-deep .tile-extra-info-cg-tooltip{
    position: absolute !important; 
    bottom: -13px !important;
    left: 65px;
    padding: 5px !important;
    // word-break: break-all;
    // width: 235px;
    width: auto;
    font-size: 11px !important;
  }
  
  :ng-deep .tooltip-sessionReport-tswc{
    position: relative;
    left: -154px;
    width: 300px;
    word-break: break-all;
  }
  
  ::ng-deep .an_text_truncate_tcs{
    position: relative !important; 
    bottom: 13px !important;
    word-break: break-all;
  }
  
  ::ng-deep .an-tooltip-sessionReport-tcssearches {
    position: relative !important;
    bottom: 13px !important;
    word-break: break-all;
    left: 64px !important;
  }
  
  ::ng-deep .an-tooltip-sessionReport-tcssearches {
    /* position: relative; */
    width: 300px;
    /* left: -35px; */
    word-break: break-all;
    /* right: -24px; */
    margin-left: 0;
  }
  
  ::ng-deep  .an-tooltip-sessionReport-tcsr {
    left: -244px;
    top: 0px;
  }
  
  ::ng-deep .scd-tooltipAlignment {
    width: 300px;
    word-break: break-all;
    position: relative;
    left: -92px;
  }
  
  ::ng-deep .ascr-tooltipAlignment {
    position: relative;
    left: 56px;
    width: 300px;
    word-break: break-all;
  }
  ::ng-deep .ascr-linkSharingReport-tooltipAlignment {
    position: relative;
    left: 56px;
    // width:fit-content;
    max-width: 300px;
    word-break: break-all;
  }
  
  ::ng-deep .ascr-linkSharingReport-tooltipAlignment {
    position: relative;
    left: 56px;
    // width:fit-content;
    max-width: 300px;
    word-break: break-all;
  }
  
  ::ng-deep .sl-alignment{
    left: 70px;
  }
  ::ng-deep .an-tooltip-sessionReport_tcsr{
    width: 300px;
    position: relative;
  
  }
  
  ::ng-deep .an-tooltip-sessionReport-sr{
    position: relative;
    width: 300px;
    left: -35px;
    word-break: break-all;
  }
  
  ::ng-deep .tcs-rs-alignment{
    width: 300px;
    position: relative;
    left: -100px;
  }
  
  ::ng-deep .tooltip-sessionReport-swnresult {
    width: 300px;
    position: relative;
    left: 54px;
    bottom: 18px;
    text-overflow: clip;
    word-break: break-all;
  }
  
  ::ng-deep .tile-data-reload-tooltip {
    background-color: #0cf !important;
    position: absolute !important;
    top: -10px !important;
    width: 105px !important;
    left: -100px !important;
  }
  
  .high-conversion-result {
    padding: 18px;
    background: white;
    width: 100%;
    display: inline-block;
  }
  
  .highConversionSort{
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  
  .highConversionHeader th{
    background-color: #e0e8ec !important
  }
  
  span.highConversion-icons-th + .left {
    right: 10px !important;
  }
  
  #highConversion input[type=search].filled{
    padding: 0px 8px 2px 6px !important;
  }
  
  .highConversionSessionEmail{
    padding-top: 15px;
  }
  
  ::ng-deep .mat-figure > div {
    overflow: hidden !important;
  }
  
  .searchReportExtra{
  overflow-y: auto;
  max-height: 400px;
  }
  
  th.SearchSubReport {
    background: #fff;
    font-size: 12px !important;
    padding: 0px 19px !important;
    text-align: left;
    position: relative;
  }
  
  
  #sr_search_value input[type=search].filled, #sr_search_type input[type=search].filled {
    width: calc(85% - 26px);
    padding: 0px 19px 0px 6px;
    color: #4a83c0;
    background-color: #fff;
    cursor: pointer;
    font-size: 12px;
    position: absolute;
    left: 0;
    border: 1px solid #5ec8fc;
    border-radius: 4px;
    margin-left: 25px;
  
  }
  
  #sr_search_value input[type=search].filled{
    width: calc(90% - 26px) !important;
  }
  
  .SearchSubReportable tr td {
    width: 4%;
  }
  
  .srtableBoxclose {
      z-index: 10;
      border: none;
      background: none;
      font-size: 12px;
      position: absolute;
      right: 9%;
      width: 10px;
      padding-top: 1px;
  }
  
  #sr_search_value .srtableBoxclose {
    right: 0%;
  }
  
  .highConverion-select-list:hover {
    background-color: white;
  }
  
  .highConverion-select-list.active {
    background-color: white;
    background-image: none;
    border-left: 3px solid #56c7ff;
  }
  
  .highConverion-selected-list {
    cursor: pointer;
    font-weight: bold;
    background-color: white;
    border-left: 3px solid #56c7ff;
  }
  
  .highConversionClickCount{
    display: flex;
    justify-content: center;
    text-indent: -34px;
  }
  .tile-extra-info{
    padding-left: 15px;
    font-size: 11px;
    width: 180px !important;
    font-weight: 400;
  }
  
  .extra-info-footer-padding{
    padding-top: 18px;
  }
  
  .extra-info-count-padding{
    padding-bottom: 0px;
  }
  
  // 07/09/21
  .ad_analytics-list-item {
      position: relative;
  }
  .ad_analytics-list-item mat-select {
      min-width: 190px;
  }
  .su__sc-pin-analytics {
    position: absolute;
    top: 1px;
    right: 2px;
  }
  ::ng-deep .an-tooltip-agentReport{
    position: relative !important; 
    bottom: 13px !important;
    left: 0px;
    word-break: break-all;
    width: 200px;
  }
  
  ::ng-deep .an-tooltip-sub-agentReport{
    position: relative !important; 
    bottom: 13px !important;
    left: 50px;
    word-break: break-all;
    width: 300px;
  }
  .main-content[data-background-color="black"] ah-analytics .focus rect.tool[stroke="#f48b00"]{
    padding-bottom: 10px;
  }
  
  //  Action status dropdown css 
  .actionDropdown {
    display: inline-block;
    margin-bottom: 0px;
    cursor: pointer;
  }
  
  .actionDropdown mat-form-field {
    margin-bottom: 0px;
  }
  
  ::ng-deep .actionDropdown .mat-form-field-infix {
    margin-top: 0px !important;
    padding: 0px;
  }
  
  ::ng-deep .actionDropdown .mat-form-field-underline {
    display: none;
  }
  
  ::ng-deep .actionDropdownSelect.mat-select-panel {
    position: relative;
    min-width: 0px !important;
    right: 110px !important;
    margin-top: 18px !important;
    max-height: none;
  }
  
  ::ng-deep .actionDropdownSelect .mat-option {
    height: 2.2em !important;
    padding: 0px 12px 0px 10px;
    @media (max-width: 1445px) {
      padding: 0px 7px 0px 6px;
    }
  }
  
  ::ng-deep .cdk-overlay-container.black .actionDropdownSelect.mat-select-panel {
    background-color: #1f1e40;
  }
  
  ::ng-deep .actionDropdown .mat-select-arrow {
    border-image-source: url('./../../../../../assets/img/expand_more-24px.svg');
    border-image-repeat: stretch;
    border-left: 20px solid transparent;
    border-right: 0px;
    border-top: 20px solid transparent;
    margin-top: -3px !important;
  }
  
  ::ng-deep .actionTooltip.mat-tooltip {
    margin-top: 2px;
  }
  
  // Action dropdown filter css 
  
  ::ng-deep .filterActionDropdown .mat-select-arrow {
    border-image-source: url('./../../../../../assets/img/filter_list_dark.svg');
    border-image-repeat: stretch;
    border-left: 20px solid transparent;
    border-right: 0px;
    border-top: 20px solid transparent;
    margin-top: -3px !important;
  }
  
  .filterActionDropdown {
    display: inline-block;
    margin-bottom: 0px;
  }
  
  .filterActionDropdown mat-form-field {
    margin-bottom: 0px;
  }
  
  ::ng-deep .filterActionDropdown .mat-form-field-infix {
    margin-top: 0px !important;
    padding: 0px;
  }
  
  ::ng-deep .filterActionDropdown .mat-form-field-underline {
    display: none;
  }
  
  ::ng-deep .actionDropdownFilterSelect.mat-select-panel {
    position: absolute;
    left: -72px;
    margin-top: 27px !important;
    max-height: none;
  
    @media (max-width: 1329px) {
      left: -28px;
    }
  }
  
  ::ng-deep .actionDropdownFilterSelect .mat-option {
    height: 2.1em !important;
    padding: 0px 10px 0px 8px;
  }
  
  ::ng-deep .actionDropdownFilterSelect .mat-pseudo-checkbox-checked {
    background: #55c6ff;
  }
  
  ::ng-deep .cdk-overlay-container.black .actionDropdownFilterSelect.mat-select-panel {
    background-color: #1f1e40;
  }
  .singleLineheading{
    white-space: nowrap !important; 
  }
  
  .extra-info-footer-padding-tile1{
    padding-top: 1px;
  }
  
  .extra-info-div{
    height: 28px;
  }
  
  .analytics-footer-content {
    padding: 15px 15px 5px 15px;
  }
  
  .analytics-section-heading-height-content {
    height: 65px;
  }
  
  .analytics-footer-content {
    display: none;
  }
  
  .extra-info-footer-padding-content{
    padding-top: 5px;
  }
  
  .searchFeedbkClickedPos {
    min-width: 22px;
    min-height: 22px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border: 1px solid #55C6FF;
    border-radius: 50%;
    opacity: 1;
    line-height: 20px;
    font: normal normal medium 9px/11px Montserrat;
    letter-spacing: 0px;
    color: #55C6FF;
    cursor: pointer;
    margin-right: 5px;
    display: inline-block;
    text-align: center;
    float:left;
  }
  .searchFeedbkClickedPos:hover{
    background-color: #55C6FF;
    background: #55C6FF 0% 0% no-repeat padding-box;
    color: #FFFFFF;
  }
  .searchFeedbkFeedback{
    max-width: 60px;
  }
  
  .searchFeedbkTable td
  {
    text-align: left;
  }
  
  .searchFeedbkTable th{
    font-size: 14px;
    text-align: left;
  }
  
  .searchFeedbkClick{
    max-width: 60px;
  }
  .searchFeedbkReportedBy{
    max-width: 40px;
  }
  .showMoreSearchFeedbk{
    cursor: pointer;
    color: #55C6FF;
    white-space: nowrap;
  }
  
  .search-feedbk-header {
    font-size: 16px;
    background-color: #f3f8f9;
    height: 49px;
    padding: 0.75em 1em;
    font-weight: 500;
    line-height: 1.7;
    letter-spacing: 0.2px;
    text-align: center;
    color: #868585;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
  }
  
  .search-feedbk-footer {
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
  }
  .searchFeedbkBody{
    overflow-y: auto;
    overflow-x: auto;
  }
  
  .main-panel[data-background-color='black'] .searchFeedbkClickedPos {
    background: #1b1a3f 0% 0% no-repeat padding-box;
    border-style: dashed;
  }
  
  .searchFeedbkData {
    max-height: 450px !important; 
    height:auto !important; 
    margin: 0 auto; 
    overflow-y:auto; 
    overflow-x: hidden; 
    border: 2px solid rgb(239, 239, 239);
    padding-bottom: 0px;
  }
  .advertisementData{
    max-height: 450px !important; 
    margin: 0 auto; 
    overflow-y:auto; 
    overflow-x: hidden; 
    border: 2px solid rgb(239, 239, 239);
    padding-bottom: 0px;
  }
  .gptfeedbackdata{
    max-height: 450px !important; 
    margin: 0 auto; 
    overflow-y:auto; 
    overflow-x: hidden; 
    border: 2px solid rgb(239, 239, 239);
    padding-bottom: 0px;
  }
  
  .searchFeedbkClickRes{
    text-decoration: underline;
  }
  
  ::ng-deep .innerFilterPanel .mat-option.mat-selected:not(.mat-option-disabled) {
    color: #55c6ff;
  }
  
  ::ng-deep .innerFilterPanel .mat-pseudo-checkbox-checked {
    background: #55c6ff !important;
  }
  
  .shareResultsPaddingBox {
    background: #EFF3F7 0% 0% no-repeat padding-box;
    border-radius: 11px;
    opacity: 1;
    display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      max-height: 36px;
      word-break: break-all;
  }
  
  ::ng-deep .shareResultsSelect .mat-select-arrow-wrapper {
    background-image: url("../../../../../../resources/Assets/filter_alt_black_24dp.svg") !important;
   }
  
   ::ng-deep .shareResultsSelect .mat-select-arrow {
     opacity: 0 !important;
   }
  
   ::ng-deep .shareResultsToggle .badge {
    position: absolute;
    top: -7px;
    right: -31px;
    padding: 3px 6px;
    border-radius: 50%;
    background-color: #5BBDFE;
    color: white;
  }
  
  input#caseNumberShareResults {
    width: calc(100% - 52px) !important;
    padding: 0px 19px 0px 24px !important;
  }
  
  input#caseNumberShareResults[type="search"]::-webkit-search-cancel-button {
    display: none;
  }
  
  input#linkedByShareResults {
    width: calc(100% - 52px) !important;
    padding: 0px 19px 0px 24px !important;
  }
  
  input#linkedByShareResults[type="search"]::-webkit-search-cancel-button {
    display: none;
  }
  
  .shareResultsUrlCol {
    display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      max-height: 36px;
      word-break: break-all;
  }
  
  ::ng-deep .shareResultsUrlDarkmode {
    color: black;
  }
  
  ::ng-deep .main-panel[data-background-color='black'] .shareResultsUrlDarkmode {
    color: white !important;
  }
  
  .share-result-analytics-header {
    font-size: 16px;
    background-color: #f3f8f9;
    height: 49px;
    padding: 0.75em 1em;
    font-weight: 500;
    line-height: 1.7;
    letter-spacing: 0.2px;
    text-align: center;
    color: #868585;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
  }
  
  .shareResultsAnalyticsDate {
    text-align: left;
    font: normal normal normal 12px/18px Poppins;
    font-family: Montserrat;
    letter-spacing: 0px;
    opacity: 1;
  }
  
  ::ng-deep .main-panel[data-background-color='black'] .shareResultsPaddingBox {
    background: #5BBDFE 0% 0% no-repeat padding-box !important;
    border-radius: 11px;
    opacity: 1;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 36px;
    word-break: break-all;
  }
  .shareResultEmailCol {
    display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
  }
  
  .splitOnHover {
    position: absolute; 
    display: flex;
    flex-direction: column;
    z-index: 1; 
    width: 200px;
    padding: 10px 18px 18px 10px;
    border: 1px solid #5FB3FB; 
    border-radius: 6px; 
    background: #FFFFFF 0% 0% no-repeat padding-box; 
    box-shadow: 0px 3px 40px #00000029; 
    top: 60px;
    left: 70px;
    overflow-y: auto; 
    max-height: 200px;
  }
  
  .splitOnHoverContent {
    flex: 2; 
    margin-top: 10px; 
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
  }
  
  .splitSearchClient {
    color: #4F4E66; 
    font: normal normal normal 12px/15px Montserrat; 
    overflow: hidden; text-overflow: ellipsis; 
    margin-right: 10px;
  }
  
  ::ng-deep .analytics-dropdown .mat-expansion-panel-content{
    overflow: auto !important;
    max-height: 165px !important;
  }
  
  mat-expansion-panel.analytics-dropdown.mat-expansion-panel.mat-expanded.mat-expansion-panel-spacing {
    margin: 0 !important;
  }
  
  ::ng-deep .analytics-dropdown  .mat-expansion-panel-header{
    position: relative;
    height: 32px !important;
    padding: 0 18px;
  }
  
  ::ng-deep .scPanelExpand .mat-expansion-panel-content{
    height: auto !important;
    visibility: visible !important;
  }
  
  ::ng-deep .ecoPanelExpand .mat-expansion-panel-content{
    height: auto !important;
    visibility: visible !important;
  }
  
  
  ::ng-deep .analytics-dropdown  .mat-expansion-panel-body{
    padding: 0px;
  }
  
  .analytics-dropdown{
    .mat-option{
      padding: 0 18px;
      font-size: 12px;
      height: 32px !important;
      line-height: 32px !important;
      color: #333A4D;
    }
  
    .mat-option.mat-selected:not(.mat-option-disabled){
      color: #55c6ff;
      background:white
    }
  }
  
  
  ::ng-deep .analytics-dropdown div#cdk-accordion-child-0::-webkit-scrollbar {
    width: 3px;
    margin-right: 6px;
  }
  
  ::ng-deep .analytics-dropdown div#cdk-accordion-child-0::-webkit-scrollbar-thumb {
    width: 3px;
    background: #E0E8EC;
    border-radius: 20px;
  }
  
  ::ng-deep .analytics-dropdown div#cdk-accordion-child-1::-webkit-scrollbar {
    width: 3px;
  }
  
  ::ng-deep .analytics-dropdown div#cdk-accordion-child-1::-webkit-scrollbar-thumb {
    width: 3px;
    background: #E0E8EC;
    border-radius: 20px;
  }
  
  ::ng-deep .ng-trigger-transformPanel.mat-select-panel.mat-primary.matRole {
    padding: 8px 6px 14px 0px;
    min-width: 233px !important;
    transform: translateX(-31px) translateY(30px);
  }
  
  .searchClient {
    flex: 2; 
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  
  .su__radio-btn-column{
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    margin-bottom: 20px;
  }
  .su__text-accordion{
    display: flex;
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 13px;
  }
  .su_filter_feedback_modal{
    margin-top: 240px;
    margin-left: 403px;
  }
  ::ng-deep .su__matMenu .mat-menu-content {
    padding: 0 !important;
  }
  
  ::ng-deep .su__matMenu .mat-menu-panel{
    height: 230px;
    width: 205px;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 10px 60px #3d3d3d4f;
    border-radius: 4px;
    opacity: 1;
  }
  
  ::ng-deep .page-rating-menu + div .su__matMenu .mat-radio-container,
  ::ng-deep .page-rating-menu + div .su__matMenu .mat-radio-container .mat-radio-inner-circle,
  ::ng-deep .page-rating-menu + div .su__matMenu .mat-radio-container .mat-radio-outer-circle{
    height:16px;
    width:16px;
  }
  ::ng-deep .page-rating-menu + div .su__matMenu .mat-radio-label-content{
    font-size: 14px;
    font-weight: 600;
  }
  ::ng-deep .page-rating-menu + div .su__matMenu .mat-expansion-panel-header,
  ::ng-deep .page-rating-menu + div .su__matMenu .mat-expansion-panel-body{
    padding: 0px 15px;
    color: #707070;
  } 
  ::ng-deep .page-rating-menu + div .su__matMenu .mat-expansion-panel-header {
    height: 55px !important;
  }
  
  ::ng-deep .su__matMenu  .mat-checkbox-label{
    margin-left: 10px;
    line-height: normal;
  }
  
  .showDialog{
    opacity: 0.4;
  }
  
  ::ng-deep .page-rating-menu + div .mat-radio-label {
    margin: 0px;
    
  }
  ::ng-deep .page-rating-menu + div .cdk-overlay-pane .su__matMenu{
    width: 100%;
  }
  ::ng-deep .page-rating-menu + div .cdk-overlay-pane {
    width: 225px;
    max-width: 225px;
  }
  ::ng-deep .page-rating-menu + div .mat-expansion-panel {
    margin: 0px;
    border-bottom: 1px solid #70707040;
    box-shadow: none;
    border-radius: 0;
  }
  
  .su__radio-btn-column mat-radio-button{
    margin-bottom: 8px;
  }
  ::ng-deep mat-checkbox.su__star-emoticons .mat-checkbox-layout .mat-checkbox-inner-container .mat-checkbox-background{
    height: 18px !important;
    width: 18px !important;
  }
  ::ng-deep mat-checkbox.su__star-emoticons .mat-checkbox-layout {
    align-items: center;
    margin: 0px;
  }
  ::ng-deep mat-checkbox.su__star-emoticons .mat-checkbox-layout .mat-checkbox-inner-container {
    margin: 0px !important;
  }
  ::ng-deep mat-checkbox.su__star-emoticons .mat-checkbox-layout .mat-checkbox-inner-container {
    margin: 0 !important;
    width: 18px !important;
    height: 18px !important;
  }
  
  ::ng-deep .page-rating-menu + div .mat-expansion-panel:nth-child(3) .mat-expansion-panel-body {
    padding-left: 30px;
    padding-right: 0;
    padding-bottom: 10px;
  }
  
  .su__star-emoticons {
    width: 60px !important;
    margin: 0 0 0 0 !important;
  }
  
  ::ng-deep .page-rating-menu + div .mat-expansion-panel:nth-child(3) .mat-checkbox-layout .mat-checkbox-inner-container .mat-checkbox-frame{
    border-width: 1px !important;
    border-radius: 3px !important;
  }
  .su__align_thumbs{
    margin-left: 28px;
    margin-right: 6px;
    font-family: 'Montserrat';
    font-size: 14px;
    font-weight: 600;
  }
  .su__align_avgRating{
    margin-left: 22px;
  font-size: 14px;
  font-family: 'Montserrat';
  font-weight: 600;
  }
  .su_align_star_rating{
    margin-left: 22px;
    font-size: 14px;
    font-family: 'Montserrat';
    font-weight: 600;
  }
  .su__align_yes_vote{
  margin-right: 6px;
  font-size: 14px;
  font-family: 'Montserrat';
  font-weight: 600;
  }
  .su__star_align{
    margin-right: 4px;
  }
  ::ng-deep .su__filter_rating .mat-expansion-panel-body{
    display: flex;
    flex-wrap: wrap; 
    padding-left: 24px;
    padding-right: 21px;
  }
  .su__star_position{
    width: 60px;
    margin: 0 0 13px 0 !important;
    padding: 0;
  }
  .su_star{
    margin-right: -11px;
  }
  .su__url_align{
  padding-top: 25px;
  }
  ::ng-deep .su__matMenu .mat-radio-button{
    width:100%
  }
  ::ng-deep .su__matMenu .mat-radio-label-content{
    width:100%
  }
  ::ng-deep .feedback_radio .mat-radio-label{
    padding: 20px 0px 20px 0px;
    display: flex;
  }
  .info-align{
    position: absolute;
    right: 47px;
    top: 20px;
    z-index: 999;
  }
  .su__apply-btn{
    width: 74px;
      height: 28px;
      background: #FFFFFF 0% 0% no-repeat padding-box;
      border: 1px solid #53BFF7;
      border-radius: 4px;
      opacity: 1;
      margin-left: 72px;
      margin-top: 10.5px;
      cursor: pointer;
      margin-bottom: 10px;
  }
  .su__apply_style{
    letter-spacing: 0px;
      color: #53BFF7;
      opacity: 1;
      font-size: 14px;
      font-family: 'Montserrat';
      font-weight: 600;
      width: 41px;
      height: 18px;
      text-align: left;
      padding: 3px 16px 5px 16px;
  }
  ::ng-deep .su__matMenu  .mat-radio-label{
    padding: 0 15px;
    height: 55px;
    z-index: 991;
    position: relative;
  }
  ::ng-deep .su__matMenu .mat-expansion-indicator {
    position: absolute;
    right: 10px;
  }
  ::ng-deep .su__matMenu .mat-expansion-panel-header{
    padding: 0px 0px !important;
    position: relative;
  }
  ::ng-deep .su__matMenu .mat-expansion-panel-body .mat-radio-label {
    height: auto;
    padding: 0;
  }
  .su__no_filter_img{
    height:150px;
    width: 184px;
  }
  .su__no_filter{
    display: flex;
    justify-content: center;
  }
  .su__no_filter_align{
    margin-left: 240px;
    margin-top: 30px;
  }
  .su__no_filter_img_text{
    font-size: 20px;
      font-family: 'Montserrat';
      font-weight: 600;
      color:#707070;
      margin-left: 54px;
  }
  .su__no_filter_img_text2{
    font-size: 16px;
    font-family: 'Montserrat';
    color: #707070;
    margin-left:24px
  }
  .container-voting {
    width: 150px;
    display: flex;
  }   
  .su_input_align {
    position: unset !important;
    width: 300px !important;
    margin-right: 10px !important;
    background-color: #fff !important;
    color: #4a83c0 !important;
    padding: 0px 25px 0px 10px;
  }
  
  .su_button_align {
    position: absolute !important;
    margin-right: -6px !important
  }
  .su_search_label {
    display: flex;
    position: relative;
    width: 300px
  }
  .su__advertsiment_sorting_svg  path.b{
  fill :#53BFF7 !important;
  }
  .su__gpt_icons_container{
    padding: 7px 11px 6px;
    border-radius: 2px;
    background: transparent linear-gradient(92deg, #f2f5f7 0%, #f2f5f7 100%) 0% 0% no-repeat padding-box;
    
  }
  .main-panel[data-background-color="black"] .su__gpt_icons_container{
    background: none !important;
  }
  .gpt-feedback {
    height: 56px;
  }
  .su__gpt_icons_container {
    display: flex;
    min-width: 147px;
    justify-content: space-between;
    padding: 0 11px 0;
    height: 30px;
    line-height: normal;
    align-items: center;
    span {
      position: relative;
      &:after {
          content: "";
          width: 22px;
          height: 22px;
          position: absolute;
          left: 0px;
          top: 2px;
          border-radius: 100%;
      }
      font-size: 18px;
      font-weight: 500;
        svg {
          z-index: 1;
          position: relative;
          width: 22px;
          height: 22px;
          padding: 5px;
        }
      &.total-dislikes {
        &:after {
          background: #f3eee2;
        }
        svg {
          transform: rotate(180deg);
        }
      }
      &.total-likes {
        &:after {
          background: #dfeef8;
        }
      }
    }
  }
  .su__gpt_filters_container{
      width: 244px;
      height: 167px;
      display: flex;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border-radius: 5px;
      position: absolute;
      z-index: 10;
      background: #fff;
      right: 0;
      top: 48px  
  }
  .su__gpt_radio_group{
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-left: 10px;
  }.su__gptFilters_modal {
    position: relative;
  }
  
  .su__comment-btn-response {
    border: none;
    right: auto;
    padding:0;
    height: 0;
    width: 0;
    position: absolute;
    top: 14px;
    display: inline-block;
    height: 25px;
    background: none;
  }
  .gpt-feedback .su__comment-btn-response .su__filter_svg_align path.highlight {
    fill: #56C5FE;
  }
  .su__comment-btn {
    border: none;
    padding: 0;
    margin: 0;
    height: 0;
  }
  
  .su__padding-zero {
    padding: 0px;
  }
  
  .feedback-tags {
    margin: 0 0 8px 0;
    padding: 0;
    list-style: none;
    display: flex;
    li {
      background: #D9F2FF;
      font-size: 13px;
      color: #707070;
      font-weight: 300;
      border-radius: 2px;
      margin-right: 5px;
      line-height: normal;
      padding: 3px 6px;
    }
  }
  
  // Modal with Scrollbar css Starts here
  /* Scrollbar Styling */
  ::ng-deep {
    .custom-mat-menu {
      position: relative;
      min-height: auto;
      max-height: 262px;
      max-width: 353px;
      box-shadow: 0px 3px 20px #00000063;
      .mat-menu-content {
  
        padding: 0;
  
        .menu-content {
          background-color: #fff;
          font-family: "Montserrat";
          color: #616161;
          font-size: 13px;
          font-weight: 400;
          word-break: break-word;
          padding: 8px 10px;
          line-height: 20px;
        }
      }
  
      .close-menu {
  
        position: absolute;
        left: 15px;
        content: ' ';
        height: 33px;
        width: 2px;
        background-color: #333;
  
        &::before {
          transform: rotate(45deg);
        }
  
        &::after {
          transform: rotate(-45deg);
        }
      }
  
      .highlight {
        color: #59BDFD;
        font-weight: 600;
        background: transparent !important;
        padding: unset;
      }
    }
  }
  .su_search_label input::-webkit-search-cancel-button {
    display: none !important;
  }
  
  ::ng-deep .custom-mat-menu::-webkit-scrollbar-track
  {
      border-radius: 10px;
    left:-100px;
      background-color: transparent;
  }
  
  ::ng-deep .custom-mat-menu::-webkit-scrollbar
  {
      width: 5px;
    left:-100px;
      background-color: transparent;
  }
  
  ::ng-deep .custom-mat-menu::-webkit-scrollbar-thumb
  {
      border-radius: 20px;
    left:-100px;
      background-color: #E0E8EC;
  }
  
  //Leadership Dashboard css
  .ad_buisness-on-page-label-adoption-dashboard {
    display: flex;
    justify-content: center;
    align-items: center;
    /* padding-left: 15px; */
  
  } 
  
  .ad_buisness-on-page-label-inner-adoption-dashboard {
    font-weight: 500;
    width: 10rem;
    display: inline-block;
    border: 2px solid #ACB1C0;
    color: #FFFFFF;
    background-color: #ACB1C0;
    border-radius: 4px;
    text-align: center;
  }
  
  .ad_buisness-on-page-line-adoption-dashboard {
    width: 94.5%;
    height: 2px;
    background-image: linear-gradient(to right, #e0efff 57%, rgb(243, 248, 249) 0%);
    background-position: top;
    background-size: 12px 3px;
    background-repeat: repeat-x;
  }
  
  #buisness-label-adoption-dashboard {
    float: none !important;
  }

  .dropdown-adoption {
    padding-right: 5px;
    padding-left: 10px;
    margin-right: 10px;
    width: 180px;
    background-color: white;
  }

  .adoption-dashboard .dropdown-adoption {
    background-color: white;
  }

  .main-panel[data-background-color="black"]
    .adoption-dashboard .dropdown-adoption {
      background-color: rgb(18, 18, 48);
  }

  .adoption-email-adoption-dashboard {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    flex-direction: row-reverse;
  }
  
  .adoption-dashboard .mat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background-color: #e0e8ec;
  }
  
  .adoption-dashboard .panel-title {
    flex-grow: 1;
  }
  
  .adoption-dashboard .panel-description {
    display: flex;
    align-items: center;
  }
  
  .adoption-dashboard .mat-expansion-panel-header-title {
    justify-content: flex-start;
    align-items: center;
    // color: #42435D !important;
    font: normal normal normal 14px/18px Montserrat;
    font-weight: 500;
  }
  
  .adoption-dashboard {
    .mat-accordian,
    .mat-expansion-panel {
      /* transform: translateY(-25px); */
      margin: -25px 0px 0px 0px !important;
      font: normal normal normal 12px/21px Montserrat;
    }
  }
  
  .adoption-dashboard .contentFilter-adoption-dashboard {padding-right: 5px; padding-left: 10px;margin-right: 10px;width: 180px;background-color: white;}
  
  .contentFilter-adoption-dashboard {
     background-color: white;
  }
  
  .main-panel[data-background-color="black"]
    .adoption-dashboard .mat-header {
      background-color: #1f1e40 !important;
    }
  
  .main-panel[data-background-color="black"]
    .contentFilter-adoption-dashboard {
      background-color: rgb(18, 18, 48);
  }
  
  .no-docs-adoption {
      font-weight: 400;
      color: rgb(75, 75, 75);
  }
  
  ::ng-deep .analyticsV2 .ad-heading-fixed .mat-form-field-infix {
    width: 100% !important;
    cursor: pointer;
    border-top: none !important;
    margin-top: 15px !important;
    font-size: 14px;
    font-family: "Montserrat";
  }
  
  .Cost-Savings-email-adoption-dashboard {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    flex-direction: row-reverse;
  }
  
  .form-field {
    display: block !important; 
    border: none;
  }
  
  .custom-form-field input::-webkit-inner-spin-button,
  .custom-form-field input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  .custom-form-field {
    display: flex;
    padding: 28px 5px;
  }
  
  .custom-form-field label{
    margin-right: 10px;
    display: inline-block;
    font: normal normal 600 12px/22px Montserrat;
    color: #1B1E25;
    opacity: 0.7;
    width: 88px;
    height: 15px;
  }
  
  .highcharts-background{
    margin-bottom: 65px;
  }
  
  .cost-formatter {
    width: 62rem;
    height: 100%;
  }
  
  .currency-symbol,
  .at-symbol {
    width: 9px;
  height: 18px;
  text-align: left;
  font: normal normal medium 14px/22px Montserrat;
  letter-spacing: 0px;
  color: #7F8FA4;
  opacity: 1;
  }
  
  .input-container {
      display: flex;
    align-items: center;
      gap: 5px;
  }
  
  .underlined-wrapper {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #5F6166; 
    width: 65px;
    flex: 1;
  }
  
  .underlined-wrapper.invalid {
    border-bottom: 1px solid #F44336;
  }
  
  .underlined-wrapper.focused {
    border-bottom: 1px solid #59BDFD; 
  }
  
  .cost-container input {
    font: normal normal 500 14px Montserrat;
    border: none;
    width: 50px;
    padding: 0;
    margin: 0px 6px;
    color: #7F8FA4;
  }
  .feedback-filter-trigger, .custom-select-open {
    margin-left: 10px;
    cursor: pointer;
  }
  ::ng-deep .feedback-filter-trigger app-svg svg {
    scale: 1.2;
  }
  ::ng-deep .feedback-filter-trigger app-svg svg:hover rect {
    fill: #5cbafd !important;
  }
  ::ng-deep .feedback-filter-trigger app-svg svg:hover path {
    fill: #ffffff !important;
  }
  ::ng-deep .custom-select-open app-svg svg {
    rect {
      fill: transparent !important;
    }
    path {
      fill: #5cbafd !important;
    }
  }  
  
  ::ng-deep .table-header-right mat-select {
    color: transparent;
    height: 1px;
    width: 1px;
    visibility: hidden;
    right: 11rem;
    position: relative;
    top: 38px;
  }
  .table-header-right {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ::ng-deep .custom-select-panel .mat-option.mat-selected:not(.mat-option-disabled) .mat-pseudo-checkbox-checked,
  ::ng-deep .custom-select-panel .mat-option.mat-selected:not(.mat-option-disabled) .mat-pseudo-checkbox-indeterminate {
    background-color: #56C1FA !important;
  }

  ::ng-deep .custom-select-panel .mat-pseudo-checkbox-checked::after {
    color: white;
    top: 3.4px;
    left: 2px;
  }
  ::ng-deep .mat-select-panel.custom-select-panel {
    border-radius: 0;
    background: #FFFFFF;
    box-shadow: 0px 3px 6px #00000029;
    min-width: 244px !important;
  }
  ::ng-deep .cdk-overlay-pane:has(.custom-select-panel) {
    transform: translate(-50px, -12px) !important;
  }
  ::ng-deep .custom-select-panel .mat-option.mat-selected:not(.mat-option-disabled) {
    color: #56C1FA !important;
  }
  ::ng-deep .mat-select-panel .mat-option.mat-selected .mat-pseudo-checkbox {  
    border-color: #56C1FA !important;
  }
  ::ng-deep .custom-select-panel .mat-option-text {  
    color: #373D3F;
  }
  ::ng-deep .mat-select-panel .mat-option .mat-pseudo-checkbox {  
    border-color: #DBDBDC !important;
  }