import { Component, OnInit, <PERSON>Child, ElementRef } from "@angular/core";
import moment from 'moment/moment';
import { ToastyService, ToastyConfig, ToastOptions } from 'ng2-toasty';
import * as momentZone from 'moment-timezone';
import { DaterangePickerComponent } from 'ng2-daterangepicker';
// import { Variables } from '../../../../../variables/contants';
import { Variables } from "app/variables/contants";
import { AnalyticV2Service } from "app/services/analytics-v2.service";
import { AhAnalyticsService } from "app/services/ah-analytics.service";
import { TimezoneService } from "app/services/timezone.service";
import { MatSelect} from '@angular/material/select';
import { ContentSourceService } from "app/services/contentSource.service";
import { AgentHelperService } from "app/services/agent-helper.service";

@Component({
    selector: "ah-analytics",
    templateUrl: "agent-helper-analytics.html",
    styleUrls: ["agent-helper-analytics.scss"],
    providers: [ToastyService, ToastyConfig, AnalyticV2Service, AhAnalyticsService, ContentSourceService, AgentHelperService],
})
export class AHAnalyticsComponent implements OnInit {
    @ViewChild('caseSearchInput', { static: false }) caseSearchInput: ElementRef;
    @ViewChild('synSelect') synSelect : MatSelect;
    private rowIDOpen = "";
    private previousGenerativeContent: String = "";
    private caseIdHeaderSearchBar: boolean = false;
    private agentFilter = [];
    private featureFilter = [];
    private agentNameFilter;
    private featureFilterHandler;
    private selectedUID;
    private rowExpanded: boolean = false;
    private daterangepickerOptions: any = {};
    dateRange: any;
    private range_days = 1;
    @ViewChild(DaterangePickerComponent)
    private picker: DaterangePickerComponent;
    private range: any = {
        "from": '2017-01-01',
        "to": (new Date()).toISOString().split("T")[0]
    };

    private paginationCount;
    private pageNumber:any = 1;
    private expandedRowLoader: boolean = false;
    private expandedRowError: boolean = false;

    private rowFeedbackData:any = []; 
    private rowFeedbackFilterData:any = [];
    private agentNames:any = [];
    private features:any = [];
    private agentFilterWorking:boolean = false;
    searchClientChoice: any;
    
    userTimeZone: any = 'UTC';
    today = new Date()
    utcToday: any;
    private isLoading: Boolean = true;
    private noDataToShow: Boolean = false;
    private allGraphLoaded = false;
    private totalFeedbackSortPattern: String = "";
    private feedbackData:any =[];
    private totalFeedbackData:any = {}; 
    private totalFeedbacks;
    private noUID: Boolean = false;
    agentHelperAnalytics: any  = {
        "tabName" : "ah-analytics",
        "Reports" : [
            "Agent Helper Analytics"
        ]
    };
    
    productFilterOptions=[];

    constructor(private toastyService: ToastyService,  private analyticsV2Service: AnalyticV2Service, private ahAnalyticsService:AhAnalyticsService, private TimezoneService: TimezoneService,private contentSourceService: ContentSourceService, private agentHelperService: AgentHelperService) {
        TimezoneService.getUserTimeZone().subscribe((data)=>{
            this.today = new Date()
            this.utcToday = new Date(this.today.getUTCFullYear(), this.today.getUTCMonth(), this.today.getUTCDate(), this.today.getUTCHours(), this.today.getUTCMinutes(), this.today.getUTCSeconds(), this.today.getUTCMilliseconds());
            this.userTimeZone = data
            this.utcToday = momentZone.utc(this.today).tz(this.userTimeZone)
            this.daterangepickerOptions = {
                locale: { format: "YYYY-MM-DD" },
                alwaysShowCalendars: false,
                autoApply: true,
                startDate: moment(localStorage.startDate),
                endDate: moment(localStorage.endDate),
                minDate: moment(this.utcToday).subtract(12, 'month').startOf('month'),
                maxDate: moment(this.utcToday),
                ranges: {
                    'Today': [moment(this.utcToday), moment(this.utcToday)],
                    'Yesterday': [moment(this.utcToday).subtract(1, 'days'), moment(this.utcToday).subtract(1, 'days')],
                    'Last 7 Days': [moment(this.utcToday).subtract(6, 'days'), moment(this.utcToday)],
                    'Last 30 Days': [moment(this.utcToday).subtract(29, 'days'), moment(this.utcToday)],
                    'This Month': [moment(this.utcToday).startOf('month'), moment(this.utcToday).endOf('month')],
                    'Last Month': [moment(this.utcToday).subtract(1, 'month').startOf('month'), moment(this.utcToday).subtract(1, 'month').endOf('month')]
                },
                get from() { return this.startDate.format("YYYY-MM-DD"); },
                get to() { return this.endDate.format("YYYY-MM-DD"); },
                set from(value) { this.startDate = moment(value); },
                set to(value) { this.endDate = moment(value); }
            };
            this.range = this.daterangepickerOptions;
        });
    }
    ngOnInit() {        
        this.getUIDs();
    }

    getFeedbacks(toSort){
        this.caseIdHeaderSearchBar = false;
        if(!toSort) this.totalFeedbackSortPattern = "";
        this.isLoading = true; 
        this.noDataToShow = false;
        let data: any = {
            uid: this.selectedUID,
            from: this.range.from,
            to: this.range.to,
            limit: 10,
            offset: this.pageNumber,
        };
    
        if (toSort) {
            data.sortingField = "feedbackCount";
            data.sortType = this.totalFeedbackSortPattern;
        }
        this.ahAnalyticsService.getFeedbackData(data).then((result) => {
            this.isLoading = false;
            this.feedbackDataFormat(result);
        }).catch((error) => {
            this.isLoading = false;
            this.noDataToShow = true;
        });
    }
    

    totalFeedbackSortHandler(flag){ 
        // flag ? "desc" :"asc"
        if((flag && this.totalFeedbackSortPattern === "desc") || (!flag && this.totalFeedbackSortPattern === "asc")) 
                return;
        this.totalFeedbackSortPattern = flag ? "desc" :"asc";
        this.getFeedbacks(true);
    }

    getUIDs() {
        Promise.all([this.getSearchClientsUID(), this.getAgentsList()])
            .then((results) => {
                const [allUID, agentHelperUID] = results;
    
                // Now finding the common in both 
                let products = [];
                allUID.forEach((searchClient) => {
                  const index = agentHelperUID.findIndex((ah) => ah.uid === searchClient.uid);
                  if (index !== -1) {
                    if (agentHelperUID[index].type === 'Salesforce service console') {
                      products.push({
                        label: searchClient.name,
                        uid: searchClient.uid,
                      });
                    } else {
                      products.push({
                        label: agentHelperUID[index].agent_helper_name,
                        uid: searchClient.uid,
                    });
                    }
                  }
                });
    
                this.productFilterOptions = products;
                if(!this.productFilterOptions || this.productFilterOptions.length == 0){     
                    this.noUID = true;
                }else{
                    this.noUID = false;
                    let data = localStorage.getItem("AH_SU_selectedUID");
                    if(data){
                        this.selectedUID = data
                    }else{
                        if (this.productFilterOptions.length > 0) {
                            this.selectedUID = this.productFilterOptions[0].uid;
                        }
                    }
                    // this.getFeedbacks(false);
                }
            })
            .catch((error) => {
                this.isLoading = false; this.noDataToShow = true;
            });
    }
    
    getAgentsList() {
      return Promise.all([
        this.agentHelperService.getAgentList(), // Responsible for fetching list of SF agent helpers
        this.agentHelperService.getAgentHelperList() // Responsible for fetching list of other platform agent helpers (Zendesk, NetSuite)
      ])
        .then((results) => {
          const [sfAgentHelpers, otherPlatformAgentHelpers] = results;

          return [...sfAgentHelpers.data, ...otherPlatformAgentHelpers.data];
        }).catch(() => {
          return [];
        });
    }
    
    getSearchClientsUID() {
        return this.contentSourceService.getContentSources().then((result) => result.message);
    }
    
    ngOnDestroy(){
        this.analyticsV2Service.getAnalyticsStatus(true);
    }

    padWithZero(num) {
        return num < 10 ? '0' + num : num;
    }

    convertDate(inputDateStr, flag) {
        const dateObj = new Date(inputDateStr);
    
        // Extract individual components
        const day = dateObj.getUTCDate();
        const month = dateObj.toLocaleString('default', { month: 'short', timeZone: 'UTC' });
        const year = dateObj.getUTCFullYear();
        const hours = this.padWithZero(dateObj.getUTCHours());
        const minutes = this.padWithZero(dateObj.getUTCMinutes());
        const seconds = this.padWithZero(dateObj.getUTCSeconds());
    
        // Assemble the formatted string
        if (flag) {
            return `${day} ${month} ${year}  ${hours}:${minutes}:${seconds}`;
        } else {
            return `${day} ${month} ${year} | ${hours}:${minutes}:${seconds}`;
        }
    }
    


    formatingIncomingData(incomingData){
        const formattedCasesData = incomingData
            .map((item) => {
                return {
                    ...item,
                    case_created_date: this.convertDate(item.case_created_date, false),
                    last_feedback_date: this.convertDate(item.last_feedback_date, false),
                };
            });
        return formattedCasesData;
    }
    
    getPagination(){
        if(this.feedbackData.length <=10) this.paginationCount = 1;
        else{
            let page=String(this.feedbackData.length/10);
            page = page.split(".")[0];
            this.paginationCount = parseInt(page,10)+1;
        }
    }

    handlePagination(index){
        this.pageNumber = index;
        this.getFeedbacks(false);
        this.expandedRowHandler(this.rowIDOpen,"");
    }


    feedbackDataFormat(result){
        if(result.data.total == 0 )
            this.noDataToShow = true; 
        else this.noDataToShow = false;
        this.totalFeedbacks = result.data.total;
        this.feedbackData = this.formatingIncomingData(result.data.feedback);
        this.getPagination();
        this.totalFeedbackData = {
            totalThumbsUp: this.getCountInThousands(result.data.thumbs_up_count),
            totalThumbsDown:this.getCountInThousands(result.data.thumbs_down_count),
        }
    }

    getCountInThousands(number){
        if(number >= 1000){
            const count = String(number/1000).split(".")[0]+"k";
            return count;
        }else return number;
    }

    caseIdSearchHandler(){
        this.caseIdHeaderSearchBar = !this.caseIdHeaderSearchBar;
    }


    selectedDate(value: any, datepicker?: any) {
        this.range_days = Math.ceil((value.end - value.start) / 1000 / 60 / 60 / 24);
        if (this.range_days > Variables.dateRangeLimitDays) {
            var toastOptions: ToastOptions = {
                title: "Date Range Exceeded!",
                msg: `You cannot select more than ${Variables.dateRangeLimitDays} days.`,
                showClose: true,
                timeout: 5000,
                theme: 'default'
            };
            this.toastyService.error(toastOptions);
            this.picker.datePicker.setStartDate(moment(localStorage.startDate).format('YYYY-MM-DD'));
            this.picker.datePicker.setEndDate(moment(localStorage.endDate).format('YYYY-MM-DD'));
        } else {
            this.allGraphLoaded = false;
            if (value.interactive_search) {
                this.range.from = value.startDate;
                this.range.to = value.endDate;
            }
            else {
                this.range.from = value.start.format("YYYY-MM-DD");
                this.range.to = value.end.format("YYYY-MM-DD");
            }
            if (typeof (Storage) !== "undefined") {
                localStorage.startDate = this.range.from;
                localStorage.endDate = this.range.to;
            }
        }

       this.getFeedbacks(false);
    }

    getAgentNames(caseFeedbackData){
        let agents=new Set();
        this.agentNames=caseFeedbackData.map((item)=>{
            const newAgent = {
                agent_email:item.agent_email,
                agent_name:item.agent_name,
                isSelected:false,
            }
            if (!agents.has(newAgent.agent_email)) {
                agents.add(newAgent.agent_email);
                return newAgent;
            } else return null;
        }).filter((item)=> item!=null); 
    }

    getFeatureNames(caseFeedbackData){
        let featuresSet=new Set();
        this.features = caseFeedbackData.map((item)=>{
            const newFeature =  {
                feature_type:item.feature_type,
                feature_type_name:item.feature_type_name,
                isSelected:false,
            }
            if(!featuresSet.has(newFeature.feature_type)){
                featuresSet.add(newFeature.feature_type);
                return newFeature;
            } else return null;
        }).filter((item)=> item!=null);
    }

    rowFeedbackFormatData(feedbacks) {
        let newFeedback;
        function formatFeatureName(name){
            let newName = "";
            try{
                newName = name[0].toUpperCase();
                for(let i=1;i<name.length;i++){
                    if(name[i] != '-') newName += name[i];
                    else if(name[i] == "-"){
                        newName += " ";
                        newName += name[i+1].toUpperCase();
                        i = i+1;
                    }
                }
                return newName;
            }
            catch{
                return name;
            }
        }

        newFeedback = feedbacks.map((item)=>{
            return {
                ...item,
                feature_type_name:formatFeatureName(item.feature_type),
            }
        })

        return newFeedback;
    }

    async getCaseFeedback(caseId){
        let feedbacks;
        const data={
            uid:this.selectedUID,
            from: this.range.from,
            to: this.range.to,
            caseId:caseId
        }
        await this.ahAnalyticsService.getFeedbackDataByCaseId(data)
        .then((result)=> {
            feedbacks = result.data;
            feedbacks = this.rowFeedbackFormatData(feedbacks);
            this.expandedRowLoader = false;
        })
        .catch((error)=>{
            this.expandedRowError = true;
            this.expandedRowLoader = false;
            console.log("error in getting feedback for the case id");
        });
        return feedbacks;
    }


    formatingAhResponse(feedbackArray){

        function removeNode(array){
            const newArray = array.filter((item)=> item.actorType);
            return newArray;
        }

        const formatedFeedbackArray = feedbackArray.map((item)=>{
            const ahResponse = JSON.parse(item.ah_response);
            if(item.feature_type === "case-timeline"){
                return{
                    ...item,
                    ts:this.convertDate(item.ts,true),
                    ah_response: removeNode(ahResponse.activities),
                }
            }
            else{
                return{
                    ...item,
                    ts:this.convertDate(item.ts,true),
                    ah_response: ahResponse,
                }
            }
        });

        return formatedFeedbackArray;

    }

    async expandedRowHandler(rowID, caseId) {
        const popUp = document.getElementById("rowPopUpAHA");
        const tr = document.getElementById(`row${rowID}`);
        if (rowID === this.rowIDOpen) { //closing already open
            popUp.style.display = "none";
            tr.style.position = "static";
            const oldTd=document.getElementById(this.rowIDOpen);
            oldTd.classList.remove("css-rotate-180Degree");
            this.rowIDOpen = ""; 
        }else{
            this.expandedRowLoader = true;
            const topValue = 372 + rowID * 60;
            popUp.style.display = "block";
            popUp.style.top = `${topValue}px`;
            const td=document.getElementById(rowID);
            if(td) td.classList.add("css-rotate-180Degree");
            else console.log("no children of tr");
            const feedbacks = await this.getCaseFeedback(caseId);
            let feedbacksArray = this.formatingAhResponse(feedbacks);
            this.rowFeedbackData=feedbacksArray; this.rowFeedbackFilterData=feedbacksArray;
            tr.style.position = "relative";
            this.getAgentNames(feedbacksArray);
            this.getFeatureNames(feedbacksArray);
            if (rowID != this.rowIDOpen && this.rowIDOpen) { //closing the already open , and opening the new one
                const oldTr = document.getElementById(`row${this.rowIDOpen}`);
                oldTr.style.position = "static";
                const oldTd=document.getElementById(this.rowIDOpen);
                oldTd.classList.remove("css-rotate-180Degree");
            }
            this.expandedRowLoader = false;
            this.rowIDOpen = rowID;
        }
        this.expandedRowFilterReset(); //reset filters if new row opened 
    }

    isSelectedCheck(val,loc){
        if(loc){
            const index=this.agentNames.findIndex((item)=>item.agent_email === val);
            if(index === -1) return false;
            return this.agentNames[index].isSelected;
        }
        else{
            const index=this.features.findIndex((item)=>item.feature_type === val);
            if(index === -1) return false;
            return this.features[index].isSelected;
        }
        
    }
    
    //func to filtering the data
    filterFeedbackRowData(loc){ //filter for both and then find intersection
        let agentNameFilter = this.rowFeedbackData;

        agentNameFilter =  agentNameFilter.filter((item)=> this.isSelectedCheck(item.agent_email,1));
        if(agentNameFilter.length == 0) agentNameFilter=this.rowFeedbackData;

        let featureFilter=this.rowFeedbackData;
    
        if(this.agentFilterWorking) featureFilter = this.rowFeedbackFilterData;
        featureFilter = featureFilter.filter((item)=> this.isSelectedCheck(item.feature_type,0));

        if(featureFilter.length == 0) featureFilter = this.rowFeedbackData;

        this.rowFeedbackFilterData = agentNameFilter.filter(element => featureFilter.includes(element));
    }

    agentNameFilterHandler(agentName){
        const newAgentNames=this.agentNames.map((item)=>{
            if(item.agent_name === agentName){
                return {agent_name:agentName,agent_email:item.agent_email,isSelected:!item.isSelected}
            } else return item
        })
        this.agentNames=newAgentNames;
        this.agentNameFilter = !this.agentNameFilter;

       this.filterFeedbackRowData(0);
    }


    featureFilterHandlerFunc(featureName){
       const index=this.features.findIndex((item)=>item.feature_type === featureName);
       if(index !==-1){
            const newFeatures=this.features.map((item)=>{
                if(item.feature_type === featureName){
                    return {
                        ...item,
                        feature_type:featureName,
                        isSelected:!item.isSelected,
                    }
                }else return item
            })
            this.features=newFeatures;
       }
       this.featureFilterHandler = !this.featureFilterHandler;

       this.filterFeedbackRowData(1);
    }

    toggleFilter(filterType) {    
        if (filterType === "agent") {
            this.agentNameFilter = !this.agentNameFilter;
            this.featureFilterHandler = false;
        } else if (filterType === "feature") {
            this.agentNameFilter = false;
            this.featureFilterHandler = !this.featureFilterHandler;
        }
    }

    productFilterHandler(){
        localStorage.setItem("AH_SU_selectedUID", this.selectedUID);
        this.getFeedbacks(false);
    }

    expandedRowFilterReset(){
        const resetFeatures=this.features.map((item)=>{
            return {...item,isSelected:false}
        })
        this.features=resetFeatures;

        const resetAgentNames=this.agentNames.map((item)=>{
            return {agent_name:item.agent_name,agent_email:item.agent_email,isSelected:false}
        })
        this.agentNames=resetAgentNames;

        this.agentNameFilter = false;
        this.featureFilterHandler = false;
        this.agentFilterWorking = false; 
    }

    //func to search for the case ID
    searchCaseTitle(targetValue,loc){
        this.isLoading = true; this.noDataToShow = false;
        let data: any = {
            uid: this.selectedUID,
            from: this.range.from,
            to: this.range.to,
            limit:10,
            offset:this.pageNumber,
        };
        if(loc) data.caseId = targetValue
        this.ahAnalyticsService.getFeedbackData(data)
        .then((result) => {
            this.isLoading = false;
            this.noDataToShow = false;
            this.feedbackDataFormat(result);
        }).catch((error) => {
            this.isLoading = false;
            this.noDataToShow = true;
        });
        if(!loc){
            this.caseIdSearchHandler();
        }
    }

    generativeContendHandler(index){
        // if(this.previousGenerativeContent){
        //     const previous = document.getElementById(`generativeContent${this.previousGenerativeContent}`);
        //     previous.classList.toggle("visibiltyClassAhAnalytics");
        // }
        const generativeContendHolder = document.getElementById(`generativeContent${index}`);
        generativeContendHolder.classList.toggle("visibiltyClassAhAnalytics");
        this.previousGenerativeContent = index;
    }
}
