import { Injectable} from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { HttpClient } from '@angular/common/http';
import { Variables } from '../variables/contants'
import { Router } from '@angular/router';
import { TokenInterceptorService } from './csrfToken.service';
import { saveAs } from 'file-saver';

@Injectable()
export class AhAnalyticsService{
    
    constructor(private http: HttpClient, private router: Router, private interceptor: TokenInterceptorService ) { }

    private handleError = (error: any): Promise<any> => {
        if (error.status === 401) {
            this.router.navigate(['/'])
        }
        console.error('An error occurred', error);
        return Promise.reject(error || error.message);
    }


    exportQueries(range: any, url: any, name: string, method?: string) {
        // Xhr creates new context so we need to create reference to this
        // let self = this;
        {
            if (url.indexOf("?") > -1)
                url += "&";
            else
                url += "?";
            url = url + "_csrf=" + localStorage.getItem("_csrf");

        }
        // Status flag used in the template.

        // Create the Xhr request object
        let xhr = new XMLHttpRequest();
        var params = (JSON.stringify(range));
        if (method === "GET")
            xhr.open('GET', url, true);
        else
            xhr.open('POST', url, true);
        xhr.responseType = 'blob';
        xhr.setRequestHeader("Content-type", "application/json");
        xhr.setRequestHeader("export", "1");
        xhr.setRequestHeader("selectedTabs", this.interceptor.getSelectedTabs() ? this.interceptor.getSelectedTabs().selectedTabs:[]);
        // Xhr callback when we get a result back
        // We are not using arrow function because we need the 'this' context
        xhr.onreadystatechange = function () {

            // We use setTimeout to trigger change detection in Zones
            // setTimeout( () => { self.pending = false; }, 0);

            // If we get an HTTP status OK (200), save the file using fileSaver
            if (xhr.readyState === 4 && xhr.status === 200) {
                // console.log(blob);
                var ext = '.xlsx';
                if (this.response.type && this.response.type == 'text/csv') {
                    ext = '.csv';
                }
                var blob = new Blob([this.response], { type: 'application/octet-stream' });
                saveAs(blob, name + ext);
            }
        };

        // Start the Ajax request
        xhr.send(params);
    }
    


    getFeedbackData(data){
        let headers = new HttpHeaders();
        let url = Variables.baseHref + `/analytics/agent-helper/response-feedback`;
        const body = {
            uid:data.uid,
            from: data.from,
            to: data.to,
            limit:data.limit,
            offset:data.offset,
            sortingField:data.sortingField,
            sortType:data.sortType,
            caseId: data.caseId,
            featureTypes: data.featureTypes
        };
        return this.http.post<any>(url, body, { headers: headers })
            .toPromise()
            .then(res => res)
            .catch(this.handleError)
    }

    getFeedbackDataByCaseId(data){
        let headers = new HttpHeaders();
        let url = Variables.baseHref + `/analytics/agent-helper/response-feedback-details`;
        const body = {
            uid:data.uid,
            from: data.from,
            to: data.to,
            caseId:data.caseId,
            featureTypes: data.featureTypes
        };
        return this.http.post<any>(url, body, { headers: headers })
            .toPromise()
            .then(res => res)
            .catch(this.handleError)

    }

    downloadAgentHelperAnalytics(payload, reportsDownloadValue:any, emailForReports:any, featureTypes:any){
        let headers = new HttpHeaders();
        var body = {
            from: payload.range.from,
            to: payload.range.to,
            csv: payload.csv,
            uid:payload.uid,
            sendToEmail: reportsDownloadValue,
            email: emailForReports,
            featureTypes: featureTypes
        }
        let url = Variables.baseHref + '/analytics/agent-helper/response-feedback-details-download';
        if (payload.csv && !reportsDownloadValue) {
            this.exportQueries(body, url, 'Agent helper Feedback');
            return Promise.resolve({ status: true, message: 'success' });
        }
        return this.http.post<any>(url, body, { headers: headers })
            .toPromise()
            .then(res => res)
            .catch(this.handleError)
    }

}
