import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Variables } from '../variables/contants';
import { Router } from '@angular/router';


@Injectable()
export class isService {

    constructor(private http: HttpClient, private router: Router) {

    }

    getIndexServiceConfiguration(): Promise<any> {
        let url =  Variables.baseHref + '/admin/indexService/getIndexingConfiguration';
        return this.http.get<any>(url)
            .toPromise()
            .then(res => res)
            .catch(this.handleError)
    }

    updateIndexingConfiguration(objToSend){
        let url = Variables.baseHref + '/admin/indexService/updateIndexingConfiguration';
        return this.http.post<any>(url,objToSend)
            .toPromise()
            .then(res => res)
            .catch(this.handleError)
    }

    resetIndexingConfiguration({config_key}) {
        let url = Variables.baseHref + '/admin/indexService/resetIndexingConfiguration';
        return this.http.post<any>(url, {config_key})
            .toPromise()
            .then(res => res)
            .catch(this.handleError)
    }

    toggleVectorIndexing() {
        let url = Variables.baseHref + '/admin/indexService/toggleVectorIndexing';
        return this.http.post<any>(url, {})
            .toPromise()
            .then(res => res)
            .catch(this.handleError);

    }

    refreshIndexes() {
        let url = Variables.baseHref + '/admin/indexService/refreshIndexes';
        return this.http.post<any>(url, {})
            .toPromise()
            .then(res => res)
            .catch(this.handleError);
    }

    private handleError = (error: any): Promise<any> => {
        if (error.status === 401) {
            this.router.navigate(['/'])
        }
        console.error('An error occurred', error);
        return Promise.reject(error || error.message);
    }
}

