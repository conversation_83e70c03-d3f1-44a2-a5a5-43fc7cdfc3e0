import { Routes, RouterModule } from '@angular/router';
import { DashboardComponent } from '../dashboard/components/dashboard';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivateChildRouteGuard } from './activate-child-route.guard';
import { CookieService } from 'app/services/cookie.service';
const appRoutes: Routes = [
    { path: '', loadChildren: () => import('../../pages/login/login.module').then(m => m.LoginModule) },
    { path: 'admin-login', loadChildren: () => import('../../pages/login/login.module').then(m => m.LoginModule)},
    {
        path: 'svg-gallery',
        loadChildren: () => import('../../shared/svgs/svg-gallery/svg-gallery.module').then(m => m.SVGGalleryModule),
        canActivate : [ActivateChildRouteGuard],
    },
    {
        path: 'signup',
        loadChildren: () => import('../../pages/signup/signup.module').then(m => m.SignupModule)
    },
    {
        path: '404',
        loadChildren: () => import('../../pages/404/404.module').then(m => m.NotFoundModule)
    },
    {
        path: 'forgot-password',
        loadChildren: () => import('../../pages/forgotPassword/forgotPassword.module').then(m => m.ForgotPasswordModule)
    },
    {
        path: 'dashboard', component: DashboardComponent,
        children: [
            { path: '', redirectTo: 'home', pathMatch: 'full' },
            {
                path: 'home',
                loadChildren: () => import('../../pages/home/<USER>').then(m => m.HomeModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'content-sources',
                loadChildren: () => import('../../pages/content-sources/content_sources.module').then(m => m.ContentSourcesModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'generate-search-client',
                loadChildren: () => import('../../pages/generate-search-client/generate-search-client.module').then(m => m.GenerateSearchClientModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'scconfig',
                loadChildren: () => import('../../pages/search-client-configs/search-client-configs.module').then(m => m.SearchClientConfigsModule),
                canActivate : [ActivateChildRouteGuard],
            },
            {
                path: 'search-tuning',
                loadChildren: () => import('../../pages/search-tuning/search_tuning.module').then(m => m.SearchTuningModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'notifications',
                loadChildren: () => import('../../pages/notifications/notifications.module').then(m => m.NotificationsModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'config',
                loadChildren: () => import('../../pages/configurations/configurations.module').then(m => m.ConfigurationsModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'analyticsconfig',
                loadChildren: () => import('../../pages/analytics-configurations/analytics-configurations.module').then(m => m.AnalyticsConfigurationsModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'home/:div',
                loadChildren: () => import('../../pages/home/<USER>').then(m => m.HomeModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'analytics-v2',
                loadChildren: () => import('../../pages/charts-d3/components/analytics-v2/analytics-v2.module').then(m => m.AnalyticsV2Module),
                canActivate: [ActivateChildRouteGuard]
            },{
               path: 'ah-analytics',
               loadChildren: () => import('../../pages/charts-d3/components/agent-helper-analytics/agent-helper-analytics.module').then(m => m.AgentHelperAnalyticsModule),
               canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'analytics-v2/:div',
                loadChildren: () => import('../../pages/charts-d3/components/analytics-v2/analytics-v2.module').then(m => m.AnalyticsV2Module),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'account',
                loadChildren: () => import('../../pages/account/account.module').then(m => m.AccountModule),
                canActivate: [ActivateChildRouteGuard]
            },{
                path: 'user-role-management',
                loadChildren: () => import('../../pages/user-role-management/user-role-management.module').then(m => m.UserRoleManagementModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'manage-synonyms',
                loadChildren: () => import('../../pages/synonym/synonym.module').then(m => m.SynonymModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'marketplace',
                loadChildren: () => import('../../pages/marketplace/marketplace.module').then(m => m.MarketplaceModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'apps',
                loadChildren: () => import('../../pages/addons/addons.module').then(m => m.AddonsModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'api',
                loadChildren: () => import('../../pages/oauthClients/oauthClients.module').then(m => m.OauthClientsModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'manage-stopwords',
                loadChildren: () => import('../../pages/stopwords/stopwords.module').then(m => m.StopwordsModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'security',
                loadChildren: () => import('../../pages/security/security.module').then(m => m.SecurityModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'knowledge-graph',
                loadChildren: () => import('../../pages/knowledge-graph/knowledgeGraph.module').then(m => m.KnowledgeGraphModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'agent-helper',
                loadChildren: () => import('../../pages/agent-helper/agentHelper.module').then(m => m.AgentHelperModule),
                canActivate: [ActivateChildRouteGuard]
            },  {
                path: 'content-duplicacy',
                loadChildren: () => import('../../pages/duplicacy-checker/duplicacyChecker.module').then(m => m.DuplicacyCheckerModule),
                canActivate: [ActivateChildRouteGuard]
            },  {
                path: 'csm',
                loadChildren: () => import('../../pages/CSM/CSM-dialog.module').then(m => m.CSMModule),
                canActivate: [ActivateChildRouteGuard]
            },  {
                path: 'recommendations',
                loadChildren: () => import('../../pages/recommendations/recommendations.module').then(m => m.RecommendationsModule),
                canActivate: [ActivateChildRouteGuard]
            },  {
                path: 'community-helper',
                loadChildren: () => import('../../pages/community-helper/community-helper.module').then(m => m.CommunityHelperModule),
                canActivate: [ActivateChildRouteGuard]
            },{
                path: 'accountDetails',
                loadChildren: () => import('../../pages/accountDetails/accountDetails.module').then(m => m.AccountDetailsModule),
                canActivate: [ActivateChildRouteGuard]
            },{
                path: 'crons',
                loadChildren: () => import('../../pages/crons/crons.module').then(m => m.CronsModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'workbench',
                loadChildren: () => import('../../pages/workbench/workbench.module').then(m => m.WorkbenchModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'is-config',
                loadChildren: () => import('../../pages/index-service-configurations/index-service-configurations.module').then(m => m.IndexServiceConfigurationsModule),
                canActivate: [ActivateChildRouteGuard]
            },{
                path: 'llm-integration',
                loadChildren: () => import('../../pages/llm-integration/llm-integration.module').then(m => m.LlmIntegrationModule),
                canActivate: [ActivateChildRouteGuard]
            }, {
                path: 'error-logs',
                loadChildren: () => import('../../pages/error-logs/error-logs.module').then(m => m.ErrorLogsModule),
                canActivate: [ActivateChildRouteGuard]
            }
            ,
            {
                path: 'llm-usage-insights',
                loadChildren: () => import('../../pages/llm-usage-insights/llm-usage-insights.module').then(m => m.LlmUsageInsightsModule),
                canActivate: [ActivateChildRouteGuard]
            },
            {
                path: 'merge-fields',
                loadChildren: () => import('../../pages/merge-fields/merge-fields.module').then(m => m.MergeFieldsModule),
                canActivate: [ActivateChildRouteGuard]
            },
            {
                path: 'content-annotations',
                loadChildren: () => import('../../pages/annotation/annotation.module').then(m => m.ContentAnnotationModule),
                canActivate: [ActivateChildRouteGuard]
            },      
            {
                path: 'ab-testing',
                loadChildren: () => import('../../pages/ab-testing/ab-testing.module').then(m => m.AbTestingModule),
                canActivate: [ActivateChildRouteGuard]
            }
        ]
    },
    {
        path: 'api-docs',
        loadChildren: () => import('../../pages/api-docs/api-docs.module').then(m => m.ApiDocsModule)
    },
    { path: '404', loadChildren: () => import('../../pages/404/404.module').then(m => m.NotFoundModule) },
    { path: '**', redirectTo: '/404' }
];

@NgModule({
    imports: [RouterModule.forRoot(appRoutes)],
    exports: [RouterModule],
    schemas: [ CUSTOM_ELEMENTS_SCHEMA ],
    providers: [CookieService, ActivateChildRouteGuard]
})
export class AppRoutingModule {
}
