# Enhanced App-SVG Component Usage

## Overview

The `app-svg` component has been enhanced to support additional attributes and events for **NEW SVGs only**. All existing SVGs remain unchanged and continue to work exactly as before.

Please check the `svg-gallery`to view all svgs at one place to manually look for duplicates. This is to avoid re-adding SVGs that already exist by hitting the below route :

`/svg-gallery`

## What's New

### ✅ Additional Input Properties (for NEW SVGs)
- `viewBox` - SVG viewBox attribute
- `preserveAspectRatio` - SVG preserveAspectRatio attribute  
- `style` - Inline styles
- `id` - Element ID
- `title` - SVG title for accessibility
- `role` - ARIA role
- `ariaLabel` - ARIA label
- `ariaHidden` - ARIA hidden state
- `tabindex` - Tab index for keyboard navigation
- `matTooltip` - Material tooltip text
- `matTooltipPosition` - Material tooltip position

### ✅ Additional Event Outputs (for NEW SVGs)
- `(svgMouseenter)` - Mouse enter event
- `(svgMouseleave)` - Mouse leave event
- `(svgMouseover)` - Mouse over event
- `(svgMouseout)` - Mouse out event
- `(svgMousedown)` - Mouse down event
- `(svgMouseup)` - Mouse up event
- `(svgFocus)` - Focus event
- `(svgBlur)` - Blur event
- `(svgKeydown)` - Key down event
- `(svgKeyup)` - Key up event
- `(svgKeypress)` - Key press event

## Usage Examples

### Basic NEW SVG Usage
```html
<app-svg 
  name="example-new-svg" 
  width="24" 
  height="24"
  viewBox="0 0 24 24"
  fill="blue">
</app-svg>
```

### NEW SVG with Events
```html
<app-svg 
  name="example-new-svg" 
  width="24" 
  height="24"
  class="interactive-icon"
  (svgClick)="handleClick($event)"
  (svgMouseenter)="onHover($event)"
  (svgMouseleave)="onLeave($event)">
</app-svg>
```

### NEW SVG with Accessibility
```html
<app-svg 
  name="example-new-svg" 
  width="24" 
  height="24"
  role="button"
  ariaLabel="Close dialog"
  tabindex="0"
  (svgClick)="closeDialog()"
  (svgKeydown)="handleKeydown($event)">
</app-svg>
```

### NEW SVG with Angular Directives
```html
<app-svg 
  *ngIf="showIcon"
  name="example-new-svg" 
  [width]="iconSize"
  [height]="iconSize"
  [class]="iconClass"
  [style]="iconStyle"
  (svgClick)="toggleAction()">
</app-svg>
```

## Adding a NEW SVG to the Component

When adding a new SVG to `app-svg.html`, use this template:

```html
<ng-container *ngIf="name === 'your-new-svg-name'">
  <svg xmlns="http://www.w3.org/2000/svg" 
       [attr.width]="width || null" 
       [attr.height]="height || null" 
       [attr.viewBox]="viewBox || 'your-default-viewbox'"
       [attr.preserveAspectRatio]="preserveAspectRatio || null"
       [attr.fill]="fill || 'currentColor'"
       [attr.id]="id || null"
       [attr.title]="title || null"
       [attr.role]="role || null"
       [attr.aria-label]="ariaLabel || null"
       [attr.aria-hidden]="ariaHidden || null"
       [attr.tabindex]="tabindex || null"
       [attr.style]="style || null"
       [class]="class || null"
       (click)="svgClick.emit($event)"
       (mouseenter)="svgMouseenter.emit($event)"
       (mouseleave)="svgMouseleave.emit($event)"
       (mouseover)="svgMouseover.emit($event)"
       (mouseout)="svgMouseout.emit($event)"
       (mousedown)="svgMousedown.emit($event)"
       (mouseup)="svgMouseup.emit($event)"
       (focus)="svgFocus.emit($event)"
       (blur)="svgBlur.emit($event)"
       (keydown)="svgKeydown.emit($event)"
       (keyup)="svgKeyup.emit($event)"
       (keypress)="svgKeypress.emit($event)">
    <!-- Your SVG content here -->
    <path d="..."/>
  </svg>
</ng-container>
```

## Important Notes

1. **Consistent event handling** - All SVGs now use direct event emission with `svgClick.emit($event)` for better consistency
2. **Event object passing** - All events now pass the actual event object (`$event`) for more detailed handling
3. **Enhanced attributes available** - New attributes and events are available for all SVGs, but existing SVGs continue to work as before
4. **Backward compatibility maintained** - All existing usage patterns continue to work exactly the same
5. **Optional attributes** - All new attributes are optional and use null coalescing (`|| null`)

## Benefits for NEW SVGs

- Full support for Angular directives (`*ngIf`, `*ngFor`, `[ngClass]`, etc.)
- Comprehensive event handling with proper event objects
- Better accessibility support
- Material tooltip integration (when Material module is imported)
- More flexible styling options
- Proper keyboard navigation support

## Migration

No migration needed! This is purely additive functionality for future SVGs.
