import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-svg',
    templateUrl: 'app-svg.html',
    styleUrls: ['app-svg.scss']
})

export class SVG {
    @Input() name: string = '';
    @Input() width: string = '';
    @Input() height: string = '';
    @Input() fill: string = '';
    @Input() class: string = '';
    @Output() svgClick = new EventEmitter<void>();
    constructor(){}
    handleClick() {
        this.svgClick.emit();
      }
}
