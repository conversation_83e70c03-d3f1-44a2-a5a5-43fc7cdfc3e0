import { Component, Input, Output, EventEmitter } from '@angular/core';
import { default as svgList } from './app-svg.json';

@Component({
    selector: 'app-svg',
    templateUrl: 'app-svg.html',
    styleUrls: ['app-svg.scss']
})

export class SVG {
    // Inputs
    @Input() name: string = '';
    @Input() width: string = '';
    @Input() height: string = '';
    @Input() fill: string = '';
    @Input() class: string = '';
    @Input() viewBox: string = '';
    @Input() preserveAspectRatio: string = '';
    @Input() style: string = '';
    @Input() id: string = '';
    @Input() title: string = '';
    @Input() role: string = '';
    @Input() ariaLabel: string = '';
    @Input() ariaHidden: string = '';
    @Input() tabindex: string = '';
    @Input() matTooltip: string = '';
    @Input() matTooltipPosition: string = '';

    // Outputs
    @Output() svgClick = new EventEmitter<void>();
    @Output() svgMouseenter = new EventEmitter<Event>();
    @Output() svgMouseleave = new EventEmitter<Event>();
    @Output() svgMouseover = new EventEmitter<Event>();
    @Output() svgMouseout = new EventEmitter<Event>();
    @Output() svgMousedown = new EventEmitter<Event>();
    @Output() svgMouseup = new EventEmitter<Event>();
    @Output() svgFocus = new EventEmitter<Event>();
    @Output() svgBlur = new EventEmitter<Event>();
    @Output() svgKeydown = new EventEmitter<Event>();
    @Output() svgKeyup = new EventEmitter<Event>();
    @Output() svgKeypress = new EventEmitter<Event>();

    SVGs = svgList;

    constructor(){}
}
