<div class="svg-gallery-container">
  <h1>SVG Gallery - All Available Icons</h1>
  <p class="description">This page displays all SVGs available in the app-svg component. Before you add a new one, take a look if same svg already exists:</p>
  
  <!-- Search functionality -->
  <div class="search-container">
    <input 
      type="text" 
      placeholder="Search SVG names..." 
      [(ngModel)]="searchTerm"
      class="search-input">
  </div>
  <!-- All SVGs in one section (for easy comparison) -->
  <div class="category-section">
    <h2 class="category-title">All SVGs (Alphabetical)</h2>
    <div class="svg-grid">
      <div
        *ngFor="let svgName of sortAlphabetically(svgNames)"
        class="svg-item"
        [class.hidden]="searchTerm && !svgName.toLowerCase().includes(searchTerm.toLowerCase())">
        
        <div class="svg-display">
          <app-svg 
            [name]="svgName" 
            [width]="getSvgSize().width" 
            [height]="getSvgSize().height"
            class="svg-icon">
          </app-svg>
        </div>
        
        <div class="svg-info">
          <div class="svg-name">{{ svgName }}</div>
          <div class="svg-usage">
            <code>&lt;app-svg name="{{ svgName }}"&gt;</code>
          </div>
        </div>
        
        <!-- Copy button -->
        <button 
          class="copy-btn" 
          (click)="copySvgUsage(svgName)"
          title="Copy usage code">
          📋
        </button>
      </div>
    </div>
  </div>

  <!-- Statistics -->
  <div class="stats-section">
    <h3>Statistics</h3>
    <p>Total SVGs: {{ svgNames.length }}</p>
  </div>
</div>
