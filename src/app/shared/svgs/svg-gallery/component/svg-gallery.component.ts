import { Component } from '@angular/core';
import { default as svgList } from '../../app-svg.json';

@Component({
  selector: 'app-svg-gallery',
  templateUrl: './svg-gallery.component.html',
  styleUrls: ['./svg-gallery.component.scss']
})
export class SvgGalleryComponent {
  searchTerm: string = '';
  previewSvg: string | null = null;
  showPreview: boolean = false;

  svgNames: string[] = Object.values(svgList);

  constructor() { }

  // Get SVG size based on name
  getSvgSize(): { width: string, height: string } {
    // Default size
    return { width: '24', height: '24' };
  }

  // Copy SVG usage code to clipboard
  copySvgUsage(svgName: string): void {
    const usageCode = `<app-svg name="${svgName}"></app-svg>`;

    if (navigator.clipboard) {
      navigator.clipboard.writeText(usageCode).then(() => {
        console.log('Copied to clipboard:', usageCode);
        // You could add a toast notification here
      }).catch(err => {
        console.error('Failed to copy to clipboard:', err);
      });
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = usageCode;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      console.log('Copied to clipboard (fallback):', usageCode);
    }
  }

  // Sort SVGs alphabetically
  sortAlphabetically(svgs: string[]): string[] {
    return svgs.slice().sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));
  }

  // Open preview modal
  openPreview(svgName: string): void {
    this.previewSvg = svgName;
    this.showPreview = true;
    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';
  }

  // Close preview modal
  closePreview(): void {
    this.showPreview = false;
    this.previewSvg = null;
    // Restore body scrolling
    document.body.style.overflow = 'auto';
  }

  // Handle escape key to close preview
  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape' && this.showPreview) {
      this.closePreview();
    }
  }
}