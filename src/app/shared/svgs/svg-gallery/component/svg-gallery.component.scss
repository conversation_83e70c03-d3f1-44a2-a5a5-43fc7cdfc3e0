.svg-gallery-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
  min-height: 100vh;
  background-color: #ffffff;
  transition: background-color 0.3s ease, color 0.3s ease;

  h1 {
    color: #333;
    margin-bottom: 10px;
    transition: color 0.3s ease;
  }

  .description {
    color: #666;
    margin-bottom: 20px;
    transition: color 0.3s ease;
  }

  // Dark mode styles
  &.dark-mode {
    background-color: #1a202c;
    color: #e2e8f0;

    h1 {
      color: #f7fafc;
    }

    .description {
      color: #a0aec0;
    }
  }
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .header-content {
    flex: 1;
  }
}

.main-dark-mode-toggle {
  background: #f8f9fa;
  border: 1px solid #ddd;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-left: 20px;

  &:hover {
    background-color: #e9ecef;
    border-color: #bbb;
    color: #333;
    transform: scale(1.05);
  }

  .dark-mode & {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;

    &:hover {
      background-color: #4a5568;
      border-color: #718096;
      color: #f7fafc;
    }
  }
}

.search-container {
  margin-bottom: 20px;

  .search-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    background-color: #ffffff;
    color: #333;
    transition: all 0.3s ease;

    &:focus {
      outline: none;
      border-color: #56C5FF;
      box-shadow: 0 0 0 2px rgba(86, 197, 255, 0.2);
    }

    &::placeholder {
      color: #999;
      transition: color 0.3s ease;
    }

    .dark-mode & {
      background-color: #2d3748;
      border-color: #4a5568;
      color: #e2e8f0;

      &::placeholder {
        color: #a0aec0;
      }

      &:focus {
        border-color: #56C5FF;
        box-shadow: 0 0 0 2px rgba(86, 197, 255, 0.3);
      }
    }
  }
}

.category-section {
  margin-bottom: 30px;

  .category-title {
    color: #333;
    padding-bottom: 8px;
    border-bottom: 2px solid #f48b00;
    margin-bottom: 15px;
    transition: color 0.3s ease;

    .dark-mode & {
      color: #f7fafc;
    }
  }
}

.svg-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.svg-item {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #ffffff;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-color: #ddd;

    .copy-btn {
      opacity: 1;
    }
  }

  &.hidden {
    display: none;
  }

  .dark-mode & {
    background-color: #2d3748;
    border-color: #4a5568;

    &:hover {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      border-color: #718096;
    }
  }
}

.svg-display {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  cursor: pointer;
  border-radius: 4px;
  background-color: #ffffff;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  app-svg {
    max-width: 100%;
    max-height: 70px;
    overflow: auto;
  }

  .dark-mode & {
    background-color: #4a5568;

    &:hover {
      background-color: #718096;
    }
  }
}

.svg-info {
  text-align: center;

  .svg-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
    word-break: break-word;
    transition: color 0.3s ease;

    .dark-mode & {
      color: #f7fafc;
    }
  }

  .svg-usage {
    background-color: #f5f5f5;
    padding: 5px;
    border-radius: 4px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: background-color 0.3s ease;

    code {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
      color: inherit;
    }

    .dark-mode & {
      background-color: #1a202c;
      color: #e2e8f0;
    }
  }
}

.copy-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  .dark-mode & {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;

    &:hover {
      background-color: #718096;
    }
  }
}

.stats-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  transition: background-color 0.3s ease;

  h3 {
    margin-top: 0;
    color: #333;
    transition: color 0.3s ease;

    .dark-mode & {
      color: #f7fafc;
    }
  }

  p {
    color: #666;
    transition: color 0.3s ease;

    .dark-mode & {
      color: #a0aec0;
    }
  }

  .dark-mode & {
    background-color: #2d3748;
  }
}

// Preview Modal Styles
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.preview-modal {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  min-width: 400px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;

  h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .bg-toggle-btn {
    background: none;
    border: 1px solid #ddd;
    font-size: 16px;
    cursor: pointer;
    color: #666;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e9ecef;
      border-color: #bbb;
      color: #333;
    }
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e9ecef;
      color: #333;
    }
  }
}

.preview-content {
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background-color: white;
  transition: background-color 0.3s ease;

  &.dark-background {
    background-color: #2d3748;
  }

  .preview-svg {
    max-width: 100%;
    max-height: 300px;
  }
}

.preview-footer {
  padding: 20px 24px;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .preview-usage {
    flex: 1;

    strong {
      color: #333;
      margin-right: 8px;
    }

    code {
      background-color: #e9ecef;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
    }
  }

  .copy-preview-btn {
    background-color: #f48b00;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #e67e00;
    }
  }
}

// Animations
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;

    .main-dark-mode-toggle {
      align-self: flex-end;
      margin-left: 0;
    }
  }

  .preview-modal {
    min-width: 90vw;
    margin: 20px;
  }

  .preview-content {
    padding: 20px;
  }

  .preview-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .copy-preview-btn {
      width: 100%;
      justify-content: center;
    }
  }
}
