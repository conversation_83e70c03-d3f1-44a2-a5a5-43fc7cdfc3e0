.svg-gallery-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
  
  h1 {
    color: #333;
    margin-bottom: 10px;
  }
  
  .description {
    color: #666;
    margin-bottom: 20px;
  }
}

.search-container {
  margin-bottom: 20px;
  
  .search-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #56C5FF;
      box-shadow: 0 0 0 2px rgba(86, 197, 255, 0.2);
    }
  }
}

.category-section {
  margin-bottom: 30px;
  
  .category-title {
    color: #333;
    padding-bottom: 8px;
    border-bottom: 2px solid #f48b00;
    margin-bottom: 15px;
  }
}

.svg-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.svg-item {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-color: #ddd;
    
    .copy-btn {
      opacity: 1;
    }
  }
  
  &.hidden {
    display: none;
  }
}

.svg-display {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  
  app-svg {
    max-width: 100%;
    max-height: 70px;
    overflow: auto;
  }
}

.svg-info {
  text-align: center;
  
  .svg-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
    word-break: break-word;
  }
  
  .svg-usage {
    background-color: #f5f5f5;
    padding: 5px;
    border-radius: 4px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    
    code {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
  }
}

.copy-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  &:hover {
    background-color: #f5f5f5;
  }
}

.stats-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    color: #333;
  }
  
  .category-stat {
    margin: 5px 0;
    color: #666;
  }
}
