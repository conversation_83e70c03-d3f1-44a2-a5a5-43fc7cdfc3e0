import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from "../../../layouts/base/shared.module";
import { SvgGalleryComponent } from './component/svg-gallery.component';
import { SVGRoutingModule } from './router/svg-gallery.routing';


@NgModule({
    imports: [
        SVGRoutingModule,
        CommonModule,
        SharedModule,
    ],
    declarations: [
        SvgGalleryComponent
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SVGGalleryModule { }
