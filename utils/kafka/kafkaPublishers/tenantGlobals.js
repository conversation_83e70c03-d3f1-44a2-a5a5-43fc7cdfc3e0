const kafka = require("../kafka-lib");
var config = require("config");
const {
    errorlogger: { error }, fetchIndexingConfig
} = require("../../commonFunctions");

/**
 * tenantGlobals - publishes tenantGlobals kafka topic
 * @param {string} tenantId - tanant ID
 * @param {object} incoming - incoming data to publish
 */
const tenantGlobals = async (tenantId, incoming = {}) => {
    try {
        const data = { tenantId, globals : {  ...incoming } };

        /* populate gpt with existing data if does not exist */
        if (!incoming.hasOwnProperty("gpt")) {
            const sql = "SELECT gpt FROM tenant_globals";
            const gpt = await new Promise((s, x) => {
                connection[tenantId].execute.query(sql, [], (err, rows) => {
                    if (err || !rows || !rows.length) {
                        return x(err || "no data found in tenant_globals");
                    }
                    s(rows[0].gpt);
                });
            });
            data.globals.gpt = JSON.parse(gpt);
        }

        /* populate exact match with existing data if does not exist */
        if (!incoming.hasOwnProperty("exactMatch")) {
            const sql = `SELECT * FROM synonym_settings`;
            const exactMatch = await new Promise((s, x) => {
                connection[tenantId].execute.query(sql, function (err, rows) {
                    if (err || !rows || !rows.length) {
                        return x(err || "no data found in synonym_settings");
                    }
                    s(rows[0].exact_match == 1);
                });
            });
            data.globals.exactMatch = exactMatch;
        }
        /* populate multilingual with existing data if does not exist */
        if (!incoming.hasOwnProperty("enableMultilingual")) {
            const enableMultilingual = await new Promise((resolve, reject) => {
                try {
                    fetchIndexingConfig({ tenantId }, (response) => {
                        if (response.isError) {
                            reject(false); // error while fetching multilingual configuration so setting the flag to false to avoid vectorization.
                        }
                        if (
                            response.vectorConfiguration &&
                            response.vectorConfiguration.hasOwnProperty(
                                "enableMultilingual"
                            )
                        ) {
                            resolve(
                                response.vectorConfiguration.enableMultilingual
                            );
                        }
                    });
                } catch (err) {
                    reject(err);
                }
            }).catch((err) => {
                error("Error while fetching multilingual",err);
            });
            data.globals.enableMultilingual = enableMultilingual || false;
        }

        /* publish tenantGlobals kafka topic */
        kafka.publishMessage({
            topic: config.get("kafkaTopic.tenantGlobals"),
            messages: [
                {
                    value: JSON.stringify(data),
                },
            ],
        });
    } catch (e) {
        error(e);
    }
};

module.exports = {
    tenantGlobals,
};
